#!/bin/bash

# 创建本地 Pod 目录结构
echo "创建本地 Pod 目录结构..."

# 创建主目录
mkdir -p ~/LocalPods

# 创建 AdsSDK
mkdir -p ~/LocalPods/AdsSDK/Classes
cp AdsSDK.podspec ~/LocalPods/AdsSDK/

# 创建 OpenCVSDK
mkdir -p ~/LocalPods/OpenCVSDK/Classes
cp OpenCVSDK.podspec ~/LocalPods/OpenCVSDK/

echo "目录结构创建完成！"
echo ""
echo "目录结构："
echo "~/LocalPods/"
echo "├── AdsSDK/"
echo "│   ├── AdsSDK.podspec"
echo "│   └── Classes/"
echo "└── OpenCVSDK/"
echo "    ├── OpenCVSDK.podspec"
echo "    └── Classes/"
echo ""
echo "使用方法："
echo "在项目的 Podfile 中添加："
echo "# 需要 Ads-CN 时："
echo "pod 'AdsSDK', :path => '~/LocalPods/AdsSDK'"
echo ""
echo "# 需要 OpenCV2 时："
echo "pod 'OpenCVSDK', :path => '~/LocalPods/OpenCVSDK'"
echo ""
echo "# 两个都需要时："
echo "pod 'AdsSDK', :path => '~/LocalPods/AdsSDK'"
echo "pod 'OpenCVSDK', :path => '~/LocalPods/OpenCVSDK'"
