# 本地 Pod 使用说明

## 目录结构
```
~/LocalPods/
├── AdsSDK/
│   ├── AdsSDK.podspec
│   └── Classes/
└── OpenCVSDK/
    ├── OpenCVSDK.podspec
    └── Classes/
```

## 设置步骤

1. **运行设置脚本**：
```bash
chmod +x setup_local_pods.sh
./setup_local_pods.sh
```

2. **在项目中使用**：

### 只需要 Ads-CN 的项目
```ruby
# Podfile
platform :ios, '12.0'

target 'YourProject' do
  use_frameworks!
  
  # 大型依赖
  pod 'AdsSDK', :path => '~/LocalPods/AdsSDK'
  
  # 其他依赖
  pod 'SnapKit'
  pod 'Alamofire'
  # ...
end
```

### 只需要 OpenCV2 的项目
```ruby
# Podfile
platform :ios, '12.0'

target 'YourProject' do
  use_frameworks!
  
  # 大型依赖
  pod 'OpenCVSDK', :path => '~/LocalPods/OpenCVSDK'
  
  # 其他依赖
  pod 'SnapKit'
  pod 'Alamofire'
  # ...
end
```

### 需要两个库的项目
```ruby
# Podfile
platform :ios, '12.0'

target 'YourProject' do
  use_frameworks!
  
  # 大型依赖
  pod 'AdsSDK', :path => '~/LocalPods/AdsSDK'
  pod 'OpenCVSDK', :path => '~/LocalPods/OpenCVSDK'
  
  # 其他依赖
  pod 'SnapKit'
  pod 'Alamofire'
  # ...
end
```

## 优势

1. **节省空间**：所有项目共享同一份库文件
2. **按需使用**：每个项目只引入需要的库
3. **版本统一**：所有项目使用相同版本
4. **管理简单**：每个库独立管理

## 添加新的大型库

如果需要添加新的大型库（比如 TensorFlow），创建新的 podspec：

```ruby
# ~/LocalPods/TensorFlowSDK/TensorFlowSDK.podspec
Pod::Spec.new do |spec|
  spec.name         = "TensorFlowSDK"
  spec.version      = "1.0.0"
  spec.summary      = "TensorFlow SDK wrapper"
  spec.platform     = :ios, "12.0"
  spec.source       = { :path => "." }
  spec.dependency 'TensorFlowLiteSwift'
end
```

然后在需要的项目中添加：
```ruby
pod 'TensorFlowSDK', :path => '~/LocalPods/TensorFlowSDK'
```
