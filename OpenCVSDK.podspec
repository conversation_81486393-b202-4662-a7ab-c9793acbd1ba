Pod::Spec.new do |spec|
  spec.name         = "OpenCVSDK"
  spec.version      = "1.0.0"
  spec.summary      = "OpenCV2 SDK wrapper for multiple projects"
  spec.description  = "A local pod that manages OpenCV2 SDK to avoid duplicating downloads across multiple projects"
  
  spec.homepage     = "https://github.com/yourname/OpenCVSDK"
  spec.license      = { :type => "MIT" }
  spec.author       = { "Your Name" => "<EMAIL>" }
  
  spec.platform     = :ios, "12.0"
  spec.source       = { :path => "." }
  
  # 如果有源文件
  spec.source_files = "Classes/**/*"
  
  # 只包含 OpenCV2
  spec.dependency 'OpenCV2'
  
end
