Pod::Spec.new do |spec|
  spec.name         = "LargeSDKs"
  spec.version      = "1.0.0"
  spec.summary      = "Large SDKs wrapper with subspecs for selective inclusion"
  spec.description  = "A local pod that manages large SDKs with subspecs to allow selective inclusion across multiple projects"
  
  spec.homepage     = "https://github.com/yourname/LargeSDKs"
  spec.license      = { :type => "MIT", :file => "LICENSE" }
  spec.author       = { "Your Name" => "<EMAIL>" }
  
  spec.platform     = :ios, "12.0"
  spec.source       = { :path => "." }
  
  # 默认不包含任何依赖
  spec.default_subspecs = []
  
  # Ads-CN 子规格
  spec.subspec 'Ads' do |ads|
    ads.dependency 'Ads-CN'
  end
  
  # OpenCV2 子规格
  spec.subspec 'OpenCV' do |opencv|
    opencv.dependency 'OpenCV2'
  end
  
  # 组合子规格
  spec.subspec 'All' do |all|
    all.dependency 'LargeSDKs/Ads'
    all.dependency 'LargeSDKs/OpenCV'
  end
  
end
