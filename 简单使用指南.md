# 共享库简单使用指南

## 🎯 目标
让多个项目共享同一份大型库文件，节省磁盘空间

## 📝 步骤

### 1. 在 Podfile 中直接写库名
```ruby
# ✅ 正确写法 - 直接写原始库名
pod 'Ads-CN'
pod 'OpenCV2'
pod 'FURenderKit'

# ❌ 错误写法 - 不要用本地Pod路径
# pod 'AdsSDK', :path => '~/LocalPods/AdsSDK'
```

### 2. 使用共享脚本安装
```bash
# 在项目目录运行
./pod_install_with_sharing.sh
```

## 🔄 完整流程示例

### 项目1（第一个项目）
```bash
cd /path/to/project1
# 编辑 Podfile，添加: pod 'Ads-CN'
./pod_install_with_sharing.sh
```
**结果**：库下载到 `~/SharedLibs/Ads-CN` (809M)

### 项目2（后续项目）
```bash
cd /path/to/project2  
# 编辑 Podfile，添加: pod 'Ads-CN'
./pod_install_with_sharing.sh
```
**结果**：创建符号链接，节省 809M 空间

### 项目3、4、5...
```bash
cd /path/to/projectN
# 编辑 Podfile，添加: pod 'Ads-CN'
./pod_install_with_sharing.sh
```
**结果**：每个项目都节省 809M 空间

## 📊 空间对比

```
传统方式：
项目1/Pods/Ads-CN: 809M
项目2/Pods/Ads-CN: 809M  
项目3/Pods/Ads-CN: 809M
总计: 2427M

共享方式：
~/SharedLibs/Ads-CN: 809M (实际文件)
项目1/Pods/Ads-CN: 0M (符号链接)
项目2/Pods/Ads-CN: 0M (符号链接)
项目3/Pods/Ads-CN: 0M (符号链接)
总计: 809M

节省: 1618M (66.7%)
```

## ⚠️ 重要提醒

1. **Podfile 中直接写原始库名**，不要用本地Pod路径
2. **必须使用 `pod_install_with_sharing.sh` 脚本**，不要直接 `pod install`
3. **第一个项目会下载库**，后续项目会自动共享

## 🛠️ 如果需要 OpenCV2

```ruby
# Podfile
pod 'Ads-CN'      # 需要广告SDK
pod 'OpenCV2'     # 需要OpenCV
```

然后运行：
```bash
./pod_install_with_sharing.sh
```

脚本会自动处理两个库的共享。
