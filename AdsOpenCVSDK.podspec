Pod::Spec.new do |spec|
  spec.name         = "AdsOpenCVSDK"
  spec.version      = "1.0.0"
  spec.summary      = "Ads-CN and OpenCV2 SDK wrapper for multiple projects"
  spec.description  = "A local pod that manages both Ads-CN and OpenCV2 SDK to avoid duplicating downloads across multiple projects"
  
  spec.homepage     = "https://github.com/yourname/AdsOpenCVSDK"
  spec.license      = { :type => "MIT", :file => "LICENSE" }
  spec.author       = { "Your Name" => "<EMAIL>" }
  
  spec.platform     = :ios, "12.0"
  spec.source       = { :path => "." }
  
  # 如果有源文件
  spec.source_files = "Classes/**/*"
  
  # 包含两个大型依赖
  spec.dependency 'Ads-CN'
  spec.dependency 'OpenCV2'
  
end
