# Uncomment the next line to define a global platform for your project
 platform :ios, '12.0'

target 'HairCut' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for HairCut
  pod 'SnapKit', '5.7.1'
  pod 'Alamofire', '5.9.1'
  pod 'ReachabilitySwift', '5.2.1'
  pod 'SDWebImage', '5.19.4'
  pod 'PromisesSwift', '2.4.0'
  pod 'SwiftyJSON', '5.0.2'
  pod 'Starscream', '4.0.8'
  pod 'BSImagePicker', '3.3.1'
  pod 'Ads-CN'
  pod 'SVProgressHUD'
  pod 'Masonry'
  pod 'UMCommon'
  pod 'UMDevice'
  pod 'UMAPM'
  pod 'JXSegmentedView'
  pod 'FURenderKit'
  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings["IPHONEOS_DEPLOYMENT_TARGET"] = "12.0"
      end
    end
  end
  
end
