<!doctype html><html lang="zh"><head><script>window.gfdatav1={"env":"prod","idc":"hl","ver":"*********","canary":0,"envName":"prod","region":"cn","runtime":"workerV2","vdc":"hl","vregion":"China-North","extra":{"canaryType":null}}</script><script type="application/json" id="__MODERN_SERVER_DATA__">{"router":{"baseUrl":"/","params":{}}}</script><script>!function(e,r,n,t,s,a,o,c,i,l,d,p,f,m){a="precollect",o="getAttribute",c="addEventListener",l=function(e){(d=[].slice.call(arguments)).push(Date.now(),location.href),(e==a?l.p.a:l.q).push(d)},l.q=[],l.p={a:[]},e[s]=l,(p=document.createElement("script")).src=n+"?bid=volc_fe_portal_doccenter&globalName="+s,p.crossOrigin=n.indexOf("sdk-web")>0?"anonymous":"use-credentials",r.getElementsByTagName("head")[0].appendChild(p),c in e&&(l.pcErr=function(r){r=r||e.event,(f=r.target||r.srcElement)instanceof Element||f instanceof HTMLElement?f[o]("integrity")?e[s](a,"sri",f[o]("href")||f[o]("src")):e[s](a,"st",{tagName:f.tagName,url:f[o]("href")||f[o]("src")}):e[s](a,"err",r.error||r.message)},l.pcRej=function(r){r=r||e.event,e[s](a,"err",r.reason||r.detail&&r.detail.reason)},e[c]("error",l.pcErr,!0),e[c]("unhandledrejection",l.pcRej,!0)),"PerformanceLongTaskTiming"in e&&((m=l.pp={entries:[]}).observer=new PerformanceObserver((function(e){m.entries=m.entries.concat(e.getEntries())})),m.observer.observe({entryTypes:["longtask","largest-contentful-paint","layout-shift"]}))}(window,document,"https://lf3-short.ibytedapm.com/slardar/fe/sdk-web/browser.cn.js",0,"Slardar")</script><script >
            ;(function(){
              window._MODERNJS_ROUTE_MANIFEST = {"routeAssets":{"main":{"chunkIds":[691,378,740,74,801,835,792],"assets":["static/css/lib-arco.647f5d8f.css","static/js/lib-arco.90c7eee0.js","static/js/lib-polyfill.0c4329cb.js","static/js/lib-lodash.ec2ff0d5.js","static/js/lib-axios.08d2720e.js","static/css/835.c7946532.css","static/js/835.1906de57.js","static/css/main.f8db2ef3.css","static/js/main.69d79e89.js"],"referenceCssAssets":["static/css/lib-arco.647f5d8f.css","static/css/835.c7946532.css","static/css/main.f8db2ef3.css"]},"$":{"chunkIds":[161],"assets":["static/css/async/$.45105145.css","static/js/async/$.7c8b1aad.js"],"referenceCssAssets":["static/css/async/$.45105145.css"]},"docs/page":{"chunkIds":[188],"assets":["static/css/async/docs/page.7cc33c7c.css","static/js/async/docs/page.bf874678.js"],"referenceCssAssets":["static/css/async/docs/page.7cc33c7c.css"]},"docs/(libid)/(docid$)/page":{"chunkIds":[831],"assets":["static/css/async/docs/(libid)/(docid$)/page.50f0e7b2.css","static/js/async/docs/(libid)/(docid$)/page.9e8c8f72.js"],"referenceCssAssets":["static/css/async/docs/(libid)/(docid$)/page.50f0e7b2.css"]},"docs/(libid)/layout":{"chunkIds":[741],"assets":["static/css/async/docs/(libid)/layout.b45bf613.css","static/js/async/docs/(libid)/layout.0f54d13d.js"],"referenceCssAssets":["static/css/async/docs/(libid)/layout.b45bf613.css"]},"docs/favorite/page":{"chunkIds":[273],"assets":["static/css/async/docs/favorite/page.ccda7d0f.css","static/js/async/docs/favorite/page.27095483.js"],"referenceCssAssets":["static/css/async/docs/favorite/page.ccda7d0f.css"]},"docs/material-download/page":{"chunkIds":[175],"assets":["static/css/async/docs/material-download/page.5d489a71.css","static/js/async/docs/material-download/page.82c51706.js"],"referenceCssAssets":["static/css/async/docs/material-download/page.5d489a71.css"]},"docs/practice/page":{"chunkIds":[952],"assets":["static/css/async/docs/practice/page.4f40aa7a.css","static/js/async/docs/practice/page.3d908ae8.js"],"referenceCssAssets":["static/css/async/docs/practice/page.4f40aa7a.css"]},"docs/practice/layout":{"chunkIds":[675],"assets":["static/css/async/docs/practice/layout.b8b785f9.css","static/js/async/docs/practice/layout.8df7a31a.js"],"referenceCssAssets":["static/css/async/docs/practice/layout.b8b785f9.css"]},"docs/layout":{"chunkIds":[143],"assets":["static/js/async/docs/layout.826290f5.js"],"referenceCssAssets":[]},"viewer-v2":{"chunkIds":[18],"assets":["static/css/async/viewer-v2.eaec6f8a.css","static/js/async/viewer-v2.d88751d7.js"],"referenceCssAssets":["static/css/async/viewer-v2.eaec6f8a.css"]},"highlightjs":{"chunkIds":[940],"assets":["static/js/async/highlightjs.4c652e8f.js"],"referenceCssAssets":[]},"inlineCodeWorker":{"chunkIds":[377],"assets":["static/js/async/inlineCodeWorker.089139d7.js"],"referenceCssAssets":[]}}};
            })();
          </script><title data-react-helmet="true">公共参数--API签名调用指南-火山引擎</title><meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no,viewport-fit=cover,minimum-scale=1,maximum-scale=1,user-scalable=no"><meta http-equiv="x-ua-compatible" content="ie=edge"><meta name="renderer" content="webkit"><meta name="layoutmode" content="standard"><meta name="imagemode" content="force"><meta name="wap-font-scale" content="no"><meta name="format-detection" content="telephone=no"><link href="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/css/lib-arco.647f5d8f.css" rel="stylesheet" crossorigin="anonymous"><link href="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/css/835.c7946532.css" rel="stylesheet" crossorigin="anonymous"><link href="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/css/main.f8db2ef3.css" rel="stylesheet" crossorigin="anonymous"><meta charset="utf-8"><meta viewport="width=device-width,maximum-scale=1,minimum-scale=1"><link rel="preconnect" href="//portal.volccdn.com"><link rel="dns-prefetch" href="//portal.volccdn.com"><link rel="preconnect" href="//console.volcengine.com"><link rel="dns-prefetch" href="//console.volcengine.com"><meta msapplication-tileimage="//portal.volccdn.com/obj/volcfe/misc/favicon.png"><meta og:image="//portal.volccdn.com/obj/volcfe/misc/favicon.png"><link rel="icon" href="//portal.volccdn.com/obj/volcfe/misc/favicon.png" sizes="32x32"><link rel="icon" href="//portal.volccdn.com/obj/volcfe/misc/favicon.png" sizes="192x192"><link apple-touch-icon-precomposed="//portal.volccdn.com/obj/volcfe/misc/favicon.png"><meta bytedance-verification-code="BFlDDn5NtCfKBA015qLJ"><meta referrer="always"><noscript>You need to enable JavaScript to run this app.</noscript><script>!function(n,t){if(n.LogAnalyticsObject=t,!n[t]){let e=function(){e.q.push(arguments)};e.q=e.q||[],n[t]=e}n[t].l=Number(new Date)}(window,"collectEvent")</script><link crossorigin="anonymous" href="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/css/async/docs/(libid)/layout.b45bf613.css" rel="stylesheet" /><link crossorigin="anonymous" href="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/css/async/docs/(libid)/(docid$)/page.50f0e7b2.css" rel="stylesheet" />  <link data-react-helmet="true" rel="canonical" href="https://www.volcengine.com/docs/6369/67268"/>
  <meta data-react-helmet="true" name="keywords" content="API签名调用指南"/><meta data-react-helmet="true" name="description" content="火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验"/><meta data-react-helmet="true" name="sharecontent" data-msg-img="https://portal.volccdn.com/obj/volcfe/misc/favicon.png" data-msg-title="公共参数--API签名调用指南-火山引擎" data-msg-content="火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验" data-msg-callback="" data-line-img="https://portal.volccdn.com/obj/volcfe/misc/favicon.png" data-line-title="公共参数--API签名调用指南-火山引擎" data-line-callback=""/>
  <script data-react-helmet="true" >try{if("undefined"!=typeof fetch){for(var e="",t=document.cookie.split("; "),o=0;o<t.length;o++){var n=t[o].split("="),c=n[0];if("csrfToken"===(c=c.trim())){e=n[1].replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent);break}}window.volcfeGetUserPromise=fetch("https://console.volcengine.com/api/passport/account/getUser",{method:"POST",credentials:"include",headers:{accept:"application/json, text/plain, */*","content-type":"application/json; charset=UTF-8","x-csrf-token":e}})}}catch(e){}</script><script data-react-helmet="true" >
(function(win, export_obj) {
  win['AccountInfo'] = {"Id":0};
})(window, 'accountInfo');
</script><script data-react-helmet="true" >window._volfce_login_modal_enabled=true;</script><script data-react-helmet="true" crossOrigin="anonymous" defer="true" src="https://lf-cdn-tos.bytescm.com/obj/static/bytereplay/********/snapshot/index.umd.js"></script>
</head><body><div id="root"><div class="wrap-ZYMs"><div class="root-h4Ln volcfe-flex" id="withRoot"><div style="width:0" id="volcfe-nav-scroll-padding" class="volcfe-nav-pc"></div><div id="appbar" data-version="4.0.4"><div id="volcfe-nav-top-banner" style="height:52px;top:-52px" class="volcfe-nav-top-banner"><div class="volcfe-nav-top-banner-inner"><span class="volcfe-nav-top-banner-tooltip"></span><a class="volcfe-nav-top-banner-text"></a><a class="volcfe-nav-top-banner-link volcfe-nav-pc"></a><div class="volcfe-nav-top-banner-close volcfe-nav-pc"></div></div></div><div style="top:0px" data-hidden="false" id="volcfe-nav"><div class="volcfe-flex-middle volcfe-nav-content volcfe-nav-theme-light"><div class="volcfe-nav-left"><a href="//www.volcengine.com" target="_self" class="volcfe-nav-link volcfe-nav-clickable volcfe-nav-logo volcfe-nav-element volcfe-nav-logo-volcengine"></a><div class="volcfe-nav-divider volcfe-nav-pc"></div><div class="volcfe-nav-element volcfe-nav-subtitle volcfe-nav-pc">文档中心</div></div><div class="volcfe-nav-middle"></div><div class="volcfe-nav-right"><div class="volcfe-nav-search-wrap"><div class="volcfe-flex-middle volcfe-nav-search"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="volcfe-nav-search-btn"><path fill-rule="evenodd" d="M16.657 5.343c2.885 2.885 3.106 7.425.663 10.563l4.145 4.145c.078.078.078.205 0 .283l-1.131 1.131c-.078.078-.205.078-.283 0l-4.146-4.144c-3.138 2.442-7.677 2.22-10.562-.664-3.124-3.124-3.124-8.19 0-11.314 3.124-3.124 8.19-3.124 11.314 0zm-1.414 1.414c-2.344-2.343-6.142-2.343-8.486 0-2.343 2.344-2.343 6.142 0 8.486 2.344 2.343 6.142 2.343 8.486 0 2.343-2.344 2.343-6.142 0-8.486z"></path></svg><form><input placeholder="请输入关键字" class="volcfe-nav-search-input" type="string" value=""/></form></div></div><a href="https://www.volcengine.com/docs" target="_blank" class="volcfe-nav-link volcfe-nav-clickable volcfe-nav-pc volcfe-nav-element volcfe-flex volcfe-nav-menu-entry">文档</a><a href="https://www.volcengine.com/beian" target="_blank" class="volcfe-nav-link volcfe-nav-clickable volcfe-nav-pc volcfe-nav-element volcfe-flex volcfe-nav-menu-entry">备案</a><a href="https://console.volcengine.com" target="_blank" class="volcfe-nav-link volcfe-nav-clickable volcfe-nav-pc volcfe-nav-element volcfe-flex volcfe-nav-menu-entry">控制台</a><div id="volcfe-nav-right"><div class="volcfe-flex-middle volcfe-nav-pc"><a href="//console.volcengine.com/auth/login?redirectURI=%2F%2Fwww.volcengine.com%2Fdocs%2F6369%2F67268" class="volcfe-nav-link volcfe-nav-clickable volcfe-nav-element volcfe-nav-menu-entry" target="_self">登录</a><a href="//console.volcengine.com/auth/signup?redirectURI=%2F%2Fwww.volcengine.com%2Fdocs%2F6369%2F67268" class="volcfe-nav-link volcfe-nav-clickable volcfe-nav-register-btn volcfe-nav-register-btn-mini" target="_self">立即注册</a></div><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg" class="volcfe-nav-element volcfe-nav-mob-button volcfe-nav-mobile"><path d="M11.9976 16.5C15.0516 16.5 17.9081 17.7521 19.971 19.922C20.3515 20.3222 20.3355 20.9552 19.9352 21.3357C19.535 21.7163 18.902 21.7003 18.5215 21.3C16.8324 19.5233 14.4979 18.5 11.9976 18.5C9.47485 18.5 7.12122 19.542 5.42958 21.3468C5.0519 21.7498 4.41907 21.7703 4.01611 21.3926C3.61315 21.0149 3.59265 20.3821 3.97033 19.9792C6.03624 17.7749 8.91613 16.5 11.9976 16.5ZM11.95 2.5C15.4017 2.5 18.2 5.29822 18.2 8.75C18.2 12.2018 15.4017 15 11.95 15C8.49818 15 5.69995 12.2018 5.69995 8.75C5.69995 5.29822 8.49818 2.5 11.95 2.5ZM11.95 4.5C9.60275 4.5 7.69995 6.40279 7.69995 8.75C7.69995 11.0972 9.60275 13 11.95 13C14.2972 13 16.2 11.0972 16.2 8.75C16.2 6.40279 14.2972 4.5 11.95 4.5Z"></path></svg></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="volcfe-nav-element volcfe-nav-mob-button volcfe-nav-mobile"><path fill-rule="evenodd" d="M19.8 17c.11 0 .2.09.2.2v1.6c0 .11-.09.2-.2.2H4.2c-.11 0-.2-.09-.2-.2v-1.6c0-.11.09-.2.2-.2h15.6zm0-6c.11 0 .2.09.2.2v1.6c0 .11-.09.2-.2.2H4.2c-.11 0-.2-.09-.2-.2v-1.6c0-.11.09-.2.2-.2h15.6zm0-6c.11 0 .2.09.2.2v1.6c0 .11-.09.2-.2.2H4.2c-.11 0-.2-.09-.2-.2V5.2c0-.11.09-.2.2-.2h15.6z"></path></svg></div></div></div><div id="volcfe-nav-mobile-menu-container"></div></div><div class="content-y9Pp volcfe-flex" id="app-content"><div class="box-k7TH"><div class="box-YLbM"><div class="mshowbtn-dxyW"><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" style="margin-right:4px" class="arco-icon arco-icon-unordered-list"><path d="M13 24h30M5 11h4m4 26h30M13 11h30M5 24h4M5 37h4"></path></svg> 导航</div><div class="sidebar-SoP1  false"><div class="content-ldPN"><div class="sidebarhead-nnRI"><div class="titlebar-LIQn"><h6>API签名调用指南</h6><div class="sidebarfloatmenuicon-GtJH"></div></div></div><div class="searchwrap-i_F4"><div role="combobox" aria-haspopup="listbox" aria-autocomplete="list" aria-expanded="false" tabindex="0" class="arco-select arco-select-single arco-select-show-search arco-select-size-default search-RqeY"><div title="" class="arco-select-view"><span class="arco-select-view-selector"><input autoComplete="off" placeholder="搜索目录或文档标题" class="arco-select-view-input" style="width:100%" value=""/><span class="arco-select-view-value-mirror">搜索目录或文档标题</span><span style="display:none" class="arco-select-view-value">搜索目录或文档标题</span></span><div aria-hidden="true" class="arco-select-suffix"><span class="arco-select-suffix-icon"><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" class="arco-icon arco-icon-search"><path d="M33.072 33.071c6.248-6.248 6.248-16.379 0-22.627-6.249-6.249-16.38-6.249-22.628 0-6.248 6.248-6.248 16.379 0 22.627 6.248 6.248 16.38 6.248 22.628 0Zm0 0 8.485 8.485"></path></svg></span></div></div></div></div><div class="box-VqUF"><div role="menu" class="arco-menu arco-menu-light arco-menu-vertical menu-sYoy"><div class="arco-menu-inner"><div tabindex="0" role="menuitem" class="arco-menu-item item-JA2c"><a href="/docs/6369/67265"><span class="label-z77I" style="padding-left:0px">概览</span></a></div><div class="arco-menu-inline item-JA2c"><div tabindex="0" aria-expanded="true" class="arco-menu-inline-header arco-menu-selected"><span><span class="label-z77I" style="padding-left:0px">调用方法</span></span><span class="arco-menu-icon-suffix is-open"><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" class="arco-icon arco-icon-down"><path d="M39.6 17.443 24.043 33 8.487 17.443"></path></svg></span></div><div class="arco-menu-inline-content" style="height:auto"><div tabindex="0" role="menuitem" class="arco-menu-item arco-menu-item-indented item-JA2c"><span><span class="arco-menu-indent"></span></span><span class="arco-menu-item-inner" style="display:block"><a href="/docs/6369/67267"><span class="label-z77I" style="padding-left:20px">请求结构</span></a></span></div><div tabindex="0" role="menuitem" class="arco-menu-item arco-menu-selected arco-menu-item-indented item-JA2c"><span><span class="arco-menu-indent"></span></span><span class="arco-menu-item-inner" style="display:block"><a href="/docs/6369/67268"><span class="label-z77I" style="padding-left:20px">公共参数</span></a></span></div><div tabindex="0" role="menuitem" class="arco-menu-item arco-menu-item-indented item-JA2c"><span><span class="arco-menu-indent"></span></span><span class="arco-menu-item-inner" style="display:block"><a href="/docs/6369/67269"><span class="label-z77I" style="padding-left:20px">签名方法</span></a></span></div><div tabindex="0" role="menuitem" class="arco-menu-item arco-menu-item-indented item-JA2c"><span><span class="arco-menu-indent"></span></span><span class="arco-menu-item-inner" style="display:block"><a href="/docs/6369/80336"><span class="label-z77I" style="padding-left:20px">返回结构</span></a></span></div><div tabindex="0" role="menuitem" class="arco-menu-item arco-menu-item-indented item-JA2c"><span><span class="arco-menu-indent"></span></span><span class="arco-menu-item-inner" style="display:block"><a href="/docs/6369/68677"><span class="label-z77I" style="padding-left:20px">公共错误码</span></a></span></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex="0" aria-expanded="false" class="arco-menu-inline-header"><span><span class="label-z77I" style="padding-left:0px">签名示例</span></span><span class="arco-menu-icon-suffix "><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" class="arco-icon arco-icon-down"><path d="M39.6 17.443 24.043 33 8.487 17.443"></path></svg></span></div><div class="arco-menu-inline-content" style="height:0;visibility:hidden"><div tabindex="0" role="menuitem" class="arco-menu-item arco-menu-item-indented item-JA2c"><span><span class="arco-menu-indent"></span></span><span class="arco-menu-item-inner" style="display:block"><a href="/docs/6369/67270"><span class="label-z77I" style="padding-left:20px">签名过程Demo</span></a></span></div><div tabindex="0" role="menuitem" class="arco-menu-item arco-menu-item-indented item-JA2c"><span><span class="arco-menu-indent"></span></span><span class="arco-menu-item-inner" style="display:block"><a href="/docs/6369/185600"><span class="label-z77I" style="padding-left:20px">签名源码示例</span></a></span></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex="0" aria-expanded="false" class="arco-menu-inline-header"><span><span class="label-z77I" style="padding-left:0px">SDK参考</span></span><span class="arco-menu-icon-suffix "><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" class="arco-icon arco-icon-down"><path d="M39.6 17.443 24.043 33 8.487 17.443"></path></svg></span></div><div class="arco-menu-inline-content" style="height:0;visibility:hidden"><div tabindex="0" role="menuitem" class="arco-menu-item arco-menu-item-indented item-JA2c"><span><span class="arco-menu-indent"></span></span><span class="arco-menu-item-inner" style="display:block"><a href="/docs/6369/156029"><span class="label-z77I" style="padding-left:20px">SDK概览</span></a></span></div></div></div></div></div></div><div class="mclosebtn-Hz9J"><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" class="arco-icon arco-icon-double-right"><path d="m11.143 38.1 14.142-14.142L11.143 9.816M22.456 38.1l14.142-14.142L22.456 9.816"></path></svg></div></div></div><div class="content-XH4l contentdoc-qaP7"><div class="box-sIN6"><ul class="breadcrumb-ev36"><li class="item-Dz0C">文档首页</li><span class="divider-DURY">/</span><span class="item-Dz0C">API签名调用指南</span><span class="divider-DURY">/</span><span class="item-Dz0C">调用方法</span><span class="divider-DURY">/</span><span class="item-Dz0C">公共参数</span></ul><div class="inputbox-XiQh"><div class="arco-input-group-wrapper arco-input-group-wrapper-default arco-input-has-suffix arco-input-search input-jHpV"><span class="arco-input-group"><span class="arco-input-inner-wrapper arco-input-inner-wrapper-default"><input placeholder="在本产品文档中搜索" class="arco-input arco-input-size-default" value=""/><span class="arco-input-group-suffix"><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" class="arco-icon arco-icon-search"><path d="M33.072 33.071c6.248-6.248 6.248-16.379 0-22.627-6.249-6.249-16.38-6.249-22.628 0-6.248 6.248-6.248 16.379 0 22.627 6.248 6.248 16.38 6.248 22.628 0Zm0 0 8.485 8.485"></path></svg></span></span></span></div></div></div><div class="title-wrap-E0Mf"><div><div class="title-M_b0">公共参数</div><div class="info-TbRN"><span>最近更新时间：<!-- -->2025.05.19 20:12:26</span><span>首次发布时间：<!-- -->2021.02.25 21:02:47</span></div></div><div class="wrap-ZgQx"><div class="toolbar-aZII"><div id="editor-foldbox"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="icon-Wtup"><path fill="#181818" fill-opacity="0.8" fill-rule="evenodd" d="M8 1.667H1.667v12.666h12.666V8h-1v5.333H2.667V2.667H8z" clip-rule="evenodd"></path><path fill="#181818" fill-opacity="0.8" d="M14.333 1.667v1H10v-1z"></path><path fill="#181818" fill-opacity="0.8" d="M13.333 1.667h1V6h-1z"></path><path fill="#181818" fill-opacity="0.8" d="m13.011 2.333.707.707-5.177 5.177-.707-.707z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="icon-Wtup "><path fill="#181818" fill-opacity="0.8" fill-rule="evenodd" d="m8 11.5 6 3.167V1.333H2v13.334zm-5 1.508 5-2.639 5 2.64V2.332H3z" clip-rule="evenodd"></path></svg><div class="divider-Xg3C"></div><a class="favorite-ckJs" href="/docs/favorite">我的收藏</a><div class="likewrap-DbBF "><div class="wideshow-AGRc"><div class="btnwrap-Sj2x "><div class="btn-BTHl"><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" class="arco-icon arco-icon-thumb-up"><path d="M7 17v26m35.17-21.394-5.948 18.697a1 1 0 0 1-.953.697H14V19h3l9.403-12.223a1 1 0 0 1 1.386-.196l2.535 1.87a6 6 0 0 1 2.044 6.974L31 19h9.265a2 2 0 0 1 1.906 2.606Z"></path></svg><span>有用</span></div></div></div><div class="smalshow-SBCL"><div class="btnwrap-Sj2x "><div class="btn-BTHl"><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" class="arco-icon arco-icon-thumb-up"><path d="M7 17v26m35.17-21.394-5.948 18.697a1 1 0 0 1-.953.697H14V19h3l9.403-12.223a1 1 0 0 1 1.386-.196l2.535 1.87a6 6 0 0 1 2.044 6.974L31 19h9.265a2 2 0 0 1 1.906 2.606Z"></path></svg><span>有用</span></div></div></div><div class="wideshow-AGRc"><div class="btnwrap-Sj2x "><div class="btn-BTHl"><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" style="transform:matrix(1, 0, 0, -1, 0, 0)" class="arco-icon arco-icon-thumb-up"><path d="M7 17v26m35.17-21.394-5.948 18.697a1 1 0 0 1-.953.697H14V19h3l9.403-12.223a1 1 0 0 1 1.386-.196l2.535 1.87a6 6 0 0 1 2.044 6.974L31 19h9.265a2 2 0 0 1 1.906 2.606Z"></path></svg><span>无用</span></div></div></div><div class="smalshow-SBCL"><div class="btnwrap-Sj2x "><div class="btn-BTHl"><svg fill="none" stroke="currentColor" stroke-width="4" viewBox="0 0 48 48" aria-hidden="true" focusable="false" style="transform:matrix(1, 0, 0, -1, 0, 0)" class="arco-icon arco-icon-thumb-up"><path d="M7 17v26m35.17-21.394-5.948 18.697a1 1 0 0 1-.953.697H14V19h3l9.403-12.223a1 1 0 0 1 1.386-.196l2.535 1.87a6 6 0 0 1 2.044 6.974L31 19h9.265a2 2 0 0 1 1.906 2.606Z"></path></svg><span>无用</span></div></div></div></div></div></div></div><div style="margin-top:24px;margin-bottom:24px;border-bottom:1px solid #E5E6EB"></div><div class="box-srhj"><div class="contentfeedback-PaAn" style="left:0px;top:0px;display:none">文档反馈</div><div><div class="volc-md-viewer content-yaZD " style="overflow:initial"><p>所有接口请求中都必须携带公共参数，为了避免重复说明，产品的API文档中可能不再重复描述这部分参数，请您在请求API时携带这部分参数，否则请求将无法通过合法性验证。</p><h2 id="公共参数如下表">公共参数如下表</h2><h3 id="_1-action与version">1. Action与Version</h3><div class="volc-custom-block-tip volc-custom-block"><p class="custom-block-title">说明</p><p>Action和Version必须放在query当中</p></div><table class=" volc-viewer-table"><thead><tr><th>名称</th><th>类型</th><th>是否必填</th><th>参数格式</th><th>描述</th><th>示例值</th></tr></thead><tbody><tr><td>Action</td><td>String</td><td>是</td><td>[a-zA-Z]+</td><td>接口名称。实际调用时请参考您使用的产品的API文档取值。</td><td>CreateUser</td></tr><tr><td>Version</td><td>String</td><td>是</td><td>YYYY-MM-DD</td><td>接口的版本。 实际调用时请参考您使用的产品的API文档取值。</td><td>2018-01-01</td></tr><tr><td>X-Expires</td><td>Int</td><td>否</td><td>整数</td><td>签名的有效时间，单位为秒，不填时默认值为900。</td><td>900</td></tr></tbody></table><h3 id="_2-签名参数">2. 签名参数</h3><div class="volc-custom-block-tip volc-custom-block"><p class="custom-block-title">说明</p><p>签名参数可以在query中，也可以在header中。</p></div><h4>（1）在Header中的场景</h4><table class=" volc-viewer-table"><thead><tr><th>名称</th><th>类型</th><th>是否必填</th><th>描述</th><th>示例值</th></tr></thead><tbody><tr><td>X-Date</td><td>String</td><td>是</td><td>使用UTC时间，精确到秒，使用遵循ISO 8601标准的格式：<code>YYYYMMDD&#x27;T&#x27;HHMMSS&#x27;Z&#x27;</code></td><td><code>20201103T104027Z</code></td></tr><tr><td>Authorization</td><td>String</td><td>是</td><td><code>HMAC-SHA256 Credential={AccessKeyId}/{ShortDate}/{Region}/{Service}/request, SignedHeaders={SignedHeaders}, Signature={Signature}</code> 。如何构建，请参考<a href="https://www.volcengine.com/docs/6369/67269" target="_blank" rel="noreferrer" class="external external">签名方法<svg class="icon outbound" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></a></td><td><code>HMAC-SHA256 Credential=AKLTMjI2ODVlYzI3ZGY1NGU4ZjhjYWRjMTlmNTM5OTZkYzE/20201230/cn-north-1/iam/request, SignedHeaders=content-type;host;x-content-sha256;x-date, Signature=28eeabbbd726b87002e0fe58ad8c1c768e619b06e2646f35b6ad7ed029a6d8a7</code></td></tr><tr><td>X-Security-Token</td><td>String</td><td>否</td><td>指安全令牌服务（Security Token Service，STS） 颁发的临时安全凭证中的SessionToken，使用长期密钥时无需填写该参数。</td><td><code>nCitKRW94N1M5aTcwQ0tTY2dpRlM0bHczaVlaekpHdnZUd253QkI2OWxQSE9N.Cj8KK09aVXA4TEstYVg5RkE3dHdqTVhNVk8wRnFjdGI3WF9mQ0RQZ3JwV3d3eTgSEFbw1JQe0EEKsrdI3CLVHosQsMXHqQYYwOHHqQYgoJaAASgBMKCWgAE6B0V4YW1wbGVCA2lhbVIMU2Vzc2lvbiBOYW1lWAFgAQ.TEMEegRPn47UwXZqD742jSTU2tzCPUWaTSsQw7CuSXGa1PlhtQfjXFbPodBHhKTdMCF8_K10OhBF6FXy4eoPQw</code></td></tr></tbody></table><p>Authorization中的信息含义：</p><table class=" volc-viewer-table"><thead><tr><th>名称</th><th>类型</th><th>备注</th><th>示例值</th></tr></thead><tbody><tr><td>AccessKeyId</td><td>String</td><td>请求的Access Key ID。</td><td><code>AKLTMjI2ODVlYzI3ZGY1NGU4ZjhjYWRjMTlmNTM5OTZkYzE</code></td></tr><tr><td>ShortDate</td><td>String</td><td>请求的短时间，使用UTC时间，精确到日。请使用格式：<code>YYYYMMDD</code></td><td><code>20180201</code></td></tr><tr><td>Region</td><td>String</td><td>请求的地域，例如：<code>cn-beijing</code> 。当您使用的产品按Region提供服务时，该参数值请填写您实际要访问的Region；当您使用非Region服务类产品时，您可以填写任一region，例如<code>cn-beijing</code>。</td><td><code>cn-beijing</code></td></tr><tr><td>Service</td><td>String</td><td>请求的服务名，请参考您使用的产品的API文档获取Service值，例如访问控制服务名为<code>iam</code></td><td><code>iam</code></td></tr><tr><td>SignedHeaders</td><td>String</td><td>参与签名的Header，多个Header间用分号分隔，目的是指明哪些header参与签名计算，从而忽略请求被proxy添加的额外header，其中host、x-date如果存在header中则必选参与。</td><td><code>content-type;host;x-content-sha256;x-date</code></td></tr><tr><td>Signature</td><td>String</td><td>计算完毕的签名。详细说明请参考<a href="https://www.volcengine.com/docs/6369/67269" target="_blank" rel="noreferrer" class="external external">签名方法<svg class="icon outbound" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></a></td><td><code>28eeabbbd726b87002e0fe58ad8c1c768e619b06e2646f35b6ad7ed029a6d8a7</code></td></tr></tbody></table><h4>（2）在Query中的场景</h4><table class=" volc-viewer-table"><thead><tr><th>名称</th><th>类型</th><th>是否必填</th><th>描述</th><th>示例值</th></tr></thead><tbody><tr><td>X-Date</td><td>String</td><td>是</td><td>使用UTC时间，精确到秒</td><td><code>20201103T104027Z</code></td></tr><tr><td>X-Algorithm</td><td>String</td><td>是</td><td>固定值：<code>HMAC-SHA256</code></td><td><code>HMAC-SHA256</code></td></tr><tr><td>X-Credential</td><td>String</td><td>是</td><td>由<code>{AccessKeyId}/{ShortDate}/{Region}/{Service}/request</code>组成。</td><td><code>AKLTMjI2ODVlYzI3ZGY1NGU4ZjhjYWRjMTlmNTM5OTZkYzE/20201230/cn-north-1/iam/request</code></td></tr><tr><td>X-SignedHeaders</td><td>String</td><td>是</td><td>参与签名的Header，多个Header间用分号分隔，目的是指明哪些header参与签名计算，从而忽略请求被proxy添加的额外header，其中host、x-date如果存在header中则必选参与。</td><td><code>content-type;host;x-content-sha256;x-date</code></td></tr><tr><td>X-Signature</td><td>String</td><td>是</td><td>计算完毕的签名。详细说明请参考<a href="https://www.volcengine.com/docs/6369/67269" target="_blank" rel="noreferrer" class="external external">签名方法<svg class="icon outbound" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></a></td><td><code>28eeabbbd726b87002e0fe58ad8c1c768e619b06e2646f35b6ad7ed029a6d8a7</code></td></tr></tbody></table></div></div></div></div><div></div></div></div><div id="app-modal" data-bytereplay-mask="ignore"></div></div><div id="app-footer"></div></div></div></div><script id="__LOADABLE_REQUIRED_CHUNKS__" type="application/json">[143,741,831]</script><script id="__LOADABLE_REQUIRED_CHUNKS___ext" type="application/json">{"namedChunks":["docs/layout","docs/(libid)/layout","docs/(libid)/(docid$)/page"]}</script><script crossorigin="anonymous" defer="true" src="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/js/async/docs/layout.826290f5.js"></script><script crossorigin="anonymous" defer="true" src="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/js/async/docs/(libid)/layout.0f54d13d.js"></script><script crossorigin="anonymous" defer="true" src="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/js/async/docs/(libid)/(docid$)/page.9e8c8f72.js"></script><script>window._SSR_DATA = {"data":{},"context":{"request":{"params":{},"query":{},"pathname":"\u002Fdocs\u002F6369\u002F67268","host":"www.volcengine.com","url":"https:\u002F\u002Fwww.volcengine.com\u002Fdocs\u002F6369\u002F67268"},"reporter":{"sessionId":"202507141435238D4D9E51D4BE1B48E7CE"}},"mode":"string","renderLevel":2}</script>
<script>window._ROUTER_DATA = {"loaderData":{"layout":{"lang":"zh"},"docs\u002Flayout":null,"docs\u002F(libid)\u002Flayout":{"curLibId":6369,"isContentDoc":false,"curLib":{"BusinessID":6369,"Category":"servicesupport","CategoryEnName":"","CategoryID":12,"CategoryName":"服务支持","Code":"","CreatedTime":"2021-02-23T06:15:49Z","Creator":"","Description":"","DirectionPath":"","DocCount":0,"DocWordCount":0,"EnName":"API","HasBindApp":false,"Index":6369,"IsCollection":0,"IsOfficialWebsiteContentCategory":2,"Language":"zh","Languages":null,"LibNameID":194,"LibVersion":"","LibraryCode":"","LibraryID":6369,"LibraryIsReused":0,"MainLibraryID":0,"Name":"API签名调用指南","NormID":26,"PublishType":1,"RelationType":0,"Status":2,"SubProductID":"P90000696","TagList":null,"UpdatedTime":"2023-03-22T05:19:42Z","VersionType":0},"nodeMap":{"0":{"children":[67265,67266,185599,156028]},"67265":{"value":{"BusinessID":67265,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:18:33Z","DocumentCode":"","DocumentID":67265,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442627322880,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":0,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"概览","Type":0,"UpdatedTime":"2021-12-02T12:29:35Z"},"children":[],"next":67267},"67266":{"value":{"BusinessID":67266,"Content":"","ContentType":"","CreatedTime":"2021-02-23T06:18:42Z","DocumentCode":"","DocumentID":67266,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442627847168,"Keywords":"","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":0,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":1,"Title":"调用方法","Type":1,"UpdatedTime":"2021-02-23T06:18:42Z"},"children":[67267,67268,67269,80336,68677]},"67267":{"value":{"BusinessID":67267,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:19:02Z","DocumentCode":"","DocumentID":67267,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442629221376,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"请求结构","Type":0,"UpdatedTime":"2025-04-23T04:05:47Z"},"children":[],"prev":67265,"next":67268},"67268":{"value":{"BusinessID":67268,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:19:09Z","DocumentCode":"","DocumentID":67268,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442629616640,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"公共参数","Type":0,"UpdatedTime":"2025-05-19T12:11:33Z"},"children":[],"prev":67267,"next":67269},"67269":{"value":{"BusinessID":67269,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:19:22Z","DocumentCode":"","DocumentID":67269,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442630468608,"Keywords":"签名,签名算法","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"签名方法","Type":0,"UpdatedTime":"2025-06-20T03:01:02Z"},"children":[],"prev":67268,"next":80336},"67270":{"value":{"BusinessID":67270,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:19:42Z","DocumentCode":"","DocumentID":67270,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":8604187329536,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":185599,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"签名过程Demo","Type":0,"UpdatedTime":"2024-09-14T07:12:14Z"},"children":[],"prev":68677,"next":185600},"68677":{"value":{"BusinessID":68677,"Content":"","ContentType":"md","CreatedTime":"2021-05-08T10:33:49Z","DocumentCode":"","DocumentID":68677,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":6040445204480,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"公共错误码","Type":0,"UpdatedTime":"2023-07-05T11:44:52Z"},"children":[],"prev":80336,"next":67270},"80336":{"value":{"BusinessID":80336,"Content":"","ContentType":"md","CreatedTime":"2021-12-02T10:44:35Z","DocumentCode":"","DocumentID":80336,"EnContent":"","EnTitle":"Response","FirstPublishedTime":"","HasFavorite":false,"Index":4862642088960,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"返回结构","Type":0,"UpdatedTime":"2023-07-27T09:38:34Z"},"children":[],"prev":67269,"next":68677},"156028":{"value":{"BusinessID":156028,"Content":"","ContentType":"","CreatedTime":"2022-11-11T08:52:00Z","DocumentCode":"","DocumentID":156028,"EnContent":"","EnTitle":"SDK","FirstPublishedTime":"","HasFavorite":false,"Index":7987837022208,"Keywords":"","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":0,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":1,"Title":"SDK参考","Type":1,"UpdatedTime":"2022-11-11T08:52:00Z"},"children":[156029]},"156029":{"value":{"BusinessID":156029,"Content":"","ContentType":"md","CreatedTime":"2022-11-11T08:52:24Z","DocumentCode":"","DocumentID":156029,"EnContent":"","EnTitle":"SDK Guide","FirstPublishedTime":"","HasFavorite":false,"Index":7987838535680,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":156028,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"SDK概览","Type":0,"UpdatedTime":"2024-03-11T09:09:18Z"},"children":[],"prev":185600},"185599":{"value":{"BusinessID":185599,"Content":"","ContentType":"","CreatedTime":"2023-02-28T05:18:00Z","DocumentCode":"","DocumentID":185599,"EnContent":"","EnTitle":"SignatureDemo","FirstPublishedTime":"","HasFavorite":false,"Index":4442631844864,"Keywords":"","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":0,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":1,"Title":"签名示例","Type":1,"UpdatedTime":"2023-02-28T05:18:00Z"},"children":[67270,185600]},"185600":{"value":{"BusinessID":185600,"Content":"","ContentType":"md","CreatedTime":"2023-02-28T05:18:52Z","DocumentCode":"","DocumentID":185600,"EnContent":"","EnTitle":"SignatureCode","FirstPublishedTime":"","HasFavorite":false,"Index":8604190793728,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":185599,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"签名源码示例","Type":0,"UpdatedTime":"2023-03-22T04:26:10Z"},"children":[],"prev":67270,"next":156029}}},"docs\u002F(libid)\u002F(docid$)\u002Fpage":{"docType":0,"apiType":"online","isContentDoc":false,"isPanelDoc":false,"curLibId":6369,"curDocId":67268,"curLib":{"BusinessID":6369,"Category":"servicesupport","CategoryEnName":"","CategoryID":12,"CategoryName":"服务支持","Code":"","CreatedTime":"2021-02-23T06:15:49Z","Creator":"","Description":"","DirectionPath":"","DocCount":0,"DocWordCount":0,"EnName":"API","HasBindApp":false,"Index":6369,"IsCollection":0,"IsOfficialWebsiteContentCategory":2,"Language":"zh","Languages":null,"LibNameID":194,"LibVersion":"","LibraryCode":"","LibraryID":6369,"LibraryIsReused":0,"MainLibraryID":0,"Name":"API签名调用指南","NormID":26,"PublishType":1,"RelationType":0,"Status":2,"SubProductID":"P90000696","TagList":null,"UpdatedTime":"2023-03-22T05:19:42Z","VersionType":0},"curDoc":{"BusinessID":67268,"Content":"\n所有接口请求中都必须携带公共参数，为了避免重复说明，产品的API文档中可能不再重复描述这部分参数，请您在请求API时携带这部分参数，否则请求将无法通过合法性验证。\n\n## 公共参数如下表\n\n### 1\\. Action与Version\n:::tip\n Action和Version必须放在query当中\n :::\n| 名称 | 类型 | 是否必填 | 参数格式 | 描述 | 示例值 |\n| --- | --- | --- | --- | --- | --- |\n| Action | String | 是 | \\[a-zA-Z\\]+ | 接口名称。实际调用时请参考您使用的产品的API文档取值。 | CreateUser |\n| Version | String | 是 | YYYY-MM-DD | 接口的版本。 实际调用时请参考您使用的产品的API文档取值。| 2018-01-01 |\n| X-Expires | Int | 否 | 整数 | 签名的有效时间，单位为秒，不填时默认值为900。 | 900 |\n\n### 2\\. 签名参数\n:::tip\n 签名参数可以在query中，也可以在header中。\n:::\n\n#### （1）在Header中的场景\n| 名称 | 类型 | 是否必填 | 描述 | 示例值 |\n| --- | --- | --- | --- | --- |\n| X-Date | String | 是 | 使用UTC时间，精确到秒，使用遵循ISO 8601标准的格式：`YYYYMMDD'T'HHMMSS'Z'` | `20201103T104027Z` |\n| Authorization | String | 是 | `HMAC-SHA256 Credential={AccessKeyId}\u002F{ShortDate}\u002F{Region}\u002F{Service}\u002Frequest, SignedHeaders={SignedHeaders}, Signature={Signature}` 。如何构建，请参考[签名方法](https:\u002F\u002Fwww.volcengine.com\u002Fdocs\u002F6369\u002F67269)| `HMAC-SHA256 Credential=AKLTMjI2ODVlYzI3ZGY1NGU4ZjhjYWRjMTlmNTM5OTZkYzE\u002F20201230\u002Fcn-north-1\u002Fiam\u002Frequest, SignedHeaders=content-type;host;x-content-sha256;x-date, Signature=28eeabbbd726b87002e0fe58ad8c1c768e619b06e2646f35b6ad7ed029a6d8a7` |\n| X-Security-Token | String | 否 | 指安全令牌服务（Security Token Service，STS） 颁发的临时安全凭证中的SessionToken，使用长期密钥时无需填写该参数。 | `nCitKRW94N1M5aTcwQ0tTY2dpRlM0bHczaVlaekpHdnZUd253QkI2OWxQSE9N.Cj8KK09aVXA4TEstYVg5RkE3dHdqTVhNVk8wRnFjdGI3WF9mQ0RQZ3JwV3d3eTgSEFbw1JQe0EEKsrdI3CLVHosQsMXHqQYYwOHHqQYgoJaAASgBMKCWgAE6B0V4YW1wbGVCA2lhbVIMU2Vzc2lvbiBOYW1lWAFgAQ.TEMEegRPn47UwXZqD742jSTU2tzCPUWaTSsQw7CuSXGa1PlhtQfjXFbPodBHhKTdMCF8_K10OhBF6FXy4eoPQw` |\n\nAuthorization中的信息含义：\n| 名称 | 类型 | 备注 | 示例值 |\n| --- | --- | --- | --- |\n| AccessKeyId | String | 请求的Access Key ID。 | `AKLTMjI2ODVlYzI3ZGY1NGU4ZjhjYWRjMTlmNTM5OTZkYzE` |\n| ShortDate | String | 请求的短时间，使用UTC时间，精确到日。请使用格式：`YYYYMMDD` | `20180201` |\n| Region | String | 请求的地域，例如：`cn-beijing` 。当您使用的产品按Region提供服务时，该参数值请填写您实际要访问的Region；当您使用非Region服务类产品时，您可以填写任一region，例如`cn-beijing`。| `cn-beijing` |\n| Service | String | 请求的服务名，请参考您使用的产品的API文档获取Service值，例如访问控制服务名为`iam` | `iam` |\n| SignedHeaders | String | 参与签名的Header，多个Header间用分号分隔，目的是指明哪些header参与签名计算，从而忽略请求被proxy添加的额外header，其中host、x-date如果存在header中则必选参与。 | `content-type;host;x-content-sha256;x-date` |\n| Signature | String | 计算完毕的签名。详细说明请参考[签名方法](https:\u002F\u002Fwww.volcengine.com\u002Fdocs\u002F6369\u002F67269)| `28eeabbbd726b87002e0fe58ad8c1c768e619b06e2646f35b6ad7ed029a6d8a7` |\n\n#### （2）在Query中的场景\n| 名称 | 类型 | 是否必填 | 描述 | 示例值 |\n| --- | --- | --- | --- | --- |\n| X-Date | String | 是 | 使用UTC时间，精确到秒 | `20201103T104027Z` |\n| X-Algorithm | String | 是 | 固定值：`HMAC-SHA256` | `HMAC-SHA256` |\n| X-Credential | String | 是 | 由`{AccessKeyId}\u002F{ShortDate}\u002F{Region}\u002F{Service}\u002Frequest`组成。 | `AKLTMjI2ODVlYzI3ZGY1NGU4ZjhjYWRjMTlmNTM5OTZkYzE\u002F20201230\u002Fcn-north-1\u002Fiam\u002Frequest` |\n| X-SignedHeaders | String | 是 | 参与签名的Header，多个Header间用分号分隔，目的是指明哪些header参与签名计算，从而忽略请求被proxy添加的额外header，其中host、x-date如果存在header中则必选参与。 | `content-type;host;x-content-sha256;x-date` |\n| X-Signature | String | 是 | 计算完毕的签名。详细说明请参考[签名方法](https:\u002F\u002Fwww.volcengine.com\u002Fdocs\u002F6369\u002F67269) | `28eeabbbd726b87002e0fe58ad8c1c768e619b06e2646f35b6ad7ed029a6d8a7` |","ContentType":"md","CreatedTime":"2021-02-23T06:19:09Z","DocumentCode":"","DocumentID":67268,"EnContent":"","EnTitle":"","FirstPublishedTime":"2021-02-25T13:02:47Z","HasFavorite":false,"Index":4442629616640,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":[],"Status":2,"Title":"公共参数","Type":0,"UpdatedTime":"2025-05-19T12:12:26Z"},"nodeMap":{"0":{"children":[67265,67266,185599,156028]},"67265":{"value":{"BusinessID":67265,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:18:33Z","DocumentCode":"","DocumentID":67265,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442627322880,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":0,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"概览","Type":0,"UpdatedTime":"2021-12-02T12:29:35Z"},"children":[],"next":67267},"67266":{"value":{"BusinessID":67266,"Content":"","ContentType":"","CreatedTime":"2021-02-23T06:18:42Z","DocumentCode":"","DocumentID":67266,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442627847168,"Keywords":"","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":0,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":1,"Title":"调用方法","Type":1,"UpdatedTime":"2021-02-23T06:18:42Z"},"children":[67267,67268,67269,80336,68677]},"67267":{"value":{"BusinessID":67267,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:19:02Z","DocumentCode":"","DocumentID":67267,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442629221376,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"请求结构","Type":0,"UpdatedTime":"2025-04-23T04:05:47Z"},"children":[],"prev":67265,"next":67268},"67268":{"value":{"BusinessID":67268,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:19:09Z","DocumentCode":"","DocumentID":67268,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442629616640,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"公共参数","Type":0,"UpdatedTime":"2025-05-19T12:11:33Z"},"children":[],"prev":67267,"next":67269},"67269":{"value":{"BusinessID":67269,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:19:22Z","DocumentCode":"","DocumentID":67269,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":4442630468608,"Keywords":"签名,签名算法","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"签名方法","Type":0,"UpdatedTime":"2025-06-20T03:01:02Z"},"children":[],"prev":67268,"next":80336},"67270":{"value":{"BusinessID":67270,"Content":"","ContentType":"md","CreatedTime":"2021-02-23T06:19:42Z","DocumentCode":"","DocumentID":67270,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":8604187329536,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":185599,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"签名过程Demo","Type":0,"UpdatedTime":"2024-09-14T07:12:14Z"},"children":[],"prev":68677,"next":185600},"68677":{"value":{"BusinessID":68677,"Content":"","ContentType":"md","CreatedTime":"2021-05-08T10:33:49Z","DocumentCode":"","DocumentID":68677,"EnContent":"","EnTitle":"","FirstPublishedTime":"","HasFavorite":false,"Index":6040445204480,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"公共错误码","Type":0,"UpdatedTime":"2023-07-05T11:44:52Z"},"children":[],"prev":80336,"next":67270},"80336":{"value":{"BusinessID":80336,"Content":"","ContentType":"md","CreatedTime":"2021-12-02T10:44:35Z","DocumentCode":"","DocumentID":80336,"EnContent":"","EnTitle":"Response","FirstPublishedTime":"","HasFavorite":false,"Index":4862642088960,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":67266,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"返回结构","Type":0,"UpdatedTime":"2023-07-27T09:38:34Z"},"children":[],"prev":67269,"next":68677},"156028":{"value":{"BusinessID":156028,"Content":"","ContentType":"","CreatedTime":"2022-11-11T08:52:00Z","DocumentCode":"","DocumentID":156028,"EnContent":"","EnTitle":"SDK","FirstPublishedTime":"","HasFavorite":false,"Index":7987837022208,"Keywords":"","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":0,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":1,"Title":"SDK参考","Type":1,"UpdatedTime":"2022-11-11T08:52:00Z"},"children":[156029]},"156029":{"value":{"BusinessID":156029,"Content":"","ContentType":"md","CreatedTime":"2022-11-11T08:52:24Z","DocumentCode":"","DocumentID":156029,"EnContent":"","EnTitle":"SDK Guide","FirstPublishedTime":"","HasFavorite":false,"Index":7987838535680,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":156028,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"SDK概览","Type":0,"UpdatedTime":"2024-03-11T09:09:18Z"},"children":[],"prev":185600},"185599":{"value":{"BusinessID":185599,"Content":"","ContentType":"","CreatedTime":"2023-02-28T05:18:00Z","DocumentCode":"","DocumentID":185599,"EnContent":"","EnTitle":"SignatureDemo","FirstPublishedTime":"","HasFavorite":false,"Index":4442631844864,"Keywords":"","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":0,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":1,"Title":"签名示例","Type":1,"UpdatedTime":"2023-02-28T05:18:00Z"},"children":[67270,185600]},"185600":{"value":{"BusinessID":185600,"Content":"","ContentType":"md","CreatedTime":"2023-02-28T05:18:52Z","DocumentCode":"","DocumentID":185600,"EnContent":"","EnTitle":"SignatureCode","FirstPublishedTime":"","HasFavorite":false,"Index":8604190793728,"Keywords":"API签名调用指南","Language":"zh","LibraryCode":"","LibraryID":6369,"MDContent":"","ParentCode":"","ParentID":185599,"RedirectInfo":null,"ReuseDocumentVersionList":null,"Status":2,"Title":"签名源码示例","Type":0,"UpdatedTime":"2023-03-22T04:26:10Z"},"children":[],"prev":67270,"next":156029}},"growthMap":null,"feedbackShow":false,"feedbackType":"unknow","feedbackContent":"","tagIDs":""}},"errors":null}</script><script defer="defer" src="//portal.volccdn.com/obj/volcfe-scm/common-lib/static/js/index.********.js" crossorigin="anonymous"></script><script defer="defer">!function(){"use strict";var e,t,n,r,o,i={},c={};function a(e){var t=c[e];if(void 0!==t)return t.exports;var n=c[e]={id:e,loaded:!1,exports:{}};return i[e].call(n.exports,n,n.exports,a),n.loaded=!0,n.exports}a.m=i,e=[],a.O=function(t,n,r,o){if(!n){var i=1/0;for(f=0;f<e.length;f++){n=e[f][0],r=e[f][1],o=e[f][2];for(var c=!0,u=0;u<n.length;u++)(!1&o||i>=o)&&Object.keys(a.O).every((function(e){return a.O[e](n[u])}))?n.splice(u--,1):(c=!1,o<i&&(i=o));if(c){e.splice(f--,1);var d=r();void 0!==d&&(t=d)}}return t}o=o||0;for(var f=e.length;f>0&&e[f-1][2]>o;f--)e[f]=e[f-1];e[f]=[n,r,o]},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},n=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__},a.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var o=Object.create(null);a.r(o);var i={};t=t||[null,n({}),n([]),n(n)];for(var c=2&r&&e;"object"==typeof c&&!~t.indexOf(c);c=n(c))Object.getOwnPropertyNames(c).forEach((function(t){i[t]=function(){return e[t]}}));return i.default=function(){return e},a.d(o,i),o},a.d=function(e,t){for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.f={},a.e=function(e){return Promise.all(Object.keys(a.f).reduce((function(t,n){return a.f[n](e,t),t}),[]))},a.u=function(e){return"static/js/async/"+({18:"viewer-v2",143:"docs/layout",161:"$",175:"docs/material-download/page",188:"docs/page",273:"docs/favorite/page",377:"inlineCodeWorker",675:"docs/practice/layout",741:"docs/(libid)/layout",831:"docs/(libid)/(docid$)/page",940:"highlightjs",952:"docs/practice/page"}[e]||e)+"."+{18:"d88751d7",124:"27d3d8c0",138:"65985269",143:"826290f5",161:"7c8b1aad",175:"82c51706",188:"bf874678",273:"27095483",359:"c243125f",377:"089139d7",407:"4ad9a124",518:"800f010f",520:"7a97f860",558:"badc7231",675:"8df7a31a",741:"0f54d13d",748:"fd118f2d",766:"8f09a22d",831:"9e8c8f72",893:"99486a52",940:"4c652e8f",952:"3d908ae8",957:"106da15f"}[e]+".js"},a.miniCssF=function(e){return"static/css/async/"+({18:"viewer-v2",161:"$",175:"docs/material-download/page",188:"docs/page",273:"docs/favorite/page",675:"docs/practice/layout",741:"docs/(libid)/layout",831:"docs/(libid)/(docid$)/page",952:"docs/practice/page"}[e]||e)+"."+{18:"eaec6f8a",161:"45105145",175:"5d489a71",188:"7cc33c7c",273:"ccda7d0f",518:"9b783ec2",675:"b8b785f9",741:"b45bf613",748:"1ba7527d",831:"50f0e7b2",893:"e0d3a7b9",952:"4f40aa7a"}[e]+".css"},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.hmd=function(e){return(e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:function(){throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r={},o="doccenter:",a.l=function(e,t,n,i){if(r[e])r[e].push(t);else{var c,u;if(void 0!==n)for(var d=document.getElementsByTagName("script"),f=0;f<d.length;f++){var l=d[f];if(l.getAttribute("src")==e||l.getAttribute("data-webpack")==o+n){c=l;break}}c||(u=!0,(c=document.createElement("script")).charset="utf-8",c.timeout=120,a.nc&&c.setAttribute("nonce",a.nc),c.setAttribute("data-webpack",o+n),c.src=e,0!==c.src.indexOf(window.location.origin+"/")&&(c.crossOrigin="anonymous")),r[e]=[t];var s=function(t,n){c.onerror=c.onload=null,clearTimeout(p);var o=r[e];if(delete r[e],c.parentNode&&c.parentNode.removeChild(c),o&&o.forEach((function(e){return e(n)})),t)return t(n)},p=setTimeout(s.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=s.bind(null,c.onerror),c.onload=s.bind(null,c.onload),u&&document.head.appendChild(c)}},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e},a.p="//portal.volccdn.com/obj/volcfe-scm/doccenter/",function(){if("undefined"!=typeof document){var e={691:0};a.f.miniCss=function(t,n){e[t]?n.push(e[t]):0!==e[t]&&{18:1,161:1,175:1,188:1,273:1,518:1,675:1,741:1,748:1,831:1,893:1,952:1}[t]&&n.push(e[t]=function(e){return new Promise((function(t,n){var r=a.miniCssF(e),o=a.p+r;if(function(e,t){for(var n=document.getElementsByTagName("link"),r=0;r<n.length;r++){var o=(c=n[r]).getAttribute("data-href")||c.getAttribute("href");if("stylesheet"===c.rel&&(o===e||o===t))return c}var i=document.getElementsByTagName("style");for(r=0;r<i.length;r++){var c;if((o=(c=i[r]).getAttribute("data-href"))===e||o===t)return c}}(r,o))return t();!function(e,t,n,r,o){var i=document.createElement("link");i.rel="stylesheet",i.type="text/css",a.nc&&(i.nonce=a.nc),i.onerror=i.onload=function(n){if(i.onerror=i.onload=null,"load"===n.type)r();else{var c=n&&n.type,a=n&&n.target&&n.target.href||t,u=new Error("Loading CSS chunk "+e+" failed.\n("+c+": "+a+")");u.name="ChunkLoadError",u.code="CSS_CHUNK_LOAD_FAILED",u.type=c,u.request=a,i.parentNode&&i.parentNode.removeChild(i),o(u)}},i.href=t,0!==i.href.indexOf(window.location.origin+"/")&&(i.crossOrigin="anonymous"),document.head.appendChild(i)}(e,o,0,t,n)}))}(t).then((function(){e[t]=0}),(function(n){throw delete e[t],n})))}}}(),function(){var e={691:0};a.f.j=function(t,n){var r=a.o(e,t)?e[t]:void 0;if(0!==r)if(r)n.push(r[2]);else if(691!=t){var o=new Promise((function(n,o){r=e[t]=[n,o]}));n.push(r[2]=o);var i=a.p+a.u(t),c=new Error;a.l(i,(function(n){if(a.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var o=n&&("load"===n.type?"missing":n.type),i=n&&n.target&&n.target.src;c.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",c.name="ChunkLoadError",c.type=o,c.request=i,r[1](c)}}),"chunk-"+t,t)}else e[t]=0},a.O.j=function(t){return 0===e[t]};var t=function(t,n){var r,o,i=n[0],c=n[1],u=n[2],d=0;if(i.some((function(t){return 0!==e[t]}))){for(r in c)a.o(c,r)&&(a.m[r]=c[r]);if(u)var f=u(a)}for(t&&t(n);d<i.length;d++)o=i[d],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return a.O(f)},n=self.__LOADABLE_LOADED_CHUNKS__=self.__LOADABLE_LOADED_CHUNKS__||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))}(),a.nc=void 0}()</script><script defer="defer" src="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/js/lib-arco.90c7eee0.js" crossorigin="anonymous"></script><script defer="defer" src="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/js/lib-polyfill.0c4329cb.js" crossorigin="anonymous"></script><script defer="defer" src="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/js/lib-lodash.ec2ff0d5.js" crossorigin="anonymous"></script><script defer="defer" src="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/js/lib-axios.08d2720e.js" crossorigin="anonymous"></script><script defer="defer" src="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/js/835.1906de57.js" crossorigin="anonymous"></script><script defer="defer" src="//portal.volccdn.com/obj/volcfe-scm/doccenter/static/js/main.69d79e89.js" crossorigin="anonymous"></script></body></html>