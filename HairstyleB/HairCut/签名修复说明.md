# 签名修复说明

## 🔐 问题分析

### 原始错误
```json
{
  "CodeN": 100010,
  "Code": "SignatureDoesNotMatch", 
  "Message": "The request signature we calculated does not match the signature you provided. Check your Secret Access Key and signing method."
}
```

### 问题原因
1. **查询字符串缺失**: 原始签名算法没有包含Action和Version参数
2. **规范请求格式错误**: 没有按照火山引擎的标准格式构建
3. **日志输出过长**: base64图片数据和完整JSON导致日志不可读

## 🔧 修复内容

### 1. 签名算法修复

#### 修复前
```swift
// 构建规范请求
let canonicalRequest = """
POST
/

content-type:application/json
host:visual.volcengineapi.com
x-date:\(dateString)

content-type;host;x-date
\(bodyHash)
"""
```

#### 修复后
```swift
// 构建查询字符串（包含Action和Version）
let queryString = "Action=\(action)&Version=\(version)"

// 构建规范请求
let canonicalRequest = """
POST
/
\(queryString)
content-type:application/json
host:visual.volcengineapi.com
x-date:\(dateString)

content-type;host;x-date
\(bodyHash)
"""
```

### 2. 日志输出优化

#### 修复前
```swift
// 打印完整的base64和JSON
print("binary_data_base64: \(base64String)")  // 几万字符
print("JSON Body: \(fullJsonString)")         // 完整JSON
```

#### 修复后
```swift
// 只显示长度信息
print("binary_data_base64: [\"base64_length_\(first.count)\"]")
// 移除完整JSON输出，只保留关键参数
```

### 3. 签名调试信息

添加了详细的签名调试输出：
```swift
print("🔐 ===== 签名调试信息 =====")
print("📅 Date String: \(dateString)")
print("📅 Date Only: \(dateOnly)")
print("🔗 Query String: \(queryString)")
print("📄 Body Hash: \(bodyHash)")
print("📋 Canonical Request:")
print(canonicalRequest)
print("🔐 String To Sign:")
print(stringToSign)
print("🔐 Final Signature: \(signature)")
```

## 🧪 测试方法

### 1. 签名调试测试
```swift
// 在任何视图控制器中调用
self.testSignatureDebug()
// 选择 "测试签名生成"
```

### 2. 完整请求测试
```swift
// 测试完整的API请求
self.testSignatureDebug()
// 选择 "测试完整请求"
```

### 3. Postman测试
```swift
// 生成Postman测试信息
self.testSignatureDebug()
// 选择 "生成Postman信息"
```

## 📋 控制台输出示例

### 签名调试信息
```
🔐 ===== 签名调试信息 =====
📅 Date String: 20250714T123456Z
📅 Date Only: 20250714
🔗 Query String: Action=CVSync2AsyncSubmitTask&Version=2022-08-31
📄 Body Hash: a1b2c3d4e5f6...
📋 Canonical Request:
POST
/
Action=CVSync2AsyncSubmitTask&Version=2022-08-31
content-type:application/json
host:visual.volcengineapi.com
x-date:20250714T123456Z

content-type;host;x-date
a1b2c3d4e5f6...

🔐 String To Sign:
HMAC-SHA256
20250714T123456Z
20250714/cn-north-1/cv/request
canonical_request_hash...

🔐 Final Signature: final_signature_hash...
```

### 简化的请求日志
```
🔗 ===== 提交任务请求详情 =====
📍 URL: https://visual.volcengineapi.com/?Action=CVSync2AsyncSubmitTask&Version=2022-08-31
🔧 Method: POST
📋 Headers:
   Content-Type: application/json
   X-Date: 20250714T123456Z
   Authorization: HMAC-SHA256 Credential=...
📦 Body Parameters:
   req_key: seededit_v3.0
   binary_data_base64: ["base64_length_12345"]
   prompt: 测试美甲效果
   scale: 1
   seed: -1
```

## 🔍 错误排查

### 1. 如果仍然出现签名错误
- 检查Access Key ID和Secret Access Key是否正确
- 确认时间戳是否在有效范围内（通常15分钟）
- 验证网络时间是否准确

### 2. 如果出现其他错误
- 400错误：检查请求参数格式
- 401错误：检查认证信息
- 403错误：检查账户权限和服务开通状态
- 429错误：请求频率过高，需要限流

### 3. 调试步骤
1. 运行签名调试测试
2. 检查控制台的签名调试信息
3. 对比火山引擎文档的签名示例
4. 使用Postman验证请求

## 📊 验证清单

- [ ] 查询字符串包含Action和Version
- [ ] 规范请求格式正确
- [ ] 时间戳格式为UTC时间
- [ ] Body哈希计算正确
- [ ] 签名密钥派生正确
- [ ] 最终签名计算正确
- [ ] 请求头格式正确

## 🚀 下一步

1. **运行测试**: 使用新的签名调试功能验证修复
2. **检查日志**: 查看简化后的日志输出
3. **Postman验证**: 使用生成的信息在Postman中测试
4. **实际使用**: 在美甲处理功能中测试完整流程

---

💡 **提示**: 如果签名仍然有问题，可以使用签名调试功能查看详细的签名计算过程，对比火山引擎官方文档进行排查。
