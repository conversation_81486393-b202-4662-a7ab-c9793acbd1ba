//
//  NailModel.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import Foundation
import UIKit
import Alamofire
import Promises

class NailModel: Decodable {
    var id: String = ""
    var image: String = ""
    var name: String = ""
    var isVip: Bool = false
    var type: Int = 0
    var sex: Int = 0
    var prompt: String = ""
    var name_en: String = ""
    var origin_image: UIImage?
    
    // 使用通用解码方式，让任何字段缺失都不会导致解析失败
    required init(from decoder: Decoder) throws {
        // 首先尝试按键值解析
        if let container = try? decoder.container(keyedBy: CodingKeys.self) {
            // 必须的字段
            id = try container.decodeIfPresent(String.self, forKey: .id) ?? ""
            image = try container.decodeIfPresent(String.self, forKey: .image) ?? ""
            name = try container.decodeIfPresent(String.self, forKey: .name) ?? ""
            
            // 所有其他字段都视为可选
            isVip = try container.decodeIfPresent(Bool.self, forKey: .isVip) ?? false
            type = try container.decodeIfPresent(Int.self, forKey: .type) ?? 0
            sex = try container.decodeIfPresent(Int.self, forKey: .sex) ?? 0
            prompt = try container.decodeIfPresent(String.self, forKey: .prompt) ?? ""
            name_en = try container.decodeIfPresent(String.self, forKey: .name_en) ?? ""
        }
    }
    
    // 如果 JSON 字段名和属性名不一致，需定义 CodingKeys
    private enum CodingKeys: String, CodingKey {
        case id, image, name, isVip, type, sex, prompt, name_en
    }
    
    // MARK: - 网络请求方法
    
    /// 获取美甲数据
    /// - Parameters:
    ///   - completion: 完成回调
    static func fetchNailData(completion: @escaping (Result<[NailModel], Error>) -> Void) {
        let url = "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/nailsmodel.json"
        let timestamp = Date().timeIntervalSince1970
        
        AF.request(
            url,
            parameters: ["_t": timestamp],
            requestModifier: { $0.timeoutInterval = 15 }
        )
        .validate(statusCode: 200..<300)
        .responseDecodable(of: [NailModel].self) { response in
            switch response.result {
            case .success(let data):
                completion(.success(data))
            case .failure(let error):
                let statusCode = response.response?.statusCode ?? -1
                let enhancedError = NSError(
                    domain: "NetworkError",
                    code: statusCode,
                    userInfo: [NSLocalizedDescriptionKey: "请求失败: \(error.localizedDescription)"]
                )
                completion(.failure(enhancedError))
            }
        }
    }
    
    /// 使用Promise方式获取美甲数据
    /// - Returns: Promise<[NailModel]>
    static func fetchNailDataPromise() -> Promise<[NailModel]> {
        let promise = Promise<[NailModel]>.pending()
        
        fetchNailData { result in
            switch result {
            case .success(let models):
                promise.fulfill(models)
            case .failure(let error):
                promise.reject(error)
            }
        }
        
        return promise
    }
    
    /// 从指定URL获取美甲数据
    /// - Parameters:
    ///   - url: 数据URL
    ///   - parameters: 请求参数
    ///   - completion: 完成回调
    static func fetchNailData(
        from url: String,
        parameters: [String: Any]? = nil,
        completion: @escaping (Result<[NailModel], Error>) -> Void
    ) {
        let timestamp = Date().timeIntervalSince1970
        
        AF.request(
            url,
            parameters: parameters ?? ["_t": timestamp],
            requestModifier: { $0.timeoutInterval = 15 }
        )
        .validate(statusCode: 200..<300)
        .responseDecodable(of: [NailModel].self) { response in
            switch response.result {
            case .success(let data):
                completion(.success(data))
            case .failure(let error):
                let statusCode = response.response?.statusCode ?? -1
                let enhancedError = NSError(
                    domain: "NetworkError",
                    code: statusCode,
                    userInfo: [NSLocalizedDescriptionKey: "请求失败: \(error.localizedDescription)"]
                )
                completion(.failure(enhancedError))
            }
        }
    }
    
    /// 转换为字典格式，用于AI处理
    /// - Returns: 包含美甲处理所需参数的字典
    public func toDictionary() -> [String: Any] {
        var result = [String: Any]()
        result["img"] = self.origin_image
        result["prompt"] = self.prompt
        result["negative_prompt"] = "blurry, low quality, distorted, artifacts, noise, bad composition, messy background, cluttered background, harsh lighting"
        result["type"] = "nail"
        result["nail_style"] = self.name
        
        return result
    }
}

// MARK: - 美甲数据管理器
class NailDataManager {
    static let shared = NailDataManager()
    
    private var allNailModels: [NailModel] = []
    private var filteredModels: [NailModel] = []
    
    private init() {}
    
    /// 加载美甲数据
    /// - Parameter completion: 完成回调
    func loadNailData(completion: @escaping (Result<[NailModel], Error>) -> Void) {
        NailModel.fetchNailData { [weak self] result in
            switch result {
            case .success(let models):
                self?.allNailModels = models
                self?.filteredModels = models
                completion(.success(models))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    /// 获取所有美甲模型
    /// - Returns: 美甲模型数组
    func getAllModels() -> [NailModel] {
        return allNailModels
    }
    
    /// 根据性别筛选美甲模型
    /// - Parameter sex: 性别 (0: 女性, 1: 男性)
    /// - Returns: 筛选后的美甲模型数组
    func getModelsBySex(_ sex: Int) -> [NailModel] {
        return allNailModels.filter { $0.sex == sex }
    }
    
    /// 根据类型筛选美甲模型
    /// - Parameter type: 类型
    /// - Returns: 筛选后的美甲模型数组
    func getModelsByType(_ type: Int) -> [NailModel] {
        return allNailModels.filter { $0.type == type }
    }
    
    /// 获取VIP美甲模型
    /// - Returns: VIP美甲模型数组
    func getVipModels() -> [NailModel] {
        return allNailModels.filter { $0.isVip }
    }
    
    /// 获取免费美甲模型
    /// - Returns: 免费美甲模型数组
    func getFreeModels() -> [NailModel] {
        return allNailModels.filter { !$0.isVip }
    }
    
    /// 根据ID查找美甲模型
    /// - Parameter id: 模型ID
    /// - Returns: 美甲模型或nil
    func getModelById(_ id: String) -> NailModel? {
        return allNailModels.first { $0.id == id }
    }
    
    /// 搜索美甲模型
    /// - Parameter keyword: 搜索关键词
    /// - Returns: 匹配的美甲模型数组
    func searchModels(keyword: String) -> [NailModel] {
        if keyword.isEmpty {
            return allNailModels
        }
        
        return allNailModels.filter { model in
            model.name.lowercased().contains(keyword.lowercased()) ||
            model.name_en.lowercased().contains(keyword.lowercased()) ||
            model.prompt.lowercased().contains(keyword.lowercased())
        }
    }
}
