//
//  FaceTestData.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/6.
//

import Foundation

class FaceTestData: NSObject {
    var gender: FaceTestGender
    var age: Int = 0
    var glassess: FaceTestGlasses
    var faceshape: FaceShape = .noChoose
    var beauty: Int
    var expression: FaceTestExpression
    var letfEyeCenter: CGPoint
    var rightEyeCenter: CGPoint
    var rotation: Int64
    
    init(gender: FaceTestGender, age: Int, glassess: FaceTestGlasses, faceshape: FaceShape, beauty: Int, expression: FaceTestExpression, letfEyeCenter: CGPoint, rightEyeCenter: CGPoint, rotation: Int64) {
        self.gender = gender
        self.age = age
        self.glassess = glassess
        self.faceshape = faceshape
        self.beauty = beauty
        self.expression = expression
        self.letfEyeCenter = letfEyeCenter
        self.rightEyeCenter = rightEyeCenter
        self.rotation = rotation
    }
}
