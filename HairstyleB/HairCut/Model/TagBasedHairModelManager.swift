import Foundation

class TagBasedHairModelManager {
    // 所有模型数据
    private var allModels: [AIHairModel] = []
    // 按标签分组的模型数据
    private var modelsByTag: [String: [AIHairModel]] = [:]
    // 所有唯一标签
    private var allTags: [String] = []
    // 固定顺序的标签
    private let fixedOrderTags: [String]
    
    // 初始化方法
    init(models: [AIHairModel] = []) {
        // 根据当前语言环境初始化固定标签
        let currentLanguage = LanguageTool.currentLanguage()
        if currentLanguage == .chineseSimplified || currentLanguage == .chineseTranditional {
            fixedOrderTags = ["发型", "造型", "美甲" ]
        } else {
            fixedOrderTags = ["Hair", "Look", "Nails"]
        }
        
        if !models.isEmpty {
            updateModels(models)
        }
    }
    
    // 更新模型数据并重新分组
    func updateModels(_ models: [AIHairModel]) {
        self.allModels = models
        generateTagGroups()
    }
    
    // 生成标签分组
    private func generateTagGroups() {
        // 清空现有分组
        modelsByTag.removeAll()
        var tagSet = Set<String>()
        
        // 获取当前语言环境
        let currentLanguage = LanguageTool.currentLanguage()
        let isChinese = currentLanguage == .chineseSimplified || currentLanguage == .chineseTranditional
        
        // 预先创建固定顺序的标签组
        for tag in fixedOrderTags {
            modelsByTag[tag] = []
        }
        
        // 添加"发型"分类下的所有模型 (移除了全部分类)
        // modelsByTag[fixedOrderTags[0]] = allModels
        
        // 遍历所有模型
        for model in allModels {
            // 根据语言环境选择合适的标签数组
            let tagArray = isChinese ? model.tag : (model.tag_en.isEmpty ? model.tag : model.tag_en)
            
            // 遍历模型的每个标签
            for tag in tagArray {
                // 如果该标签还没有对应的数组，创建一个
                if modelsByTag[tag] == nil {
                    modelsByTag[tag] = []
                }
                // 将模型添加到该标签的数组中
                modelsByTag[tag]?.append(model)
                // 添加标签到集合中以去重
                tagSet.insert(tag)
            }
        }
        
        // 整理标签顺序：先固定顺序，再其他标签
        allTags = []
        
        // 首先添加固定顺序的标签
        for tag in fixedOrderTags {
            allTags.append(tag)
        }
        
        // 然后添加其他标签（排除已添加的固定标签）
        let otherTags = Array(tagSet).filter { !fixedOrderTags.contains($0) }.sorted()
        allTags.append(contentsOf: otherTags)
    }
    
    // 获取所有标签
    func getAllTags() -> [String] {
        return allTags
    }
    
    // 获取特定标签下的所有模型
    func getModels(forTag tag: String) -> [AIHairModel] {
        return modelsByTag[tag] ?? []
    }
    
    // 获取指定索引位置的标签
    func getTag(at index: Int) -> String? {
        guard index >= 0 && index < allTags.count else {
            return nil
        }
        return allTags[index]
    }
    
    // 获取标签数量
    func getTagCount() -> Int {
        return allTags.count
    }
    
    // 该方法已不需要，因为已在生成标签时添加了"全部"分类
    func addAllCategory() {
        // 实现保留但功能已在generateTagGroups中实现
    }
} 
