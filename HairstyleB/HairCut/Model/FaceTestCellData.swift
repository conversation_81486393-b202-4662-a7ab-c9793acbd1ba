//
//  FaceTestCellData.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/8/9.
//

import Foundation
import UIKit

class FaceTestCellData: NSObject {
    public var leftEye: CGPoint?
    public var rightEye: CGPoint?
    public var rotation: Int64?
    public var hairImageUrlString: String?
    public var image: UIImage?
    
    init(leftEye: CGPoint? = nil, rightEye: CGPoint? = nil, rotation: Int64? = nil, hairImageUrlString: String? = nil, image: UIImage? = nil) {
        self.leftEye = leftEye
        self.rightEye = rightEye
        self.rotation = rotation
        self.hairImageUrlString = hairImageUrlString
        self.image = image
    }
}
