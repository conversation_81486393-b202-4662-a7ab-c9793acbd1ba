//
//  HairEditModel.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import UIKit

/// 发型编辑功能模型
struct HairEditFunction {
    let id: String
    let name: String
    let imageName: String
    let isOriginal: Bool // 是否是原图按钮
    
    init(id: String, name: String, imageName: String, isOriginal: Bool = false) {
        self.id = id
        self.name = name
        self.imageName = imageName
        self.isOriginal = isOriginal
    }
}

/// 发型编辑管理器
class HairEditManager {
    static let shared = HairEditManager()

    private init() {}

    /// 获取所有发型编辑功能
    func getAllHairEditFunctions() -> [HairEditFunction] {
        return [
            HairEditFunction(id: "original", name: "原图".localized, imageName: "原图", isOriginal: true),
            HairEditFunction(id: "bangs", name: "刘海".localized, imageName: "刘海"),
            HairEditFunction(id: "long_hair", name: "长发".localized, imageName: "长发"),
            HairEditFunction(id: "bangs_long_hair", name: "刘海加长发".localized, imageName: "刘海加长发"),
            HairEditFunction(id: "straight_hair", name: "直发".localized, imageName: "直发"),
            HairEditFunction(id: "light_curl", name: "轻程度卷发".localized, imageName: "轻度卷发"),
            HairEditFunction(id: "heavy_curl", name: "重程度卷发".localized, imageName: "重度卷发"),
            HairEditFunction(id: "short_hair", name: "短发".localized, imageName: "短发"),
            HairEditFunction(id: "light_volume", name: "轻程度增发".localized, imageName: "轻度增发"),
            HairEditFunction(id: "medium_volume", name: "中程度增发".localized, imageName: "中度增发"),
            HairEditFunction(id: "heavy_volume", name: "重程度增发".localized, imageName: "重度增发"),
            HairEditFunction(id: "hairline_fill", name: "补发际线".localized, imageName: "补发际线"),
            HairEditFunction(id: "hair_part_fill", name: "补发缝".localized, imageName: "补发缝"),
            HairEditFunction(id: "oil_removal", name: "头发去油".localized, imageName: "头发去油"),
            HairEditFunction(id: "blonde_hair", name: "金发".localized, imageName: "金发"),
            HairEditFunction(id: "smooth_hair", name: "头发柔顺".localized, imageName: "头发柔顺")
        ]
    }

    /// 将功能ID转换为API参数
    func getHairTypeForFunction(_ functionId: String) -> Int {
        switch functionId {
        case "bangs":
            return 101  // 刘海（默认）
        case "long_hair":
            return 201  // 长发
        case "bangs_long_hair":
            return 301  // 刘海加长发
        case "medium_volume":
            return 401  // 中程度增发
        case "light_volume":
            return 402  // 轻程度增发
        case "heavy_volume":
            return 403  // 重程度增发
        case "light_curl":
            return 502  // 轻程度卷发
        case "heavy_curl":
            return 503  // 重程度卷发
        case "short_hair":
            return 603  // 短发
        case "blonde_hair":
            return 801  // 金发
        case "straight_hair":
            return 901  // 直发
        case "oil_removal":
            return 1001 // 头发去油
        case "hairline_fill":
            return 1101 // 补发际线
        case "smooth_hair":
            return 1201 // 头发柔顺
        case "hair_part_fill":
            return 1301 // 补发缝
        default:
            return 101  // 默认刘海
        }
    }

    /// 应用发型编辑效果
    func applyHairEditEffect(_ functionId: String, to image: UIImage, completion: @escaping (Result<UIImage, Error>) -> Void) {
        // 如果是原图，直接返回
        if functionId == "original" {
            completion(.success(image))
            return
        }

        // 获取对应的发型类型
        let hairType = getHairTypeForFunction(functionId)

        let request = VolcanoEngineAPI.HairEditRequest(
            originalImage: image,
            hairType: hairType
        )

        // 使用动态密钥方法
        VolcanoEngineAPI.processHairEditWithDynamicKeys(request: request) { result in
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }
}
