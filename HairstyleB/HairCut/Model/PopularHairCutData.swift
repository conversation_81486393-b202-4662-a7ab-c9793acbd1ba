//
//  PopularHairCutData.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/6.
//

import Foundation

class PopularHairCutData: NSObject {
    let titleString: String
    let popularType: String
    
    init(titleString: String, popularType: String) {
        self.titleString = titleString
        self.popularType = popularType
    }
    
    func getPopularTypeImage() -> String? {
        switch self.popularType {
        case FeMalePopularTemplate.Lolita.rawValue: return "洛丽塔发型.jpg"
        case FeMalePopularTemplate.GreyGradientCurl.rawValue: return "灰色渐变卷发.jpg"
        case FeMalePopularTemplate.BraidedWithHighlights.rawValue: return "挑染编织发.jpg"
        case MalePopularTemplate.WoolRoll.rawValue: return "羊毛卷.jpg"
        case MalePopularTemplate.GrainPressing.rawValue: return "纹理烫.jpg"
        case MalePopularTemplate.ExplosiveHead.rawValue: return "爆炸头.jpg"
        default:
            return nil
        }
    }
    
    func getAIHairCutData() -> AIHairCutData? {
        let data = AIHairCutData()
        switch self.popularType {
        case FeMalePopularTemplate.Lolita.rawValue: 
            data.prompt = FeMalePopularTemplate.Lolita.rawValue
            return data
        case FeMalePopularTemplate.GreyGradientCurl.rawValue: 
            data.prompt = FeMalePopularTemplate.GreyGradientCurl.rawValue
            return data
        case FeMalePopularTemplate.BraidedWithHighlights.rawValue:
            data.prompt = FeMalePopularTemplate.BraidedWithHighlights.rawValue
            return data
        case MalePopularTemplate.WoolRoll.rawValue:
            data.prompt = MalePopularTemplate.WoolRoll.rawValue
            return data
        case MalePopularTemplate.GrainPressing.rawValue:
            data.prompt = MalePopularTemplate.GrainPressing.rawValue
            return data
        case MalePopularTemplate.ExplosiveHead.rawValue:
            data.prompt = MalePopularTemplate.ExplosiveHead.rawValue
            return data
        default:
            return nil
        }
    }
}
