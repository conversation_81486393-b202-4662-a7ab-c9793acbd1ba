//
//  DeveloperApp.swift
//  HairCut
//
//  Created by AI Assistant on 2025/01/21.
//

import Foundation

// MARK: - 开发者应用数据模型
struct DeveloperApp: Codable {
    let logo: String        // logo的URL
    let name: String        // app的中文名字
    let name_en: String     // app的英文名字
    let appid: String       // app的appid

    enum CodingKeys: String, CodingKey {
        case logo
        case name
        case name_en
        case appid
    }

    /// 根据当前语言环境返回合适的应用名称
    var displayName: String {
        let currentLanguage = LanguageTool.currentLanguage()
        return currentLanguage == .chineseSimplified ? name : name_en
    }
}

// MARK: - 开发者应用管理器
class DeveloperAppManager {
    static let shared = DeveloperAppManager()
    private let jsonURL = "https://controls.oss-cn-hangzhou.aliyuncs.com/ios-same-company.json"
    
    private init() {}
    
    /// 获取开发者应用列表
    func fetchDeveloperApps(completion: @escaping (Result<[DeveloperApp], Error>) -> Void) {
        guard let url = URL(string: jsonURL) else {
            completion(.failure(NSError(domain: "InvalidURL", code: 0, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "NoData", code: 0, userInfo: [NSLocalizedDescriptionKey: "No data received"])))
                return
            }
            
            do {
                let apps = try JSONDecoder().decode([DeveloperApp].self, from: data)
                completion(.success(apps))
            } catch {
                completion(.failure(error))
            }
        }
        
        task.resume()
    }
    
    /// 打开App Store应用页面
    func openAppStore(appId: String) {
        let appStoreURL = "https://apps.apple.com/app/id\(appId)"
        guard let url = URL(string: appStoreURL) else {
            print("❌ 无效的App Store URL: \(appStoreURL)")
            return
        }
        
        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        } else {
            print("❌ 无法打开App Store URL: \(appStoreURL)")
        }
    }
}
