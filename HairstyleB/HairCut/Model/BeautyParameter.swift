//
//  BeautyParameter.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import Foundation

// MARK: - 美颜参数模型
struct BeautyParameter: Codable {
    let cnname: String          // 中文名称
    let enname: String          // 英文名称
    let name: String            // 参数名称（用于FURenderer）
    let type: Int               // 参数类型
    var currentValue: Double    // 当前值
    let defaultValue: Double    // 默认值
    let minValue: Double        // 最小值
    let maxValue: Double        // 最大值
}

// MARK: - 美颜配置模型
struct BeautyConfig: Codable {
    var skinBeauty: [BeautyParameter]      // 美肤类型
    var faceShape: [BeautyParameter]       // 脸型类型
    var eyeShape: [BeautyParameter]        // 眼型类型
    var noseShape: [BeautyParameter]       // 鼻子类型
    var mouthShape: [BeautyParameter]      // 嘴巴类型
    var eyebrowShape: [BeautyParameter]    // 眉毛类型
    
    // 获取所有参数的扁平化数组
    func getAllParameters() -> [BeautyParameter] {
        return skinBeauty + faceShape + eyeShape + noseShape + mouthShape + eyebrowShape
    }
    
    // 根据参数名称查找参数
    func findParameter(by name: String) -> BeautyParameter? {
        return getAllParameters().first { $0.name == name }
    }
    
    // 更新指定参数的值
    mutating func updateParameter(name: String, value: Double) -> Bool {
        // 更新美肤参数
        if let index = skinBeauty.firstIndex(where: { $0.name == name }) {
            skinBeauty[index].currentValue = value
            return true
        }
        
        // 更新脸型参数
        if let index = faceShape.firstIndex(where: { $0.name == name }) {
            faceShape[index].currentValue = value
            return true
        }
        
        // 更新眼型参数
        if let index = eyeShape.firstIndex(where: { $0.name == name }) {
            eyeShape[index].currentValue = value
            return true
        }
        
        // 更新鼻子参数
        if let index = noseShape.firstIndex(where: { $0.name == name }) {
            noseShape[index].currentValue = value
            return true
        }
        
        // 更新嘴巴参数
        if let index = mouthShape.firstIndex(where: { $0.name == name }) {
            mouthShape[index].currentValue = value
            return true
        }
        
        // 更新眉毛参数
        if let index = eyebrowShape.firstIndex(where: { $0.name == name }) {
            eyebrowShape[index].currentValue = value
            return true
        }
        
        return false
    }
}

// MARK: - 美颜配置管理器
class BeautyConfigManager {
    static let shared = BeautyConfigManager()
    
    private let defaultConfigFileName = "beauty"
    private let userConfigFileName = "user_beauty_config.json"
    
    private var currentConfig: BeautyConfig?
    
    private init() {}
    
    // MARK: - 公共方法
    
    /// 加载美颜配置
    func loadConfig() -> BeautyConfig {
        if let config = currentConfig {
            return config
        }
        
        // 首先尝试加载用户自定义配置
        if let userConfig = loadUserConfig() {
            currentConfig = userConfig
            return userConfig
        }
        
        // 如果没有用户配置，加载默认配置
        let defaultConfig = loadDefaultConfig()
        currentConfig = defaultConfig
        return defaultConfig
    }
    
    /// 更新参数值
    func updateParameter(name: String, value: Double) -> Bool {
        var config = loadConfig()
        let success = config.updateParameter(name: name, value: value)
        
        if success {
            currentConfig = config
            saveUserConfig(config)
        }
        
        return success
    }
    
    /// 重置所有参数到默认值
    func resetToDefault() {
        let defaultConfig = loadDefaultConfig()
        currentConfig = defaultConfig
        saveUserConfig(defaultConfig)
    }
    
    /// 获取指定参数的当前值
    func getCurrentValue(for parameterName: String) -> Double? {
        let config = loadConfig()
        return config.findParameter(by: parameterName)?.currentValue
    }

    /// 保存配置
    func saveConfig(_ config: BeautyConfig) {
        currentConfig = config
        saveUserConfig(config)
    }
    
    // MARK: - 私有方法
    
    /// 加载默认配置
    private func loadDefaultConfig() -> BeautyConfig {
        guard let path = Bundle.main.path(forResource: defaultConfigFileName, ofType: "json"),
              let data = NSData(contentsOfFile: path) as Data?,
              let config = try? JSONDecoder().decode(BeautyConfig.self, from: data) else {
            print("❌ 无法加载默认美颜配置文件")
            return createEmptyConfig()
        }
        
        print("✅ 成功加载默认美颜配置")
        return config
    }
    
    /// 加载用户配置
    private func loadUserConfig() -> BeautyConfig? {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        let filePath = (documentsPath as NSString).appendingPathComponent(userConfigFileName)
        
        guard let data = NSData(contentsOfFile: filePath) as Data?,
              let config = try? JSONDecoder().decode(BeautyConfig.self, from: data) else {
            return nil
        }
        
        print("✅ 成功加载用户美颜配置")
        return config
    }
    
    /// 保存用户配置
    private func saveUserConfig(_ config: BeautyConfig) {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        let filePath = (documentsPath as NSString).appendingPathComponent(userConfigFileName)
        
        do {
            let data = try JSONEncoder().encode(config)
            try data.write(to: URL(fileURLWithPath: filePath))
            print("✅ 成功保存用户美颜配置")
        } catch {
            print("❌ 保存用户美颜配置失败: \(error)")
        }
    }
    
    /// 创建空配置
    private func createEmptyConfig() -> BeautyConfig {
        return BeautyConfig(
            skinBeauty: [],
            faceShape: [],
            eyeShape: [],
            noseShape: [],
            mouthShape: [],
            eyebrowShape: []
        )
    }
}
