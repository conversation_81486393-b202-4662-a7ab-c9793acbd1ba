//
//  File.swift
//  HairCut
//
//  Created by fs0011 on 2025/3/12.
//

import Foundation
import UIKit
import Alamofire
import Promises

class AIHairModel: Decodable {
    var image: String = ""
    var name: String = ""
    var isVip: Bool = false
    var type: Int = 0
    var sex: Int = 0
    var prompt: String = ""
    var NegativePrompt: String = ""
    var name_en : String = ""
    var origin_image : UIImage!
    var ID : String = ""
    // 添加遮罩图片属性
    var mask_image : UIImage?
    // 添加标签数组
    var tag: [String] = []
    // 添加英文标签数组
    var tag_en: [String] = []
    
    // New property: original image size string, e.g. "1014*1328"
    var size: String = ""
    // Convenience: aspect ratio (height / width) parsed from `size`, default 1.0
    var aspectRatio: CGFloat {
        let comps = size.split(separator: "*").compactMap { Double($0) }
        if comps.count == 2, comps[0] > 0 {
            return CGFloat(comps[1] / comps[0])
        }
        return 1.0
    }
    
    // 是否启用了手动选择区域
    var isAreaSelectionEnabled: Bool = false
    
    // 使用通用解码方式，让任何字段缺失都不会导致解析失败
    required init(from decoder: Decoder) throws {
        // 首先尝试按键值解析
        if let container = try? decoder.container(keyedBy: CodingKeys.self) {
            // 唯一必须的字段，如果这些都没有，才真正判断为解析失败
            ID = try container.decodeIfPresent(String.self, forKey: .ID) ?? ""
            image = try container.decodeIfPresent(String.self, forKey: .image) ?? ""
            name = try container.decodeIfPresent(String.self, forKey: .name) ?? ""
            
            // 所有其他字段都视为可选
            isVip = try container.decodeIfPresent(Bool.self, forKey: .isVip) ?? false
            type = try container.decodeIfPresent(Int.self, forKey: .type) ?? 0
            sex = try container.decodeIfPresent(Int.self, forKey: .sex) ?? 0
            prompt = try container.decodeIfPresent(String.self, forKey: .prompt) ?? ""
            NegativePrompt = try container.decodeIfPresent(String.self, forKey: .NegativePrompt) ?? ""
            name_en = try container.decodeIfPresent(String.self, forKey: .name_en) ?? ""
            
            // 新增 size 解析
            size = try container.decodeIfPresent(String.self, forKey: .size) ?? ""
            
            // 数组类型特殊处理
            if let tagArray = try? container.decodeIfPresent([String].self, forKey: .tag) {
                tag = tagArray
            }
            
            // 英文标签处理
            if let tagEnArray = try? container.decodeIfPresent([String].self, forKey: .tag_en) {
                tag_en = tagEnArray
            }
        }
    }
    
    // 如果 JSON 字段名和属性名不一致，需定义 CodingKeys
    private enum CodingKeys: String, CodingKey {
        case ID = "id", image, name, name_en, isVip = "isVip", type, sex, prompt, NegativePrompt, tag, tag_en, size
    }
    
    // MARK: - 通用数据获取方法
    static func fectchQuestJson(type: Int = 0, completion: @escaping (Result<Data, Error>) -> Void)
    {
        let timestamp = Date().timeIntervalSince1970
        let url = type==0 ? "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/haircut_clothes_request.json" : "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/haircut_hair_request.json"
        AF.request(url,
                   parameters: ["_t": timestamp],
                   requestModifier: { $0.timeoutInterval = 15 }).response(completionHandler: { response in
            switch response.result {
            case .success(let data):
                completion(.success(data!))
            case .failure(let error):
                let statusCode = response.response?.statusCode ?? -1
                let enhancedError = NSError(
                    domain: "NetworkError",
                    code: statusCode,
                    userInfo: [NSLocalizedDescriptionKey: "请求失败: \(error.localizedDescription)"]
                )
                completion(.failure(enhancedError))
            }
            
        })
    }
    
    // 新增方法，可以直接指定URL获取JSON
    static func fectchQuestJson(url: String) -> Promise<Data> {
        let promise = Promise<Data>.pending()
        let timestamp = Date().timeIntervalSince1970
        
        AF.request(url,
                   parameters: ["_t": timestamp],
                   requestModifier: { $0.timeoutInterval = 15 }).response(completionHandler: { response in
            switch response.result {
            case .success(let data):
                if let data = data {
                    promise.fulfill(data)
                } else {
                    let error = NSError(
                        domain: "DataError",
                        code: -1,
                        userInfo: [NSLocalizedDescriptionKey: "数据为空"]
                    )
                    promise.reject(error)
                }
            case .failure(let error):
                let statusCode = response.response?.statusCode ?? -1
                let enhancedError = NSError(
                    domain: "NetworkError",
                    code: statusCode,
                    userInfo: [NSLocalizedDescriptionKey: "请求失败: \(error.localizedDescription)"]
                )
                promise.reject(enhancedError)
            }
        })
        
        return promise
    }
    
    static func fetchHairData(
        from url: String,
        parameters: [String: Any]? = nil,
        completion: @escaping (Result<[AIHairModel], Error>) -> Void
    ) {
        let timestamp = Date().timeIntervalSince1970
        
        AF.request(
            url,
            parameters: parameters ?? ["_t": timestamp],
            requestModifier: { $0.timeoutInterval = 15 }
        )
        .validate(statusCode: 200..<300)
        .responseDecodable(of: [AIHairModel].self) { response in
            switch response.result {
            case .success(let data):
                completion(.success(data))
            case .failure(let error):
                let statusCode = response.response?.statusCode ?? -1
                let enhancedError = NSError(
                    domain: "NetworkError",
                    code: statusCode,
                    userInfo: [NSLocalizedDescriptionKey: "请求失败: \(error.localizedDescription)"]
                )
                completion(.failure(enhancedError))
            }
        }
    }
    
    public func toDictionary() -> [String: Any] {
        var result = [String: Any]()
        result["img"] = self.origin_image
        result["negative_prompt"] = self.NegativePrompt
        result["bg"] = "1"
        result["hair"] = "0"
        result["prompt"] = self.prompt
        
        // 如果有遮罩图片，添加到结果中
        if let maskImg = self.mask_image {
            result["mask_img"] = maskImg
            result["use_mask"] = true
        }
        
        return result
    }
}
