//
//  AIHairCutData.swift
//  HairCut
//
//  Created by Bigger on 2024/8/5.
//

import Foundation
import UIKit

class AIHairCutData {
    var image: UIImage? = nil
    var prompt: String = ""
    
    init() {
        
    }
    
    public func toDictionary() -> [String: Any] {
        var result = [String: Any]()
        result["img"] = self.image
        result["negative_prompt"] = "face, body, person, human, skin, eyes, nose, mouth, ears, torso, arms, hands, legs, feet, messy background, cluttered background, harsh lighting, blurry, distorted, artifacts, noise, low quality, bad composition"
        result["bg"] = "1"
        result["hair"] = "0"
        result["prompt"] = self.prompt
        return result
    }
}
