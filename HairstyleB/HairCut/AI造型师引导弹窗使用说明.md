# AI造型师引导弹窗使用说明

## 概述
AI造型师引导弹窗是一个全屏弹窗组件，用于向用户展示AI换发型和AI美甲功能，包含次数显示和添加次数按钮。

## 功能特点

### 1. 全屏弹窗设计
- 全屏背景遮罩，点击背景可关闭弹窗
- 白色内容区域，圆角设计
- 弹出和关闭动画效果

### 2. 图片展示
- 换发型图片：272*200比例，使用"引导页换发型"资源
- 美甲图片：272*200比例，使用"引导页美甲"资源
- 图片圆角处理，美观展示

### 3. 文字信息
- **换发型区域**：
  - 标题：AI换发型/换衣服
  - 描述：一键生成多款发型
  - 次数显示：剩余次数: 10
- **美甲区域**：
  - 标题：AI美甲
  - 描述：可爱美甲随心试
  - 次数显示：剩余次数: 10

### 4. 交互按钮
- 添加次数按钮：使用"aihaircut_add"图标，24*24尺寸
- 透明点击区域：覆盖整个功能区域，便于用户点击

## 使用方法

### 1. 基本使用
```swift
let popupView = AIStylistGuidePopupView()

// 设置回调
popupView.onHairStyleButtonTapped = {
    // 处理换发型按钮点击
    print("跳转到换发型页面")
}

popupView.onNailArtButtonTapped = {
    // 处理美甲按钮点击
    print("跳转到美甲页面")
}

popupView.onDismiss = {
    // 处理弹窗关闭
    print("弹窗已关闭")
}

// 显示弹窗
popupView.show(in: view)
```

### 2. 手动关闭
```swift
popupView.hide()
```

## 布局说明

### 1. 整体布局
- 全屏背景遮罩
- 内容区域全屏显示

### 2. 换发型区域
- 图片：距离顶部120px，距离左侧40px
- 标题：距离图片底部16px
- 描述：距离标题底部4px
- 次数：距离描述底部8px
- 添加按钮：与次数标签垂直居中对齐

### 3. 美甲区域
- 图片：距离换发型次数标签底部40px
- 其他元素布局与换发型区域相同

## 颜色规范

### 1. 文字颜色
- 标题文字：#333333 (51/255, 51/255, 51/255)
- 描述文字：#999999 (153/255, 153/255, 153/255)
- 次数文字：#333333 (51/255, 51/255, 51/255)

### 2. 背景颜色
- 遮罩背景：黑色50%透明度
- 内容背景：白色
- 按钮背景：透明

## 字体规范
- 标题：系统字体16pt，中等粗细
- 描述：系统字体14pt，常规粗细
- 次数：系统字体14pt，常规粗细

## 图片资源
- 换发型图片：引导页换发型.imageset
- 美甲图片：引导页美甲.imageset
- 添加按钮：aihaircut_add.imageset

## 注意事项

1. **图片比例**：确保图片资源为272*200比例，避免变形
2. **全屏适配**：弹窗为全屏设计，适配各种屏幕尺寸
3. **回调处理**：务必设置相应的回调函数处理用户交互
4. **内存管理**：使用weak self避免循环引用
5. **动画效果**：弹窗有弹出和关闭动画，提升用户体验

## 测试页面
可以使用`AIStylistGuideTestViewController`进行功能测试和效果预览。
