//
//  TestNailAPI.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import Foundation
import UIKit

/// 简单的美甲API测试类
class TestNailAPI {
    
    /// 测试美甲API连接和数据解析
    static func testNailAPIConnection() {
        print("🔗 开始测试美甲API连接...")
        
        let url = "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/nailsmodel.json"
        
        guard let apiURL = URL(string: url) else {
            print("❌ URL格式错误")
            return
        }
        
        let task = URLSession.shared.dataTask(with: apiURL) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ 网络请求失败: \(error.localizedDescription)")
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("❌ 无效的HTTP响应")
                    return
                }
                
                print("📡 HTTP状态码: \(httpResponse.statusCode)")
                
                guard httpResponse.statusCode == 200 else {
                    print("❌ HTTP请求失败，状态码: \(httpResponse.statusCode)")
                    return
                }
                
                guard let data = data else {
                    print("❌ 没有接收到数据")
                    return
                }
                
                print("✅ 成功接收到数据，大小: \(data.count) 字节")
                
                // 尝试解析JSON
                do {
                    let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])
                    
                    if let jsonArray = jsonObject as? [[String: Any]] {
                        print("✅ JSON解析成功，包含 \(jsonArray.count) 个美甲样式")
                        
                        // 打印前3个样式的基本信息
                        for (index, item) in jsonArray.prefix(3).enumerated() {
                            print("\n--- 美甲样式 \(index + 1) ---")
                            if let id = item["id"] as? String {
                                print("ID: \(id)")
                            }
                            if let name = item["name"] as? String {
                                print("名称: \(name)")
                            }
                            if let isVip = item["isVip"] as? Bool {
                                print("VIP: \(isVip)")
                            }
                            if let sex = item["sex"] as? Int {
                                print("性别: \(sex == 0 ? "女性" : "男性")")
                            }
                        }
                        
                        // 统计信息
                        let vipCount = jsonArray.filter { ($0["isVip"] as? Bool) == true }.count
                        let femaleCount = jsonArray.filter { ($0["sex"] as? Int) == 0 }.count
                        let maleCount = jsonArray.filter { ($0["sex"] as? Int) == 1 }.count
                        
                        print("\n📊 统计信息:")
                        print("总数量: \(jsonArray.count)")
                        print("VIP样式: \(vipCount)")
                        print("女性样式: \(femaleCount)")
                        print("男性样式: \(maleCount)")
                        
                    } else {
                        print("❌ JSON格式不正确，期望数组格式")
                    }
                    
                } catch {
                    print("❌ JSON解析失败: \(error.localizedDescription)")
                    
                    // 打印原始数据的前200个字符用于调试
                    if let dataString = String(data: data, encoding: .utf8) {
                        let preview = String(dataString.prefix(200))
                        print("原始数据预览: \(preview)...")
                    }
                }
            }
        }
        
        task.resume()
    }
    
    /// 测试使用NailModel解析数据
    static func testNailModelParsing() {
        print("\n🧪 开始测试NailModel数据解析...")
        
        NailModel.fetchNailData { result in
            switch result {
            case .success(let models):
                print("✅ NailModel解析成功！")
                print("📊 解析出 \(models.count) 个美甲模型")
                
                // 验证数据完整性
                var validCount = 0
                var invalidCount = 0
                
                for model in models {
                    if !model.id.isEmpty && !model.name.isEmpty && !model.image.isEmpty {
                        validCount += 1
                    } else {
                        invalidCount += 1
                        print("⚠️ 发现无效数据: ID=\(model.id), Name=\(model.name)")
                    }
                }
                
                print("✅ 有效数据: \(validCount) 个")
                if invalidCount > 0 {
                    print("⚠️ 无效数据: \(invalidCount) 个")
                }
                
                // 测试数据管理器
                testDataManager(with: models)
                
            case .failure(let error):
                print("❌ NailModel解析失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 测试数据管理器功能
    static func testDataManager(with models: [NailModel]) {
        print("\n📋 测试数据管理器功能...")
        
        let manager = NailDataManager.shared
        
        // 手动设置数据（模拟加载完成）
        manager.loadNailData { result in
            switch result {
            case .success(_):
                print("✅ 数据管理器加载成功")
                
                // 测试各种筛选功能
                let allModels = manager.getAllModels()
                let femaleModels = manager.getModelsBySex(0)
                let maleModels = manager.getModelsBySex(1)
                let vipModels = manager.getVipModels()
                let freeModels = manager.getFreeModels()
                
                print("筛选测试结果:")
                print("  全部: \(allModels.count)")
                print("  女性: \(femaleModels.count)")
                print("  男性: \(maleModels.count)")
                print("  VIP: \(vipModels.count)")
                print("  免费: \(freeModels.count)")
                
                // 测试搜索功能
                let searchResults1 = manager.searchModels(keyword: "渐变")
                let searchResults2 = manager.searchModels(keyword: "大理石")
                let searchResults3 = manager.searchModels(keyword: "")
                
                print("搜索测试结果:")
                print("  '渐变': \(searchResults1.count)")
                print("  '大理石': \(searchResults2.count)")
                print("  空字符串: \(searchResults3.count)")
                
                // 测试ID查找
                if let firstModel = allModels.first {
                    let foundModel = manager.getModelById(firstModel.id)
                    print("ID查找测试: \(foundModel != nil ? "成功" : "失败")")
                }
                
            case .failure(let error):
                print("❌ 数据管理器测试失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🚀 开始运行美甲API测试套件...")
        print("=" * 50)
        
        // 1. 测试API连接
        testNailAPIConnection()
        
        // 2. 延迟测试模型解析
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            testNailModelParsing()
        }
        
        print("\n⏳ 测试正在进行中，请查看控制台输出...")
    }
}

// MARK: - 便捷调用方法
extension TestNailAPI {
    
    /// 在视图控制器中调用测试
    /// - Parameter viewController: 调用测试的视图控制器
    static func runTestsFromViewController(_ viewController: UIViewController) {
        let alert = UIAlertController(
            title: "美甲API测试",
            message: "是否开始测试美甲API连接和数据解析？",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "开始测试", style: .default) { _ in
            runAllTests()
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        viewController.present(alert, animated: true)
    }
}
