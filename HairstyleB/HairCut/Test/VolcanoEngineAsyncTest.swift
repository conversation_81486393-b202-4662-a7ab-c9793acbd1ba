//
//  VolcanoEngineAsyncTest.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import UIKit
import SVProgressHUD

/// 火山引擎异步API测试类
class VolcanoEngineAsyncTest {
    
    /// 测试异步美甲处理流程
    /// - Parameter fromViewController: 启动测试的视图控制器
    static func testAsyncNailProcess(from fromViewController: UIViewController) {
        let alert = UIAlertController(
            title: "火山引擎异步API测试",
            message: "选择测试方式",
            preferredStyle: .actionSheet
        )
        
        alert.addAction(UIAlertAction(title: "测试完整异步流程", style: .default) { _ in
            testCompleteAsyncFlow(from: fromViewController)
        })
        
        alert.addAction(UIAlertAction(title: "测试任务提交", style: .default) { _ in
            testTaskSubmission()
        })
        
        alert.addAction(UIAlertAction(title: "测试API连接", style: .default) { _ in
            testAPIConnection()
        })

        alert.addAction(UIAlertAction(title: "生成Postman测试信息", style: .default) { _ in
            generatePostmanInfo()
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // 为iPad设置popover
        if let popover = alert.popoverPresentationController {
            popover.sourceView = fromViewController.view
            popover.sourceRect = CGRect(x: fromViewController.view.bounds.midX, y: fromViewController.view.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }
        
        fromViewController.present(alert, animated: true)
    }
    
    /// 测试完整的异步处理流程
    private static func testCompleteAsyncFlow(from fromViewController: UIViewController) {
        print("🧪 开始测试完整异步流程...")
        
        // 创建测试图片
        let testImage = createTestNailImage()
        
        // 获取第一个美甲样式
        SVProgressHUD.show(withStatus: "获取美甲样式...")
        
        NailModel.fetchNailData { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let models):
                    if let firstModel = models.first {
                        SVProgressHUD.show(withStatus: "开始异步处理...")
                        
                        let request = VolcanoEngineAPI.NailProcessRequest(
                            originalImage: testImage,
                            prompt: firstModel.prompt
                        )
                        
                        VolcanoEngineAPI.processNail(request: request) { result in
                            DispatchQueue.main.async {
                                SVProgressHUD.dismiss()
                                
                                switch result {
                                case .success(let processedImage):
                                    SVProgressHUD.showSuccess(withStatus: "异步处理成功！")
                                    showResultImage(processedImage, from: fromViewController)
                                    
                                case .failure(let error):
                                    SVProgressHUD.showError(withStatus: "异步处理失败")
                                    print("❌ 异步处理失败: \(error.localizedDescription)")
                                }
                            }
                        }
                    } else {
                        SVProgressHUD.showError(withStatus: "没有可用的美甲样式")
                    }
                    
                case .failure(let error):
                    SVProgressHUD.showError(withStatus: "获取美甲样式失败")
                    print("❌ 获取美甲样式失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    /// 测试任务提交
    private static func testTaskSubmission() {
        print("🧪 测试任务提交...")
        
        SVProgressHUD.show(withStatus: "测试任务提交...")
        
        let testImage = createTestNailImage()
        let request = VolcanoEngineAPI.NailProcessRequest(
            originalImage: testImage,
            prompt: "添加粉色渐变美甲效果"
        )
        
        // 这里我们需要访问私有方法，所以创建一个简化的测试
        VolcanoEngineAPI.processNail(request: request) { result in
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()
                
                switch result {
                case .success(_):
                    SVProgressHUD.showSuccess(withStatus: "任务提交测试成功")
                    print("✅ 任务提交测试成功")
                    
                case .failure(let error):
                    SVProgressHUD.showError(withStatus: "任务提交测试失败")
                    print("❌ 任务提交测试失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    /// 测试API连接
    private static func testAPIConnection() {
        print("🧪 测试API连接...")
        
        SVProgressHUD.show(withStatus: "测试API连接...")
        
        // 创建一个简单的测试请求
        let testImage = createTestNailImage()
        let request = VolcanoEngineAPI.NailProcessRequest(
            originalImage: testImage,
            prompt: "测试连接"
        )
        
        VolcanoEngineAPI.processNail(request: request) { result in
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()
                
                switch result {
                case .success(_):
                    SVProgressHUD.showSuccess(withStatus: "API连接正常")
                    print("✅ API连接测试成功")
                    
                case .failure(let error):
                    let errorMessage = error.localizedDescription
                    if errorMessage.contains("401") || errorMessage.contains("403") {
                        SVProgressHUD.showError(withStatus: "API密钥错误")
                    } else if errorMessage.contains("网络") || errorMessage.contains("Network") {
                        SVProgressHUD.showError(withStatus: "网络连接失败")
                    } else {
                        SVProgressHUD.showError(withStatus: "API连接失败")
                    }
                    print("❌ API连接测试失败: \(errorMessage)")
                }
            }
        }
    }

    /// 生成Postman测试信息
    private static func generatePostmanInfo() {
        print("🧪 生成Postman测试信息...")

        let testImage = createTestNailImage()
        VolcanoEngineAPI.generatePostmanTestInfo(with: testImage)

        SVProgressHUD.showInfo(withStatus: "Postman信息已输出到控制台")
    }

    /// 创建测试用的美甲图片
    private static func createTestNailImage() -> UIImage {
        let size = CGSize(width: 400, height: 600)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        
        // 绘制背景
        UIColor.systemBackground.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        
        // 绘制手的形状
        UIColor.systemPink.setFill()
        
        // 手掌
        let palmRect = CGRect(x: 100, y: 200, width: 200, height: 250)
        UIBezierPath(ovalIn: palmRect).fill()
        
        // 手指
        let fingerWidth: CGFloat = 30
        let fingerHeight: CGFloat = 80
        
        for i in 0..<5 {
            let x = 120 + CGFloat(i) * 35
            let y: CGFloat = 120
            let fingerRect = CGRect(x: x, y: y, width: fingerWidth, height: fingerHeight)
            UIBezierPath(ovalIn: fingerRect).fill()
            
            // 指甲
            UIColor.white.setFill()
            let nailRect = CGRect(x: x + 5, y: y, width: 20, height: 15)
            UIBezierPath(ovalIn: nailRect).fill()
        }
        
        // 添加文字
        let text = "测试美甲图片"
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.boldSystemFont(ofSize: 20),
            .foregroundColor: UIColor.label
        ]
        let textSize = text.size(withAttributes: attributes)
        let textRect = CGRect(
            x: (size.width - textSize.width) / 2,
            y: size.height - 80,
            width: textSize.width,
            height: textSize.height
        )
        text.draw(in: textRect, withAttributes: attributes)
        
        let image = UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
        UIGraphicsEndImageContext()
        
        return image
    }
    
    /// 显示处理结果图片
    private static func showResultImage(_ image: UIImage, from fromViewController: UIViewController) {
        let alert = UIAlertController(title: "处理结果", message: "美甲处理完成！", preferredStyle: .alert)
        
        alert.addAction(UIAlertAction(title: "保存到相册", style: .default) { _ in
            UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
            SVProgressHUD.showSuccess(withStatus: "已保存到相册")
        })
        
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        
        fromViewController.present(alert, animated: true)
    }
    
    /// 运行所有异步测试
    static func runAllAsyncTests() {
        print("🚀 开始运行所有异步测试...")
        print("=" * 60)
        
        // 1. 测试API连接
        testAPIConnection()
        
        // 2. 延迟测试任务提交
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            testTaskSubmission()
        }
        
        print("\n⏳ 异步测试正在进行中，请查看控制台输出和进度提示...")
    }
}

// MARK: - UIViewController扩展
extension UIViewController {
    
    /// 便捷方法：测试异步美甲处理
    func testAsyncNailProcess() {
        VolcanoEngineAsyncTest.testAsyncNailProcess(from: self)
    }
    
    /// 便捷方法：运行所有异步测试
    func runAllAsyncTests() {
        VolcanoEngineAsyncTest.runAllAsyncTests()
    }
}
