//
//  SignatureDebugTest.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import UIKit
import SVProgressHUD

/// 签名调试测试类
class SignatureDebugTest {
    
    /// 测试签名生成
    /// - Parameter fromViewController: 启动测试的视图控制器
    static func testSignatureGeneration(from fromViewController: UIViewController) {
        let alert = UIAlertController(
            title: "签名调试测试",
            message: "选择测试方式",
            preferredStyle: .actionSheet
        )
        
        alert.addAction(UIAlertAction(title: "测试签名生成", style: .default) { _ in
            testSignatureOnly()
        })
        
        alert.addAction(UIAlertAction(title: "测试完整请求", style: .default) { _ in
            testFullRequest()
        })
        
        alert.addAction(UIAlertAction(title: "生成Postman信息", style: .default) { _ in
            generatePostmanInfo()
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // 为iPad设置popover
        if let popover = alert.popoverPresentationController {
            popover.sourceView = fromViewController.view
            popover.sourceRect = CGRect(x: fromViewController.view.bounds.midX, y: fromViewController.view.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }
        
        fromViewController.present(alert, animated: true)
    }
    
    /// 仅测试签名生成
    private static func testSignatureOnly() {
        print("🔐 ===== 签名生成测试 =====")
        
        let testImage = createSimpleTestImage()
        
        guard let imageData = testImage.jpegData(compressionQuality: 0.8) else {
            print("❌ 图片数据转换失败")
            return
        }
        
        let base64Image = imageData.base64EncodedString()
        
        let parameters: [String: Any] = [
            "req_key": "seededit_v3.0",
            "binary_data_base64": [base64Image],
            "prompt": "测试美甲效果",
            "scale": 1,
            "seed": -1
        ]
        
        let timestamp = Date()
        
        // 这里会调用签名生成方法并打印调试信息
        print("🔐 开始生成签名...")
        
        // 注意：这里需要访问私有方法，所以我们通过实际请求来触发签名生成
        let request = VolcanoEngineAPI.NailProcessRequest(
            originalImage: testImage,
            prompt: "测试美甲效果"
        )
        
        print("✅ 签名测试完成，请查看上方的签名调试信息")
        
        SVProgressHUD.showInfo(withStatus: "签名调试信息已输出到控制台")
    }
    
    /// 测试完整请求
    private static func testFullRequest() {
        print("🧪 ===== 完整请求测试 =====")
        
        SVProgressHUD.show(withStatus: "测试完整请求...")
        
        let testImage = createSimpleTestImage()
        let request = VolcanoEngineAPI.NailProcessRequest(
            originalImage: testImage,
            prompt: "测试美甲效果"
        )
        
        VolcanoEngineAPI.processNail(request: request) { result in
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()
                
                switch result {
                case .success(_):
                    SVProgressHUD.showSuccess(withStatus: "请求成功！")
                    print("✅ 完整请求测试成功")
                    
                case .failure(let error):
                    let errorMessage = error.localizedDescription
                    print("❌ 完整请求测试失败: \(errorMessage)")
                    
                    if errorMessage.contains("SignatureDoesNotMatch") {
                        SVProgressHUD.showError(withStatus: "签名错误")
                        print("🔐 签名不匹配，请检查签名算法")
                    } else if errorMessage.contains("401") || errorMessage.contains("403") {
                        SVProgressHUD.showError(withStatus: "认证失败")
                        print("🔑 认证失败，请检查Access Key")
                    } else {
                        SVProgressHUD.showError(withStatus: "请求失败")
                    }
                }
            }
        }
    }
    
    /// 生成Postman测试信息
    private static func generatePostmanInfo() {
        print("📋 ===== 生成Postman测试信息 =====")
        
        let testImage = createSimpleTestImage()
        VolcanoEngineAPI.generatePostmanTestInfo(with: testImage)
        
        SVProgressHUD.showInfo(withStatus: "Postman信息已输出到控制台")
    }
    
    /// 创建简单的测试图片
    private static func createSimpleTestImage() -> UIImage {
        let size = CGSize(width: 200, height: 300)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        
        // 绘制简单的背景
        UIColor.lightGray.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        
        // 绘制手的简化形状
        UIColor.systemPink.setFill()
        let handRect = CGRect(x: 50, y: 50, width: 100, height: 200)
        UIBezierPath(ovalIn: handRect).fill()
        
        // 绘制指甲
        UIColor.white.setFill()
        for i in 0..<5 {
            let x = 60 + CGFloat(i) * 16
            let y: CGFloat = 50
            let nailRect = CGRect(x: x, y: y, width: 12, height: 8)
            UIBezierPath(ovalIn: nailRect).fill()
        }
        
        // 添加文字
        let text = "Test"
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 16),
            .foregroundColor: UIColor.black
        ]
        let textSize = text.size(withAttributes: attributes)
        let textRect = CGRect(
            x: (size.width - textSize.width) / 2,
            y: size.height - 40,
            width: textSize.width,
            height: textSize.height
        )
        text.draw(in: textRect, withAttributes: attributes)
        
        let image = UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
        UIGraphicsEndImageContext()
        
        return image
    }
    
    /// 分析错误信息
    static func analyzeError(_ error: Error) {
        let errorDescription = error.localizedDescription
        
        print("🔍 ===== 错误分析 =====")
        print("原始错误: \(errorDescription)")
        
        if errorDescription.contains("SignatureDoesNotMatch") {
            print("🔐 签名不匹配错误")
            print("可能原因:")
            print("1. 签名算法不正确")
            print("2. 时间戳格式错误")
            print("3. 请求头顺序错误")
            print("4. Body哈希计算错误")
            print("5. 查询字符串格式错误")
        } else if errorDescription.contains("401") {
            print("🔑 认证失败")
            print("可能原因:")
            print("1. Access Key ID错误")
            print("2. Secret Access Key错误")
            print("3. 签名过期")
        } else if errorDescription.contains("403") {
            print("🚫 权限不足")
            print("可能原因:")
            print("1. 账户权限不足")
            print("2. 服务未开通")
            print("3. 配额不足")
        }
        
        print("🔍 ===== 错误分析结束 =====")
    }
}

// MARK: - UIViewController扩展
extension UIViewController {
    
    /// 便捷方法：测试签名调试
    func testSignatureDebug() {
        SignatureDebugTest.testSignatureGeneration(from: self)
    }
}
