//
//  PortraitBeautyFlowTest.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import Foundation
import UIKit

/// 人像美容流程测试
class PortraitBeautyFlowTest {
    
    /// 测试完整的人像美容流程
    static func testCompleteFlow() {
        print("🧪 开始人像美容流程测试")
        
        testPortraitBeautyVCCreation()
        testSkinBeautyEditVCCreation()
        testBeautyParametersLoading()
        testImageProcessing()
        
        print("✅ 人像美容流程测试完成")
    }
    
    /// 测试PortraitBeautyVC创建
    static func testPortraitBeautyVCCreation() {
        print("📋 测试PortraitBeautyVC创建")

        let portraitBeautyVC = PortraitBeautyVC()

        // 验证VC创建成功
        if portraitBeautyVC.isKind(of: UIViewController.self) {
            print("✅ PortraitBeautyVC创建成功")
        } else {
            print("❌ PortraitBeautyVC创建失败")
        }

        // 验证hidesBottomBarWhenPushed属性
        portraitBeautyVC.hidesBottomBarWhenPushed = true
        if portraitBeautyVC.hidesBottomBarWhenPushed {
            print("✅ hidesBottomBarWhenPushed属性设置成功")
        } else {
            print("❌ hidesBottomBarWhenPushed属性设置失败")
        }

        // 测试sourceImage属性
        if let testImage = createTestImage() {
            portraitBeautyVC.sourceImage = testImage
            if portraitBeautyVC.sourceImage != nil {
                print("✅ sourceImage属性设置成功")
            } else {
                print("❌ sourceImage属性设置失败")
            }
        }
    }
    
    /// 测试SkinBeautyEditVC创建
    static func testSkinBeautyEditVCCreation() {
        print("📋 测试SkinBeautyEditVC创建")
        
        let skinBeautyEditVC = SkinBeautyEditVC()
        
        // 验证VC创建成功
        if skinBeautyEditVC.isKind(of: UIViewController.self) {
            print("✅ SkinBeautyEditVC创建成功")
        } else {
            print("❌ SkinBeautyEditVC创建失败")
        }
        
        // 验证hidesBottomBarWhenPushed属性
        skinBeautyEditVC.hidesBottomBarWhenPushed = true
        if skinBeautyEditVC.hidesBottomBarWhenPushed {
            print("✅ hidesBottomBarWhenPushed属性设置成功")
        } else {
            print("❌ hidesBottomBarWhenPushed属性设置失败")
        }
    }
    
    /// 测试美颜参数加载
    static func testBeautyParametersLoading() {
        print("📋 测试美颜参数加载")
        
        // 加载美颜配置
        let config = BeautyConfigManager.shared.loadConfig()
        
        // 验证美肤参数
        if !config.skinBeauty.isEmpty {
            print("✅ 美肤参数加载成功，共\(config.skinBeauty.count)个参数")
            
            // 打印前几个参数
            for (index, parameter) in config.skinBeauty.prefix(3).enumerated() {
                print("  \(index + 1). \(parameter.cnname) (\(parameter.name)): \(parameter.currentValue)")
            }
        } else {
            print("❌ 美肤参数加载失败")
        }
        
        // 验证脸型参数
        if !config.faceShape.isEmpty {
            print("✅ 脸型参数加载成功，共\(config.faceShape.count)个参数")
        } else {
            print("❌ 脸型参数加载失败")
        }
        
        // 验证眼型参数
        if !config.eyeShape.isEmpty {
            print("✅ 眼型参数加载成功，共\(config.eyeShape.count)个参数")
        } else {
            print("❌ 眼型参数加载失败")
        }
    }
    
    /// 测试图片处理
    static func testImageProcessing() {
        print("📋 测试图片处理")
        
        // 创建测试图片
        guard let testImage = createTestImage() else {
            print("❌ 无法创建测试图片")
            return
        }
        
        print("✅ 测试图片创建成功，尺寸: \(testImage.size)")
        
        // 初始化美颜SDK
        PTMFilterHelper.setupSDK()
        print("✅ 美颜SDK初始化完成")
        
        // 设置一些测试参数
        let testParameters: [(String, Double)] = [
            ("blur_level", 3.0),
            ("color_level", 0.3),
            ("red_level", 0.2),
            ("cheek_v", 0.2)
        ]
        
        for (name, value) in testParameters {
            let success = PTMFilterHelper.updateBeautyParameter(name, value: value)
            if success {
                print("✅ 设置参数 \(name) = \(value)")
            } else {
                print("❌ 设置参数 \(name) 失败")
            }
        }
        
        // 处理图片
        if let processedImage = PTMFilterHelper.processImageWithBeauty(testImage) {
            print("✅ 图片处理成功，处理后尺寸: \(processedImage.size)")
        } else {
            print("❌ 图片处理失败")
        }
    }
    
    /// 创建测试图片
    static func createTestImage() -> UIImage? {
        let size = CGSize(width: 300, height: 300)
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        
        // 绘制渐变背景
        guard let context = UIGraphicsGetCurrentContext() else {
            UIGraphicsEndImageContext()
            return nil
        }
        
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let colors = [
            UIColor(red: 1.0, green: 0.8, blue: 0.8, alpha: 1.0).cgColor,
            UIColor(red: 0.8, green: 0.8, blue: 1.0, alpha: 1.0).cgColor
        ]
        
        guard let gradient = CGGradient(colorsSpace: colorSpace, colors: colors as CFArray, locations: nil) else {
            UIGraphicsEndImageContext()
            return nil
        }
        
        let startPoint = CGPoint(x: 0, y: 0)
        let endPoint = CGPoint(x: size.width, y: size.height)
        
        context.drawLinearGradient(gradient, start: startPoint, end: endPoint, options: [])
        
        // 添加文字
        let text = "Beauty Test"
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.boldSystemFont(ofSize: 24),
            .foregroundColor: UIColor.black
        ]
        
        let textSize = text.size(withAttributes: attributes)
        let textPoint = CGPoint(
            x: (size.width - textSize.width) / 2,
            y: (size.height - textSize.height) / 2
        )
        
        text.draw(at: textPoint, withAttributes: attributes)
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return image
    }
    
    /// 测试UI组件创建
    static func testUIComponentsCreation() {
        print("📋 测试UI组件创建")
        
        // 测试按钮创建
        let testButton = UIButton(type: .custom)
        testButton.setImage(UIImage(named: "美肤"), for: .normal)
        testButton.setTitle("美肤", for: .normal)
        testButton.setImagePosition(with: .top, spacing: 4)
        
        if testButton.currentImage != nil && testButton.currentTitle != nil {
            print("✅ 按钮创建成功，图片和标题设置正确")
        } else {
            print("❌ 按钮创建失败")
        }
        
        // 测试颜色创建
        let testColor = UIColor(hex: "#F9F9F9")
        print("✅ 颜色创建成功: \(testColor)")
        
        let themeColor = UIColor(hex: "#FFEC53")
        print("✅ 主题色创建成功: \(themeColor)")
    }
}

// MARK: - 使用示例
extension PortraitBeautyFlowTest {
    
    /// 在AppDelegate中调用此方法进行测试
    static func runTestsInAppDelegate() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            testCompleteFlow()
            testUIComponentsCreation()
        }
    }
    
    /// 在ViewController中调用此方法进行测试
    static func runTestsInViewController(_ viewController: UIViewController) {
        // 测试页面跳转
        let portraitBeautyVC = PortraitBeautyVC()
        portraitBeautyVC.hidesBottomBarWhenPushed = true
        
        print("🧪 测试页面跳转")
        viewController.navigationController?.pushViewController(portraitBeautyVC, animated: true)
        print("✅ 页面跳转测试完成")
    }
}
