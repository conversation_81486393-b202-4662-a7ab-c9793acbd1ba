//
//  NailModelTest.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import Foundation
import UIKit

class NailModelTest {
    
    /// 测试美甲数据获取
    static func testFetchNailData() {
        print("开始测试美甲数据获取...")
        
        NailModel.fetchNailData { result in
            switch result {
            case .success(let models):
                print("✅ 美甲数据获取成功！")
                print("📊 总共获取到 \(models.count) 个美甲模型")
                
                // 打印前几个模型的详细信息
                for (index, model) in models.prefix(3).enumerated() {
                    print("\n--- 美甲模型 \(index + 1) ---")
                    print("ID: \(model.id)")
                    print("名称: \(model.name)")
                    print("英文名称: \(model.name_en)")
                    print("图片URL: \(model.image)")
                    print("是否VIP: \(model.isVip)")
                    print("类型: \(model.type)")
                    print("性别: \(model.sex)")
                    print("提示词: \(model.prompt)")
                }
                
                // 统计信息
                let vipCount = models.filter { $0.isVip }.count
                let freeCount = models.filter { !$0.isVip }.count
                let femaleCount = models.filter { $0.sex == 0 }.count
                let maleCount = models.filter { $0.sex == 1 }.count
                
                print("\n📈 统计信息:")
                print("VIP模型: \(vipCount) 个")
                print("免费模型: \(freeCount) 个")
                print("女性模型: \(femaleCount) 个")
                print("男性模型: \(maleCount) 个")
                
            case .failure(let error):
                print("❌ 美甲数据获取失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 测试美甲数据管理器
    static func testNailDataManager() {
        print("\n开始测试美甲数据管理器...")
        
        let manager = NailDataManager.shared
        
        manager.loadNailData { result in
            switch result {
            case .success(let models):
                print("✅ 数据管理器加载成功！")
                
                // 测试各种筛选功能
                let allModels = manager.getAllModels()
                print("所有模型数量: \(allModels.count)")
                
                let femaleModels = manager.getModelsBySex(0)
                print("女性模型数量: \(femaleModels.count)")
                
                let maleModels = manager.getModelsBySex(1)
                print("男性模型数量: \(maleModels.count)")
                
                let vipModels = manager.getVipModels()
                print("VIP模型数量: \(vipModels.count)")
                
                let freeModels = manager.getFreeModels()
                print("免费模型数量: \(freeModels.count)")
                
                // 测试搜索功能
                let searchResults = manager.searchModels(keyword: "渐变")
                print("搜索'渐变'结果: \(searchResults.count) 个")
                
                // 测试根据ID查找
                if let firstModel = allModels.first {
                    let foundModel = manager.getModelById(firstModel.id)
                    print("根据ID查找模型: \(foundModel != nil ? "成功" : "失败")")
                }
                
            case .failure(let error):
                print("❌ 数据管理器加载失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 测试JSON解析
    static func testJSONParsing() {
        print("\n开始测试JSON解析...")
        
        // 模拟JSON数据
        let jsonString = """
        [
            {
                "id": "2",
                "image": "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/images/25.png",
                "name": "大理石纹美甲",
                "isVip": false,
                "type": 1,
                "sex": 0,
                "prompt": "半透明裸色底油上晕染着优雅的灰白色大理石纹理，高亮光封层，奢华现代氛围，时尚大片质感",
                "name_en": "Cotton Candy Balayage"
            },
            {
                "id": "1",
                "image": "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/images/25.png",
                "name": "浪漫果冻渐变",
                "isVip": false,
                "type": 1,
                "sex": 0,
                "prompt": "精致果冻感美甲，柔和渐变从甲根半透婴儿粉过渡到指尖半透樱桃红，超高亮面水润光泽效果，如水果软糖般剔透，长椭圆形甲型，梦幻甜美氛围",
                "name_en": "Cotton Candy Balayage"
            }
        ]
        """
        
        guard let jsonData = jsonString.data(using: .utf8) else {
            print("❌ JSON字符串转换失败")
            return
        }
        
        do {
            let models = try JSONDecoder().decode([NailModel].self, from: jsonData)
            print("✅ JSON解析成功！")
            print("解析出 \(models.count) 个模型")
            
            for model in models {
                print("\n模型信息:")
                print("ID: \(model.id)")
                print("名称: \(model.name)")
                print("提示词: \(model.prompt)")
            }
            
        } catch {
            print("❌ JSON解析失败: \(error)")
        }
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🧪 开始运行美甲模型测试套件...")
        print("=" * 50)
        
        // 测试JSON解析
        testJSONParsing()
        
        // 等待一秒后测试网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            testFetchNailData()
            
            // 再等待一秒后测试数据管理器
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                testNailDataManager()
            }
        }
    }
}

// MARK: - 字符串扩展，用于重复字符
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}
