//
//  NailFlowTest.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import UIKit
import SVProgressHUD

/// 美甲流程测试类
class NailFlowTest {
    
    /// 测试完整的美甲流程
    /// - Parameter fromViewController: 启动测试的视图控制器
    static func testCompleteNailFlow(from fromViewController: UIViewController) {
        let alert = UIAlertController(
            title: "美甲流程测试",
            message: "选择测试方式",
            preferredStyle: .actionSheet
        )
        
        alert.addAction(UIAlertAction(title: "测试Banner点击流程", style: .default) { _ in
            testBannerClickFlow(from: fromViewController)
        })
        
        alert.addAction(UIAlertAction(title: "直接测试美甲处理", style: .default) { _ in
            testDirectNailProcess(from: fromViewController)
        })
        
        alert.addAction(UIAlertAction(title: "测试相机功能", style: .default) { _ in
            testCameraFlow(from: fromViewController)
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // 为iPad设置popover
        if let popover = alert.popoverPresentationController {
            popover.sourceView = fromViewController.view
            popover.sourceRect = CGRect(x: fromViewController.view.bounds.midX, y: fromViewController.view.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }
        
        fromViewController.present(alert, animated: true)
    }
    
    /// 测试Banner点击流程
    private static func testBannerClickFlow(from fromViewController: UIViewController) {
        print("🧪 开始测试Banner点击流程...")
        
        // 模拟banner点击，直接打开相机
        let nailCameraVC = NailCameraViewController()
        nailCameraVC.delegate = TestNailCameraDelegate(parentVC: fromViewController)
        nailCameraVC.modalPresentationStyle = .fullScreen
        fromViewController.present(nailCameraVC, animated: true)
    }
    
    /// 直接测试美甲处理
    private static func testDirectNailProcess(from fromViewController: UIViewController) {
        print("🧪 开始测试美甲处理...")
        
        // 创建测试图片
        let testImage = createTestHandImage()
        
        // 直接跳转到美甲处理页面
        let nailProcessVC = NailProcessViewController(userImage: testImage)
        nailProcessVC.hidesBottomBarWhenPushed = true
        
        if let navController = fromViewController.navigationController {
            navController.pushViewController(nailProcessVC, animated: true)
        } else {
            let navController = UINavigationController(rootViewController: nailProcessVC)
            fromViewController.present(navController, animated: true)
        }
    }
    
    /// 测试相机功能
    private static func testCameraFlow(from fromViewController: UIViewController) {
        print("🧪 开始测试相机功能...")
        
        let nailCameraVC = NailCameraViewController()
        nailCameraVC.delegate = TestNailCameraDelegate(parentVC: fromViewController)
        nailCameraVC.modalPresentationStyle = .fullScreen
        fromViewController.present(nailCameraVC, animated: true)
    }
    
    /// 创建测试用的手部图片
    private static func createTestHandImage() -> UIImage {
        let size = CGSize(width: 400, height: 600)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        
        // 绘制背景
        UIColor.lightGray.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        
        // 绘制手的形状（简化版）
        UIColor.systemPink.setFill()
        
        // 手掌
        let palmRect = CGRect(x: 100, y: 200, width: 200, height: 250)
        UIBezierPath(ovalIn: palmRect).fill()
        
        // 手指
        let fingerWidth: CGFloat = 30
        let fingerHeight: CGFloat = 80
        
        for i in 0..<5 {
            let x = 120 + CGFloat(i) * 35
            let y: CGFloat = 120
            let fingerRect = CGRect(x: x, y: y, width: fingerWidth, height: fingerHeight)
            UIBezierPath(ovalIn: fingerRect).fill()
        }
        
        // 添加文字
        let text = "测试手部图片"
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.boldSystemFont(ofSize: 24),
            .foregroundColor: UIColor.white
        ]
        let textSize = text.size(withAttributes: attributes)
        let textRect = CGRect(
            x: (size.width - textSize.width) / 2,
            y: size.height - 100,
            width: textSize.width,
            height: textSize.height
        )
        text.draw(in: textRect, withAttributes: attributes)
        
        let image = UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
        UIGraphicsEndImageContext()
        
        return image
    }
    
    /// 测试API连接
    static func testAPIConnection() {
        print("🧪 测试API连接...")
        
        SVProgressHUD.show(withStatus: "测试API连接...")
        
        // 创建测试图片
        let testImage = createTestHandImage()
        
        // 获取第一个美甲样式进行测试
        NailModel.fetchNailData { result in
            switch result {
            case .success(let models):
                if let firstModel = models.first {
                    print("✅ 获取到美甲数据，开始测试API...")
                    
                    VolcanoEngineAPI.processNailWithModel(
                        image: testImage,
                        nailModel: firstModel
                    ) { result in
                        DispatchQueue.main.async {
                            SVProgressHUD.dismiss()
                            
                            switch result {
                            case .success(_):
                                SVProgressHUD.showSuccess(withStatus: "API测试成功！")
                                print("✅ API测试成功")
                                
                            case .failure(let error):
                                SVProgressHUD.showError(withStatus: "API测试失败")
                                print("❌ API测试失败: \(error.localizedDescription)")
                            }
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        SVProgressHUD.showError(withStatus: "没有可用的美甲样式")
                    }
                }
                
            case .failure(let error):
                DispatchQueue.main.async {
                    SVProgressHUD.showError(withStatus: "获取美甲数据失败")
                    print("❌ 获取美甲数据失败: \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - 测试用的相机代理
class TestNailCameraDelegate: NSObject, NailCameraViewControllerDelegate {
    weak var parentVC: UIViewController?
    
    init(parentVC: UIViewController) {
        self.parentVC = parentVC
    }
    
    func nailCameraDidSelectImage(_ image: UIImage) {
        print("✅ 相机测试：用户选择了图片")
        
        // 模拟HomeVC的行为，跳转到美甲处理页面
        let nailProcessVC = NailProcessViewController(userImage: image)
        nailProcessVC.hidesBottomBarWhenPushed = true
        
        if let navController = parentVC?.navigationController {
            navController.pushViewController(nailProcessVC, animated: true)
        } else {
            let navController = UINavigationController(rootViewController: nailProcessVC)
            parentVC?.present(navController, animated: true)
        }
    }
    
    func nailCameraDidCancel() {
        print("ℹ️ 相机测试：用户取消了拍照")
    }
}

// MARK: - UIViewController扩展
extension UIViewController {
    
    /// 便捷方法：启动美甲流程测试
    func testNailFlow() {
        NailFlowTest.testCompleteNailFlow(from: self)
    }
    
    /// 便捷方法：测试API连接
    func testNailAPI() {
        NailFlowTest.testAPIConnection()
    }
}
