//
//  PTMFilterHelperTest.m
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "PTMFilterHelper.h"

@interface PTMFilterHelperTest : NSObject

+ (void)runAllTests;
+ (void)testBasicBeautyParameters;
+ (void)testParameterUpdate;
+ (void)testImageProcessing;
+ (void)testConfigPersistence;

@end

@implementation PTMFilterHelperTest

+ (void)runAllTests {
    NSLog(@"🧪 开始PTMFilterHelper测试");
    
    [self testBasicBeautyParameters];
    [self testParameterUpdate];
    [self testImageProcessing];
    [self testConfigPersistence];
    
    NSLog(@"✅ PTMFilterHelper测试完成");
}

+ (void)testBasicBeautyParameters {
    NSLog(@"📋 测试基础美颜参数");
    
    // 初始化SDK
    [PTMFilterHelper setupSDK];
    
    // 测试设置参数
    BOOL success1 = [PTMFilterHelper updateBeautyParameter:@"blur_level" value:4.0];
    BOOL success2 = [PTMFilterHelper updateBeautyParameter:@"color_level" value:0.3];
    BOOL success3 = [PTMFilterHelper updateBeautyParameter:@"cheek_v" value:0.2];
    
    NSLog(@"设置磨皮参数: %@", success1 ? @"成功" : @"失败");
    NSLog(@"设置美白参数: %@", success2 ? @"成功" : @"失败");
    NSLog(@"设置V脸参数: %@", success3 ? @"成功" : @"失败");
    
    // 测试获取参数
    double blurValue = [PTMFilterHelper getCurrentValue:@"blur_level"];
    double colorValue = [PTMFilterHelper getCurrentValue:@"color_level"];
    double vFaceValue = [PTMFilterHelper getCurrentValue:@"cheek_v"];
    
    NSLog(@"当前磨皮值: %.1f", blurValue);
    NSLog(@"当前美白值: %.1f", colorValue);
    NSLog(@"当前V脸值: %.1f", vFaceValue);
}

+ (void)testParameterUpdate {
    NSLog(@"🔄 测试参数更新");
    
    // 设置一组参数
    NSDictionary *testParameters = @{
        @"blur_level": @(3.0),
        @"color_level": @(0.4),
        @"red_level": @(0.2),
        @"eye_bright": @(0.3),
        @"cheek_v": @(0.25),
        @"eye_enlarging": @(0.3)
    };
    
    for (NSString *paramName in testParameters) {
        NSNumber *value = testParameters[paramName];
        BOOL success = [PTMFilterHelper updateBeautyParameter:paramName value:[value doubleValue]];
        NSLog(@"更新 %@: %@", paramName, success ? @"成功" : @"失败");
    }
    
    // 验证参数是否正确设置
    for (NSString *paramName in testParameters) {
        NSNumber *expectedValue = testParameters[paramName];
        double currentValue = [PTMFilterHelper getCurrentValue:paramName];
        
        if (fabs(currentValue - [expectedValue doubleValue]) < 0.001) {
            NSLog(@"✅ %@ 值正确: %.3f", paramName, currentValue);
        } else {
            NSLog(@"❌ %@ 值错误: 期望%.3f, 实际%.3f", paramName, [expectedValue doubleValue], currentValue);
        }
    }
}

+ (void)testImageProcessing {
    NSLog(@"🖼️ 测试图片处理");
    
    // 创建一个测试图片
    UIImage *testImage = [self createTestImage];
    
    if (!testImage) {
        NSLog(@"❌ 无法创建测试图片");
        return;
    }
    
    NSLog(@"原始图片尺寸: %.0fx%.0f", testImage.size.width, testImage.size.height);
    
    // 设置一些美颜参数
    [PTMFilterHelper updateBeautyParameter:@"blur_level" value:2.0];
    [PTMFilterHelper updateBeautyParameter:@"color_level" value:0.2];
    
    // 处理图片
    UIImage *processedImage = [PTMFilterHelper processImageWithBeauty:testImage];
    
    if (processedImage) {
        NSLog(@"✅ 图片处理成功，处理后尺寸: %.0fx%.0f", processedImage.size.width, processedImage.size.height);
    } else {
        NSLog(@"❌ 图片处理失败");
    }
    
    // 测试兼容接口
    UIImage *filterImage = [PTMFilterHelper processImage:testImage withFilterName:@"ziran1" filterLevel:0.5];
    
    if (filterImage) {
        NSLog(@"✅ 滤镜处理成功");
    } else {
        NSLog(@"❌ 滤镜处理失败");
    }
}

+ (void)testConfigPersistence {
    NSLog(@"💾 测试配置持久化");
    
    // 设置一些参数
    [PTMFilterHelper updateBeautyParameter:@"blur_level" value:5.0];
    [PTMFilterHelper updateBeautyParameter:@"color_level" value:0.5];
    
    double blurBefore = [PTMFilterHelper getCurrentValue:@"blur_level"];
    double colorBefore = [PTMFilterHelper getCurrentValue:@"color_level"];
    
    NSLog(@"设置前 - 磨皮: %.1f, 美白: %.1f", blurBefore, colorBefore);
    
    // 重置到默认值
    [PTMFilterHelper resetToDefault];
    
    double blurAfterReset = [PTMFilterHelper getCurrentValue:@"blur_level"];
    double colorAfterReset = [PTMFilterHelper getCurrentValue:@"color_level"];
    
    NSLog(@"重置后 - 磨皮: %.1f, 美白: %.1f", blurAfterReset, colorAfterReset);
    
    // 再次设置参数
    [PTMFilterHelper updateBeautyParameter:@"blur_level" value:4.5];
    [PTMFilterHelper updateBeautyParameter:@"color_level" value:0.35];
    
    double blurFinal = [PTMFilterHelper getCurrentValue:@"blur_level"];
    double colorFinal = [PTMFilterHelper getCurrentValue:@"color_level"];
    
    NSLog(@"最终设置 - 磨皮: %.1f, 美白: %.1f", blurFinal, colorFinal);
}

+ (UIImage *)createTestImage {
    CGSize size = CGSizeMake(200, 200);
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0);
    
    // 绘制一个简单的渐变背景
    CGContextRef context = UIGraphicsGetCurrentContext();
    
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGFloat colors[] = {
        1.0, 0.8, 0.8, 1.0,  // 浅粉色
        0.8, 0.8, 1.0, 1.0   // 浅蓝色
    };
    
    CGGradientRef gradient = CGGradientCreateWithColorComponents(colorSpace, colors, NULL, 2);
    
    CGPoint startPoint = CGPointMake(0, 0);
    CGPoint endPoint = CGPointMake(size.width, size.height);
    
    CGContextDrawLinearGradient(context, gradient, startPoint, endPoint, 0);
    
    // 添加一些文字
    NSString *text = @"Test";
    NSDictionary *attributes = @{
        NSFontAttributeName: [UIFont boldSystemFontOfSize:24],
        NSForegroundColorAttributeName: [UIColor blackColor]
    };
    
    CGSize textSize = [text sizeWithAttributes:attributes];
    CGPoint textPoint = CGPointMake((size.width - textSize.width) / 2, (size.height - textSize.height) / 2);
    
    [text drawAtPoint:textPoint withAttributes:attributes];
    
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    CGGradientRelease(gradient);
    CGColorSpaceRelease(colorSpace);
    
    return image;
}

@end
