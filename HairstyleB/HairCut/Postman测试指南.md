# Postman测试指南

## 🚀 快速获取测试信息

### 方法1：通过应用生成
```swift
// 在任何视图控制器中调用
self.testAsyncNailProcess()
// 选择 "生成Postman测试信息"
```

### 方法2：直接调用
```swift
let testImage = UIImage(named: "test_hand")!
VolcanoEngineAPI.generatePostmanTestInfo(with: testImage)
```

## 📋 控制台输出信息

运行上述代码后，控制台会输出完整的请求信息，包括：

### 1. 提交任务请求
```
🔗 ===== 提交任务请求详情 =====
📍 URL: https://visual.volcengineapi.com/?Action=CVSync2AsyncSubmitTask&Version=2022-08-31
🔧 Method: POST
📋 Headers:
   Content-Type: application/json
   X-Date: 20250714T123456Z
   Authorization: HMAC-SHA256 Credential=...
📦 Body Parameters:
   req_key: seededit_v3.0
   binary_data_base64: ["base64_data_length_xxxxx"]
   prompt: 添加粉色渐变美甲效果
   scale: 1
   seed: -1
📄 JSON Body:
{
  "req_key" : "seededit_v3.0",
  "binary_data_base64" : [ "iVBORw0KGgoAAAANSUhEUgAA..." ],
  "prompt" : "添加粉色渐变美甲效果",
  "scale" : 1,
  "seed" : -1
}
```

### 2. 响应信息
```
📥 ===== 提交任务响应详情 =====
🔢 HTTP Status Code: 200
📄 Response Data:
{
  "code": 10000,
  "data": {
    "task_id": "7392616336519610409"
  },
  "message": "Success",
  "request_id": "20240720103939AF0029465CF6A74E51EC",
  "status": 10000,
  "time_elapsed": "104.852309ms"
}
```

## 🔧 Postman设置步骤

### 提交任务请求
1. **新建POST请求**
2. **设置URL**: 
   ```
   https://visual.volcengineapi.com/?Action=CVSync2AsyncSubmitTask&Version=2022-08-31
   ```
3. **添加Headers**:
   ```
   Content-Type: application/json
   X-Date: 20250714T123456Z
   Authorization: HMAC-SHA256 Credential=...
   ```
4. **设置Body**:
   - 选择 `raw` → `JSON`
   - 粘贴控制台输出的JSON Body

### 查询任务结果
1. **新建POST请求**
2. **设置URL**:
   ```
   https://visual.volcengineapi.com/?Action=CVSync2AsyncGetResult&Version=2022-08-31
   ```
3. **添加相同的Headers**
4. **设置Body**:
   ```json
   {
     "req_key": "seededit_v3.0",
     "task_id": "YOUR_TASK_ID_HERE",
     "req_json": "{\"return_url\":false}"
   }
   ```

## 🔍 调试技巧

### 1. 检查签名
如果返回401/403错误，检查：
- X-Date格式是否正确
- Authorization签名是否正确
- 时间戳是否在有效范围内

### 2. 检查参数
如果返回400错误，检查：
- req_key是否为"seededit_v3.0"
- binary_data_base64是否为有效的base64
- prompt是否符合要求

### 3. 检查网络
如果请求超时，检查：
- 网络连接是否正常
- 防火墙是否阻止请求
- DNS解析是否正确

## 📊 常见响应

### 成功响应
```json
{
  "code": 10000,
  "data": {
    "task_id": "7392616336519610409"
  },
  "message": "Success"
}
```

### 错误响应
```json
{
  "code": 50411,
  "message": "Pre Img Risk Not Pass",
  "request_id": "xxx"
}
```

### 查询结果 - 处理中
```json
{
  "code": 10000,
  "data": {
    "status": "generating"
  },
  "message": "Success"
}
```

### 查询结果 - 完成
```json
{
  "code": 10000,
  "data": {
    "binary_data_base64": ["iVBORw0KGgoAAAANSUhEUgAA..."],
    "status": "done"
  },
  "message": "Success"
}
```

## 🛠️ 故障排除

### 问题1: 签名错误
**症状**: 返回401或403
**解决**: 
1. 检查Access Key和Secret Key
2. 确认时间戳格式
3. 验证签名算法

### 问题2: 参数错误
**症状**: 返回400
**解决**:
1. 检查req_key拼写
2. 验证图片base64格式
3. 确认prompt长度

### 问题3: 任务未找到
**症状**: 查询时返回"not_found"
**解决**:
1. 确认task_id正确
2. 检查任务是否过期
3. 重新提交任务

## 📝 测试清单

- [ ] 提交任务请求成功
- [ ] 获取到有效的task_id
- [ ] 查询任务状态正常
- [ ] 任务处理完成
- [ ] 获取到结果图片
- [ ] base64解码图片正常

---

💡 **提示**: 使用应用内的测试功能可以自动生成所有必需的请求信息，包括正确的签名和时间戳！
