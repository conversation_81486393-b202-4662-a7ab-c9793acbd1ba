# 美甲功能快速启动指南

## 🚀 快速开始

### 1. 基础调用
在任何视图控制器中，只需一行代码即可启动美甲处理：

```swift
// 启动完整的美甲处理界面
let userImage = UIImage(named: "hand_photo")!
self.startNailProcess(with: userImage)
```

### 2. 演示界面
运行演示界面查看完整功能：

```swift
let demoVC = NailProcessDemoViewController()
present(demoVC, animated: true)
```

### 3. 集成到现有相机
在相机拍照完成后添加美甲选项：

```swift
// 在 NailCameraViewController 中已经集成
// 拍照后会自动显示"直接使用"或"美甲处理"选项
```

## 📱 用户流程

1. **选择图片** → 2. **选择美甲样式** → 3. **开始处理** → 4. **查看结果** → 5. **保存图片**

## 🔧 核心API

### 美甲数据获取
```swift
NailModel.fetchNailData { result in
    switch result {
    case .success(let models):
        print("获取到 \(models.count) 个美甲样式")
    case .failure(let error):
        print("获取失败: \(error)")
    }
}
```

### 火山引擎处理
```swift
VolcanoEngineAPI.processNailWithModel(
    image: userImage,
    nailModel: selectedModel
) { result in
    // 处理结果
}
```

### 便捷方法
```swift
// 快速处理（自动选择第一个样式）
self.quickNailProcess(with: image)

// 图片验证
let validation = NailProcessHelper.validateImageForNailProcess(image)
if validation.isValid {
    // 图片符合要求
}
```

## 🧪 测试功能

### 运行所有测试
```swift
// 测试美甲数据
NailModelTest.runAllTests()

// 测试API连接
TestNailAPI.runAllTests()

// 集成演示
NailIntegrationDemo.runAllDemos()
```

### 在界面中测试
```swift
// 从任何视图控制器调用
TestNailAPI.runTestsFromViewController(self)
```

## 📁 文件结构

```
美甲处理系统/
├── Model/
│   ├── NailModel.swift                    # 数据模型
│   └── NailDataManager.swift              # 数据管理
├── View/
│   ├── NailProcessViewController.swift    # 主处理界面
│   └── NailStyleViewController.swift      # 样式选择界面
├── Tool/
│   ├── VolcanoEngineAPI.swift            # 火山引擎API
│   └── NailProcessHelper.swift           # 处理助手
├── Demo/
│   ├── NailProcessDemoViewController.swift # 演示界面
│   └── NailIntegrationDemo.swift         # 集成演示
└── Test/
    ├── NailModelTest.swift               # 数据测试
    └── TestNailAPI.swift                 # API测试
```

## ⚡ 快速集成示例

### 在现有按钮中添加美甲功能
```swift
@IBAction func nailButtonTapped(_ sender: UIButton) {
    // 方式1: 从相册选择
    let imagePicker = UIImagePickerController()
    imagePicker.delegate = self
    imagePicker.sourceType = .photoLibrary
    present(imagePicker, animated: true)
}

// 图片选择完成后
func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
    if let image = info[.originalImage] as? UIImage {
        picker.dismiss(animated: true) {
            self.startNailProcess(with: image)
        }
    }
}
```

### 在TabBar中添加美甲选项
```swift
// 在 AppDelegate 或 SceneDelegate 中
let nailTab = UINavigationController(rootViewController: NailProcessDemoViewController())
nailTab.tabBarItem = UITabBarItem(title: "美甲", image: UIImage(systemName: "paintbrush"), tag: 3)
tabBarController.viewControllers?.append(nailTab)
```

## 🔍 调试技巧

### 查看网络请求
```swift
// 在 VolcanoEngineAPI.swift 中添加日志
print("请求URL: \(url)")
print("请求头: \(headers)")
print("请求体: \(parameters)")
```

### 查看处理进度
```swift
// 使用 SVProgressHUD 显示状态
SVProgressHUD.show(withStatus: "正在处理...")
SVProgressHUD.showProgress(0.5, status: "处理中 50%")
SVProgressHUD.showSuccess(withStatus: "处理完成")
```

### 错误处理
```swift
// 统一错误处理
func handleNailProcessError(_ error: Error) {
    print("美甲处理错误: \(error.localizedDescription)")
    SVProgressHUD.showError(withStatus: "处理失败，请重试")
}
```

## 📋 检查清单

在部署前请确认：

- [ ] 火山引擎API密钥配置正确
- [ ] 网络权限已添加到Info.plist
- [ ] 相册访问权限已配置
- [ ] 相机权限已配置（如果使用相机功能）
- [ ] SDWebImage依赖已正确安装
- [ ] 测试用例运行通过

## 🆘 常见问题

### Q: API调用失败怎么办？
A: 检查网络连接，确认API密钥正确，查看控制台错误日志

### Q: 图片处理很慢？
A: 确保图片尺寸合适（建议1024x1024以下），检查网络状况

### Q: 美甲样式加载不出来？
A: 检查数据源URL是否可访问，确认网络权限

### Q: 如何自定义美甲样式？
A: 修改nailsmodel.json数据源，或在NailModel中添加本地样式

---

🎉 **恭喜！美甲处理功能已经完全集成，开始享受AI美甲的乐趣吧！**
