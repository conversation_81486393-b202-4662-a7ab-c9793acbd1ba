# 美甲详情页面UI修改说明

## 修改概述

按照用户要求，对 `NailProcessViewController` 进行了全面的UI重新设计，实现了更符合美甲应用场景的界面布局。

## 主要修改内容

### 1. 页面布局结构
- **从上到下布局**：剩余次数 → 重拍按钮 → 图片显示区域 → 颜色选择 → 甲型选择 → 开始/保存按钮
- **使用ScrollView**：确保内容可以滚动，适配不同屏幕尺寸
- **主题色统一**：使用 `#FFEC53` 作为主题色

### 2. 新增UI元素

#### 剩余次数显示
- 位置：页面顶部左侧
- 功能：显示文字"剩余次数：10"（暂不实现功能逻辑）
- 样式：灰色文字，14号字体

#### 重拍按钮
- 位置：页面顶部右侧
- 功能：调用 `NailCameraViewController` 进行重新拍照
- 图片资源：使用 `重拍` 图片资源
- 回调：通过 `NailCameraViewControllerDelegate` 更新原始图片

#### 图片容器
- 背景色：`#F7F7F7`
- 圆角：12px
- 功能：显示用户选择的图片，处理完成后显示结果图片

### 3. 颜色选择区域

#### 标题
- 文字："颜色："
- 样式：16号粗体，颜色 `#333333`

#### 美甲类型展示
- 使用 `UICollectionView` 横向滚动
- 每个Cell显示美甲类型的图片（从API获取）
- 选中状态：3px厚度的主题色边框 `#FFEC53`
- 不显示文字，只显示图片效果

### 4. 甲型选择区域

#### 标题
- 文字："甲型："
- 样式：16号粗体，颜色 `#333333`

#### 甲型按钮
- 4个甲型选项：杏形、圆形、尖形、方形
- 使用 `ZYEButtonPositionAndSpace` 实现上图下文布局
- 图片资源：`甲型1_normal/select`、`甲型2_normal/select` 等
- 选中状态：
  - 背景色：`#FFF8C5`
  - 文字色：`#333333`
  - 图片：使用 `_select` 版本
- 未选中状态：
  - 背景色：`#F7F7F7`
  - 文字色：`#999999`
  - 图片：使用 `_normal` 版本
- 默认选中第一个（杏形）

### 5. 按钮区域

#### 开始按钮
- 文字："开始"
- 背景色：主题色 `#FFEC53`
- 功能：可多次点击生成不同结果
- 每次都使用原始图片进行处理

#### 保存按钮
- 文字："保存"
- 背景色：主题色 `#FFEC53`
- 初始状态：隐藏
- 显示时机：处理完成后显示

## 核心功能逻辑

### 1. 图片管理
- `originalImage`：保存用户最初选择的原始图片
- `userImage`：当前显示的图片
- 重拍功能：更新 `originalImage` 和 `userImage`
- 处理功能：始终使用 `originalImage` 进行API调用

### 2. Prompt构建
- 默认甲型（杏形）：直接使用美甲类型的prompt
- 其他甲型：在prompt前添加甲型描述
- 格式：`"甲型描述。美甲类型prompt"`

### 3. 状态管理
- 选中美甲类型后启用开始按钮
- 处理完成后显示保存按钮
- 重拍后重置处理状态

## 颜色规范

| 用途 | 颜色值 | 说明 |
|------|--------|------|
| 主题色 | #FFEC53 | 选中边框、按钮背景 |
| 甲型选中背景 | #FFF8C5 | 甲型按钮选中状态 |
| 未选中背景 | #F7F7F7 | 甲型按钮、图片容器背景 |
| 选中文字 | #333333 | 选中状态文字颜色 |
| 未选中文字 | #999999 | 未选中状态文字颜色 |
| 页面背景 | #FFFFFF | 整个页面背景 |

## 图片资源

### 甲型图片
- `甲型1_normal.png` / `甲型1_select.png` - 杏形
- `甲型2_normal.png` / `甲型2_select.png` - 圆形  
- `甲型3_normal.png` / `甲型3_select.png` - 尖形
- `甲型4_normal.png` / `甲型4_select.png` - 方形

### 功能图片
- `重拍.png` - 重拍按钮图片

## 代理协议

### NailCameraViewControllerDelegate
```swift
func nailCameraDidSelectImage(_ image: UIImage)
func nailCameraDidCancel()
```

## 技术要点

1. **使用现有UIColor扩展**：`UIColor.hex(string: "#FFEC53")`
2. **ZYEButtonPositionAndSpace**：实现图片在上文字在下的按钮布局
3. **SnapKit约束**：响应式布局适配不同屏幕
4. **SDWebImage**：异步加载美甲类型图片
5. **SVProgressHUD**：处理过程中的进度提示

## 测试建议

1. 测试重拍功能是否正确更新原始图片
2. 测试甲型选择的视觉反馈
3. 测试美甲类型选择的边框效果
4. 测试多次点击开始按钮的处理逻辑
5. 测试不同屏幕尺寸的布局适配
