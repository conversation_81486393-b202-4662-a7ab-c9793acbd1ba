# 人像美容功能快速启动指南

## 🚀 快速开始

### 1. 运行项目
1. 打开Xcode项目
2. 选择目标设备或模拟器
3. 点击运行按钮 (⌘+R)

### 2. 进入人像美容
1. 在首页找到"人像美容"按钮
2. 点击后弹出选择框：拍照 / 相册 / 取消
3. 选择拍照或从相册选择图片
4. 自动进入人像美容页面，显示选择的图片
5. 看到三个功能选项：美肤、面部重塑、发型编辑

### 3. 体验美肤功能
1. 点击"美肤"按钮
2. 进入美肤编辑页面
3. 使用滑块调节各种美肤参数
4. 实时查看效果变化
5. 点击"保存"保存处理后的图片

## 📱 页面预览

### 人像美容入口页面
```
┌─────────────────────────────┐
│  ←                    保存   │ ← 顶部导航栏
├─────────────────────────────┤
│                             │
│                             │
│        人像图片显示区域        │
│                             │
│                             │
├─────────────────────────────┤
│  美肤    面部重塑   发型编辑   │ ← 底部工具栏
└─────────────────────────────┘
```

### 美肤编辑页面
```
┌─────────────────────────────┐
│  ←                    保存   │ ← 顶部导航栏
├─────────────────────────────┤
│                             │
│      图片预览区域(正方形)      │
│                             │
├─────────────────────────────┤
│ 磨皮              6.0       │
│ ━━━━━━━━━━○━━━━━━━━━━━━━━━━━ │
│                             │
│ 美白              0.3       │
│ ━━━━○━━━━━━━━━━━━━━━━━━━━━━ │
│                             │
│ 红润              0.2       │
│ ━━○━━━━━━━━━━━━━━━━━━━━━━━━ │
│                             │
│ ...更多参数...               │
└─────────────────────────────┘
```

## 🛠️ 开发测试

### 运行测试
```swift
// 在AppDelegate或任意ViewController中调用
PortraitBeautyFlowTest.testCompleteFlow()
```

### 测试内容
- ✅ ViewController创建
- ✅ 美颜参数加载
- ✅ 图片处理功能
- ✅ UI组件创建

### 查看测试结果
在Xcode控制台中查看测试输出：
```
🧪 开始人像美容流程测试
📋 测试PortraitBeautyVC创建
✅ PortraitBeautyVC创建成功
✅ hidesBottomBarWhenPushed属性设置成功
📋 测试SkinBeautyEditVC创建
✅ SkinBeautyEditVC创建成功
...
✅ 人像美容流程测试完成
```

## 🎨 自定义配置

### 修改美颜参数默认值
编辑 `HairCut/Resource/Json/beauty.json`：
```json
{
  "skinBeauty": [
    {
      "cnname": "磨皮",
      "name": "blur_level",
      "currentValue": 4.0,  // 修改默认值
      "defaultValue": 4.0,
      "minValue": 0.0,
      "maxValue": 6.0
    }
  ]
}
```

### 修改UI颜色
在代码中修改颜色值：
```swift
// 背景色
view.backgroundColor = UIColor.hex(string: "#F9F9F9")

// 主题色
saveButton.backgroundColor = UIColor.hex(string: "#FFEC53")
```

### 添加新的美颜参数
1. 在 `beauty.json` 中添加新参数
2. 确保参数名称与FaceUnity SDK一致
3. 重新运行应用，新参数会自动显示

## 🔧 常见问题

### Q: 美颜效果不明显？
A: 检查以下几点：
- 确保FaceUnity SDK正确初始化
- 检查美颜道具包是否正确加载
- 尝试增大参数值

### Q: 图片处理很慢？
A: 优化建议：
- 图片会自动缩放到2048像素以内
- 在后台线程处理图片
- 避免频繁调节参数

### Q: 参数设置不生效？
A: 检查步骤：
- 确认参数名称正确
- 检查参数值范围
- 查看控制台错误信息

### Q: 页面跳转失败？
A: 可能原因：
- 检查navigationController是否存在
- 确认ViewController继承关系正确
- 查看编译错误信息

## 📚 相关文档

- [PTMFilterHelper美颜系统使用说明](Tool/beauteFace/README.md)
- [人像美容功能使用说明](人像美容功能使用说明.md)
- [美颜参数配置说明](Resource/Json/beauty.json)

## 🎯 下一步

1. **体验基础功能**
   - 尝试所有美肤参数
   - 测试图片保存功能
   - 体验实时预览效果

2. **自定义配置**
   - 修改默认参数值
   - 调整UI颜色和布局
   - 添加自定义美颜参数

3. **扩展功能**
   - 实现面部重塑功能
   - 集成发型编辑功能
   - 添加滤镜效果

4. **性能优化**
   - 优化图片处理速度
   - 减少内存占用
   - 提升用户体验

## 💡 提示

- 首次使用建议先运行测试确保功能正常
- 可以通过修改beauty.json快速调整默认参数
- 所有用户调节的参数会自动保存到沙盒
- 支持实时预览，无需等待处理完成

开始享受人像美容功能吧！🎉
