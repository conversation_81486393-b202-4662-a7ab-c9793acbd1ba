Incident Identifier: 3D5D5EE1-A2A5-4ED8-9C7B-91B21116DF2C
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone17,2
Process:             发型测试 [40914]
Path:                /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             2.0.0 (2)
AppStoreTools:       16C5031b
AppVariant:          1:iPhone17,2:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [31826]

Date/Time:           2025-03-01 11:48:17.0377 +0800
Launch Time:         2025-03-01 10:32:58.9200 +0800
OS Version:          iPhone OS 18.1.1 (22B91)
Release Type:        User
Baseband Version:    1.11.01
Report Version:      104

Exception Type:  EXC_CRASH (SIGABRT)
Exception Codes: 0x0000000000000000, 0x0000000000000000
Triggered by Thread:  0

Last Exception Backtrace:
0   CoreFoundation                	0x195a0c7cc __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x192cdf2e4 objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreFoundation                	0x195b07748 +[NSException raise:format:] + 128 (NSException.m:0)
3   QuartzCore                    	0x197443680 CA::Layer::set_position(CA::Vec2<double> const&, bool) + 160 (CALayer.mm:4716)
4   QuartzCore                    	0x1974435b8 -[CALayer setPosition:] + 52 (CALayer.mm:4753)
5   UIKitCore                     	0x1981aec0c -[UIView _backing_setPosition:] + 176 (_UIViewBacking.m:235)
6   UIKitCore                     	0x1981ae538 -[UIView setCenter:] + 216 (UIView.m:9656)
7   UIKitCore                     	0x1996c7320 -[UIImageView setCenter:] + 52 (UIImageView.m:1797)
8   发型测试                          	0x100663f40 0x1005a0000 + 802624
9   发型测试                          	0x100664000 0x1005a0000 + 802816
10  UIKitCore                     	0x198562ac4 -[UIGestureRecognizerTarget _sendActionWithGestureRecognizer:] + 128 (UIGestureRecognizer.m:179)
11  UIKitCore                     	0x198562934 _UIGestureRecognizerSendTargetActions + 92 (UIGestureRecognizer.m:1751)
12  UIKitCore                     	0x1985626f4 _UIGestureRecognizerSendActions + 284 (UIGestureRecognizer.m:1790)
13  UIKitCore                     	0x198215b28 -[UIGestureRecognizer _updateGestureForActiveEvents] + 572 (UIGestureRecognizer.m:0)
14  UIKitCore                     	0x1981e7724 _UIGestureEnvironmentUpdate + 2488 (UIGestureEnvironment.m:192)
15  UIKitCore                     	0x1982dba00 -[UIGestureEnvironment _deliverEvent:toGestureRecognizers:usingBlock:] + 336 (UIGestureEnvironment.m:843)
16  UIKitCore                     	0x19847bfe4 -[UIGestureEnvironment _updateForEvent:window:] + 188 (UIGestureEnvironment.m:810)
17  UIKitCore                     	0x19847b3c8 -[UIWindow sendEvent:] + 2948 (UIWindow.m:3632)
18  UIKitCore                     	0x19830fb70 -[UIApplication sendEvent:] + 376 (UIApplication.m:12972)
19  UIKitCore                     	0x19831009c __dispatchPreprocessedEventFromEventQueue + 1048 (UIEventDispatcher.m:2686)
20  UIKitCore                     	0x198319f3c __processEventQueue + 5696 (UIEventDispatcher.m:3044)
21  UIKitCore                     	0x198212c60 updateCycleEntry + 160 (UIEventDispatcher.m:133)
22  UIKitCore                     	0x1982109d8 _UIUpdateSequenceRun + 84 (_UIUpdateSequence.mm:136)
23  UIKitCore                     	0x198210628 schedulerStepScheduledMainSection + 172 (_UIUpdateScheduler.m:1171)
24  UIKitCore                     	0x19821159c runloopSourceCallback + 92 (_UIUpdateScheduler.m:1334)
25  CoreFoundation                	0x1959e0328 __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 28 (CFRunLoop.c:1970)
26  CoreFoundation                	0x1959e02bc __CFRunLoopDoSource0 + 176 (CFRunLoop.c:2014)
27  CoreFoundation                	0x1959dddc0 __CFRunLoopDoSources0 + 244 (CFRunLoop.c:2051)
28  CoreFoundation                	0x1959dcfbc __CFRunLoopRun + 840 (CFRunLoop.c:2969)
29  CoreFoundation                	0x1959dc830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
30  GraphicsServices              	0x1e19bc1c4 GSEventRunModal + 164 (GSEvent.c:2196)
31  UIKitCore                     	0x198542eb0 -[UIApplication _run] + 816 (UIApplication.m:3844)
32  UIKitCore                     	0x1985f15b4 UIApplicationMain + 340 (UIApplication.m:5496)
33  UIKitCore                     	0x19892bfa8 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
34  发型测试                          	0x1006919ec 0x1005a0000 + 989676
35  发型测试                          	0x100691964 0x1005a0000 + 989540
36  发型测试                          	0x100691ab0 0x1005a0000 + 989872
37  dyld                          	0x1bb3caec8 start + 2724 (dyldMain.cpp:1334)

Thread 0 name:
Thread 0 Crashed:
0   libsystem_kernel.dylib        	0x00000001e5da91d4 __pthread_kill + 8 (:-1)
1   libsystem_pthread.dylib       	0x000000021df1aef8 pthread_kill + 268 (pthread.c:1721)
2   libsystem_c.dylib             	0x000000019d79bad8 abort + 128 (abort.c:122)
3   libc++abi.dylib               	0x000000021dd295b8 abort_message + 132 (abort_message.cpp:78)
4   libc++abi.dylib               	0x000000021dd17bac demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
5   libobjc.A.dylib               	0x0000000192cfae14 _objc_terminate() + 156 (objc-exception.mm:496)
6   libc++abi.dylib               	0x000000021dd2887c std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000021dd2c0ac __cxa_rethrow + 204 (cxa_exception.cpp:648)
8   libobjc.A.dylib               	0x0000000192cf6650 objc_exception_rethrow + 44 (objc-exception.mm:399)
9   CoreFoundation                	0x00000001959dc934 CFRunLoopRunSpecific + 848 (CFRunLoop.c:3450)
10  GraphicsServices              	0x00000001e19bc1c4 GSEventRunModal + 164 (GSEvent.c:2196)
11  UIKitCore                     	0x0000000198542eb0 -[UIApplication _run] + 816 (UIApplication.m:3844)
12  UIKitCore                     	0x00000001985f15b4 UIApplicationMain + 340 (UIApplication.m:5496)
13  UIKitCore                     	0x000000019892bfa8 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
14  发型测试                          	0x00000001006919ec 0x1005a0000 + 989676
15  发型测试                          	0x0000000100691964 0x1005a0000 + 989540
16  发型测试                          	0x0000000100691ab0 0x1005a0000 + 989872
17  dyld                          	0x00000001bb3caec8 start + 2724 (dyldMain.cpp:1334)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001e5d9e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e5da1d98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e5da1cb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e5da1afc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001959dda84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001959dd130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x00000001959dc830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   Foundation                    	0x0000000194684500 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x0000000194684350 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x0000000198556358 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1241)
10  Foundation                    	0x00000001946956c8 __NSThread__start__ + 724 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000021df1937c _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000021df14494 thread_start + 8 (:-1)

Thread 2 name:
Thread 2:
0   libsystem_kernel.dylib        	0x00000001e5da41b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019d73ba78 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019d73b990 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000100d06e70 0x1005a0000 + 7761520
4   发型测试                          	0x0000000100cb9bc0 0x1005a0000 + 7445440
5   libsystem_pthread.dylib       	0x000000021df1937c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021df14494 thread_start + 8 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001e5da41b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019d73ba78 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019d73b990 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000100d06e70 0x1005a0000 + 7761520
4   发型测试                          	0x0000000100cb9bc0 0x1005a0000 + 7445440
5   libsystem_pthread.dylib       	0x000000021df1937c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021df14494 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001e5d9e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e5da1d98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e5da1cb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e5da1afc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001959dda84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001959dd130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x00000001959dc830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CFNetwork                     	0x0000000196f58ee0 +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x00000001946956c8 __NSThread__start__ + 724 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000021df1937c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021df14494 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001e5d9e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e5da1d98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e5da1cb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e5da1afc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001959dda84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001959dd130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x00000001959dc830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CoreFoundation                	0x0000000195a47cec CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001a2e6c084 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000021df1937c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021df14494 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e5d9e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e5da1d98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e5da1cb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e5da1afc mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000100c23278 0x1005a0000 + 6828664
5   libsystem_pthread.dylib       	0x000000021df1937c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021df14494 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e5da41b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019d73ba78 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019d73b990 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000100c1e870 0x1005a0000 + 6809712
4   libsystem_pthread.dylib       	0x000000021df1937c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021df14494 thread_start + 8 (:-1)

Thread 8:
0   libsystem_kernel.dylib        	0x00000001e5da3f90 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x000000021df16a50 _pthread_cond_wait + 1204 (pthread_cond.c:862)
2   libc++.1.dylib                	0x00000001a5faf584 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 28 (condition_variable.cpp:30)
3   发型测试                          	0x0000000100afba64 0x1005a0000 + 5618276
4   发型测试                          	0x0000000100afd4bc 0x1005a0000 + 5625020
5   libsystem_pthread.dylib       	0x000000021df1937c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021df14494 thread_start + 8 (:-1)

Thread 9:
0   libsystem_pthread.dylib       	0x000000021df14480 start_wqthread + 0 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x000000021df14480 start_wqthread + 0 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x000000021df14480 start_wqthread + 0 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x000000021df14480 start_wqthread + 0 (:-1)


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000000
    x4: 0x000000021dd2df3b   x5: 0x000000016f85f4c0   x6: 0x000000000000006e   x7: 0x0000000000000000
    x8: 0xdc8fb69dcb488ccc   x9: 0xdc8fb69c3233dc8c  x10: 0x0000000000000051  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x0000000195e36bbc  x14: 0x00000000001ff800  x15: 0x00000000000007fb
   x16: 0x0000000000000148  x17: 0x00000001f97b5040  x18: 0x0000000000000000  x19: 0x0000000000000006
   x20: 0x0000000000000103  x21: 0x00000001f97b5120  x22: 0x0000000300bdc340  x23: 0x0000000300bdc350
   x24: 0x0000000000000001  x25: 0x0000000000000001  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016f85f430   lr: 0x000000021df1aef8
    sp: 0x000000016f85f410   pc: 0x00000001e5da91d4 cpsr: 0x40000000
   esr: 0x56000080  Address size fault


Binary Images:
        0x1005a0000 -         0x100fd3fff 发型测试 arm64  <cc6cf938942c3fc09ce8738c466fb42a> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/发型测试
        0x102324000 -         0x10232ffff libobjc-trampolines.dylib arm64e  <35a44678195b39c2bdd7072893564b45> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x1023d4000 -         0x1023ebfff FBLPromises arm64  <1f7247bc92323aaea8fd60a58f93d904> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x102408000 -         0x10241bfff Reachability arm64  <d351f9e9160c3747a3126f141f76340e> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x102448000 -         0x102463fff Promises arm64  <0e3b1d6ea0cd397baa1c76bac362e169> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/Promises.framework/Promises
        0x102488000 -         0x102493fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x1024a8000 -         0x1024b7fff TTSDKStrategyLite arm64  <3b1598d2ae913d51b8bf5da7553ed0fd> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x1024c8000 -         0x1024d3fff TTSDKTTFFmpegLiveLite arm64  <85ee59668139318cabddcc2173ff607b> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x1024fc000 -         0x102523fff SnapKit arm64  <6bb9e949f3153d1a93a0cd71a8f774ef> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x102630000 -         0x10269bfff BSImagePicker arm64  <03aa03dbd3443f27bb2407777d2b1e91> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x102754000 -         0x1027d3fff SDWebImage arm64  <94f7389f01403ae0b2e5326ecd9bbbd2> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x10285c000 -         0x102883fff SwiftyJSON arm64  <09ef1d21ba2b31f9a00fa77e9d0b6a95> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x102924000 -         0x10294ffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1029a4000 -         0x102b3bfff Alamofire arm64  <fd14afff2a85358b83de1a972458336f> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x102d1c000 -         0x102d67fff Starscream arm64  <13b76bc86c9e3562a1186774ab9f8278> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x102ddc000 -         0x102e07fff TTSDKCore arm64  <c31c78044ec33e10923b655cd255f451> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x102e70000 -         0x102ed3fff TTSDKTools arm64  <0617c098c09d36d7b882a5df8bfb447c> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x103020000 -         0x103217fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x1033c8000 -         0x103433fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x1035b0000 -         0x10364bfff TTSDKLiveBase arm64  <a20645993829306cb3eb6cf7f2c51b4e> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x1039cc000 -         0x1039cffff iCloudDriveFileProviderOverride arm64e  <5de9ce32cd703abca0b951643ad20971> /System/Library/Frameworks/FileProvider.framework/OverrideBundles/iCloudDriveFileProviderOverride.bundle/iCloudDriveFileProviderOverride
        0x103a04000 -         0x103b33fff TTSDKLivePlayerLite arm64  <0225f99abbf43f989c8510ccab8311ce> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x103eec000 -         0x103ef3fff FileProviderOverride arm64e  <664c9c68fd2032c39751b24182c710a7> /System/Library/Frameworks/FileProvider.framework/OverrideBundles/FileProviderOverride.bundle/FileProviderOverride
        0x103fb8000 -         0x10416bfff TTSDKPlayerCoreLiveLite arm64  <fe27e5242e4a3b6987aa61d905a07bb5> /private/var/containers/Bundle/Application/EBFA1972-F736-4756-B7DF-DA65DAA7ADA4/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x192cc8000 -         0x192d18d5f libobjc.A.dylib arm64e  <1608892e67db3f949fc291492b86c95f> /usr/lib/libobjc.A.dylib
        0x1945cd000 -         0x1952dafff Foundation arm64e  <6d0212cc3b9e32c9be2072989ce3acb8> /System/Library/Frameworks/Foundation.framework/Foundation
        0x19598a000 -         0x195eccfff CoreFoundation arm64e  <1532d3d89b3b3f2fb35f55a20ddf411b> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x196e5b000 -         0x19721dfff CFNetwork arm64e  <999c659afc7d351fa477e97bbf2d8081> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x19743b000 -         0x1977e0fff QuartzCore arm64e  <d8e8e86d85ac3c90b2e1940235ecaa18> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x198170000 -         0x19a043fff UIKitCore arm64e  <575e5140fa6a37c2b00ba4eacedfda53> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x19d724000 -         0x19d7a3ff3 libsystem_c.dylib arm64e  <0150f750db0a3f54b23ad21c55af8824> /usr/lib/system/libsystem_c.dylib
        0x1a2e5c000 -         0x1a3260fff CoreMotion arm64e  <ad76b51c2c19371888c6e6a9d73d5868> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1a5f8e000 -         0x1a601bffb libc++.1.dylib arm64e  <491f481bd014381c904eaed69c09f984> /usr/lib/libc++.1.dylib
        0x1bb397000 -         0x1bb41a99f dyld arm64e  <3060d36a16ce3c3a92583881459f5714> /usr/lib/dyld
        0x1e19bb000 -         0x1e19c3fff GraphicsServices arm64e  <8425ea11000e3e5e8abcbddf3ff3fa32> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e5d9d000 -         0x1e5dd6ff3 libsystem_kernel.dylib arm64e  <b9618c71c0cb31b6825f92a4737c890e> /usr/lib/system/libsystem_kernel.dylib
        0x21dd16000 -         0x21dd30fff libc++abi.dylib arm64e  <5e1a37143fad3ad7a23d61c4be170233> /usr/lib/libc++abi.dylib
        0x21df13000 -         0x21df1fff3 libsystem_pthread.dylib arm64e  <3ca98e388eee3c269862c5f66aad93c0> /usr/lib/system/libsystem_pthread.dylib

EOF
