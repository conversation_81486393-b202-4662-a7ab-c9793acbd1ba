Incident Identifier: 9804EDC9-FB9D-45C0-ADC5-2E4886E040A9
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone14,5
Process:             发型测试 [77254]
Path:                /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             2.1.1 (2)
AppStoreTools:       16E137
AppVariant:          1:iPhone14,5:15
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [34260]

Date/Time:           2025-04-12 22:30:43.6781 +0800
Launch Time:         2025-04-12 22:28:21.2772 +0800
OS Version:          iPhone OS 17.6.1 (21G93)
Release Type:        User
Baseband Version:    3.50.04
Report Version:      104

Exception Type:  EXC_CRASH (SIGABRT)
Exception Codes: 0x0000000000000000, 0x0000000000000000
Termination Reason: SIGNAL 6 Abort trap: 6
Terminating Process: 发型测试 [77254]

Triggered by Thread:  0

Last Exception Backtrace:
0   CoreFoundation                	0x18849cf20 __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x1803432b8 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreFoundation                	0x18859b6dc +[NSException raise:format:] + 112 (NSException.m:0)
3   QuartzCore                    	0x189a939e0 CA::Layer::set_position(CA::Vec2<double> const&, bool) + 168 (CALayer.mm:4626)
4   QuartzCore                    	0x189a93910 -[CALayer setPosition:] + 52 (CALayer.mm:4663)
5   UIKitCore                     	0x18a6e20ec -[UIView _backing_setPosition:] + 176 (_UIViewBacking.m:237)
6   UIKitCore                     	0x18a6e1a48 -[UIView setCenter:] + 212 (UIView.m:9417)
7   发型测试                          	0x1022ee638 0x1021fc000 + 992824
8   发型测试                          	0x1022ee6f8 0x1021fc000 + 993016
9   UIKitCore                     	0x18aac6244 -[UIGestureRecognizerTarget _sendActionWithGestureRecognizer:] + 128 (UIGestureRecognizer.m:157)
10  UIKitCore                     	0x18aac60b4 _UIGestureRecognizerSendTargetActions + 92 (UIGestureRecognizer.m:1640)
11  UIKitCore                     	0x18aac5e74 _UIGestureRecognizerSendActions + 268 (UIGestureRecognizer.m:1679)
12  UIKitCore                     	0x18a7578c4 -[UIGestureRecognizer _updateGestureForActiveEvents] + 544 (UIGestureRecognizer.m:0)
13  UIKitCore                     	0x18a724fe8 _UIGestureEnvironmentUpdate + 2476 (UIGestureEnvironment.m:198)
14  UIKitCore                     	0x18a820b04 -[UIGestureEnvironment _deliverEvent:toGestureRecognizers:usingBlock:] + 300 (UIGestureEnvironment.m:1416)
15  UIKitCore                     	0x18a9d774c -[UIGestureEnvironment _updateForEvent:window:] + 188 (UIGestureEnvironment.m:1383)
16  UIKitCore                     	0x18a9d6a10 -[UIWindow sendEvent:] + 3188 (UIWindow.m:3649)
17  UIKitCore                     	0x18a857924 -[UIApplication sendEvent:] + 564 (UIApplication.m:12721)
18  UIKitCore                     	0x18a859118 __dispatchPreprocessedEventFromEventQueue + 5552 (UIEventDispatcher.m:2598)
19  UIKitCore                     	0x18a861e10 __processEventQueue + 5624 (UIEventDispatcher.m:2956)
20  UIKitCore                     	0x18a754740 updateCycleEntry + 160 (UIEventDispatcher.m:126)
21  UIKitCore                     	0x18a752660 _UIUpdateSequenceRun + 84 (_UIUpdateSequence.mm:119)
22  UIKitCore                     	0x18a7522a4 schedulerStepScheduledMainSection + 172 (_UIUpdateScheduler.m:1058)
23  UIKitCore                     	0x18a753148 runloopSourceCallback + 92 (_UIUpdateScheduler.m:1221)
24  CoreFoundation                	0x18846f834 __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 28 (CFRunLoop.c:1957)
25  CoreFoundation                	0x18846f7c8 __CFRunLoopDoSource0 + 176 (CFRunLoop.c:2001)
26  CoreFoundation                	0x18846d298 __CFRunLoopDoSources0 + 244 (CFRunLoop.c:2038)
27  CoreFoundation                	0x18846c484 __CFRunLoopRun + 828 (CFRunLoop.c:2955)
28  CoreFoundation                	0x18846bcd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
29  GraphicsServices              	0x1cceb91a8 GSEventRunModal + 164 (GSEvent.c:2196)
30  UIKitCore                     	0x18aaa5ae8 -[UIApplication _run] + 888 (UIApplication.m:3713)
31  UIKitCore                     	0x18ab59d98 UIApplicationMain + 340 (UIApplication.m:5303)
32  UIKitCore                     	0x18acd3504 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
33  发型测试                          	0x102323650 0x1021fc000 + 1209936
34  发型测试                          	0x1023235c8 0x1021fc000 + 1209800
35  发型测试                          	0x102323714 0x1021fc000 + 1210132
36  dyld                          	0x1abc43154 start + 2356 (dyldMain.cpp:1298)

Kernel Triage:
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter


Thread 0 name:
Thread 0 Crashed:
0   libsystem_kernel.dylib        	0x00000001d10fc2ec __pthread_kill + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001e4eefc0c pthread_kill + 268 (pthread.c:1721)
2   libsystem_c.dylib             	0x00000001903fbba0 abort + 180 (abort.c:118)
3   libc++abi.dylib               	0x00000001e4e0cca4 abort_message + 132 (abort_message.cpp:78)
4   libc++abi.dylib               	0x00000001e4dfce5c demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
5   libobjc.A.dylib               	0x000000018035f14c _objc_terminate() + 144 (objc-exception.mm:496)
6   发型测试                          	0x0000000102ab9ae8 0x1021fc000 + 9165544
7   libc++abi.dylib               	0x00000001e4e0c068 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
8   libc++abi.dylib               	0x00000001e4e0f60c __cxa_rethrow + 204 (cxa_exception.cpp:637)
9   libobjc.A.dylib               	0x000000018035b0b4 objc_exception_rethrow + 44 (objc-exception.mm:399)
10  CoreFoundation                	0x000000018846bd88 CFRunLoopRunSpecific + 784 (CFRunLoop.c:3436)
11  GraphicsServices              	0x00000001cceb91a8 GSEventRunModal + 164 (GSEvent.c:2196)
12  UIKitCore                     	0x000000018aaa5ae8 -[UIApplication _run] + 888 (UIApplication.m:3713)
13  UIKitCore                     	0x000000018ab59d98 UIApplicationMain + 340 (UIApplication.m:5303)
14  UIKitCore                     	0x000000018acd3504 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
15  发型测试                          	0x0000000102323650 0x1021fc000 + 1209936
16  发型测试                          	0x00000001023235c8 0x1021fc000 + 1209800
17  发型测试                          	0x0000000102323714 0x1021fc000 + 1210132
18  dyld                          	0x00000001abc43154 start + 2356 (dyldMain.cpp:1298)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001d10f16c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d10f4ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d10f4de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d10f4c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018846cf5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000018846c600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000018846bcd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   Foundation                    	0x000000018738cb5c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x000000018738c9ac -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x000000018aab981c -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1207)
10  Foundation                    	0x00000001873a3428 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x00000001e4eee06c _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x00000001e4ee90d8 thread_start + 8 (:-1)

Thread 2:
0   libsystem_kernel.dylib        	0x00000001d10f72ac __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019039c5f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001903f972c sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000102ae506c 0x1021fc000 + 9343084
4   libsystem_pthread.dylib       	0x00000001e4eee06c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000001e4ee90d8 thread_start + 8 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001d10f16c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d10f4ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d10f2c38 thread_suspend + 112 (thread_actUser.c:1036)
3   发型测试                          	0x0000000102abb248 0x1021fc000 + 9171528
4   libsystem_pthread.dylib       	0x00000001e4eee06c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000001e4ee90d8 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001d10f16c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d10f4f60 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001d10f4de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d10f4c20 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000102abb278 0x1021fc000 + 9171576
5   libsystem_pthread.dylib       	0x00000001e4eee06c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001e4ee90d8 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001d10f72ac __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019039c5f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019039c508 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001029a1e70 0x1021fc000 + 8019568
4   发型测试                          	0x0000000102954bc0 0x1021fc000 + 7703488
5   libsystem_pthread.dylib       	0x00000001e4eee06c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001e4ee90d8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001d10f72ac __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019039c5f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019039c508 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001029a1e70 0x1021fc000 + 8019568
4   发型测试                          	0x0000000102954bc0 0x1021fc000 + 7703488
5   libsystem_pthread.dylib       	0x00000001e4eee06c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001e4ee90d8 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001d10f16c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d10f4ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d10f4de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d10f4c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018846cf5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000018846c600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000018846bcd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CoreFoundation                	0x00000001884d9f04 CFRunLoopRun + 64 (CFRunLoop.c:3446)
8   CoreMotion                    	0x0000000195180e3c CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000001e4eee06c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000001e4ee90d8 thread_start + 8 (:-1)

Thread 8:
0   libsystem_pthread.dylib       	0x00000001e4ee90c4 start_wqthread + 0 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001d10f16c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d10f4ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d10f4de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d10f4c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018846cf5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000018846c600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000018846bcd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CFNetwork                     	0x000000018964cc7c +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x00000001873a3428 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x00000001e4eee06c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000001e4ee90d8 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001d10f708c __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001e4eeb6e4 _pthread_cond_wait + 1228 (pthread_cond.c:862)
2   JavaScriptCore                	0x000000019fb382a4 scavenger_thread_main + 1512 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x00000001e4eee06c _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x00000001e4ee90d8 thread_start + 8 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x00000001e4ee90c4 start_wqthread + 0 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x00000001e4ee90c4 start_wqthread + 0 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x00000001e4ee90c4 start_wqthread + 0 (:-1)


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000000
    x4: 0x00000001e4e112c3   x5: 0x000000016dc03070   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x3a5fbfb778af9227   x9: 0x3a5fbfb69074ace7  x10: 0x0000000000000200  x11: 0x000000016dc02ba0
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000148  x17: 0x00000001e8db3ec0  x18: 0x0000000000000000  x19: 0x0000000000000006
   x20: 0x0000000000000103  x21: 0x00000001e8db3fa0  x22: 0x00000001efd3fc70  x23: 0x0000000000000001
   x24: 0x0000000300258420  x25: 0x0000000000000001  x26: 0x0000000303b507b0  x27: 0x0000000000000000
   x28: 0x0000000000000001   fp: 0x000000016dc02fe0   lr: 0x00000001e4eefc0c
    sp: 0x000000016dc02fc0   pc: 0x00000001d10fc2ec cpsr: 0x40001000
   esr: 0x56000080  Address size fault


Binary Images:
        0x1021fc000 -         0x102d93fff 发型测试 arm64  <fe4df51da8db38a7abd2548dcf0a80e7> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/发型测试
        0x104254000 -         0x10425ffff libobjc-trampolines.dylib arm64e  <be553713db163c12aaa48fd6211e48ce> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x10433c000 -         0x104353fff FBLPromises arm64  <1f7247bc92323aaea8fd60a58f93d904> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x104370000 -         0x104387fff Masonry arm64  <a67d025f16a73357ab7e85f3db1cd89c> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x1043a4000 -         0x1043b7fff Reachability arm64  <d351f9e9160c3747a3126f141f76340e> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x1043e4000 -         0x1043fffff Promises arm64  <0e3b1d6ea0cd397baa1c76bac362e169> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/Promises.framework/Promises
        0x104424000 -         0x10443bfff SVProgressHUD arm64  <906f6c62b2f83e5fba9ca48bf070a622> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x104458000 -         0x104463fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x104478000 -         0x104487fff TTSDKStrategyLite arm64  <3b1598d2ae913d51b8bf5da7553ed0fd> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x104498000 -         0x1044a3fff TTSDKTTFFmpegLiveLite arm64  <85ee59668139318cabddcc2173ff607b> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x1044b4000 -         0x1044dbfff SwiftyJSON arm64  <09ef1d21ba2b31f9a00fa77e9d0b6a95> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x1049c4000 -         0x104a2ffff BSImagePicker arm64  <03aa03dbd3443f27bb2407777d2b1e91> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x104ae8000 -         0x104b67fff SDWebImage arm64  <94f7389f01403ae0b2e5326ecd9bbbd2> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x104bf0000 -         0x104c17fff SnapKit arm64  <6bb9e949f3153d1a93a0cd71a8f774ef> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x104c68000 -         0x104cb3fff Starscream arm64  <13b76bc86c9e3562a1186774ab9f8278> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x104d38000 -         0x104ecffff Alamofire arm64  <fd14afff2a85358b83de1a972458336f> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x105144000 -         0x10516ffff TTSDKCore arm64  <c31c78044ec33e10923b655cd255f451> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1051d8000 -         0x10523bfff TTSDKTools arm64  <0617c098c09d36d7b882a5df8bfb447c> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x105270000 -         0x10529bfff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1052f4000 -         0x1054ebfff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x105780000 -         0x1057ebfff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x105884000 -         0x10591ffff TTSDKLiveBase arm64  <a20645993829306cb3eb6cf7f2c51b4e> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x105cd8000 -         0x105e07fff TTSDKLivePlayerLite arm64  <0225f99abbf43f989c8510ccab8311ce> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x10628c000 -         0x10643ffff TTSDKPlayerCoreLiveLite arm64  <fe27e5242e4a3b6987aa61d905a07bb5> /private/var/containers/Bundle/Application/904FBB8C-DBC4-46F1-AEE0-120144E81FE1/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x18032c000 -         0x18037ccf3 libobjc.A.dylib arm64e  <afdf5874bc3b388e864cdc9f4cdbf4f0> /usr/lib/libobjc.A.dylib
        0x1872c5000 -         0x187e3afff Foundation arm64e  <d27a6ec5943c3b0e8d158840fd2914f0> /System/Library/Frameworks/Foundation.framework/Foundation
        0x188419000 -         0x188946fff CoreFoundation arm64e  <76a3b1983c09323e83590d4978e156f5> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x18954f000 -         0x18992bfff CFNetwork arm64e  <371394cd79f23216acb0a159c09c668d> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x189a8b000 -         0x189e19fff QuartzCore arm64e  <aedc1a5617313315a87ec6610024a405> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x18a69b000 -         0x18c1bcfff UIKitCore arm64e  <9da0d27355063712b73de0149d74c13c> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x190386000 -         0x190403ff3 libsystem_c.dylib arm64e  <7135c2c8ba5836368b46a9e6226ead45> /usr/lib/system/libsystem_c.dylib
        0x195171000 -         0x19563ffff CoreMotion arm64e  <5d6e7429116638b3807bdfad246f9132> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x19e5d1000 -         0x19fd0df3f JavaScriptCore arm64e  <2800076a7d5a38dcafa723fa080301b6> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1abc06000 -         0x1abc93937 dyld arm64e  <52039c944da13638bd52020a0b5fa399> /usr/lib/dyld
        0x1cceb8000 -         0x1ccec0fff GraphicsServices arm64e  <3ebbd576e7d83f69bcb5b9810ddcc90e> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1d10f0000 -         0x1d1129fef libsystem_kernel.dylib arm64e  <21ee5290d1193c31b948431865a67738> /usr/lib/system/libsystem_kernel.dylib
        0x1e4df8000 -         0x1e4e13ffb libc++abi.dylib arm64e  <b613e600b39c3bbf8e098a1466610355> /usr/lib/libc++abi.dylib
        0x1e4ee8000 -         0x1e4ef4ff3 libsystem_pthread.dylib arm64e  <e4a9d6dbf93b3c88bdd185671ec22e2b> /usr/lib/system/libsystem_pthread.dylib

EOF
