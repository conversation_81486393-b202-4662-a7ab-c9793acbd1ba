Incident Identifier: 7D6D4D93-8C99-464F-A75B-2C914D21B050
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone15,5
Process:             发型测试 [15164]
Path:                /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             2.1.0 (2)
AppStoreTools:       16C7015
AppVariant:          1:iPhone15,5:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [4043]

Date/Time:           2025-03-27 00:45:05.0742 +0800
Launch Time:         2025-03-27 00:38:31.7193 +0800
OS Version:          iPhone OS 18.3.1 (22D72)
Release Type:        User
Baseband Version:    2.40.05
Report Version:      104

Exception Type:  EXC_CRASH (SIGABRT)
Exception Codes: 0x0000000000000000, 0x0000000000000000
Termination Reason: SIGNAL 6 Abort trap: 6
Terminating Process: 发型测试 [15164]

Triggered by Thread:  0

Last Exception Backtrace:
0   CoreFoundation                	0x18f23e5fc __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x18c7b9244 objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreFoundation                	0x18f38b6e0 +[NSException raise:format:] + 128 (NSException.m:0)
3   QuartzCore                    	0x190d02b6c CA::Layer::set_position(CA::Vec2<double> const&, bool) + 160 (CALayer.mm:4726)
4   QuartzCore                    	0x190d02aa4 -[CALayer setPosition:] + 52 (CALayer.mm:4763)
5   UIKitCore                     	0x191aa8aa8 -[UIView _backing_setPosition:] + 176 (_UIViewBacking.m:235)
6   UIKitCore                     	0x191aa8790 -[UIView setCenter:] + 216 (UIView.m:9654)
7   UIKitCore                     	0x191a73ed4 -[UIImageView setCenter:] + 52 (UIImageView.m:1785)
8   发型测试                          	0x100c1e638 0x100b2c000 + 992824
9   发型测试                          	0x100c1e6f8 0x100b2c000 + 993016
10  UIKitCore                     	0x191e47650 -[UIGestureRecognizerTarget _sendActionWithGestureRecognizer:] + 128 (UIGestureRecognizer.m:179)
11  UIKitCore                     	0x191e474c0 _UIGestureRecognizerSendTargetActions + 92 (UIGestureRecognizer.m:1751)
12  UIKitCore                     	0x191e47280 _UIGestureRecognizerSendActions + 284 (UIGestureRecognizer.m:1790)
13  UIKitCore                     	0x191af85f8 -[UIGestureRecognizer _updateGestureForActiveEvents] + 572 (UIGestureRecognizer.m:0)
14  UIKitCore                     	0x191ab5a78 _UIGestureEnvironmentUpdate + 2488 (UIGestureEnvironment.m:192)
15  UIKitCore                     	0x191c45150 -[UIGestureEnvironment _deliverEvent:toGestureRecognizers:usingBlock:] + 336 (UIGestureEnvironment.m:843)
16  UIKitCore                     	0x191d0a67c -[UIGestureEnvironment _updateForEvent:window:] + 188 (UIGestureEnvironment.m:810)
17  UIKitCore                     	0x191d09138 -[UIWindow sendEvent:] + 2948 (UIWindow.m:3633)
18  UIKitCore                     	0x191d0808c -[UIApplication sendEvent:] + 376 (UIApplication.m:12982)
19  UIKitCore                     	0x191cf74d0 __dispatchPreprocessedEventFromEventQueue + 1048 (UIEventDispatcher.m:2691)
20  UIKitCore                     	0x191bf9f8c __processEventQueue + 5696 (UIEventDispatcher.m:3049)
21  UIKitCore                     	0x191ad28a4 updateCycleEntry + 160 (UIEventDispatcher.m:133)
22  UIKitCore                     	0x191ad0710 _UIUpdateSequenceRun + 84 (_UIUpdateSequence.mm:136)
23  UIKitCore                     	0x191ad3040 schedulerStepScheduledMainSection + 172 (_UIUpdateScheduler.m:1171)
24  UIKitCore                     	0x191ad0c5c runloopSourceCallback + 92 (_UIUpdateScheduler.m:1334)
25  CoreFoundation                	0x18f284f4c __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 28 (CFRunLoop.c:1970)
26  CoreFoundation                	0x18f284ee0 __CFRunLoopDoSource0 + 176 (CFRunLoop.c:2014)
27  CoreFoundation                	0x18f287b40 __CFRunLoopDoSources0 + 244 (CFRunLoop.c:2051)
28  CoreFoundation                	0x18f286d3c __CFRunLoopRun + 840 (CFRunLoop.c:2969)
29  CoreFoundation                	0x18f2d9284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
30  GraphicsServices              	0x1dc5454c0 GSEventRunModal + 164 (GSEvent.c:2196)
31  UIKitCore                     	0x191e22674 -[UIApplication _run] + 816 (UIApplication.m:3846)
32  UIKitCore                     	0x191a48e88 UIApplicationMain + 340 (UIApplication.m:5503)
33  UIKitCore                     	0x19218515c UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
34  发型测试                          	0x100c53650 0x100b2c000 + 1209936
35  发型测试                          	0x100c535c8 0x100b2c000 + 1209800
36  发型测试                          	0x100c53714 0x100b2c000 + 1210132
37  dyld                          	0x1b5531de8 start + 2724 (dyldMain.cpp:1338)

Thread 0 name:
Thread 0 Crashed:
0   libsystem_kernel.dylib        	0x00000001e09732d4 __pthread_kill + 8 (:-1)
1   libsystem_pthread.dylib       	0x0000000219fd159c pthread_kill + 268 (pthread.c:1721)
2   libsystem_c.dylib             	0x000000019708cb08 abort + 128 (abort.c:122)
3   libc++abi.dylib               	0x0000000219ef75b8 abort_message + 132 (abort_message.cpp:78)
4   libc++abi.dylib               	0x0000000219ee5bac demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
5   libobjc.A.dylib               	0x000000018c7bb2c4 _objc_terminate() + 156 (objc-exception.mm:496)
6   发型测试                          	0x00000001013e9ae8 0x100b2c000 + 9165544
7   libc++abi.dylib               	0x0000000219ef687c std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
8   libc++abi.dylib               	0x0000000219efa0ac __cxa_rethrow + 204 (cxa_exception.cpp:648)
9   libobjc.A.dylib               	0x000000018c795730 objc_exception_rethrow + 44 (objc-exception.mm:399)
10  CoreFoundation                	0x000000018f2d9388 CFRunLoopRunSpecific + 848 (CFRunLoop.c:3450)
11  GraphicsServices              	0x00000001dc5454c0 GSEventRunModal + 164 (GSEvent.c:2196)
12  UIKitCore                     	0x0000000191e22674 -[UIApplication _run] + 816 (UIApplication.m:3846)
13  UIKitCore                     	0x0000000191a48e88 UIApplicationMain + 340 (UIApplication.m:5503)
14  UIKitCore                     	0x000000019218515c UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
15  发型测试                          	0x0000000100c53650 0x100b2c000 + 1209936
16  发型测试                          	0x0000000100c535c8 0x100b2c000 + 1209800
17  发型测试                          	0x0000000100c53714 0x100b2c000 + 1210132
18  dyld                          	0x00000001b5531de8 start + 2724 (dyldMain.cpp:1338)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001e0968788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e096be98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e096bdb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e096bbfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018f287804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000018f286eb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000018f2d9284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   Foundation                    	0x000000018de3f0e8 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x000000018df9bbb0 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x0000000191eb5a78 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1351)
10  Foundation                    	0x000000018df2af30 __NSThread__start__ + 724 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 2:
0   libsystem_kernel.dylib        	0x00000001e096e2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019702d5cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019702d444 sleep + 52 (sleep.c:62)
3   发型测试                          	0x000000010141506c 0x100b2c000 + 9343084
4   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001e0968788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e096be98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e0969cfc thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x00000001013eb248 0x100b2c000 + 9171528
4   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001e0968788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e096bf30 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e096bdb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e096bbfc mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001013eb278 0x100b2c000 + 9171576
5   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001e096e2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019702d5cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019702d4e4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001012d1e70 0x100b2c000 + 8019568
4   发型测试                          	0x0000000101284bc0 0x100b2c000 + 7703488
5   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e096e2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019702d5cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019702d4e4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001012d1e70 0x100b2c000 + 8019568
4   发型测试                          	0x0000000101284bc0 0x100b2c000 + 7703488
5   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e0968788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e096be98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e096bdb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e096bbfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018f287804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000018f286eb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000018f2d9284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CFNetwork                     	0x00000001907f7c4c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x000000018df2af30 __NSThread__start__ + 724 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001e0968788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e096be98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e096bdb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e096bbfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018f287804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000018f286eb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000018f2d9284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CoreFoundation                	0x000000018f2ec824 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x000000019c96d950 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 9:
0   libsystem_pthread.dylib       	0x0000000219fca46c start_wqthread + 0 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x0000000219fca46c start_wqthread + 0 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x0000000219fca46c start_wqthread + 0 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x0000000219fca46c start_wqthread + 0 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x0000000219fca46c start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x0000000219fca46c start_wqthread + 0 (:-1)

Thread 15:
0   libsystem_pthread.dylib       	0x0000000219fca46c start_wqthread + 0 (:-1)

Thread 16 name:
Thread 16:
0   libsystem_kernel.dylib        	0x00000001e0968788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e096be98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e096bdb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e096bbfc mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001011ef120 0x100b2c000 + 7090464
5   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 17 name:
Thread 17:
0   libsystem_kernel.dylib        	0x00000001e096e2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019702d5cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019702d4e4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001011ea718 0x100b2c000 + 7071512
4   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)

Thread 18:
0   libsystem_kernel.dylib        	0x00000001e096e090 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x0000000219fccf98 _pthread_cond_wait + 1204 (pthread_cond.c:862)
2   libc++.1.dylib                	0x000000019fad3584 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 28 (condition_variable.cpp:30)
3   发型测试                          	0x00000001010c790c 0x100b2c000 + 5880076
4   发型测试                          	0x00000001010c9364 0x100b2c000 + 5886820
5   libsystem_pthread.dylib       	0x0000000219fca7d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000219fca480 thread_start + 8 (:-1)


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000000
    x4: 0x0000000219efbf3b   x5: 0x000000016f2d3070   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0xe03fa9f401822c83   x9: 0xe03fa9f5f6097543  x10: 0x0000000000000051  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x000000018f6be9ac  x14: 0x00000000001ff800  x15: 0x00000000000007fb
   x16: 0x0000000000000148  x17: 0x00000001f78b59c0  x18: 0x0000000000000000  x19: 0x0000000000000006
   x20: 0x0000000000000103  x21: 0x00000001f78b5aa0  x22: 0x00000003035ec5b0  x23: 0x00000003035ec5c0
   x24: 0x0000000000000001  x25: 0x0000000000000001  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016f2d2fe0   lr: 0x0000000219fd159c
    sp: 0x000000016f2d2fc0   pc: 0x00000001e09732d4 cpsr: 0x40001000
   esr: 0x56000080  Address size fault


Binary Images:
        0x100b2c000 -         0x1016c3fff 发型测试 arm64  <33ffb534e57530119ba480d642f586bb> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/发型测试
        0x102b20000 -         0x102b2bfff libobjc-trampolines.dylib arm64e  <4aba9420e4d03c989d62c653b259eab4> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x102c28000 -         0x102c3ffff FBLPromises arm64  <1f7247bc92323aaea8fd60a58f93d904> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x102c5c000 -         0x102c73fff Masonry arm64  <a67d025f16a73357ab7e85f3db1cd89c> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x102c90000 -         0x102ca3fff Reachability arm64  <d351f9e9160c3747a3126f141f76340e> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x102cd0000 -         0x102cebfff Promises arm64  <0e3b1d6ea0cd397baa1c76bac362e169> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/Promises.framework/Promises
        0x102d10000 -         0x102d27fff SVProgressHUD arm64  <906f6c62b2f83e5fba9ca48bf070a622> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x102d44000 -         0x102d4ffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x102df8000 -         0x102e07fff TTSDKStrategyLite arm64  <3b1598d2ae913d51b8bf5da7553ed0fd> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x102e18000 -         0x102e23fff TTSDKTTFFmpegLiveLite arm64  <85ee59668139318cabddcc2173ff607b> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x102ea0000 -         0x102f0bfff BSImagePicker arm64  <03aa03dbd3443f27bb2407777d2b1e91> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x103038000 -         0x10305ffff SnapKit arm64  <6bb9e949f3153d1a93a0cd71a8f774ef> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x1030cc000 -         0x10314bfff SDWebImage arm64  <94f7389f01403ae0b2e5326ecd9bbbd2> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x103214000 -         0x1033abfff Alamofire arm64  <fd14afff2a85358b83de1a972458336f> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x10358c000 -         0x1035b3fff SwiftyJSON arm64  <09ef1d21ba2b31f9a00fa77e9d0b6a95> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x10364c000 -         0x103697fff Starscream arm64  <13b76bc86c9e3562a1186774ab9f8278> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x10370c000 -         0x103737fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1037a0000 -         0x1037cbfff TTSDKCore arm64  <c31c78044ec33e10923b655cd255f451> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x103834000 -         0x103897fff TTSDKTools arm64  <0617c098c09d36d7b882a5df8bfb447c> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x103950000 -         0x103b47fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x103ddc000 -         0x103e47fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x103ee0000 -         0x103f7bfff TTSDKLiveBase arm64  <a20645993829306cb3eb6cf7f2c51b4e> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x104334000 -         0x104463fff TTSDKLivePlayerLite arm64  <0225f99abbf43f989c8510ccab8311ce> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x1048e8000 -         0x104a9bfff TTSDKPlayerCoreLiveLite arm64  <fe27e5242e4a3b6987aa61d905a07bb5> /private/var/containers/Bundle/Application/98871BFA-CBDB-4932-92B9-1933A56685F4/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x18c788000 -         0x18c7d8ccf libobjc.A.dylib arm64e  <a6a17b3c335130adaf2815a71b78f050> /usr/lib/libobjc.A.dylib
        0x18de15000 -         0x18eb45fff Foundation arm64e  <e2f95328659e3c0197f752b5b3bb7aa5> /System/Library/Frameworks/Foundation.framework/Foundation
        0x18f211000 -         0x18f754fff CoreFoundation arm64e  <0013a8b125243534b5ba681aaf18c798> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x190706000 -         0x190acafff CFNetwork arm64e  <e610c6a8da363e07910f2d4a62320985> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x190ce9000 -         0x191095fff QuartzCore arm64e  <8a08cc2400173108bea429111b40063c> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x191a34000 -         0x19394cfff UIKitCore arm64e  <8cc54497f7ec3903ae5aa274047c0cf1> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x197015000 -         0x197094ffb libsystem_c.dylib arm64e  <400d888f854833fc802ff29678681197> /usr/lib/system/libsystem_c.dylib
        0x19c95e000 -         0x19cd66fff CoreMotion arm64e  <6845c463baf9365fa47cf9617091a79a> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x19fab2000 -         0x19fb3fffb libc++.1.dylib arm64e  <09bdee26e6c335458cc96f215deafb43> /usr/lib/libc++.1.dylib
        0x1b5502000 -         0x1b5585137 dyld arm64e  <a770ff8c8fb93e0385fe7f26db36812b> /usr/lib/dyld
        0x1dc544000 -         0x1dc54cfff GraphicsServices arm64e  <3eca7962867b3029adc8bbe100f85ba5> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e0967000 -         0x1e09a0fe3 libsystem_kernel.dylib arm64e  <881fe934759c3089b98660344cb843e3> /usr/lib/system/libsystem_kernel.dylib
        0x219ee4000 -         0x219efefff libc++abi.dylib arm64e  <93fe31d773fb338eb696211de65fd7ed> /usr/lib/libc++abi.dylib
        0x219fc9000 -         0x219fd5ff3 libsystem_pthread.dylib arm64e  <6f6e49251fb43a0b99d26bd8b7b1a148> /usr/lib/system/libsystem_pthread.dylib

EOF
