Incident Identifier: 3E7163F3-E5A0-4527-8A3A-712078B409F9
Hardware Model:      iPhone11,8
Process:             发型测试 [320]
Path:                /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             2.0.0 (2)
AppStoreTools:       16C5031b
AppVariant:          1:iPhone11,8:15
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [496]

Date/Time:           2025-02-25 14:22:03.9110 +0800
Launch Time:         2025-02-25 14:06:55.3539 +0800
OS Version:          iPhone OS 17.3.1 (21D61)
Release Type:        User
Baseband Version:    6.00.00
Report Version:      104

Exception Type:  EXC_CRASH (SIGABRT)
Exception Codes: 0x0000000000000000, 0x0000000000000000
Triggered by Thread:  0

Last Exception Backtrace:
0   CoreFoundation                	0x190b4e678 __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x188e03c80 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreFoundation                	0x190bd3494 +[NSException raise:format:] + 112 (NSException.m:0)
3   QuartzCore                    	0x19209c9f0 CA::Layer::set_position(CA::Vec2<double> const&, bool) + 168 (CALayer.mm:4625)
4   QuartzCore                    	0x19209c920 -[CALayer setPosition:] + 52 (CALayer.mm:4662)
5   UIKitCore                     	0x192cefd94 -[UIView _backing_setPosition:] + 176 (_UIViewBacking.m:237)
6   UIKitCore                     	0x192cc467c -[UIView setCenter:] + 212 (UIView.m:9416)
7   发型测试                          	0x102e2bf40 0x102d68000 + 802624
8   发型测试                          	0x102e2c000 0x102d68000 + 802816
9   UIKitCore                     	0x192d9c814 -[UIGestureRecognizerTarget _sendActionWithGestureRecognizer:] + 128 (UIGestureRecognizer.m:157)
10  UIKitCore                     	0x192d9c684 _UIGestureRecognizerSendTargetActions + 92 (UIGestureRecognizer.m:1655)
11  UIKitCore                     	0x192d9b800 _UIGestureRecognizerSendActions + 268 (UIGestureRecognizer.m:1694)
12  UIKitCore                     	0x192d9959c -[UIGestureRecognizer _updateGestureForActiveEvents] + 536 (UIGestureRecognizer.m:0)
13  UIKitCore                     	0x192caae9c _UIGestureEnvironmentUpdate + 2476 (UIGestureEnvironment.m:198)
14  UIKitCore                     	0x192caa4b4 -[UIGestureEnvironment _deliverEvent:toGestureRecognizers:usingBlock:] + 300 (UIGestureEnvironment.m:1378)
15  UIKitCore                     	0x192caa360 -[UIGestureEnvironment _updateForEvent:window:] + 188 (UIGestureEnvironment.m:1345)
16  UIKitCore                     	0x192e91894 -[UIWindow sendEvent:] + 3188 (UIWindow.m:3632)
17  UIKitCore                     	0x192e90b24 -[UIApplication sendEvent:] + 560 (UIApplication.m:12641)
18  UIKitCore                     	0x192e53460 __dispatchPreprocessedEventFromEventQueue + 6492 (UIEventDispatcher.m:2571)
19  UIKitCore                     	0x192e51764 __processEventQueue + 5544 (UIEventDispatcher.m:2913)
20  UIKitCore                     	0x192f24304 __eventFetcherSourceCallback + 160 (UIEventDispatcher.m:2944)
21  CoreFoundation                	0x190a990ac __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 28 (CFRunLoop.c:1957)
22  CoreFoundation                	0x190a98328 __CFRunLoopDoSource0 + 176 (CFRunLoop.c:2001)
23  CoreFoundation                	0x190a96adc __CFRunLoopDoSources0 + 244 (CFRunLoop.c:2038)
24  CoreFoundation                	0x190a95818 __CFRunLoopRun + 828 (CFRunLoop.c:2955)
25  CoreFoundation                	0x190a953f8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
26  GraphicsServices              	0x1d40234f8 GSEventRunModal + 164 (GSEvent.c:2196)
27  UIKitCore                     	0x192ebb8a0 -[UIApplication _run] + 888 (UIApplication.m:3685)
28  UIKitCore                     	0x192ebaedc UIApplicationMain + 340 (UIApplication.m:5270)
29  UIKitCore                     	0x1930e5598 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
30  发型测试                          	0x102e599ec 0x102d68000 + 989676
31  发型测试                          	0x102e59964 0x102d68000 + 989540
32  发型测试                          	0x102e59ab0 0x102d68000 + 989872
33  dyld                          	0x1b37eadcc start + 2240 (dyldMain.cpp:1269)

Kernel Triage:
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter


Thread 0 name:
Thread 0 Crashed:
0   libsystem_kernel.dylib        	0x00000001d81d5fbc __pthread_kill + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001fab63680 pthread_kill + 268 (pthread.c:1681)
2   libsystem_c.dylib             	0x0000000198b16b90 abort + 180 (abort.c:118)
3   libc++abi.dylib               	0x00000001faa8dff8 abort_message + 132 (abort_message.cpp:78)
4   libc++abi.dylib               	0x00000001faa7df90 demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
5   libobjc.A.dylib               	0x0000000188e06da4 _objc_terminate() + 144 (objc-exception.mm:496)
6   libc++abi.dylib               	0x00000001faa8d3bc std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x00000001faa90758 __cxa_rethrow + 204 (cxa_exception.cpp:637)
8   libobjc.A.dylib               	0x0000000188e14cbc objc_exception_rethrow + 44 (objc-exception.mm:399)
9   CoreFoundation                	0x0000000190a954a8 CFRunLoopRunSpecific + 784 (CFRunLoop.c:3436)
10  GraphicsServices              	0x00000001d40234f8 GSEventRunModal + 164 (GSEvent.c:2196)
11  UIKitCore                     	0x0000000192ebb8a0 -[UIApplication _run] + 888 (UIApplication.m:3685)
12  UIKitCore                     	0x0000000192ebaedc UIApplicationMain + 340 (UIApplication.m:5270)
13  UIKitCore                     	0x00000001930e5598 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
14  发型测试                          	0x0000000102e599ec 0x102d68000 + 989676
15  发型测试                          	0x0000000102e59964 0x102d68000 + 989540
16  发型测试                          	0x0000000102e59ab0 0x102d68000 + 989872
17  dyld                          	0x00000001b37eadcc start + 2240 (dyldMain.cpp:1269)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001d81cd178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d81ccf10 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d81cce28 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d81ccc68 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000190a97a9c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x0000000190a95994 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x0000000190a953f8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   Foundation                    	0x000000018fa283ec -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x000000018fa556ac -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x0000000192e1d710 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1208)
10  Foundation                    	0x000000018faabd40 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x00000001fab604d4 _pthread_start + 136 (pthread.c:904)
12  libsystem_pthread.dylib       	0x00000001fab5fa10 thread_start + 8 (:-1)

Thread 2 name:
Thread 2:
0   libsystem_kernel.dylib        	0x00000001d81cd978 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x0000000198aaaf20 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000198aaae38 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001034cee70 0x102d68000 + 7761520
4   发型测试                          	0x0000000103481bc0 0x102d68000 + 7445440
5   libsystem_pthread.dylib       	0x00000001fab604d4 _pthread_start + 136 (pthread.c:904)
6   libsystem_pthread.dylib       	0x00000001fab5fa10 thread_start + 8 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001d81cd978 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x0000000198aaaf20 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000198aaae38 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001034cee70 0x102d68000 + 7761520
4   发型测试                          	0x0000000103481bc0 0x102d68000 + 7445440
5   libsystem_pthread.dylib       	0x00000001fab604d4 _pthread_start + 136 (pthread.c:904)
6   libsystem_pthread.dylib       	0x00000001fab5fa10 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001d81cd178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d81ccf10 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d81cce28 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d81ccc68 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000190a97a9c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x0000000190a95994 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x0000000190a953f8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CFNetwork                     	0x0000000191d93050 +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1479)
8   Foundation                    	0x000000018faabd40 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x00000001fab604d4 _pthread_start + 136 (pthread.c:904)
10  libsystem_pthread.dylib       	0x00000001fab5fa10 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001d81cd178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d81ccf10 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d81cce28 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d81ccc68 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001033eb278 0x102d68000 + 6828664
5   libsystem_pthread.dylib       	0x00000001fab604d4 _pthread_start + 136 (pthread.c:904)
6   libsystem_pthread.dylib       	0x00000001fab5fa10 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001d81cd978 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x0000000198aaaf20 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000198aaae38 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001033e6870 0x102d68000 + 6809712
4   libsystem_pthread.dylib       	0x00000001fab604d4 _pthread_start + 136 (pthread.c:904)
5   libsystem_pthread.dylib       	0x00000001fab5fa10 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001d81cd178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d81ccf10 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d81cce28 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d81ccc68 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000190a97a9c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x0000000190a95994 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x0000000190a953f8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CoreFoundation                	0x0000000190a9515c CFRunLoopRun + 64 (CFRunLoop.c:3446)
8   CoreMotion                    	0x000000019da252fc CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000001fab604d4 _pthread_start + 136 (pthread.c:904)
10  libsystem_pthread.dylib       	0x00000001fab5fa10 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001d81cdb1c __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001fab5efd4 _pthread_cond_wait + 1228 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001a7a83168 scavenger_thread_main + 1512 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x00000001fab604d4 _pthread_start + 136 (pthread.c:904)
4   libsystem_pthread.dylib       	0x00000001fab5fa10 thread_start + 8 (:-1)

Thread 9:
0   libsystem_pthread.dylib       	0x00000001fab5f9fc start_wqthread + 0 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x00000001fab5f9fc start_wqthread + 0 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x00000001fab5f9fc start_wqthread + 0 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x00000001fab5f9fc start_wqthread + 0 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x00000001fab5f9fc start_wqthread + 0 (:-1)


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000000
    x4: 0x00000001faa9236f   x5: 0x000000016d0974f0   x6: 0x000000000000006e   x7: 0x0000000000000000
    x8: 0xcb0ed041a49c42ac   x9: 0xcb0ed04045451a2c  x10: 0x0000000000000200  x11: 0x000000016d097020
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000148  x17: 0x00000001e1d95880  x18: 0x0000000000000000  x19: 0x0000000000000006
   x20: 0x0000000000000103  x21: 0x00000001e1d95960  x22: 0x00000001e73ac6a8  x23: 0x0000000000000001
   x24: 0x00000002830cc350  x25: 0x0000000000000001  x26: 0x00000002809d48c0  x27: 0x0000000000000000
   x28: 0x0000000000000001   fp: 0x000000016d097460   lr: 0x00000001fab63680
    sp: 0x000000016d097440   pc: 0x00000001d81d5fbc cpsr: 0x40000000
   esr: 0x56000080  Address size fault


Binary Images:
        0x102d68000 -         0x10379bfff 发型测试 arm64  <cc6cf938942c3fc09ce8738c466fb42a> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/发型测试
        0x104b48000 -         0x104b53fff libobjc-trampolines.dylib arm64e  <e4220a07d2c038189a9425e8dba387bc> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x104be0000 -         0x104bf7fff FBLPromises arm64  <1f7247bc92323aaea8fd60a58f93d904> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x104c14000 -         0x104c27fff Reachability arm64  <d351f9e9160c3747a3126f141f76340e> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x104c54000 -         0x104c6ffff Promises arm64  <0e3b1d6ea0cd397baa1c76bac362e169> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/Promises.framework/Promises
        0x104c94000 -         0x104c9ffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x104ccc000 -         0x104d37fff BSImagePicker arm64  <03aa03dbd3443f27bb2407777d2b1e91> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x104df0000 -         0x104dfffff TTSDKStrategyLite arm64  <3b1598d2ae913d51b8bf5da7553ed0fd> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x104e10000 -         0x104e1bfff TTSDKTTFFmpegLiveLite arm64  <85ee59668139318cabddcc2173ff607b> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x10533c000 -         0x105363fff SnapKit arm64  <6bb9e949f3153d1a93a0cd71a8f774ef> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x1053d0000 -         0x10544ffff SDWebImage arm64  <94f7389f01403ae0b2e5326ecd9bbbd2> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x1054d8000 -         0x1054fffff SwiftyJSON arm64  <09ef1d21ba2b31f9a00fa77e9d0b6a95> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x105534000 -         0x10555ffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x10563c000 -         0x1057d3fff Alamofire arm64  <fd14afff2a85358b83de1a972458336f> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x1059b4000 -         0x1059fffff Starscream arm64  <13b76bc86c9e3562a1186774ab9f8278> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x105a74000 -         0x105a9ffff TTSDKCore arm64  <c31c78044ec33e10923b655cd255f451> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x105b08000 -         0x105b6bfff TTSDKTools arm64  <0617c098c09d36d7b882a5df8bfb447c> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x105cb8000 -         0x105eaffff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x106060000 -         0x1060cbfff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x106248000 -         0x1062e3fff TTSDKLiveBase arm64  <a20645993829306cb3eb6cf7f2c51b4e> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x10669c000 -         0x1067cbfff TTSDKLivePlayerLite arm64  <0225f99abbf43f989c8510ccab8311ce> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x106c50000 -         0x106e03fff TTSDKPlayerCoreLiveLite arm64  <fe27e5242e4a3b6987aa61d905a07bb5> /private/var/containers/Bundle/Application/D4EEA705-3ED1-4CC3-AB77-A3D1E3121DFE/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x188dd8000 -         0x188e25fe0 libobjc.A.dylib arm64e  <fe61505cf2a93f78b709647d1097a06a> /usr/lib/libobjc.A.dylib
        0x18f9fc000 -         0x19055bfff Foundation arm64e  <5623831d571933a99691759f47d714d1> /System/Library/Frameworks/Foundation.framework/Foundation
        0x190a62000 -         0x190f8efff CoreFoundation arm64e  <5a6c1f41bf7032f6a1d65b894dd21362> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x191b39000 -         0x191f14fff CFNetwork arm64e  <650b083ce6403f12a26732148f1eb6b8> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x19206e000 -         0x19240ffff QuartzCore arm64e  <5c3ee3d91aaa3052a6c7b957b454ad71> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x192c8f000 -         0x194765fff UIKitCore arm64e  <2d5384466e403c108a5f559c938077a0> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x198aa1000 -         0x198b1eff3 libsystem_c.dylib arm64e  <6d03d4f6ec2f3c189013b4caf90539af> /usr/lib/system/libsystem_c.dylib
        0x19d753000 -         0x19dbf1fff CoreMotion arm64e  <be7671f8bdb730f5995710ea2e0fe9d7> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1a6500000 -         0x1a7bdef7f JavaScriptCore arm64e  <9405ca45686f37babefc8145fd21f6cb> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1b37e5000 -         0x1b386d69b dyld arm64e  <28d6d2c146ce3d58b744b06a6c573888> /usr/lib/dyld
        0x1d4020000 -         0x1d4028fff GraphicsServices arm64e  <ac07ebbed8bc3e55a13a07bb548734d1> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1d81cc000 -         0x1d8204fef libsystem_kernel.dylib arm64e  <08f02c6b1443366598054a8bee87346e> /usr/lib/system/libsystem_kernel.dylib
        0x1faa79000 -         0x1faa94fff libc++abi.dylib arm64e  <86d3dd195f9e3d528c65642b72fe0061> /usr/lib/libc++abi.dylib
        0x1fab5e000 -         0x1fab69ff3 libsystem_pthread.dylib arm64e  <8f9c865b29f5346badd5fde40e008c66> /usr/lib/system/libsystem_pthread.dylib
        0x1fff32000 -         0x20004efff RenderBox arm64e  <********************************> /System/Library/PrivateFrameworks/RenderBox.framework/RenderBox

EOF
