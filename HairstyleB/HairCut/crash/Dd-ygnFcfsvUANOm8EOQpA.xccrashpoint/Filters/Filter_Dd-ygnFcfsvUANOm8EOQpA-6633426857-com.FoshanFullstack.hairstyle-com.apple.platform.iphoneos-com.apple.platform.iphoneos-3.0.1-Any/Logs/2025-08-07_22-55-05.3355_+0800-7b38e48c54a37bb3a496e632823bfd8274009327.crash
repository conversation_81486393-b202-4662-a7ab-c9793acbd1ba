Incident Identifier: EF4B0D96-444A-45BA-A7F5-7B7246EA1BB3
Hardware Model:      iPhone13,3
Process:             发型测试 [2703]
Path:                /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone13,3:15
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [973]

Date/Time:           2025-08-07 22:55:05.3355 +0800
Launch Time:         2025-08-07 22:54:38.7234 +0800
OS Version:          iPhone OS 17.2.1 (21C66)
Release Type:        User
Baseband Version:    4.20.05
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001a2e61c44
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [2703]

Triggered by Thread:  1

Last Exception Backtrace:
0   CoreFoundation                	0x19aea269c __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x193157c80 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1bb60b6c8 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1bb6104e4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x19d1ceeb0 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1494)
5   UIKitCore                     	0x19d013900 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20016)
6   QuartzCore                    	0x19c428aa8 CA::Layer::layout_if_needed(CA::Transaction*) + 500 (CALayer.mm:10815)
7   QuartzCore                    	0x19c428630 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 144 (CALayer.mm:2597)
8   QuartzCore                    	0x19c42eb60 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2792)
9   QuartzCore                    	0x19c427e3c CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
10  QuartzCore                    	0x19c5c01dc CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
11  libsystem_pthread.dylib       	0x204da3f48 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x204d9e880 _pthread_exit + 84 (pthread.c:1730)
13  libsystem_pthread.dylib       	0x204d9fd7c _pthread_wqthread_exit + 64 (pthread.c:2589)
14  libsystem_pthread.dylib       	0x204d9b9ec _pthread_wqthread + 424 (pthread.c:2623)
15  libsystem_pthread.dylib       	0x204d9ba04 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001e24e9178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e24e8f10 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e24e8e28 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e24e8c68 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019adebb1c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000019ade9a14 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000019ade9478 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   GraphicsServices              	0x00000001de3424f8 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x000000019d20d62c -[UIApplication _run] + 888 (UIApplication.m:3685)
9   UIKitCore                     	0x000000019d20cc68 UIApplicationMain + 340 (UIApplication.m:5270)
10  UIKitCore                     	0x000000019d4373d0 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
11  发型测试                          	0x00000001027e9130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x00000001027e9130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x00000001027e9130 main + 120
14  dyld                          	0x00000001bdb0edcc start + 2240 (dyldMain.cpp:1269)

Thread 1 Crashed:
0   libsystem_c.dylib             	0x00000001a2e61c44 __abort + 168 (abort.c:171)
1   libsystem_c.dylib             	0x00000001a2e61b9c abort + 192 (abort.c:126)
2   libc++abi.dylib               	0x0000000204cc9ff8 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x0000000204cb9f90 demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000019315ada4 _objc_terminate() + 144 (objc-exception.mm:496)
5   发型测试                          	0x0000000102ff522c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x0000000204cc93bc std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x0000000204ccc4a8 __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x0000000204ccc3ec __cxa_throw + 308 (cxa_exception.cpp:283)
9   libobjc.A.dylib               	0x0000000193157de8 objc_exception_throw + 420 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001bb60b6c8 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001bb6104e4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x000000019d1ceeb0 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1494)
13  UIKitCore                     	0x000000019d013900 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20016)
14  QuartzCore                    	0x000000019c428aa8 CA::Layer::layout_if_needed(CA::Transaction*) + 500 (CALayer.mm:10815)
15  QuartzCore                    	0x000000019c428630 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 144 (CALayer.mm:2597)
16  QuartzCore                    	0x000000019c42eb60 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2792)
17  QuartzCore                    	0x000000019c427e3c CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
18  QuartzCore                    	0x000000019c5c01dc CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
19  libsystem_pthread.dylib       	0x0000000204da3f48 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x0000000204d9e880 _pthread_exit + 84 (pthread.c:1730)
21  libsystem_pthread.dylib       	0x0000000204d9fd7c _pthread_wqthread_exit + 64 (pthread.c:2589)
22  libsystem_pthread.dylib       	0x0000000204d9b9ec _pthread_wqthread + 424 (pthread.c:2623)
23  libsystem_pthread.dylib       	0x0000000204d9ba04 start_wqthread + 8 (:-1)

Thread 2 name:
Thread 2:
0   libsystem_kernel.dylib        	0x00000001e24e9178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e24e8f10 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e24e8e28 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e24e8c68 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019adebb1c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000019ade9a14 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000019ade9478 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   Foundation                    	0x0000000199d7c48c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x0000000199da974c -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x000000019d16f4a8 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1208)
10  Foundation                    	0x0000000199dffde0 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x0000000204d9c4d4 _pthread_start + 136 (pthread.c:904)
12  libsystem_pthread.dylib       	0x0000000204d9ba10 thread_start + 8 (:-1)

Thread 3:
0   libsystem_kernel.dylib        	0x00000001e24e9978 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a2df5f20 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a2e041dc sleep + 52 (sleep.c:62)
3   发型测试                          	0x000000010301eef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x0000000204d9c4d4 _pthread_start + 136 (pthread.c:904)
5   libsystem_pthread.dylib       	0x0000000204d9ba10 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001e24e9178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e24e8f10 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e24ed718 thread_suspend + 112 (thread_actUser.c:1036)
3   发型测试                          	0x0000000102ff697c handleExceptions + 120
4   libsystem_pthread.dylib       	0x0000000204d9c4d4 _pthread_start + 136 (pthread.c:904)
5   libsystem_pthread.dylib       	0x0000000204d9ba10 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001e24e9178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e24e8fa8 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e24e8e28 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e24e8c68 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000102ff69a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x0000000204d9c4d4 _pthread_start + 136 (pthread.c:904)
6   libsystem_pthread.dylib       	0x0000000204d9ba10 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e24e9978 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a2df5f20 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a2df5e38 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000102f5e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000102f10e84 thread_do + 340
5   libsystem_pthread.dylib       	0x0000000204d9c4d4 _pthread_start + 136 (pthread.c:904)
6   libsystem_pthread.dylib       	0x0000000204d9ba10 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e24e9978 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a2df5f20 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a2df5e38 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000102f5e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000102f10e84 thread_do + 340
5   libsystem_pthread.dylib       	0x0000000204d9c4d4 _pthread_start + 136 (pthread.c:904)
6   libsystem_pthread.dylib       	0x0000000204d9ba10 thread_start + 8 (:-1)

Thread 8:
0   libsystem_pthread.dylib       	0x0000000204d9b9fc start_wqthread + 0 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001e24e9b1c __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x0000000204d9afd4 _pthread_cond_wait + 1228 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001b1db31b8 scavenger_thread_main + 1512 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x0000000204d9c4d4 _pthread_start + 136 (pthread.c:904)
4   libsystem_pthread.dylib       	0x0000000204d9ba10 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001e24e9178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e24e8f10 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e24e8e28 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e24e8c68 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019adebb1c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000019ade9a14 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000019ade9478 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CFNetwork                     	0x000000019c0e7060 +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1479)
8   Foundation                    	0x0000000199dffde0 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x0000000204d9c4d4 _pthread_start + 136 (pthread.c:904)
10  libsystem_pthread.dylib       	0x0000000204d9ba10 thread_start + 8 (:-1)

Thread 11 name:
Thread 11:
0   libsystem_kernel.dylib        	0x00000001e24e9178 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e24e8f10 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e24e8e28 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e24e8c68 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019adebb1c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000019ade9a14 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000019ade9478 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CoreFoundation                	0x000000019ade91dc CFRunLoopRun + 64 (CFRunLoop.c:3446)
8   CoreMotion                    	0x00000001a7d6c61c CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x0000000204d9c4d4 _pthread_start + 136 (pthread.c:904)
10  libsystem_pthread.dylib       	0x0000000204d9ba10 thread_start + 8 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x0000000204d9b9fc start_wqthread + 0 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x0000000204d9b9fc start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x0000000204d9b9fc start_wqthread + 0 (:-1)


Thread 1 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000001ec025040  x10: 0x00000000000003e8  x11: 0x000000016d92e700
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000030  x17: 0x00000001f5493530  x18: 0x0000000000000000  x19: 0x000000016d933000
   x20: 0x000000016d92eb28  x21: 0x000000016d92ebd0  x22: 0x00000002822849e8  x23: 0x0000000000000001
   x24: 0x000000010d330c90  x25: 0x000000010bd77620  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016d92eb40   lr: 0x00000001a2e61c44
    sp: 0x000000016d92eb10   pc: 0x00000001a2e61c44 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x1025e0000 -         0x1033bffff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/发型测试
        0x1037ec000 -         0x1037f7fff libobjc-trampolines.dylib arm64e  <26d68e81199a300dbc1530ba4630a611> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x1038d0000 -         0x1038dffff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x1038fc000 -         0x10390bfff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x103924000 -         0x10392ffff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x103950000 -         0x103987fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x1039fc000 -         0x103a07fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x103a30000 -         0x103a43fff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/Promises.framework/Promises
        0x103a64000 -         0x103a77fff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x103a94000 -         0x103aa3fff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x103ab4000 -         0x103abffff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x103ae8000 -         0x103afffff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x103fe0000 -         0x10401ffff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x1040c4000 -         0x104117fff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x10419c000 -         0x1041bbfff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x10421c000 -         0x104247fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x1042a8000 -         0x1043cffff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x10460c000 -         0x10463bfff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1046a4000 -         0x104707fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x10473c000 -         0x104767fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1047b8000 -         0x1049affff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x104c44000 -         0x104caffff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x104d30000 -         0x104dc3fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x10516c000 -         0x10529bfff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x105734000 -         0x1058f3fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x105e90000 -         0x10716bfff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/DF40B3F5-73EC-4100-A4D3-FE9CF93C5899/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x19312c000 -         0x193179fe0 libobjc.A.dylib arm64e  <98c1e5b8c56e3b9c9783b0fef8f999ee> /usr/lib/libobjc.A.dylib
        0x199d50000 -         0x19a8affff Foundation arm64e  <c52a963060f13d48a6c350a20de78c6a> /System/Library/Frameworks/Foundation.framework/Foundation
        0x19adb6000 -         0x19b2e2fff CoreFoundation arm64e  <be405d8a546437a3a17e1068db404b23> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x19be8d000 -         0x19c268fff CFNetwork arm64e  <707e82b749fc31aaa493f47aac5c7c41> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x19c3c2000 -         0x19c763fff QuartzCore arm64e  <32b5a8e6ac133be9a6b4af6309173f95> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x19cfe1000 -         0x19eab6fff UIKitCore arm64e  <99ad2a319360369f9c0ba7a0af33e2e7> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1a2dec000 -         0x1a2e69ff3 libsystem_c.dylib arm64e  <6e5a7692c1e935adb20bc307991df11c> /usr/lib/system/libsystem_c.dylib
        0x1a7a9b000 -         0x1a7f38fff CoreMotion arm64e  <0202e42050043a2fbfc5ae48ef76c999> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1b0830000 -         0x1b1f0efbf JavaScriptCore arm64e  <d1f52cda68d2304d9a5254bdc911bbf5> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1bb606000 -         0x1bb64ffff CoreAutoLayout arm64e  <28683c1484733e0da646a05012b4e612> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1bdb09000 -         0x1bdb90b5b dyld arm64e  <630c2209dfd73c72b0a9e856c560f123> /usr/lib/dyld
        0x1de33f000 -         0x1de347fff GraphicsServices arm64e  <7d6f670c0e4b32158b4e599ef1ab9ed2> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e24e8000 -         0x1e2520fef libsystem_kernel.dylib arm64e  <55f2cc41248837bdbb95ea2ffd7611ae> /usr/lib/system/libsystem_kernel.dylib
        0x204cb5000 -         0x204cd0fff libc++abi.dylib arm64e  <f77d672f8d083de4b878122be53fcf99> /usr/lib/libc++abi.dylib
        0x204d9a000 -         0x204da5ff3 libsystem_pthread.dylib arm64e  <e9ae8dcfc2b635d7af24e008b8034e31> /usr/lib/system/libsystem_pthread.dylib

EOF
