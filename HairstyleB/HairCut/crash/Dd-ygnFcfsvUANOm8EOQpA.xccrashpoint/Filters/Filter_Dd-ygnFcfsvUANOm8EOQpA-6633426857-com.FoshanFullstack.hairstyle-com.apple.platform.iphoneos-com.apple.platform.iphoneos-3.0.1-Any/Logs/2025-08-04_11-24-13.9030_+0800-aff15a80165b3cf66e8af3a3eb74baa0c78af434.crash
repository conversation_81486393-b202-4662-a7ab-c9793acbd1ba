Incident Identifier: 683537C7-E097-4674-AC64-9ADC79DD53DD
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone16,1
Process:             发型测试 [92297]
Path:                /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone16,1:17
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [11158]

Date/Time:           2025-08-04 11:24:13.9030 +0800
Launch Time:         2025-08-04 11:23:54.0623 +0800
OS Version:          iPhone OS 17.5.1 (21F90)
Release Type:        User
Baseband Version:    1.60.02
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000019c082c54
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [92297]

Triggered by Thread:  11

Last Exception Backtrace:
0   CoreFoundation                	0x194124f20 __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x18bfaa018 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1b52a5224 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1b52a4ee4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x1963bba04 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
5   UIKitCore                     	0x1963329d0 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20044)
6   QuartzCore                    	0x1957913b4 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
7   QuartzCore                    	0x195790f38 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
8   QuartzCore                    	0x1957ec0e0 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
9   QuartzCore                    	0x195761028 CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
10  QuartzCore                    	0x1959423c8 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
11  libsystem_pthread.dylib       	0x1f0f7bf18 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x1f0f7bc88 _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x1f0f7bc34 _pthread_exit + 0 (pthread.c:1757)
14  libsystem_pthread.dylib       	0x1f0f7b9bc _pthread_wqthread + 424 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x1f0f780cc start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001dd1d4808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001dd1d8008 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001dd1d7f20 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001dd1d7d60 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001940f4f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001940f4600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001940f3cd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   GraphicsServices              	0x00000001d8fa41a8 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x000000019672c90c -[UIApplication _run] + 888 (UIApplication.m:3713)
9   UIKitCore                     	0x00000001967e09d0 UIApplicationMain + 340 (UIApplication.m:5303)
10  UIKitCore                     	0x000000019695a384 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
11  发型测试                          	0x0000000102819130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000102819130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000102819130 main + 120
14  dyld                          	0x00000001b77a5e4c start + 2240 (dyldMain.cpp:1298)

Thread 1:
0   libsystem_pthread.dylib       	0x00000001f0f780c4 start_wqthread + 0 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x00000001f0f780c4 start_wqthread + 0 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001dd1d4808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001dd1d8008 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001dd1d7f20 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001dd1d7d60 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001940f4f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001940f4600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001940f3cd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   Foundation                    	0x0000000193014e4c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x0000000193014c9c -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x0000000196740640 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1207)
10  Foundation                    	0x000000019302b718 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x00000001f0f7d06c _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x00000001f0f780d8 thread_start + 8 (:-1)

Thread 4:
0   libsystem_kernel.dylib        	0x00000001dd1da3ec __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019c0235f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019c08072c sleep + 52 (sleep.c:62)
3   发型测试                          	0x000000010304eef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x00000001f0f7d06c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000001f0f780d8 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001dd1d4808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001dd1d8008 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001dd1d5d78 thread_suspend + 112 (thread_actUser.c:1036)
3   发型测试                          	0x000000010302697c handleExceptions + 120
4   libsystem_pthread.dylib       	0x00000001f0f7d06c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000001f0f780d8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001dd1d4808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001dd1d80a0 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001dd1d7f20 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001dd1d7d60 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001030269a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x00000001f0f7d06c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001f0f780d8 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001dd1da3ec __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019c0235f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019c023508 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000102f8e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000102f40e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001f0f7d06c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001f0f780d8 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001dd1da3ec __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019c0235f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019c023508 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000102f8e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000102f40e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001f0f7d06c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001f0f780d8 thread_start + 8 (:-1)

Thread 9:
0   libsystem_pthread.dylib       	0x00000001f0f780c4 start_wqthread + 0 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x00000001f0f780c4 start_wqthread + 0 (:-1)

Thread 11 Crashed:
0   libsystem_c.dylib             	0x000000019c082c54 __abort + 168 (abort.c:171)
1   libsystem_c.dylib             	0x000000019c082bac abort + 192 (abort.c:126)
2   libc++abi.dylib               	0x00000001f0e9cca4 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x00000001f0e8ce5c demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000018bfc5e2c _objc_terminate() + 144 (objc-exception.mm:496)
5   发型测试                          	0x000000010302522c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x00000001f0e9c068 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x00000001f0e9f35c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x00000001f0e9f2a0 __cxa_throw + 308 (cxa_exception.cpp:283)
9   libobjc.A.dylib               	0x000000018bfaa180 objc_exception_throw + 420 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001b52a5224 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001b52a4ee4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x00000001963bba04 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
13  UIKitCore                     	0x00000001963329d0 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20044)
14  QuartzCore                    	0x00000001957913b4 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
15  QuartzCore                    	0x0000000195790f38 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
16  QuartzCore                    	0x00000001957ec0e0 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
17  QuartzCore                    	0x0000000195761028 CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
18  QuartzCore                    	0x00000001959423c8 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
19  libsystem_pthread.dylib       	0x00000001f0f7bf18 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x00000001f0f7bc88 _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x00000001f0f7bc34 _pthread_exit + 0 (pthread.c:1757)
22  libsystem_pthread.dylib       	0x00000001f0f7b9bc _pthread_wqthread + 424 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x00000001f0f780cc start_wqthread + 8 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001dd1d4808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001dd1d8008 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001dd1d7f20 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001dd1d7d60 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001940f4f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001940f4600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001940f3cd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CFNetwork                     	0x00000001952d4c90 +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x000000019302b718 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x00000001f0f7d06c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000001f0f780d8 thread_start + 8 (:-1)

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001dd1da1cc __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001f0f7a6e4 _pthread_cond_wait + 1228 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001ab7a70a4 scavenger_thread_main + 1512 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x00000001f0f7d06c _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x00000001f0f780d8 thread_start + 8 (:-1)

Thread 14 name:
Thread 14:
0   libsystem_kernel.dylib        	0x00000001dd1d4808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001dd1d8008 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001dd1d7f20 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001dd1d7d60 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001940f4f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001940f4600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001940f3cd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CoreFoundation                	0x0000000194161f04 CFRunLoopRun + 64 (CFRunLoop.c:3446)
8   CoreMotion                    	0x00000001a0e04210 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000001f0f7d06c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000001f0f780d8 thread_start + 8 (:-1)

Thread 15:
0   libsystem_pthread.dylib       	0x00000001f0f780c4 start_wqthread + 0 (:-1)


Thread 11 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000001f4e16600  x10: 0x00000000000003e8  x11: 0x000000016e2d6700
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000030  x17: 0x000000020003d598  x18: 0x0000000000000000  x19: 0x000000016e2db000
   x20: 0x000000016e2d6b28  x21: 0x000000016e2d6bd0  x22: 0x00000003014ad988  x23: 0x0000000000000001
   x24: 0x0000000103c1efe0  x25: 0x0000000105076630  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016e2d6b40   lr: 0x000000019c082c54
    sp: 0x000000016e2d6b10   pc: 0x000000019c082c54 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x102610000 -         0x1033effff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/发型测试
        0x103818000 -         0x103823fff libobjc-trampolines.dylib arm64e  <2e2c05f8377a30899ad91926d284dd03> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x1038f4000 -         0x1038fffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x103da8000 -         0x103db7fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x103dd4000 -         0x103de3fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x103dfc000 -         0x103e0ffff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/Promises.framework/Promises
        0x103e30000 -         0x103e3ffff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x103e54000 -         0x103e8bfff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x103f00000 -         0x103f0bfff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x103f28000 -         0x103f3bfff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x103f58000 -         0x103f63fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x103fac000 -         0x103fc3fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x104000000 -         0x10401ffff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x104070000 -         0x104197fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x10433c000 -         0x10437bfff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x104420000 -         0x104473fff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x104578000 -         0x1045a3fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x1045f8000 -         0x104623fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x104690000 -         0x1046bffff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x104728000 -         0x10478bfff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x10483c000 -         0x104a33fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x104cc8000 -         0x104d33fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x104db4000 -         0x104e47fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x1051f0000 -         0x10531ffff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x1057b8000 -         0x105977fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x105c58000 -         0x106f33fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/C8BF4742-D9A6-41EB-AA6C-C32BBC67BE8D/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x18bf94000 -         0x18bfe1f43 libobjc.A.dylib arm64e  <53115e1fe35330d99e8a4e6e73489f05> /usr/lib/libobjc.A.dylib
        0x192f4d000 -         0x193ac2fff Foundation arm64e  <3d3a12e3f5e9361fb00a4a5e8861aa55> /System/Library/Frameworks/Foundation.framework/Foundation
        0x1940a1000 -         0x1945cefff CoreFoundation arm64e  <00e76a98210c3cb5930bf236807ff24c> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x1951d7000 -         0x1955b3fff CFNetwork arm64e  <a5124019e235371686c7e75cf0163945> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x195713000 -         0x195aa0fff QuartzCore arm64e  <a4b65b359eeb38e49d4f7146aae1e28c> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x196322000 -         0x197e42fff UIKitCore arm64e  <1741fa374e53371e8daed611aab0043d> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x19c00d000 -         0x19c08aff3 libsystem_c.dylib arm64e  <b122f07fa15637f3a22d64627c0c4b24> /usr/lib/system/libsystem_c.dylib
        0x1a0df4000 -         0x1a12c2fff CoreMotion arm64e  <e4a6f107b302327ca121c9bebacca8f4> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1aa24c000 -         0x1ab97cf3f JavaScriptCore arm64e  <52d2aba5f8113d4fb077afae7e6f44cc> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1b5293000 -         0x1b52dcfff CoreAutoLayout arm64e  <4eb02083d9303bc69ae28df4594ac0ea> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1b7769000 -         0x1b77f5ef7 dyld arm64e  <71846eacee653697bf7d790b6a07dcdb> /usr/lib/dyld
        0x1d8fa3000 -         0x1d8fabfff GraphicsServices arm64e  <c19b2aeb6aa83f998a53f76c7a0d98fe> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1dd1d3000 -         0x1dd20cfef libsystem_kernel.dylib arm64e  <13b5134e819c3baab3004856112114cb> /usr/lib/system/libsystem_kernel.dylib
        0x1f0e88000 -         0x1f0ea3ffb libc++abi.dylib arm64e  <f603d156e9c5356380a6d2ebedc07a02> /usr/lib/libc++abi.dylib
        0x1f0f77000 -         0x1f0f83ff3 libsystem_pthread.dylib arm64e  <1196b6c3333d3450818ff3663484b8eb> /usr/lib/system/libsystem_pthread.dylib

EOF
