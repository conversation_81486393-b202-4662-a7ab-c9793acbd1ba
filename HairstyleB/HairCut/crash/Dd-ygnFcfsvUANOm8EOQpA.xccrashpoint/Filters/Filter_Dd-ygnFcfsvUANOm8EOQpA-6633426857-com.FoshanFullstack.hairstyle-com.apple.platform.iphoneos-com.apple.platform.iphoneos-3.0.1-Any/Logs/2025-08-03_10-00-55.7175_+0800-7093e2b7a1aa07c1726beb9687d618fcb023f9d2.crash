Incident Identifier: AEAF417C-D9D8-47D4-A993-007C51B44E03
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone12,1
Process:             发型测试 [10753]
Path:                /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone12,1:15
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [1039]

Date/Time:           2025-08-03 10:00:55.7175 +0800
Launch Time:         2025-08-03 10:00:34.9313 +0800
OS Version:          iPhone OS 17.5.1 (21F90)
Release Type:        User
Baseband Version:    5.00.00
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001aecdec54
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [10753]

Triggered by Thread:  9

Last Exception Backtrace:
0   CoreFoundation                	0x1a6d80f20 __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x19ec36018 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1c7f01224 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1c7f00ee4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x1a9017a04 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
5   UIKitCore                     	0x1a8f8e9d0 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20044)
6   QuartzCore                    	0x1a83ed3b4 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
7   QuartzCore                    	0x1a83ecf38 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
8   QuartzCore                    	0x1a84480e0 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
9   QuartzCore                    	0x1a83bd028 CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
10  QuartzCore                    	0x1a859e3c8 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
11  libsystem_pthread.dylib       	0x203bd7f18 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x203bd7c88 _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x203bd7c34 _pthread_wqthread_exit + 64 (pthread.c:2656)
14  libsystem_pthread.dylib       	0x203bd79bc _pthread_wqthread + 424 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x203bd40cc start_wqthread + 8 (:-1)

Thread 0:
0   libsystem_kernel.dylib        	0x00000001efe30808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001efe33f20 mach_msg_overwrite + 436 (mach_msg.c:0)
2   libsystem_kernel.dylib        	0x00000001efe33d60 mach_msg + 24 (mach_msg.c:323)
3   CoreFoundation                	0x00000001a6d50f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
4   CoreFoundation                	0x00000001a6d50600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
5   CoreFoundation                	0x00000001a6d4fcd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
6   GraphicsServices              	0x00000001ebc001a8 GSEventRunModal + 164 (GSEvent.c:2196)
7   UIKitCore                     	0x00000001a938890c -[UIApplication _run] + 888 (UIApplication.m:3713)
8   UIKitCore                     	0x00000001a943c9d0 UIApplicationMain + 340 (UIApplication.m:5303)
9   UIKitCore                     	0x00000001a95b6384 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
10  发型测试                          	0x0000000103081130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
11  发型测试                          	0x0000000103081130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
12  发型测试                          	0x0000000103081130 main + 120
13  dyld                          	0x00000001ca401e4c start + 2240 (dyldMain.cpp:1298)

Thread 1:
0   libsystem_pthread.dylib       	0x0000000203bd40c4 start_wqthread + 0 (:-1)

Thread 2 name:
Thread 2:
0   libsystem_kernel.dylib        	0x00000001efe30808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001efe33f20 mach_msg_overwrite + 436 (mach_msg.c:0)
2   libsystem_kernel.dylib        	0x00000001efe33d60 mach_msg + 24 (mach_msg.c:323)
3   CoreFoundation                	0x00000001a6d50f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
4   CoreFoundation                	0x00000001a6d50600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
5   CoreFoundation                	0x00000001a6d4fcd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
6   Foundation                    	0x00000001a5c70e4c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
7   Foundation                    	0x00000001a5c70c9c -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
8   UIKitCore                     	0x00000001a939c640 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1207)
9   Foundation                    	0x00000001a5c87718 __NSThread__start__ + 732 (NSThread.m:991)
10  libsystem_pthread.dylib       	0x0000000203bd906c _pthread_start + 136 (pthread.c:931)
11  libsystem_pthread.dylib       	0x0000000203bd40d8 thread_start + 8 (:-1)

Thread 3:
0   libsystem_kernel.dylib        	0x00000001efe363ec __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001aecdc72c sleep + 52 (sleep.c:62)
2   发型测试                          	0x00000001038b6ef8 monitorCachedData + 692
3   libsystem_pthread.dylib       	0x0000000203bd906c _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x0000000203bd40d8 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001efe30808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001efe31d78 thread_suspend + 112 (thread_actUser.c:1036)
2   发型测试                          	0x000000010388e97c handleExceptions + 120
3   libsystem_pthread.dylib       	0x0000000203bd906c _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x0000000203bd40d8 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001efe30808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001efe33f20 mach_msg_overwrite + 436 (mach_msg.c:0)
2   libsystem_kernel.dylib        	0x00000001efe33d60 mach_msg + 24 (mach_msg.c:323)
3   发型测试                          	0x000000010388e9a8 handleExceptions + 164
4   libsystem_pthread.dylib       	0x0000000203bd906c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000203bd40d8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001efe363ec __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001aec7f508 usleep + 68 (usleep.c:52)
2   发型测试                          	0x00000001037f6580 hevc_decoder_close1_::worker_thread(void*) + 996
3   发型测试                          	0x00000001037a8e84 thread_do + 340
4   libsystem_pthread.dylib       	0x0000000203bd906c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000203bd40d8 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001efe363ec __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001aec7f508 usleep + 68 (usleep.c:52)
2   发型测试                          	0x00000001037f6580 hevc_decoder_close1_::worker_thread(void*) + 996
3   发型测试                          	0x00000001037a8e84 thread_do + 340
4   libsystem_pthread.dylib       	0x0000000203bd906c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000203bd40d8 thread_start + 8 (:-1)

Thread 8:
0   libsystem_pthread.dylib       	0x0000000203bd40c4 start_wqthread + 0 (:-1)

Thread 9 Crashed:
0   libsystem_c.dylib             	0x00000001aecdec54 __abort + 168 (abort.c:171)
1   libsystem_c.dylib             	0x00000001aecdebac abort + 192 (abort.c:126)
2   libc++abi.dylib               	0x0000000203af8ca4 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x0000000203ae8e5c demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000019ec51e2c _objc_terminate() + 144 (objc-exception.mm:496)
5   发型测试                          	0x000000010388d22c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x0000000203af8068 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x0000000203afb35c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x0000000203afb2a0 __cxa_throw + 308 (cxa_exception.cpp:283)
9   libobjc.A.dylib               	0x000000019ec36180 objc_exception_throw + 420 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001c7f01224 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001c7f00ee4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x00000001a9017a04 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
13  UIKitCore                     	0x00000001a8f8e9d0 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20044)
14  QuartzCore                    	0x00000001a83ed3b4 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
15  QuartzCore                    	0x00000001a83ecf38 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
16  QuartzCore                    	0x00000001a84480e0 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
17  QuartzCore                    	0x00000001a83bd028 CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
18  QuartzCore                    	0x00000001a859e3c8 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
19  libsystem_pthread.dylib       	0x0000000203bd7f18 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x0000000203bd7c88 _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x0000000203bd7c34 _pthread_wqthread_exit + 64 (pthread.c:2656)
22  libsystem_pthread.dylib       	0x0000000203bd79bc _pthread_wqthread + 424 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x0000000203bd40cc start_wqthread + 8 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x0000000203bd40c4 start_wqthread + 0 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x0000000203bd40c4 start_wqthread + 0 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001efe361cc __psynch_cvwait + 8 (:-1)
1   JavaScriptCore                	0x00000001be4030a4 scavenger_thread_main + 1512 (pas_scavenger.c:347)
2   libsystem_pthread.dylib       	0x0000000203bd906c _pthread_start + 136 (pthread.c:931)
3   libsystem_pthread.dylib       	0x0000000203bd40d8 thread_start + 8 (:-1)

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001efe30808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001efe33f20 mach_msg_overwrite + 436 (mach_msg.c:0)
2   libsystem_kernel.dylib        	0x00000001efe33d60 mach_msg + 24 (mach_msg.c:323)
3   CoreFoundation                	0x00000001a6d50f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
4   CoreFoundation                	0x00000001a6d50600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
5   CoreFoundation                	0x00000001a6d4fcd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
6   CFNetwork                     	0x00000001a7f30c90 +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1473)
7   Foundation                    	0x00000001a5c87718 __NSThread__start__ + 732 (NSThread.m:991)
8   libsystem_pthread.dylib       	0x0000000203bd906c _pthread_start + 136 (pthread.c:931)
9   libsystem_pthread.dylib       	0x0000000203bd40d8 thread_start + 8 (:-1)

Thread 14 name:
Thread 14:
0   libsystem_kernel.dylib        	0x00000001efe30808 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001efe33f20 mach_msg_overwrite + 436 (mach_msg.c:0)
2   libsystem_kernel.dylib        	0x00000001efe33d60 mach_msg + 24 (mach_msg.c:323)
3   CoreFoundation                	0x00000001a6d50f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
4   CoreFoundation                	0x00000001a6d50600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
5   CoreFoundation                	0x00000001a6d4fcd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
6   CoreFoundation                	0x00000001a6dbdf04 CFRunLoopRun + 64 (CFRunLoop.c:3446)
7   CoreMotion                    	0x00000001b3a60210 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
8   libsystem_pthread.dylib       	0x0000000203bd906c _pthread_start + 136 (pthread.c:931)
9   libsystem_pthread.dylib       	0x0000000203bd40d8 thread_start + 8 (:-1)


Thread 9 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x0000000207a72600  x10: 0x00000000000003e8  x11: 0x000000016d8ca700
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000030  x17: 0x0000000212c99598  x18: 0x0000000000000000  x19: 0x000000016d8cf000
   x20: 0x000000016d8cab28  x21: 0x000000016d8cabd0  x22: 0x00000003000bb8c8  x23: 0x0000000000000001
   x24: 0x0000000105d0e520  x25: 0x00000001059c1ef0  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016d8cab40   lr: 0x00000001aecdec54
    sp: 0x000000016d8cab10   pc: 0x00000001aecdec54 cpsr: 0x40000000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x102e78000 -         0x103c57fff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/发型测试
        0x10408c000 -         0x104097fff libobjc-trampolines.dylib arm64e  <2e2c05f8377a30899ad91926d284dd03> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x104168000 -         0x104177fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x104194000 -         0x1041a3fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x104654000 -         0x10465ffff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x104688000 -         0x10469bfff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/Promises.framework/Promises
        0x1046bc000 -         0x1046c7fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x1046dc000 -         0x1046ebfff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x104700000 -         0x104737fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x1047ac000 -         0x1047bffff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x1047dc000 -         0x1047e7fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x104830000 -         0x104847fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x104884000 -         0x1048a3fff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x10491c000 -         0x104a43fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x104be8000 -         0x104c27fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x104ccc000 -         0x104d1ffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x104da4000 -         0x104dcffff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x104ebc000 -         0x104eebfff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x104f54000 -         0x104fb7fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x104fec000 -         0x105017fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x105068000 -         0x10525ffff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x1054f4000 -         0x10555ffff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x1055e0000 -         0x105673fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x105a1c000 -         0x105b4bfff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x105fe4000 -         0x1061a3fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x106504000 -         0x1077dffff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/D633650F-F29C-4FDF-A992-8F5502968115/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x19ec20000 -         0x19ec6df43 libobjc.A.dylib arm64e  <53115e1fe35330d99e8a4e6e73489f05> /usr/lib/libobjc.A.dylib
        0x1a5ba9000 -         0x1a671efff Foundation arm64e  <3d3a12e3f5e9361fb00a4a5e8861aa55> /System/Library/Frameworks/Foundation.framework/Foundation
        0x1a6cfd000 -         0x1a722afff CoreFoundation arm64e  <00e76a98210c3cb5930bf236807ff24c> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x1a7e33000 -         0x1a820ffff CFNetwork arm64e  <a5124019e235371686c7e75cf0163945> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x1a836f000 -         0x1a86fcfff QuartzCore arm64e  <a4b65b359eeb38e49d4f7146aae1e28c> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x1a8f7e000 -         0x1aaa9efff UIKitCore arm64e  <1741fa374e53371e8daed611aab0043d> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1aec69000 -         0x1aece6ff3 libsystem_c.dylib arm64e  <b122f07fa15637f3a22d64627c0c4b24> /usr/lib/system/libsystem_c.dylib
        0x1b3a50000 -         0x1b3f1efff CoreMotion arm64e  <e4a6f107b302327ca121c9bebacca8f4> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1bcea8000 -         0x1be5d8f3f JavaScriptCore arm64e  <52d2aba5f8113d4fb077afae7e6f44cc> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1c7eef000 -         0x1c7f38fff CoreAutoLayout arm64e  <4eb02083d9303bc69ae28df4594ac0ea> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1ca3c5000 -         0x1ca451ef7 dyld arm64e  <71846eacee653697bf7d790b6a07dcdb> /usr/lib/dyld
        0x1ebbff000 -         0x1ebc07fff GraphicsServices arm64e  <c19b2aeb6aa83f998a53f76c7a0d98fe> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1efe2f000 -         0x1efe68fef libsystem_kernel.dylib arm64e  <13b5134e819c3baab3004856112114cb> /usr/lib/system/libsystem_kernel.dylib
        0x203ae4000 -         0x203affffb libc++abi.dylib arm64e  <f603d156e9c5356380a6d2ebedc07a02> /usr/lib/libc++abi.dylib
        0x203bd3000 -         0x203bdfff3 libsystem_pthread.dylib arm64e  <1196b6c3333d3450818ff3663484b8eb> /usr/lib/system/libsystem_pthread.dylib

EOF
