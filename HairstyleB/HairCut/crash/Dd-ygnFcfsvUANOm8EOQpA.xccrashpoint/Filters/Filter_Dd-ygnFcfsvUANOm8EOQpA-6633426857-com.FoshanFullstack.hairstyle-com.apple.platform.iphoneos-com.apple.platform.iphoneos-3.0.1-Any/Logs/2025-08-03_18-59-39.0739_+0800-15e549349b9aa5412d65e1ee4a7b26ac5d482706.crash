Incident Identifier: AEF83950-64A1-409C-9386-AC01D896862F
Hardware Model:      iPhone14,5
Process:             发型测试 [16626]
Path:                /private/var/containers/Bundle/Application/********-C014-4B3C-8E03-6FD184A39332/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone14,5:15
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [3168]

Date/Time:           2025-08-03 18:59:39.0739 +0800
Launch Time:         2025-08-03 18:58:52.6807 +0800
OS Version:          iPhone OS 16.2 (20C65)
Release Type:        User
Baseband Version:    2.21.00
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000018ea88404
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [16626]

Triggered by Thread:  7

Last Exception Backtrace:
0   CoreFoundation                	0x1873b9e48 __exceptionPreprocess + 164 (NSException.m:202)
1   libobjc.A.dylib               	0x18068b8d8 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1a3dc0e84 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1a3db7e60 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1975)
4   UIKitCore                     	0x1895897e0 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1852 (NSLayoutConstraint_UIKitAdditions.m:1467)
5   QuartzCore                    	0x188a5cb0c CA::Layer::layout_if_needed(CA::Transaction*) + 500 (CALayer.mm:10226)
6   QuartzCore                    	0x188a701c0 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2516)
7   QuartzCore                    	0x188a81534 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 444 (CAContextInternal.mm:2714)
8   QuartzCore                    	0x188ab6930 CA::Transaction::commit() + 652 (CATransactionInternal.mm:432)
9   QuartzCore                    	0x188b06d54 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:635)
10  libsystem_pthread.dylib       	0x1d5442bd8 _pthread_tsd_cleanup + 620 (pthread_tsd.c:300)
11  libsystem_pthread.dylib       	0x1d5445674 _pthread_exit + 84 (pthread.c:1719)
12  libsystem_pthread.dylib       	0x1d54420e0 _pthread_wqthread_exit + 76 (pthread.c:2578)
13  libsystem_pthread.dylib       	0x1d5441e80 _pthread_wqthread + 424 (pthread.c:2612)
14  libsystem_pthread.dylib       	0x1d5441b98 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001c4d24aa8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001c4d36fc4 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001c4d37204 mach_msg_overwrite + 388 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001c4d24fec mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018742aad4 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2622)
5   CoreFoundation                	0x000000018742bd18 __CFRunLoopRun + 1232 (CFRunLoop.c:3005)
6   CoreFoundation                	0x0000000187430ec0 CFRunLoopRunSpecific + 612 (CFRunLoop.c:3418)
7   GraphicsServices              	0x00000001c1487368 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x000000018992686c -[UIApplication _run] + 888 (UIApplication.m:3754)
9   UIKitCore                     	0x00000001899264d0 UIApplicationMain + 340 (UIApplication.m:5344)
10  libswiftUIKit.dylib           	0x000000018ff4e308 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:538)
11  发型测试                          	0x0000000101165130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000101165130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000101165130 main + 120
14  dyld                          	0x00000001a5c52960 start + 2528 (dyldMain.cpp:1170)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001c4d24aa8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001c4d36fc4 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001c4d37204 mach_msg_overwrite + 388 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001c4d24fec mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018742aad4 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2622)
5   CoreFoundation                	0x000000018742bd18 __CFRunLoopRun + 1232 (CFRunLoop.c:3005)
6   CoreFoundation                	0x0000000187430ec0 CFRunLoopRunSpecific + 612 (CFRunLoop.c:3418)
7   Foundation                    	0x00000001817770d4 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x0000000181776fbc -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x0000000189a5b72c -[UIEventFetcher threadMain] + 436 (UIEventFetcher.m:1385)
10  Foundation                    	0x00000001817905a8 __NSThread__start__ + 716 (NSThread.m:963)
11  libsystem_pthread.dylib       	0x00000001d54426cc _pthread_start + 148 (pthread.c:893)
12  libsystem_pthread.dylib       	0x00000001d5441ba4 thread_start + 8 (:-1)

Thread 2:
0   libsystem_kernel.dylib        	0x00000001c4d24f68 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000018ea157d8 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000018ea29ad8 sleep + 52 (sleep.c:62)
3   发型测试                          	0x000000010199aef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x00000001d54426cc _pthread_start + 148 (pthread.c:893)
5   libsystem_pthread.dylib       	0x00000001d5441ba4 thread_start + 8 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001c4d24aa8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001c4d36fc4 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001c4d3110c thread_suspend + 112 (message.h:1112)
3   发型测试                          	0x000000010197297c handleExceptions + 120
4   libsystem_pthread.dylib       	0x00000001d54426cc _pthread_start + 148 (pthread.c:893)
5   libsystem_pthread.dylib       	0x00000001d5441ba4 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001c4d24aa8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001c4d3705c mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001c4d37204 mach_msg_overwrite + 388 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001c4d24fec mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001019729a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x00000001d54426cc _pthread_start + 148 (pthread.c:893)
6   libsystem_pthread.dylib       	0x00000001d5441ba4 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001c4d24f68 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000018ea157d8 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000018ea164a4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001018da580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x000000010188ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001d54426cc _pthread_start + 148 (pthread.c:893)
6   libsystem_pthread.dylib       	0x00000001d5441ba4 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001c4d24f68 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000018ea157d8 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000018ea164a4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001018da580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x000000010188ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001d54426cc _pthread_start + 148 (pthread.c:893)
6   libsystem_pthread.dylib       	0x00000001d5441ba4 thread_start + 8 (:-1)

Thread 7 Crashed:
0   libsystem_c.dylib             	0x000000018ea88404 __abort + 160 (abort.c:167)
1   libsystem_c.dylib             	0x000000018ea30c98 abort + 192 (abort.c:126)
2   libc++abi.dylib               	0x00000001d5387b8c abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x00000001d5377a80 demangling_terminate_handler() + 336 (cxa_default_handlers.cpp:71)
4   libobjc.A.dylib               	0x0000000180691d3c _objc_terminate() + 144 (objc-exception.mm:498)
5   发型测试                          	0x000000010197122c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x00000001d5386f28 std::__terminate(void (*)()) + 20 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x00000001d5389c2c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 36 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x00000001d5389bd8 __cxa_throw + 140 (cxa_exception.cpp:283)
9   libobjc.A.dylib               	0x000000018068ba38 objc_exception_throw + 412 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001a3dc0e84 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001a3db7e60 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1975)
12  UIKitCore                     	0x00000001895897e0 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1852 (NSLayoutConstraint_UIKitAdditions.m:1467)
13  QuartzCore                    	0x0000000188a5cb0c CA::Layer::layout_if_needed(CA::Transaction*) + 500 (CALayer.mm:10226)
14  QuartzCore                    	0x0000000188a701c0 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2516)
15  QuartzCore                    	0x0000000188a81534 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 444 (CAContextInternal.mm:2714)
16  QuartzCore                    	0x0000000188ab6930 CA::Transaction::commit() + 652 (CATransactionInternal.mm:432)
17  QuartzCore                    	0x0000000188b06d54 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:635)
18  libsystem_pthread.dylib       	0x00000001d5442bd8 _pthread_tsd_cleanup + 620 (pthread_tsd.c:300)
19  libsystem_pthread.dylib       	0x00000001d5445674 _pthread_exit + 84 (pthread.c:1719)
20  libsystem_pthread.dylib       	0x00000001d54420e0 _pthread_wqthread_exit + 76 (pthread.c:2578)
21  libsystem_pthread.dylib       	0x00000001d5441e80 _pthread_wqthread + 424 (pthread.c:2612)
22  libsystem_pthread.dylib       	0x00000001d5441b98 start_wqthread + 8 (:-1)

Thread 8:
0   libsystem_pthread.dylib       	0x00000001d5441b90 start_wqthread + 0 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001c4d24aa8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001c4d36fc4 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001c4d37204 mach_msg_overwrite + 388 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001c4d24fec mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018742aad4 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2622)
5   CoreFoundation                	0x000000018742bd18 __CFRunLoopRun + 1232 (CFRunLoop.c:3005)
6   CoreFoundation                	0x0000000187430ec0 CFRunLoopRunSpecific + 612 (CFRunLoop.c:3418)
7   CFNetwork                     	0x000000018879c078 +[__CFN_CoreSchedulingSetRunnable _run:] + 392 (CoreSchedulingSet.mm:1372)
8   Foundation                    	0x00000001817905a8 __NSThread__start__ + 716 (NSThread.m:963)
9   libsystem_pthread.dylib       	0x00000001d54426cc _pthread_start + 148 (pthread.c:893)
10  libsystem_pthread.dylib       	0x00000001d5441ba4 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001c4d24aa8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001c4d36fc4 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001c4d37204 mach_msg_overwrite + 388 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001c4d24fec mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018742aad4 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2622)
5   CoreFoundation                	0x000000018742bd18 __CFRunLoopRun + 1232 (CFRunLoop.c:3005)
6   CoreFoundation                	0x0000000187430ec0 CFRunLoopRunSpecific + 612 (CFRunLoop.c:3418)
7   CoreFoundation                	0x0000000187474ce4 CFRunLoopRun + 64 (CFRunLoop.c:3444)
8   CoreMotion                    	0x00000001929eee84 CLMotionCore::runMotionThread(void*) + 1208 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000001d54426cc _pthread_start + 148 (pthread.c:893)
10  libsystem_pthread.dylib       	0x00000001d5441ba4 thread_start + 8 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x00000001d5441b90 start_wqthread + 0 (:-1)


Thread 7 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000001dd8f8700  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000000001ff800  x14: 0x00000000000007fb  x15: 0x00000000d1e02034
   x16: 0x0000000000000030  x17: 0x00000001e299a370  x18: 0x0000000000000000  x19: 0x000000016f642808
   x20: 0x000000016f642848  x21: 0x000000016f6428b0  x22: 0x0000000282a552b0  x23: 0x000000028196cfa0
   x24: 0x0000000105f19d70  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000058
   x28: 0x0000000000000058   fp: 0x000000016f642820   lr: 0x000000018ea88404
    sp: 0x000000016f6427f0   pc: 0x000000018ea88404 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x100f5c000 -         0x101d3bfff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/********-C014-4B3C-8E03-6FD184A39332/发型测试.app/发型测试
        0x180674000 -         0x1806b7e1f libobjc.A.dylib arm64e  <d6ecfb730ca23a21a3a919e450d3b49c> /usr/lib/libobjc.A.dylib
        0x181735000 -         0x18207efff Foundation arm64e  <07a92f05d8ec327eab3341db9f77ba16> /System/Library/Frameworks/Foundation.framework/Foundation
        0x1873b0000 -         0x187795fff CoreFoundation arm64e  <725e49f4653b39bf9a7a8a3250911ecb> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x188544000 -         0x18890dfff CFNetwork arm64e  <8a75357d7e213fb38b1e4c0f63e8dc02> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x188a53000 -         0x188db1fff QuartzCore arm64e  <e0e47b5d2805361d88c4875002b0244d> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x189585000 -         0x18ad70fff UIKitCore arm64e  <59cbc9b530ae396ea269a986640001bc> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x18ea10000 -         0x18ea8fff7 libsystem_c.dylib arm64e  <f088d98df2a13452996f9e6bb5139f52> /usr/lib/system/libsystem_c.dylib
        0x18ff19000 -         0x18ff8dfff libswiftUIKit.dylib arm64e  <13add8bb92503421bfa740dd9a531e5a> /usr/lib/swift/libswiftUIKit.dylib
        0x1929db000 -         0x192de2fff CoreMotion arm64e  <b60e13f1eeef3b76bb8d421893a11b6f> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1a3db3000 -         0x1a3dfbfff CoreAutoLayout arm64e  <e96465db77983db5bc5851e367946a04> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1a5c3d000 -         0x1a5cc008f dyld arm64e  <7b63c57361613b33a3a29944ba59722f> /usr/lib/dyld
        0x1c1486000 -         0x1c148efff GraphicsServices arm64e  <5adda888f38735f787a7e01fcb9bb928> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1c4d20000 -         0x1c4d5afe3 libsystem_kernel.dylib arm64e  <9daa5c2993e03768a3e1e139995dc4af> /usr/lib/system/libsystem_kernel.dylib
        0x1d5376000 -         0x1d538dffb libc++abi.dylib arm64e  <a0028fdf20f43a76a43df0fa725bee9f> /usr/lib/libc++abi.dylib
        0x1d5441000 -         0x1d544cfff libsystem_pthread.dylib arm64e  <f2ba7ec0f75a3345b4f6f7da4979b902> /usr/lib/system/libsystem_pthread.dylib

EOF
