Incident Identifier: 88336C6E-E5B4-4AF7-851F-4B3B9F77AFF0
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone16,2
Process:             发型测试 [10286]
Path:                /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone16,2:17
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [5446]

Date/Time:           2025-08-07 17:23:29.3184 +0800
Launch Time:         2025-08-07 17:23:09.3525 +0800
OS Version:          iPhone OS 17.7.1 (21H216)
Release Type:        User
Baseband Version:    1.70.02
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001a6bfbc54
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [10286]

Triggered by Thread:  1

Last Exception Backtrace:
0   CoreFoundation                	0x19ec98f00 __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x196b132b8 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1bff44224 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1bff43ee4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x1a0f318f8 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
5   UIKitCore                     	0x1a0ea889c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20044)
6   QuartzCore                    	0x1a030626c CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
7   QuartzCore                    	0x1a0305df0 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
8   QuartzCore                    	0x1a0360fd8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
9   QuartzCore                    	0x1a02d5ee0 CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
10  QuartzCore                    	0x1a04b729c CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
11  libsystem_pthread.dylib       	0x1fb6f0f18 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x1fb6f0c88 _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x1fb6f0c34 _pthread_wqthread_exit + 64 (pthread.c:2656)
14  libsystem_pthread.dylib       	0x1fb6f09bc _pthread_wqthread + 424 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x1fb6ed0cc start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001e78f56c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e78f8ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e78f8de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e78f8c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ec68f3c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000019ec685e0 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000019ec67cb8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   GraphicsServices              	0x00000001e36bd1a8 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x00000001a12a2ae8 -[UIApplication _run] + 888 (UIApplication.m:3713)
9   UIKitCore                     	0x00000001a1356d98 UIApplicationMain + 340 (UIApplication.m:5303)
10  UIKitCore                     	0x00000001a14d0504 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
11  发型测试                          	0x0000000104bad130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000104bad130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000104bad130 main + 120
14  dyld                          	0x00000001c2448154 start + 2356 (dyldMain.cpp:1298)

Thread 1 Crashed:
0   libsystem_c.dylib             	0x00000001a6bfbc54 __abort + 168 (abort.c:171)
1   libsystem_c.dylib             	0x00000001a6bfbbac abort + 192 (abort.c:126)
2   libc++abi.dylib               	0x00000001fb610ca4 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x00000001fb600e5c demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x0000000196b2f14c _objc_terminate() + 144 (objc-exception.mm:496)
5   发型测试                          	0x00000001053b922c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x00000001fb610068 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x00000001fb61335c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x00000001fb6132a0 __cxa_throw + 308 (cxa_exception.cpp:283)
9   libobjc.A.dylib               	0x0000000196b13420 objc_exception_throw + 420 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001bff44224 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001bff43ee4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x00000001a0f318f8 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
13  UIKitCore                     	0x00000001a0ea889c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20044)
14  QuartzCore                    	0x00000001a030626c CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
15  QuartzCore                    	0x00000001a0305df0 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
16  QuartzCore                    	0x00000001a0360fd8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
17  QuartzCore                    	0x00000001a02d5ee0 CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
18  QuartzCore                    	0x00000001a04b729c CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
19  libsystem_pthread.dylib       	0x00000001fb6f0f18 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x00000001fb6f0c88 _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x00000001fb6f0c34 _pthread_wqthread_exit + 64 (pthread.c:2656)
22  libsystem_pthread.dylib       	0x00000001fb6f09bc _pthread_wqthread + 424 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x00000001fb6ed0cc start_wqthread + 8 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x00000001fb6ed0c4 start_wqthread + 0 (:-1)

Thread 3:
0   libsystem_pthread.dylib       	0x00000001fb6ed0c4 start_wqthread + 0 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001e78f56c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e78f8ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e78f8de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e78f8c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ec68f3c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000019ec685e0 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000019ec67cb8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   Foundation                    	0x000000019db88b5c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x000000019db889ac -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x00000001a12b681c -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1207)
10  Foundation                    	0x000000019db9f428 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x00000001fb6f206c _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x00000001fb6ed0d8 thread_start + 8 (:-1)

Thread 5:
0   libsystem_pthread.dylib       	0x00000001fb6ed0c4 start_wqthread + 0 (:-1)

Thread 6:
0   libsystem_kernel.dylib        	0x00000001e78fb2ac __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a6b9c5f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a6bf972c sleep + 52 (sleep.c:62)
3   发型测试                          	0x00000001053e2ef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x00000001fb6f206c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000001fb6ed0d8 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e78f56c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e78f8ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e78f6c38 thread_suspend + 112 (thread_actUser.c:1036)
3   发型测试                          	0x00000001053ba97c handleExceptions + 120
4   libsystem_pthread.dylib       	0x00000001fb6f206c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000001fb6ed0d8 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001e78f56c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e78f8f60 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e78f8de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e78f8c20 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001053ba9a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x00000001fb6f206c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001fb6ed0d8 thread_start + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001e78fb2ac __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a6b9c5f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a6b9c508 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000105322580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x00000001052d4e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001fb6f206c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001fb6ed0d8 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001e78fb2ac __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a6b9c5f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a6b9c508 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000105322580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x00000001052d4e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001fb6f206c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001fb6ed0d8 thread_start + 8 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x00000001fb6ed0c4 start_wqthread + 0 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001e78f56c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e78f8ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e78f8de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e78f8c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ec68f3c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000019ec685e0 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000019ec67cb8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CFNetwork                     	0x000000019fe48c7c +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x000000019db9f428 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x00000001fb6f206c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000001fb6ed0d8 thread_start + 8 (:-1)

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001e78fb08c __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001fb6ef6e4 _pthread_cond_wait + 1228 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001b633a2a4 0x1b4dd3000 + 22442660
3   libsystem_pthread.dylib       	0x00000001fb6f206c _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x00000001fb6ed0d8 thread_start + 8 (:-1)

Thread 14 name:
Thread 14:
0   libsystem_kernel.dylib        	0x00000001e78f56c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e78f8ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e78f8de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e78f8c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ec68f3c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000019ec685e0 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000019ec67cb8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CoreFoundation                	0x000000019ecd5ee4 CFRunLoopRun + 64 (CFRunLoop.c:3446)
8   CoreMotion                    	0x00000001ab981e3c CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000001fb6f206c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000001fb6ed0d8 thread_start + 8 (:-1)


Thread 1 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000001ff5b2600  x10: 0x00000000000003e8  x11: 0x000000016b4de700
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000030  x17: 0x000000020ab6ebb8  x18: 0x0000000000000000  x19: 0x000000016b4e3000
   x20: 0x000000016b4deb28  x21: 0x000000016b4debd0  x22: 0x0000000301b1da28  x23: 0x0000000000000001
   x24: 0x0000000107f05c10  x25: 0x0000000107486d10  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016b4deb40   lr: 0x00000001a6bfbc54
    sp: 0x000000016b4deb10   pc: 0x00000001a6bfbc54 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x1049a4000 -         0x105783fff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/发型测试
        0x105bcc000 -         0x105bd7fff libobjc-trampolines.dylib arm64e  <4500b82977f533c5a0ccf8078f4e2bbe> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x105c30000 -         0x105c43fff GAXClient arm64e  <e403470e01883c889f7827cae4c7598c> /System/Library/AccessibilityBundles/GAXClient.bundle/GAXClient
        0x105c94000 -         0x105ca3fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x105cc0000 -         0x105ccffff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x105ce8000 -         0x105cf3fff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x105d1c000 -         0x105d2ffff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/Promises.framework/Promises
        0x105d50000 -         0x105d5bfff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x106210000 -         0x106247fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x1062bc000 -         0x1062cbfff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x1062ec000 -         0x1062fffff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x10631c000 -         0x106327fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x10639c000 -         0x1063dbfff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x106480000 -         0x106497fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x1064d8000 -         0x1065fffff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x1067a4000 -         0x1067f7fff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x10687c000 -         0x1068a7fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x1068fc000 -         0x10691bfff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x1069e4000 -         0x106a13fff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x106a7c000 -         0x106adffff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x106b14000 -         0x106b3ffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x106b90000 -         0x106d87fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x10701c000 -         0x107087fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x107108000 -         0x10719bfff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x107544000 -         0x107673fff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x107b0c000 -         0x107ccbfff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x1080c0000 -         0x10939bfff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/8E61753D-DFAD-4D0D-9544-2D56CFD982D8/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x196afc000 -         0x196b4ccf3 libobjc.A.dylib arm64e  <0bdfa14677a139df8f757909ef8665d0> /usr/lib/libobjc.A.dylib
        0x19dac1000 -         0x19e636fff Foundation arm64e  <1a8fa0dcf28c3e03b5c82010cf1da382> /System/Library/Frameworks/Foundation.framework/Foundation
        0x19ec15000 -         0x19f142fff CoreFoundation arm64e  <9f4d9f6962d93377aa95ee0cb9aecc04> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x19fd4b000 -         0x1a0127fff CFNetwork arm64e  <c6981dad9a4636e28faea67c07e06f49> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x1a0287000 -         0x1a0615fff QuartzCore arm64e  <5c684316dd7b39ab9eef1622ed3ef192> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x1a0e98000 -         0x1a29b9fff UIKitCore arm64e  <e8a673bafd1b3ffd9bbacc76d2e7f68c> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1a6b86000 -         0x1a6c03ff3 libsystem_c.dylib arm64e  <867d364cc5983c6394437837fb7f65db> /usr/lib/system/libsystem_c.dylib
        0x1ab972000 -         0x1abe40fff CoreMotion arm64e  <febb87c91a6e361a8d79fe13b80f902b> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1b4dd3000 -         0x1b650ff3f JavaScriptCore arm64e  <ea101dcff459317a993a65ad08aaf329> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1bff32000 -         0x1bff7bfff CoreAutoLayout arm64e  <fa5412768b133426a42d4465b6d44da1> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1c240b000 -         0x1c2498937 dyld arm64e  <f6d9db0ea5dd33a0866c5f810665e644> /usr/lib/dyld
        0x1e36bc000 -         0x1e36c4fff GraphicsServices arm64e  <0c5b8f85b08d3431ac3adeae42532d5d> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e78f4000 -         0x1e792dfff libsystem_kernel.dylib arm64e  <a68189cf91cf3926af40320f6a64f8a2> /usr/lib/system/libsystem_kernel.dylib
        0x1fb5fc000 -         0x1fb617ffb libc++abi.dylib arm64e  <3815f04b6d9636559a0b9dfd240703a7> /usr/lib/libc++abi.dylib
        0x1fb6ec000 -         0x1fb6f8ff3 libsystem_pthread.dylib arm64e  <828a5da27ba53e06820c47311fdcd3a5> /usr/lib/system/libsystem_pthread.dylib

EOF
