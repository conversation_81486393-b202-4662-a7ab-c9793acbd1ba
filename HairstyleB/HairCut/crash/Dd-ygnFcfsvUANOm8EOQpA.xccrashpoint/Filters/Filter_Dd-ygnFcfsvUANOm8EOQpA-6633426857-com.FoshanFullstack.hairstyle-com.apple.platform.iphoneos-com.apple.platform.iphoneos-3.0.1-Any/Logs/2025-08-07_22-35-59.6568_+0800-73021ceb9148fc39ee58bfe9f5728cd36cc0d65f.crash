Incident Identifier: FE5DE42E-22A5-478F-AE70-36A08039472A
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone14,7
Process:             发型测试 [6583]
Path:                /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone14,7:16
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [2423]

Date/Time:           2025-08-07 22:35:59.6568 +0800
Launch Time:         2025-08-07 22:35:13.4575 +0800
OS Version:          iPhone OS 17.6.1 (21G101)
Release Type:        User
Baseband Version:    2.60.02
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x0000000199a07c54
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [6583]

Triggered by Thread:  7

Last Exception Backtrace:
0   CoreFoundation                	0x191aa8f20 __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x1899332b8 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1b2d4c224 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1b2d4bee4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x193d408f8 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
5   UIKitCore                     	0x193cb789c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20044)
6   QuartzCore                    	0x19311626c CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
7   QuartzCore                    	0x193115df0 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
8   QuartzCore                    	0x193170fd8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
9   QuartzCore                    	0x1930e5ee0 CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
10  QuartzCore                    	0x1932c729c CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
11  libsystem_pthread.dylib       	0x1ee4f8f18 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x1ee4f8c88 _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x1ee4f8c34 _pthread_wqthread_exit + 64 (pthread.c:2656)
14  libsystem_pthread.dylib       	0x1ee4f89bc _pthread_wqthread + 424 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x1ee4f50cc start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001da6fd6c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da700ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da700de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da700c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000191a78f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x0000000191a78600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x0000000191a77cd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   GraphicsServices              	0x00000001d64c51a8 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x00000001940b1ae8 -[UIApplication _run] + 888 (UIApplication.m:3713)
9   UIKitCore                     	0x0000000194165d98 UIApplicationMain + 340 (UIApplication.m:5303)
10  UIKitCore                     	0x00000001942df504 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
11  发型测试                          	0x0000000104745130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000104745130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000104745130 main + 120
14  dyld                          	0x00000001b524f154 start + 2356 (dyldMain.cpp:1298)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001da6fd6c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da700ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da700de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da700c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000191a78f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x0000000191a78600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x0000000191a77cd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   Foundation                    	0x0000000190998b5c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x00000001909989ac -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x00000001940c581c -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1207)
10  Foundation                    	0x00000001909af428 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x00000001ee4fa06c _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x00000001ee4f50d8 thread_start + 8 (:-1)

Thread 2:
0   libsystem_kernel.dylib        	0x00000001da7032ac __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001999a85f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000199a0572c sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000104f7aef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x00000001ee4fa06c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000001ee4f50d8 thread_start + 8 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001da6fd6c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da700ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da6fec38 thread_suspend + 112 (thread_actUser.c:1036)
3   发型测试                          	0x0000000104f5297c handleExceptions + 120
4   libsystem_pthread.dylib       	0x00000001ee4fa06c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000001ee4f50d8 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001da6fd6c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da700f60 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001da700de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da700c20 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000104f529a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x00000001ee4fa06c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001ee4f50d8 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001da7032ac __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001999a85f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001999a8508 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000104eba580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000104e6ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001ee4fa06c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001ee4f50d8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001da7032ac __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001999a85f0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001999a8508 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000104eba580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000104e6ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001ee4fa06c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000001ee4f50d8 thread_start + 8 (:-1)

Thread 7 Crashed:
0   libsystem_c.dylib             	0x0000000199a07c54 __abort + 168 (abort.c:171)
1   libsystem_c.dylib             	0x0000000199a07bac abort + 192 (abort.c:126)
2   libc++abi.dylib               	0x00000001ee418ca4 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x00000001ee408e5c demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000018994f14c _objc_terminate() + 144 (objc-exception.mm:496)
5   发型测试                          	0x0000000104f5122c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x00000001ee418068 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x00000001ee41b35c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x00000001ee41b2a0 __cxa_throw + 308 (cxa_exception.cpp:283)
9   libobjc.A.dylib               	0x0000000189933420 objc_exception_throw + 420 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001b2d4c224 _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001b2d4bee4 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x0000000193d408f8 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
13  UIKitCore                     	0x0000000193cb789c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20044)
14  QuartzCore                    	0x000000019311626c CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
15  QuartzCore                    	0x0000000193115df0 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
16  QuartzCore                    	0x0000000193170fd8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
17  QuartzCore                    	0x00000001930e5ee0 CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
18  QuartzCore                    	0x00000001932c729c CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
19  libsystem_pthread.dylib       	0x00000001ee4f8f18 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x00000001ee4f8c88 _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x00000001ee4f8c34 _pthread_wqthread_exit + 64 (pthread.c:2656)
22  libsystem_pthread.dylib       	0x00000001ee4f89bc _pthread_wqthread + 424 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x00000001ee4f50cc start_wqthread + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001da6fd6c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da700ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da700de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da700c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000191a78f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x0000000191a78600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x0000000191a77cd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CFNetwork                     	0x0000000192c58c7c +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x00000001909af428 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x00000001ee4fa06c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000001ee4f50d8 thread_start + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001da6fd6c8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da700ec8 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da700de0 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da700c20 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000191a78f5c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x0000000191a78600 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x0000000191a77cd8 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CoreFoundation                	0x0000000191ae5f04 CFRunLoopRun + 64 (CFRunLoop.c:3446)
8   CoreMotion                    	0x000000019e78ce3c CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000001ee4fa06c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000001ee4f50d8 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001da70308c __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001ee4f76e4 _pthread_cond_wait + 1228 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001a91442a4 scavenger_thread_main + 1512 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x00000001ee4fa06c _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x00000001ee4f50d8 thread_start + 8 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x00000001ee4f50c4 start_wqthread + 0 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x00000001ee4f50c4 start_wqthread + 0 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x00000001ee4f50c4 start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x00000001ee4f50c4 start_wqthread + 0 (:-1)


Thread 7 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000001f23ba600  x10: 0x00000000000003e8  x11: 0x000000016c292700
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000030  x17: 0x00000001fd976a98  x18: 0x0000000000000000  x19: 0x000000016c297000
   x20: 0x000000016c292b28  x21: 0x000000016c292bd0  x22: 0x00000003019d3828  x23: 0x0000000000000001
   x24: 0x0000000107510180  x25: 0x0000000106f662c0  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016c292b40   lr: 0x0000000199a07c54
    sp: 0x000000016c292b10   pc: 0x0000000199a07c54 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x10453c000 -         0x10531bfff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/发型测试
        0x105770000 -         0x10577bfff libobjc-trampolines.dylib arm64e  <be553713db163c12aaa48fd6211e48ce> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x10582c000 -         0x10583bfff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x105858000 -         0x105867fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x105880000 -         0x10588bfff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x1058ac000 -         0x1058e3fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x105958000 -         0x105963fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x10598c000 -         0x10599ffff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/Promises.framework/Promises
        0x1059c0000 -         0x1059d3fff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x1059f0000 -         0x1059fffff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x105a10000 -         0x105a1bfff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x105a44000 -         0x105a5bfff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x105f38000 -         0x105f77fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x10601c000 -         0x10606ffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x1060f4000 -         0x106113fff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x106174000 -         0x10619ffff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x106200000 -         0x106327fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x106564000 -         0x106593fff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1065fc000 -         0x10665ffff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x106694000 -         0x1066bffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x106710000 -         0x106907fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x106b9c000 -         0x106c07fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x106c88000 -         0x106d1bfff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x1070c4000 -         0x1071f3fff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x10768c000 -         0x10784bfff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x107de8000 -         0x1090c3fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/6414CF9B-8497-4965-8FBF-089E76508EB3/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x18991c000 -         0x18996ccf3 libobjc.A.dylib arm64e  <afdf5874bc3b388e864cdc9f4cdbf4f0> /usr/lib/libobjc.A.dylib
        0x1908d1000 -         0x191446fff Foundation arm64e  <d27a6ec5943c3b0e8d158840fd2914f0> /System/Library/Frameworks/Foundation.framework/Foundation
        0x191a25000 -         0x191f52fff CoreFoundation arm64e  <76a3b1983c09323e83590d4978e156f5> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x192b5b000 -         0x192f37fff CFNetwork arm64e  <371394cd79f23216acb0a159c09c668d> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x193097000 -         0x193425fff QuartzCore arm64e  <aedc1a5617313315a87ec6610024a405> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x193ca7000 -         0x1957c8fff UIKitCore arm64e  <9da0d27355063712b73de0149d74c13c> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x199992000 -         0x199a0fff3 libsystem_c.dylib arm64e  <7135c2c8ba5836368b46a9e6226ead45> /usr/lib/system/libsystem_c.dylib
        0x19e77d000 -         0x19ec4bfff CoreMotion arm64e  <5d6e7429116638b3807bdfad246f9132> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1a7bdd000 -         0x1a9319f3f JavaScriptCore arm64e  <2800076a7d5a38dcafa723fa080301b6> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1b2d3a000 -         0x1b2d83fff CoreAutoLayout arm64e  <e0c152e6907232c7979b89bb0807b8f6> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1b5212000 -         0x1b529f937 dyld arm64e  <52039c944da13638bd52020a0b5fa399> /usr/lib/dyld
        0x1d64c4000 -         0x1d64ccfff GraphicsServices arm64e  <3ebbd576e7d83f69bcb5b9810ddcc90e> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1da6fc000 -         0x1da735fef libsystem_kernel.dylib arm64e  <21ee5290d1193c31b948431865a67738> /usr/lib/system/libsystem_kernel.dylib
        0x1ee404000 -         0x1ee41fffb libc++abi.dylib arm64e  <b613e600b39c3bbf8e098a1466610355> /usr/lib/libc++abi.dylib
        0x1ee4f4000 -         0x1ee500ff3 libsystem_pthread.dylib arm64e  <e4a9d6dbf93b3c88bdd185671ec22e2b> /usr/lib/system/libsystem_pthread.dylib

EOF
