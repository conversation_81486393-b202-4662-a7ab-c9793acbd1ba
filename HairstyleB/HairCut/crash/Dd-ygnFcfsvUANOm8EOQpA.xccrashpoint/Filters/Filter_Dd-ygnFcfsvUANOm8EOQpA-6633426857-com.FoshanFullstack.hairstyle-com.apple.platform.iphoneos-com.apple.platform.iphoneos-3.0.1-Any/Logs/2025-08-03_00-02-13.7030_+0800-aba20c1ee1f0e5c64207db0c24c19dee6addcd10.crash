Incident Identifier: ********-B99A-4BA9-87EF-328CD58C3B61
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone12,8
Process:             发型测试 [38705]
Path:                /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone12,8:15
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [1862]

Date/Time:           2025-08-03 00:02:13.7030 +0800
Launch Time:         2025-08-03 00:01:56.2291 +0800
OS Version:          iPhone OS 17.4.1 (21E236)
Release Type:        User
Baseband Version:    5.00.00
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001a8fffc34
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [38705]

Triggered by Thread:  8

Last Exception Backtrace:
0   CoreFoundation                	0x1a1136b28 __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x198fb6f78 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1c20cf65c _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1c20d4478 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x1a34b2698 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
5   UIKitCore                     	0x1a32f907c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20031)
6   QuartzCore                    	0x1a2723e30 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
7   QuartzCore                    	0x1a27239b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
8   QuartzCore                    	0x1a2729bb4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
9   QuartzCore                    	0x1a27231bc CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
10  QuartzCore                    	0x1a28b7ee4 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
11  libsystem_pthread.dylib       	0x1fd035b8c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x1fd02ff84 _pthread_exit + 84 (pthread.c:1766)
13  libsystem_pthread.dylib       	0x1fd031854 _pthread_wqthread_exit + 64 (pthread.c:2625)
14  libsystem_pthread.dylib       	0x1fd02cfa8 _pthread_wqthread + 424 (pthread.c:2659)
15  libsystem_pthread.dylib       	0x1fd02cfc0 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001e95a5af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e95a5890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e95a57a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e95a55e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001a108001c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001a107df04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001a107d968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   GraphicsServices              	0x00000001e53734e0 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x00000001a34f0edc -[UIApplication _run] + 888 (UIApplication.m:3692)
9   UIKitCore                     	0x00000001a34f0518 UIApplicationMain + 340 (UIApplication.m:5282)
10  UIKitCore                     	0x00000001a3729734 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
11  发型测试                          	0x0000000100e05130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000100e05130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000100e05130 main + 120
14  dyld                          	0x00000001c459ed84 start + 2240 (dyldMain.cpp:1298)

Thread 1:
0   libsystem_pthread.dylib       	0x00000001fd02cfb8 start_wqthread + 0 (:-1)

Thread 2 name:
Thread 2:
0   libsystem_kernel.dylib        	0x00000001e95a5af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e95a5890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e95a57a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e95a55e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001a108001c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001a107df04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001a107d968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   Foundation                    	0x000000019ff0c4a8 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x000000019ff364e8 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x00000001a3453ac8 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1201)
10  Foundation                    	0x000000019ff7da9c __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x00000001fd02da90 _pthread_start + 136 (pthread.c:927)
12  libsystem_pthread.dylib       	0x00000001fd02cfcc thread_start + 8 (:-1)

Thread 3:
0   libsystem_kernel.dylib        	0x00000001e95a62f8 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a8f93ea0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a8fa2158 sleep + 52 (sleep.c:62)
3   发型测试                          	0x000000010163aef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x00000001fd02da90 _pthread_start + 136 (pthread.c:927)
5   libsystem_pthread.dylib       	0x00000001fd02cfcc thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001e95a5af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e95a5890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e95aa0cc thread_suspend + 112 (thread_actUser.c:1036)
3   发型测试                          	0x000000010161297c handleExceptions + 120
4   libsystem_pthread.dylib       	0x00000001fd02da90 _pthread_start + 136 (pthread.c:927)
5   libsystem_pthread.dylib       	0x00000001fd02cfcc thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001e95a5af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e95a5928 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e95a57a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e95a55e8 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001016129a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x00000001fd02da90 _pthread_start + 136 (pthread.c:927)
6   libsystem_pthread.dylib       	0x00000001fd02cfcc thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e95a62f8 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a8f93ea0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a8f93db8 usleep + 68 (usleep.c:52)
3   发型测试                          	0x000000010157a580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x000000010152ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001fd02da90 _pthread_start + 136 (pthread.c:927)
6   libsystem_pthread.dylib       	0x00000001fd02cfcc thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e95a62f8 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a8f93ea0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a8f93db8 usleep + 68 (usleep.c:52)
3   发型测试                          	0x000000010157a580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x000000010152ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001fd02da90 _pthread_start + 136 (pthread.c:927)
6   libsystem_pthread.dylib       	0x00000001fd02cfcc thread_start + 8 (:-1)

Thread 8 Crashed:
0   libsystem_c.dylib             	0x00000001a8fffc34 __abort + 168 (abort.c:171)
1   libsystem_c.dylib             	0x00000001a8fffb8c abort + 192 (abort.c:126)
2   libc++abi.dylib               	0x00000001fcf50ccc abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x00000001fcf40e84 demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x0000000198fba0bc _objc_terminate() + 144 (objc-exception.mm:496)
5   发型测试                          	0x000000010161122c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x00000001fcf50090 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x00000001fcf532e4 __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x00000001fcf53228 __cxa_throw + 308 (cxa_exception.cpp:283)
9   libobjc.A.dylib               	0x0000000198fb70e0 objc_exception_throw + 420 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001c20cf65c _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001c20d4478 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x00000001a34b2698 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
13  UIKitCore                     	0x00000001a32f907c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20031)
14  QuartzCore                    	0x00000001a2723e30 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
15  QuartzCore                    	0x00000001a27239b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
16  QuartzCore                    	0x00000001a2729bb4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
17  QuartzCore                    	0x00000001a27231bc CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
18  QuartzCore                    	0x00000001a28b7ee4 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
19  libsystem_pthread.dylib       	0x00000001fd035b8c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x00000001fd02ff84 _pthread_exit + 84 (pthread.c:1766)
21  libsystem_pthread.dylib       	0x00000001fd031854 _pthread_wqthread_exit + 64 (pthread.c:2625)
22  libsystem_pthread.dylib       	0x00000001fd02cfa8 _pthread_wqthread + 424 (pthread.c:2659)
23  libsystem_pthread.dylib       	0x00000001fd02cfc0 start_wqthread + 8 (:-1)

Thread 9:
0   libsystem_pthread.dylib       	0x00000001fd02cfb8 start_wqthread + 0 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x00000001fd02cfb8 start_wqthread + 0 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x00000001fd02cfb8 start_wqthread + 0 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001e95a649c __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001fd02c590 _pthread_cond_wait + 1228 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001b864adb0 scavenger_thread_main + 1512 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x00000001fd02da90 _pthread_start + 136 (pthread.c:927)
4   libsystem_pthread.dylib       	0x00000001fd02cfcc thread_start + 8 (:-1)

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001e95a5af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e95a5890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e95a57a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e95a55e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001a108001c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001a107df04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001a107d968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CFNetwork                     	0x00000001a23dcc48 +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1479)
8   Foundation                    	0x000000019ff7da9c __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x00000001fd02da90 _pthread_start + 136 (pthread.c:927)
10  libsystem_pthread.dylib       	0x00000001fd02cfcc thread_start + 8 (:-1)

Thread 14 name:
Thread 14:
0   libsystem_kernel.dylib        	0x00000001e95a5af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e95a5890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e95a57a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e95a55e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001a108001c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001a107df04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001a107d968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CoreFoundation                	0x00000001a107d6cc CFRunLoopRun + 64 (CFRunLoop.c:3446)
8   CoreMotion                    	0x00000001ae04a3c0 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000001fd02da90 _pthread_start + 136 (pthread.c:927)
10  libsystem_pthread.dylib       	0x00000001fd02cfcc thread_start + 8 (:-1)


Thread 8 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000002025de640  x10: 0x00000000000003e8  x11: 0x000000016fa2e700
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000030  x17: 0x000000020d754490  x18: 0x0000000000000000  x19: 0x000000016fa33000
   x20: 0x000000016fa2eb28  x21: 0x000000016fa2ebd0  x22: 0x0000000301165988  x23: 0x0000000000000001
   x24: 0x0000000157219470  x25: 0x0000000155d54160  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016fa2eb40   lr: 0x00000001a8fffc34
    sp: 0x000000016fa2eb10   pc: 0x00000001a8fffc34 cpsr: 0x40000000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x100bfc000 -         0x1019dbfff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/发型测试
        0x101dec000 -         0x101dfbfff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x101ef0000 -         0x101efbfff libobjc-trampolines.dylib arm64e  <19bc6b58cbf535a583a5fc742451547d> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x1023ac000 -         0x1023bbfff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x1023d4000 -         0x1023dffff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x102408000 -         0x10241bfff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/Promises.framework/Promises
        0x10243c000 -         0x102447fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x102458000 -         0x10248ffff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x102504000 -         0x10250ffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x102534000 -         0x102547fff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x102564000 -         0x102573fff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x1025b8000 -         0x1025cffff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x10260c000 -         0x10262bfff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x102674000 -         0x10279bfff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x102940000 -         0x10297ffff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x102a24000 -         0x102a77fff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x102b7c000 -         0x102ba7fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x102bfc000 -         0x102c27fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x102c94000 -         0x102cc3fff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x102d2c000 -         0x102d8ffff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x102e40000 -         0x103037fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x1032cc000 -         0x103337fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x1033b8000 -         0x10344bfff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x1037f4000 -         0x103923fff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x103dbc000 -         0x103f7bfff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x10425c000 -         0x105537fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/227FF3EC-C583-44F4-B8DC-038763D568AB/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x198f8c000 -         0x198fd9cc3 libobjc.A.dylib arm64e  <412fd1f44107344388efb3760778f6a7> /usr/lib/libobjc.A.dylib
        0x19fee1000 -         0x1a0a6cfff Foundation arm64e  <d92e19c162993e948614c505d5abccdb> /System/Library/Frameworks/Foundation.framework/Foundation
        0x1a104a000 -         0x1a1577fff CoreFoundation arm64e  <3a5f992ad1cd312ebd2ef7c66343a417> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x1a2182000 -         0x1a255efff CFNetwork arm64e  <a0da81af67733a72a9a5264f31047a16> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x1a26bd000 -         0x1a2a45fff QuartzCore arm64e  <a53570f9dc4a3b419932b1a081e6e520> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x1a32c6000 -         0x1a4dd6fff UIKitCore arm64e  <7bf01cfc23f1326aafd8ad967ffece28> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1a8f8a000 -         0x1a9007fff libsystem_c.dylib arm64e  <3b5201c515d0335fa91d0c63e1f6c6dc> /usr/lib/system/libsystem_c.dylib
        0x1add61000 -         0x1ae22afff CoreMotion arm64e  <1e51658a881b3bbb95c26c7c9701e878> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1b70b4000 -         0x1b881ff1f JavaScriptCore arm64e  <05ea21999e0238dea861db68c3407b98> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1c20ca000 -         0x1c2113fff CoreAutoLayout arm64e  <889eab7c907b39f0a0cd3277e3cb1b70> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1c4599000 -         0x1c4625be3 dyld arm64e  <7be2b7573b3d3e918cb774f3887660c7> /usr/lib/dyld
        0x1e5370000 -         0x1e5378fff GraphicsServices arm64e  <4cb7e98636bf38018f495d8c3c4a2127> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e95a4000 -         0x1e95ddfef libsystem_kernel.dylib arm64e  <db493af363b132209dd8dd4f86bddfc8> /usr/lib/system/libsystem_kernel.dylib
        0x1fcf3c000 -         0x1fcf57ffb libc++abi.dylib arm64e  <e14c495696043a9f9ec7f15a261f9b43> /usr/lib/libc++abi.dylib
        0x1fd02b000 -         0x1fd037fff libsystem_pthread.dylib arm64e  <a70c0def058c3cb09ec1453aa7f39df9> /usr/lib/system/libsystem_pthread.dylib

EOF
