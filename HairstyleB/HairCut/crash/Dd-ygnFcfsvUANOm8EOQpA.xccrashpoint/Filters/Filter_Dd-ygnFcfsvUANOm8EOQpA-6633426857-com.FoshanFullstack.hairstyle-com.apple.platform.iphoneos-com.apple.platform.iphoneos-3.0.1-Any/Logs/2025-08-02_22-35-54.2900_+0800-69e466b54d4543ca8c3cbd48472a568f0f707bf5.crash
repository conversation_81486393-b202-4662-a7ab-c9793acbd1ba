Incident Identifier: 626B3865-0652-4A53-B23A-16E774BD64E8
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone15,2
Process:             发型测试 [29733]
Path:                /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone15,2:16
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [26058]

Date/Time:           2025-08-02 22:35:54.2900 +0800
Launch Time:         2025-08-02 22:35:37.4544 +0800
OS Version:          iPhone OS 17.4.1 (21E236)
Release Type:        User
Baseband Version:    2.51.04
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001ab5f3c34
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [29733]

Triggered by Thread:  3

Last Exception Backtrace:
0   CoreFoundation                	0x1a372ab28 __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x19b57ef78 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1c46c365c _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1c46c8478 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x1a5aa6698 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
5   UIKitCore                     	0x1a58ed07c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20031)
6   QuartzCore                    	0x1a4d17e30 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
7   QuartzCore                    	0x1a4d179b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
8   QuartzCore                    	0x1a4d1dbb4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
9   QuartzCore                    	0x1a4d171bc CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
10  QuartzCore                    	0x1a4eabee4 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
11  libsystem_pthread.dylib       	0x1ff629b8c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x1ff623f84 _pthread_exit + 84 (pthread.c:1766)
13  libsystem_pthread.dylib       	0x1ff625854 _pthread_wqthread_exit + 64 (pthread.c:2625)
14  libsystem_pthread.dylib       	0x1ff620fa8 _pthread_wqthread + 424 (pthread.c:2659)
15  libsystem_pthread.dylib       	0x1ff620fc0 start_wqthread + 8 (:-1)

Kernel Triage:
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter
VM - (arg = 0x3) mach_vm_allocate_kernel failed within call to vm_map_enter


Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001ebb99af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ebb99890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ebb997a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ebb995e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001a367401c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001a3671f04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001a3671968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   GraphicsServices              	0x00000001e79674e0 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x00000001a5ae4edc -[UIApplication _run] + 888 (UIApplication.m:3692)
9   UIKitCore                     	0x00000001a5ae4518 UIApplicationMain + 340 (UIApplication.m:5282)
10  UIKitCore                     	0x00000001a5d1d734 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
11  发型测试                          	0x0000000102609130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000102609130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000102609130 main + 120
14  dyld                          	0x00000001c6b92d84 start + 2240 (dyldMain.cpp:1298)

Thread 1:
0   libsystem_pthread.dylib       	0x00000001ff620fb8 start_wqthread + 0 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x00000001ff620fb8 start_wqthread + 0 (:-1)

Thread 3 Crashed:
0   libsystem_c.dylib             	0x00000001ab5f3c34 __abort + 168 (abort.c:171)
1   libsystem_c.dylib             	0x00000001ab5f3b8c abort + 192 (abort.c:126)
2   libc++abi.dylib               	0x00000001ff544ccc abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x00000001ff534e84 demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000019b5820bc _objc_terminate() + 144 (objc-exception.mm:496)
5   发型测试                          	0x0000000102e1522c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x00000001ff544090 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x00000001ff5472e4 __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x00000001ff547228 __cxa_throw + 308 (cxa_exception.cpp:283)
9   libobjc.A.dylib               	0x000000019b57f0e0 objc_exception_throw + 420 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001c46c365c _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001c46c8478 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x00000001a5aa6698 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
13  UIKitCore                     	0x00000001a58ed07c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20031)
14  QuartzCore                    	0x00000001a4d17e30 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
15  QuartzCore                    	0x00000001a4d179b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
16  QuartzCore                    	0x00000001a4d1dbb4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
17  QuartzCore                    	0x00000001a4d171bc CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
18  QuartzCore                    	0x00000001a4eabee4 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
19  libsystem_pthread.dylib       	0x00000001ff629b8c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x00000001ff623f84 _pthread_exit + 84 (pthread.c:1766)
21  libsystem_pthread.dylib       	0x00000001ff625854 _pthread_wqthread_exit + 64 (pthread.c:2625)
22  libsystem_pthread.dylib       	0x00000001ff620fa8 _pthread_wqthread + 424 (pthread.c:2659)
23  libsystem_pthread.dylib       	0x00000001ff620fc0 start_wqthread + 8 (:-1)

Thread 4:
0   libsystem_pthread.dylib       	0x00000001ff620fb8 start_wqthread + 0 (:-1)

Thread 5:
0   libsystem_pthread.dylib       	0x00000001ff620fb8 start_wqthread + 0 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001ebb99af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ebb99890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ebb997a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ebb995e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001a367401c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001a3671f04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001a3671968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   Foundation                    	0x00000001a25004a8 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x00000001a252a4e8 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x00000001a5a47ac8 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1201)
10  Foundation                    	0x00000001a2571a9c __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x00000001ff621a90 _pthread_start + 136 (pthread.c:927)
12  libsystem_pthread.dylib       	0x00000001ff620fcc thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001ebb9a2f8 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001ab587ea0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001ab587db8 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000102d7e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000102d30e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001ff621a90 _pthread_start + 136 (pthread.c:927)
6   libsystem_pthread.dylib       	0x00000001ff620fcc thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001ebb9a2f8 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001ab587ea0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001ab587db8 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000102d7e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000102d30e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001ff621a90 _pthread_start + 136 (pthread.c:927)
6   libsystem_pthread.dylib       	0x00000001ff620fcc thread_start + 8 (:-1)

Thread 9:
0   libsystem_kernel.dylib        	0x00000001ebb9a2f8 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001ab587ea0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001ab596158 sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000102e3eef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x00000001ff621a90 _pthread_start + 136 (pthread.c:927)
5   libsystem_pthread.dylib       	0x00000001ff620fcc thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001ebb99af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ebb99890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ebb9e0cc thread_suspend + 112 (thread_actUser.c:1036)
3   发型测试                          	0x0000000102e1697c handleExceptions + 120
4   libsystem_pthread.dylib       	0x00000001ff621a90 _pthread_start + 136 (pthread.c:927)
5   libsystem_pthread.dylib       	0x00000001ff620fcc thread_start + 8 (:-1)

Thread 11 name:
Thread 11:
0   libsystem_kernel.dylib        	0x00000001ebb99af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ebb99928 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001ebb997a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ebb995e8 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000102e169a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x00000001ff621a90 _pthread_start + 136 (pthread.c:927)
6   libsystem_pthread.dylib       	0x00000001ff620fcc thread_start + 8 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x00000001ff620fb8 start_wqthread + 0 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x00000001ff620fb8 start_wqthread + 0 (:-1)

Thread 14 name:
Thread 14:
0   libsystem_kernel.dylib        	0x00000001ebb9a49c __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001ff620590 _pthread_cond_wait + 1228 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001bac3edb0 scavenger_thread_main + 1512 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x00000001ff621a90 _pthread_start + 136 (pthread.c:927)
4   libsystem_pthread.dylib       	0x00000001ff620fcc thread_start + 8 (:-1)

Thread 15 name:
Thread 15:
0   libsystem_kernel.dylib        	0x00000001ebb99af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ebb99890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ebb997a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ebb995e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001a367401c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001a3671f04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001a3671968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CFNetwork                     	0x00000001a49d0c48 +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1479)
8   Foundation                    	0x00000001a2571a9c __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x00000001ff621a90 _pthread_start + 136 (pthread.c:927)
10  libsystem_pthread.dylib       	0x00000001ff620fcc thread_start + 8 (:-1)

Thread 16 name:
Thread 16:
0   libsystem_kernel.dylib        	0x00000001ebb99af8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ebb99890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ebb997a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ebb995e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001a367401c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x00000001a3671f04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x00000001a3671968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CoreFoundation                	0x00000001a36716cc CFRunLoopRun + 64 (CFRunLoop.c:3446)
8   CoreMotion                    	0x00000001b063e3c0 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000001ff621a90 _pthread_start + 136 (pthread.c:927)
10  libsystem_pthread.dylib       	0x00000001ff620fcc thread_start + 8 (:-1)


Thread 3 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x0000000204bd2640  x10: 0x00000000000003e8  x11: 0x000000016dc26700
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000030  x17: 0x000000020fd48490  x18: 0x0000000000000000  x19: 0x000000016dc2b000
   x20: 0x000000016dc26b28  x21: 0x000000016dc26bd0  x22: 0x00000003013dfc88  x23: 0x0000000000000001
   x24: 0x0000000103a17250  x25: 0x00000001052692b0  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016dc26b40   lr: 0x00000001ab5f3c34
    sp: 0x000000016dc26b10   pc: 0x00000001ab5f3c34 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x102400000 -         0x1031dffff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/发型测试
        0x1035f0000 -         0x1035fffff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x10361c000 -         0x10362bfff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x103644000 -         0x10364ffff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x103678000 -         0x10368bfff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/Promises.framework/Promises
        0x1037a0000 -         0x1037abfff libobjc-trampolines.dylib arm64e  <19bc6b58cbf535a583a5fc742451547d> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x103c48000 -         0x103c7ffff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x103cf4000 -         0x103cfffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x103d24000 -         0x103d37fff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x103d54000 -         0x103d63fff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x103d74000 -         0x103d7ffff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x103dd4000 -         0x103e13fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x103eb8000 -         0x103ecffff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x103f10000 -         0x104037fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x1041dc000 -         0x10422ffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x1042b4000 -         0x1042dffff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x104334000 -         0x104353fff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x10441c000 -         0x10444bfff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1044b4000 -         0x104517fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x10454c000 -         0x104577fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1045c8000 -         0x1047bffff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x104a54000 -         0x104abffff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x104b40000 -         0x104bd3fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x104f7c000 -         0x1050abfff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x105544000 -         0x105703fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x105af8000 -         0x106dd3fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/C6810B69-8239-423A-9C4A-AA46C63EE5DD/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x19b554000 -         0x19b5a1cc3 libobjc.A.dylib arm64e  <412fd1f44107344388efb3760778f6a7> /usr/lib/libobjc.A.dylib
        0x1a24d5000 -         0x1a3060fff Foundation arm64e  <d92e19c162993e948614c505d5abccdb> /System/Library/Frameworks/Foundation.framework/Foundation
        0x1a363e000 -         0x1a3b6bfff CoreFoundation arm64e  <3a5f992ad1cd312ebd2ef7c66343a417> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x1a4776000 -         0x1a4b52fff CFNetwork arm64e  <a0da81af67733a72a9a5264f31047a16> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x1a4cb1000 -         0x1a5039fff QuartzCore arm64e  <a53570f9dc4a3b419932b1a081e6e520> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x1a58ba000 -         0x1a73cafff UIKitCore arm64e  <7bf01cfc23f1326aafd8ad967ffece28> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1ab57e000 -         0x1ab5fbfff libsystem_c.dylib arm64e  <3b5201c515d0335fa91d0c63e1f6c6dc> /usr/lib/system/libsystem_c.dylib
        0x1b0355000 -         0x1b081efff CoreMotion arm64e  <1e51658a881b3bbb95c26c7c9701e878> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1b96a8000 -         0x1bae13f1f JavaScriptCore arm64e  <05ea21999e0238dea861db68c3407b98> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1c46be000 -         0x1c4707fff CoreAutoLayout arm64e  <889eab7c907b39f0a0cd3277e3cb1b70> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1c6b8d000 -         0x1c6c19be3 dyld arm64e  <7be2b7573b3d3e918cb774f3887660c7> /usr/lib/dyld
        0x1e7964000 -         0x1e796cfff GraphicsServices arm64e  <4cb7e98636bf38018f495d8c3c4a2127> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1ebb98000 -         0x1ebbd1fef libsystem_kernel.dylib arm64e  <db493af363b132209dd8dd4f86bddfc8> /usr/lib/system/libsystem_kernel.dylib
        0x1ff530000 -         0x1ff54bffb libc++abi.dylib arm64e  <e14c495696043a9f9ec7f15a261f9b43> /usr/lib/libc++abi.dylib
        0x1ff61f000 -         0x1ff62bfff libsystem_pthread.dylib arm64e  <a70c0def058c3cb09ec1453aa7f39df9> /usr/lib/system/libsystem_pthread.dylib
        0x1fff80000 -         0x2010fbfe7 libLAPACK.dylib arm64e  <1d338c6418553f5dae06b0aa8b15f3cf> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libLAPACK.dylib

EOF
