Incident Identifier: 6807C8A6-BD15-4F62-B833-46056B85B1E3
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone13,2
Process:             发型测试 [19395]
Path:                /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone13,2:15
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [14136]

Date/Time:           2025-08-07 20:55:44.1003 +0800
Launch Time:         2025-08-07 20:55:19.6406 +0800
OS Version:          iPhone OS 17.4.1 (21E236)
Release Type:        User
Baseband Version:    4.50.06
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x0000000191f87c34
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [19395]

Triggered by Thread:  14

Last Exception Backtrace:
0   CoreFoundation                	0x18a0beb28 __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x181f32f78 objc_exception_throw + 60 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1ab05765c _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1ab05c478 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x18c43a698 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
5   UIKitCore                     	0x18c28107c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20031)
6   QuartzCore                    	0x18b6abe30 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
7   QuartzCore                    	0x18b6ab9b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
8   QuartzCore                    	0x18b6b1bb4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
9   QuartzCore                    	0x18b6ab1bc CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
10  QuartzCore                    	0x18b83fee4 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
11  libsystem_pthread.dylib       	0x1e5fbdb8c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x1e5fb7f84 _pthread_exit + 84 (pthread.c:1766)
13  libsystem_pthread.dylib       	0x1e5fb9854 _pthread_wqthread_exit + 64 (pthread.c:2625)
14  libsystem_pthread.dylib       	0x1e5fb4fa8 _pthread_wqthread + 424 (pthread.c:2659)
15  libsystem_pthread.dylib       	0x1e5fb4fc0 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001d252daf8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d252d890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d252d7a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d252d5e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018a00801c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000018a005f04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000018a005968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   GraphicsServices              	0x00000001ce2fb4e0 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x000000018c478edc -[UIApplication _run] + 888 (UIApplication.m:3692)
9   UIKitCore                     	0x000000018c478518 UIApplicationMain + 340 (UIApplication.m:5282)
10  UIKitCore                     	0x000000018c6b1734 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:539)
11  发型测试                          	0x0000000102279130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000102279130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000102279130 main + 120
14  dyld                          	0x00000001ad526d84 start + 2240 (dyldMain.cpp:1298)

Thread 1:
0   libsystem_pthread.dylib       	0x00000001e5fb4fb8 start_wqthread + 0 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x00000001e5fb4fb8 start_wqthread + 0 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001d252daf8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d252d890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d252d7a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d252d5e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018a00801c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000018a005f04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000018a005968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   Foundation                    	0x0000000188e944a8 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x0000000188ebe4e8 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x000000018c3dbac8 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1201)
10  Foundation                    	0x0000000188f05a9c __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x00000001e5fb5a90 _pthread_start + 136 (pthread.c:927)
12  libsystem_pthread.dylib       	0x00000001e5fb4fcc thread_start + 8 (:-1)

Thread 4:
0   libsystem_kernel.dylib        	0x00000001d252e2f8 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x0000000191f1bea0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000191f2a158 sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000102aaeef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x00000001e5fb5a90 _pthread_start + 136 (pthread.c:927)
5   libsystem_pthread.dylib       	0x00000001e5fb4fcc thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001d252daf8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d252d890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d25320cc thread_suspend + 112 (thread_actUser.c:1036)
3   发型测试                          	0x0000000102a8697c handleExceptions + 120
4   libsystem_pthread.dylib       	0x00000001e5fb5a90 _pthread_start + 136 (pthread.c:927)
5   libsystem_pthread.dylib       	0x00000001e5fb4fcc thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001d252daf8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d252d928 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001d252d7a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d252d5e8 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000102a869a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x00000001e5fb5a90 _pthread_start + 136 (pthread.c:927)
6   libsystem_pthread.dylib       	0x00000001e5fb4fcc thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001d252e2f8 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x0000000191f1bea0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000191f1bdb8 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001029ee580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x00000001029a0e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001e5fb5a90 _pthread_start + 136 (pthread.c:927)
6   libsystem_pthread.dylib       	0x00000001e5fb4fcc thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001d252e2f8 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x0000000191f1bea0 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000191f1bdb8 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001029ee580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x00000001029a0e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000001e5fb5a90 _pthread_start + 136 (pthread.c:927)
6   libsystem_pthread.dylib       	0x00000001e5fb4fcc thread_start + 8 (:-1)

Thread 9:
0   libsystem_pthread.dylib       	0x00000001e5fb4fb8 start_wqthread + 0 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x00000001e5fb4fb8 start_wqthread + 0 (:-1)

Thread 11 name:
Thread 11:
0   libsystem_kernel.dylib        	0x00000001d252e49c __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x00000001e5fb4590 _pthread_cond_wait + 1228 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001a15d2db0 scavenger_thread_main + 1512 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x00000001e5fb5a90 _pthread_start + 136 (pthread.c:927)
4   libsystem_pthread.dylib       	0x00000001e5fb4fcc thread_start + 8 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001d252daf8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d252d890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d252d7a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d252d5e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018a00801c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000018a005f04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000018a005968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CFNetwork                     	0x000000018b364c48 +[__CFN_CoreSchedulingSetRunnable _run:] + 384 (CoreSchedulingSet.mm:1479)
8   Foundation                    	0x0000000188f05a9c __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x00000001e5fb5a90 _pthread_start + 136 (pthread.c:927)
10  libsystem_pthread.dylib       	0x00000001e5fb4fcc thread_start + 8 (:-1)

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001d252daf8 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d252d890 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d252d7a8 mach_msg_overwrite + 436 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d252d5e8 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018a00801c __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2624)
5   CoreFoundation                	0x000000018a005f04 __CFRunLoopRun + 1208 (CFRunLoop.c:3007)
6   CoreFoundation                	0x000000018a005968 CFRunLoopRunSpecific + 608 (CFRunLoop.c:3420)
7   CoreFoundation                	0x000000018a0056cc CFRunLoopRun + 64 (CFRunLoop.c:3446)
8   CoreMotion                    	0x0000000196fd23c0 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000001e5fb5a90 _pthread_start + 136 (pthread.c:927)
10  libsystem_pthread.dylib       	0x00000001e5fb4fcc thread_start + 8 (:-1)

Thread 14 Crashed:
0   libsystem_c.dylib             	0x0000000191f87c34 __abort + 168 (abort.c:171)
1   libsystem_c.dylib             	0x0000000191f87b8c abort + 192 (abort.c:126)
2   libc++abi.dylib               	0x00000001e5ed8ccc abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x00000001e5ec8e84 demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x0000000181f360bc _objc_terminate() + 144 (objc-exception.mm:496)
5   发型测试                          	0x0000000102a8522c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x00000001e5ed8090 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x00000001e5edb2e4 __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x00000001e5edb228 __cxa_throw + 308 (cxa_exception.cpp:283)
9   libobjc.A.dylib               	0x0000000181f330e0 objc_exception_throw + 420 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001ab05765c _AssertAutoLayoutOnAllowedThreadsOnly + 328 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001ab05c478 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x000000018c43a698 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1501)
13  UIKitCore                     	0x000000018c28107c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 1404 (UIView.m:20031)
14  QuartzCore                    	0x000000018b6abe30 CA::Layer::layout_if_needed(CA::Transaction*) + 504 (CALayer.mm:10816)
15  QuartzCore                    	0x000000018b6ab9b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2598)
16  QuartzCore                    	0x000000018b6b1bb4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 464 (CAContextInternal.mm:2760)
17  QuartzCore                    	0x000000018b6ab1bc CA::Transaction::commit() + 648 (CATransactionInternal.mm:432)
18  QuartzCore                    	0x000000018b83fee4 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:636)
19  libsystem_pthread.dylib       	0x00000001e5fbdb8c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x00000001e5fb7f84 _pthread_exit + 84 (pthread.c:1766)
21  libsystem_pthread.dylib       	0x00000001e5fb9854 _pthread_wqthread_exit + 64 (pthread.c:2625)
22  libsystem_pthread.dylib       	0x00000001e5fb4fa8 _pthread_wqthread + 424 (pthread.c:2659)
23  libsystem_pthread.dylib       	0x00000001e5fb4fc0 start_wqthread + 8 (:-1)

Thread 15:
0   libsystem_pthread.dylib       	0x00000001e5fb4fb8 start_wqthread + 0 (:-1)

Thread 16:
0   libsystem_pthread.dylib       	0x00000001e5fb4fb8 start_wqthread + 0 (:-1)


Thread 14 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000001eb566640  x10: 0x00000000000003e8  x11: 0x000000016de9e700
   x12: 0x0000000000000000  x13: 0x00000000001ff800  x14: 0x0000000000000010  x15: 0x0000000000000000
   x16: 0x0000000000000030  x17: 0x00000001f66dc490  x18: 0x0000000000000000  x19: 0x000000016dea3000
   x20: 0x000000016de9eb28  x21: 0x000000016de9ebd0  x22: 0x0000000301964628  x23: 0x0000000000000001
   x24: 0x0000000135331d40  x25: 0x0000000133d8ba10  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000000000000   fp: 0x000000016de9eb40   lr: 0x0000000191f87c34
    sp: 0x000000016de9eb10   pc: 0x0000000191f87c34 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x102070000 -         0x102e4ffff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/发型测试
        0x103278000 -         0x103283fff libobjc-trampolines.dylib arm64e  <19bc6b58cbf535a583a5fc742451547d> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x103354000 -         0x10335ffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x103808000 -         0x103817fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x103834000 -         0x103843fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x10385c000 -         0x10386ffff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/Promises.framework/Promises
        0x103890000 -         0x10389ffff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x1038b4000 -         0x1038ebfff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x103960000 -         0x10396bfff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x103988000 -         0x10399bfff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x1039b8000 -         0x1039c3fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x103a0c000 -         0x103a23fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x103a60000 -         0x103a7ffff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x103ad0000 -         0x103bf7fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x103d9c000 -         0x103ddbfff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x103e80000 -         0x103ed3fff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x103fd8000 -         0x104003fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x104058000 -         0x104083fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1040f0000 -         0x10411ffff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x104188000 -         0x1041ebfff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x10429c000 -         0x104493fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x104728000 -         0x104793fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x104814000 -         0x1048a7fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x104c50000 -         0x104d7ffff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x105218000 -         0x1053d7fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x1056b8000 -         0x106993fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/4F34D8FD-7B4D-43CB-BC0E-91FF9BD78F1E/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x181f08000 -         0x181f55cc3 libobjc.A.dylib arm64e  <412fd1f44107344388efb3760778f6a7> /usr/lib/libobjc.A.dylib
        0x188e69000 -         0x1899f4fff Foundation arm64e  <d92e19c162993e948614c505d5abccdb> /System/Library/Frameworks/Foundation.framework/Foundation
        0x189fd2000 -         0x18a4fffff CoreFoundation arm64e  <3a5f992ad1cd312ebd2ef7c66343a417> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x18b10a000 -         0x18b4e6fff CFNetwork arm64e  <a0da81af67733a72a9a5264f31047a16> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x18b645000 -         0x18b9cdfff QuartzCore arm64e  <a53570f9dc4a3b419932b1a081e6e520> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x18c24e000 -         0x18dd5efff UIKitCore arm64e  <7bf01cfc23f1326aafd8ad967ffece28> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x191f12000 -         0x191f8ffff libsystem_c.dylib arm64e  <3b5201c515d0335fa91d0c63e1f6c6dc> /usr/lib/system/libsystem_c.dylib
        0x196ce9000 -         0x1971b2fff CoreMotion arm64e  <1e51658a881b3bbb95c26c7c9701e878> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1a003c000 -         0x1a17a7f1f JavaScriptCore arm64e  <05ea21999e0238dea861db68c3407b98> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1ab052000 -         0x1ab09bfff CoreAutoLayout arm64e  <889eab7c907b39f0a0cd3277e3cb1b70> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1ad521000 -         0x1ad5adbe3 dyld arm64e  <7be2b7573b3d3e918cb774f3887660c7> /usr/lib/dyld
        0x1ce2f8000 -         0x1ce300fff GraphicsServices arm64e  <4cb7e98636bf38018f495d8c3c4a2127> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1d252c000 -         0x1d2565fef libsystem_kernel.dylib arm64e  <db493af363b132209dd8dd4f86bddfc8> /usr/lib/system/libsystem_kernel.dylib
        0x1e5ec4000 -         0x1e5edfffb libc++abi.dylib arm64e  <e14c495696043a9f9ec7f15a261f9b43> /usr/lib/libc++abi.dylib
        0x1e5fb3000 -         0x1e5fbffff libsystem_pthread.dylib arm64e  <a70c0def058c3cb09ec1453aa7f39df9> /usr/lib/system/libsystem_pthread.dylib

EOF
