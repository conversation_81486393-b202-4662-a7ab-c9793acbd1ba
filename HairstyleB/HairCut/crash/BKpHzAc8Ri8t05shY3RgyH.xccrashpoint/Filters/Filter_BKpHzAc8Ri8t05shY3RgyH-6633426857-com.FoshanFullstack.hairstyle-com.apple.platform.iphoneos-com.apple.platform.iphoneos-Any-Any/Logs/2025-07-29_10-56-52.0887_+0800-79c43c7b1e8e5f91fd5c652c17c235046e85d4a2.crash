Incident Identifier: BF4CC644-0EA9-4E6E-8A48-65F9082A8158
Distributor ID:      com.apple.TestFlight
Hardware Model:      iPhone15,4
Process:             发型测试 [2595]
Path:                /private/var/containers/Bundle/Application/6BD7804D-53D9-4254-A764-C4BAEEDA7998/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (7)
AppStoreTools:       16F7
AppVariant:          1:iPhone15,4:18
Beta:                YES
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [1185]

Date/Time:           2025-07-29 10:56:52.0887 +0800
Launch Time:         2025-07-29 10:56:51.9023 +0800
OS Version:          iPhone OS 18.1 (22B83)
Release Type:        User
Baseband Version:    2.20.03
Report Version:      104

Exception Type:  EXC_CRASH (SIGABRT)
Exception Codes: 0x0000000000000000, 0x0000000000000000
Termination Reason: DYLD 4 Symbol missing
Symbol not found: ___cxa_current_primary_exception
Referenced from: <957815E6-B74C-3746-B9C6-E197C5F62536> /Volumes/VOLUME/*/发型测试.app/发型测试
Expected in:     <491F481B-D014-381C-904E-AED69C09F984> /usr/lib/libc++.1.dylib
(terminated at launch; ignore backtrace)

Triggered by Thread:  0


Thread 0 Crashed:
0   dyld                          	0x00000001b7d9b888 __abort_with_payload + 8 (:-1)
1   dyld                          	0x00000001b7da24dc abort_with_payload_wrapper_internal + 104 (terminate_with_reason.c:102)
2   dyld                          	0x00000001b7da2510 abort_with_payload + 16 (terminate_with_reason.c:124)
3   dyld                          	0x00000001b7d3955c dyld4::halt(char const*, dyld4::StructuredError const*) + 300 (DyldProcessConfig.cpp:3038)
4   dyld                          	0x00000001b7d44fe0 dyld4::prepare(dyld4::APIs&, dyld3::MachOAnalyzer const*) + 4124 (dyldMain.cpp:0)
5   dyld                          	0x00000001b7d699f8 dyld4::start(dyld4::KernelArgs*, void*, void*)::$_0::operator()() const + 544 (dyldMain.cpp:1322)
6   dyld                          	0x00000001b7d62cb0 start + 2188 (dyldMain.cpp:1299)


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000006   x1: 0x0000000000000004   x2: 0x000000016d3bd490   x3: 0x00000000000000bb
    x4: 0x000000016d3bd090   x5: 0x0000000000000000   x6: 0x0000000000000000   x7: 0x0000000000000650
    x8: 0x0000000000000020   x9: 0x000000016d3bd005  x10: 0x000000000000000a  x11: 0x0000000000000000
   x12: 0x0000000000000032  x13: 0x0000000000000000  x14: 0x000000020a39b74c  x15: 0x000000016d3bf370
   x16: 0x0000000000000209  x17: 0x00000001b7d37fd4  x18: 0x0000000000000000  x19: 0x0000000000000000
   x20: 0x000000016d3bd090  x21: 0x00000000000000bb  x22: 0x000000016d3bd490  x23: 0x0000000000000004
   x24: 0x0000000000000006  x25: 0x0000000000001690  x26: 0x000000016d3bf4e0  x27: 0x00000001f5e23fd0
   x28: 0x0000000000000000   fp: 0x000000016d3bd060   lr: 0x00000001b7da24dc
    sp: 0x000000016d3bd020   pc: 0x00000001b7d9b888 cpsr: 0x80001000
   esr: 0x56000080  Address size fault


Binary Images:
        0x102a30000 -         0x10372bfff 发型测试 arm64  <957815e6b74c3746b9c6e197c5f62536> /private/var/containers/Bundle/Application/6BD7804D-53D9-4254-A764-C4BAEEDA7998/发型测试.app/发型测试
        0x1b7d2f000 -         0x1b7db299f dyld arm64e  <3060d36a16ce3c3a92583881459f5714> /usr/lib/dyld

EOF
