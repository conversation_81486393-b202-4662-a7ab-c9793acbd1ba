Incident Identifier: DF8D29F9-959B-4D63-B634-2A77C1C4E774
Hardware Model:      iPhone11,8
Process:             发型测试 [50567]
Path:                /private/var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             2 (2.0.0)
AppStoreTools:       16C5031b
AppVariant:          1:iPhone11,8:12.2
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [4155]


Date/Time:           2025-03-19 09:53:46.2530 +0800
Launch Time:         2025-03-19 09:53:46.2045 +0800
OS Version:          iPhone OS 12.4.1 (16G102)
Baseband Version:    1.06.02
Report Version:      104

Exception Type:  EXC_CRASH (SIGABRT)
Exception Codes: 0x0000000000000000, 0x0000000000000000
Exception Note:  EXC_CORPSE_NOTIFY
Termination Description: DYLD, re-exported symbol 'unknown' not found for image Foundation expected re-exported in CFNetwork, node=0x228934a0a in /private/var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
Triggered by Thread:  0

Thread 0 Crashed:
0   dyld                          	0x00000001042ce678 __abort_with_payload + 8
1   dyld                          	0x00000001042cdc28 abort_with_payload_wrapper_internal + 104 (terminate_with_reason.c:72)
2   dyld                          	0x00000001042cdc5c abort_with_payload + 16 (terminate_with_reason.c:94)
3   dyld                          	0x000000010428cb4c dyld::halt(char const*) + 308 (dyld.cpp:4024)
4   dyld                          	0x000000010428fb0c dyld::_main(macho_header const*, unsigned long, int, char const**, char const**, char const**, unsigned long*) + 5536 (dyld.cpp:6519)
5   dyld                          	0x0000000104289044 _dyld_start + 68

Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000006   x1: 0x0000000000000009   x2: 0x000000016d92a980   x3: 0x0000000000000014
    x4: 0x000000016d92a580   x5: 0x0000000000000000   x6: 0x0000000000000001   x7: 0x0000000000000000
    x8: 0x0000000000000020   x9: 0x0000000000000009  x10: 0x656d6172662e6574  x11: 0x5354542f6b726f77
   x12: 0x77656d6172662e65  x13: 0x445354542f6b726f  x14: 0x616c506576694c4b  x15: 0x006574694c726579
   x16: 0x0000000000000209  x17: 0x0000000000000000  x18: 0x0000000000000000  x19: 0x0000000000000000
   x20: 0x000000016d92a580  x21: 0x0000000000000014  x22: 0x000000016d92a980  x23: 0x0000000000000009
   x24: 0x0000000000000006  x25: 0x000000000000002f  x26: 0x00000001042e7970  x27: 0x00000001042e7000
   x28: 0x00000001042e7d90   fp: 0x000000016d92a550   lr: 0x00000001042cdc28
    sp: 0x000000016d92a510   pc: 0x00000001042ce678 cpsr: 0x40000000

Binary Images:
0x1024d4000 - 0x102f07fff 发型测试 arm64  <cc6cf938942c3fc09ce8738c466fb42a> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/发型测试
0x1041d0000 - 0x1041e7fff FBLPromises arm64  <1f7247bc92323aaea8fd60a58f93d904> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
0x104204000 - 0x10421ffff Promises arm64  <0e3b1d6ea0cd397baa1c76bac362e169> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/Promises.framework/Promises
0x104244000 - 0x104257fff Reachability arm64  <d351f9e9160c3747a3126f141f76340e> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/Reachability.framework/Reachability
0x104288000 - 0x1042e3fff dyld arm64e  <8235e4bf8b703da69833d6fd991966b3> /usr/lib/dyld
0x10434c000 - 0x1044e3fff Alamofire arm64  <fd14afff2a85358b83de1a972458336f> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/Alamofire.framework/Alamofire
0x1046c4000 - 0x10472ffff BSImagePicker arm64  <03aa03dbd3443f27bb2407777d2b1e91> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
0x1047e8000 - 0x104867fff SDWebImage arm64  <94f7389f01403ae0b2e5326ecd9bbbd2> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
0x1048f0000 - 0x104917fff SnapKit arm64  <6bb9e949f3153d1a93a0cd71a8f774ef> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/SnapKit.framework/SnapKit
0x104968000 - 0x1049b3fff Starscream arm64  <13b76bc86c9e3562a1186774ab9f8278> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/Starscream.framework/Starscream
0x104a28000 - 0x104a4ffff SwiftyJSON arm64  <09ef1d21ba2b31f9a00fa77e9d0b6a95> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
0x104a84000 - 0x104c7bfff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
0x104e2c000 - 0x104e57fff TTSDKCore arm64  <c31c78044ec33e10923b655cd255f451> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
0x104ec0000 - 0x104f5bfff TTSDKLiveBase arm64  <a20645993829306cb3eb6cf7f2c51b4e> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
0x1050a8000 - 0x1051d7fff TTSDKLivePlayerLite arm64  <0225f99abbf43f989c8510ccab8311ce> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
0x105314000 - 0x1054c7fff TTSDKPlayerCoreLiveLite arm64  <fe27e5242e4a3b6987aa61d905a07bb5> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
0x10565c000 - 0x105667fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
0x10567c000 - 0x10568bfff TTSDKStrategyLite arm64  <3b1598d2ae913d51b8bf5da7553ed0fd> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
0x10569c000 - 0x1056a7fff TTSDKTTFFmpegLiveLite arm64  <85ee59668139318cabddcc2173ff607b> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
0x1056b8000 - 0x10571bfff TTSDKTools arm64  <0617c098c09d36d7b882a5df8bfb447c> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
0x105750000 - 0x10577bfff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
0x1057bc000 - 0x105827fff ttcrypto arm64  <********************************> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
0x1058a0000 - 0x1058e3fff libswift_Concurrency.dylib arm64  <346a0aa4e04e30568a040255f64f2dd3> /var/containers/Bundle/Application/8FFB4E13-DC76-453E-AFA3-B238BA8475B4/发型测试.app/Frameworks/libswift_Concurrency.dylib
0x1eb287000 - 0x1eb288fff libSystem.B.dylib arm64e  <3232abc44d0b350b91e4372e4b97a7e3> /usr/lib/libSystem.B.dylib
0x1eb289000 - 0x1eb2e1fff libc++.1.dylib arm64e  <d4063e21f493336286465788ebae505b> /usr/lib/libc++.1.dylib
0x1eb2e2000 - 0x1eb2f5fff libc++abi.dylib arm64e  <7c824a7ad71b3c368ab16c7ece88e15e> /usr/lib/libc++abi.dylib
0x1eb2f6000 - 0x1eba7dfff libobjc.A.dylib arm64e  <e8055992eb2530e0b15a82af9bd334e4> /usr/lib/libobjc.A.dylib
0x1eba7e000 - 0x1eba82fff libcache.dylib arm64e  <e7f774b5f94c35bca2e209117f6c6f80> /usr/lib/system/libcache.dylib
0x1eba83000 - 0x1eba8efff libcommonCrypto.dylib arm64e  <23637b31c4a13122b0b8e881766db89e> /usr/lib/system/libcommonCrypto.dylib
0x1eba8f000 - 0x1eba92fff libcompiler_rt.dylib arm64e  <4a6a8c9a64773fcfaadf87f7ea372fd1> /usr/lib/system/libcompiler_rt.dylib
0x1eba93000 - 0x1eba9bfff libcopyfile.dylib arm64e  <8039305e9f733efc9567b5ec638fe57f> /usr/lib/system/libcopyfile.dylib
0x1eba9c000 - 0x1ebb00fff libcorecrypto.dylib arm64e  <a8fc7d6fefc43f669a2cae628cbcadaa> /usr/lib/system/libcorecrypto.dylib
0x1ebb01000 - 0x1ebb3bfff libdispatch.dylib arm64e  <9bd843073b6a3b55af071d3566e62949> /usr/lib/system/libdispatch.dylib
0x1ebb3c000 - 0x1ebb66fff libdyld.dylib arm64e  <5666efd3f0dd3b1cae0b0fac7e92121c> /usr/lib/system/libdyld.dylib
0x1ebb67000 - 0x1ebb67fff liblaunch.dylib arm64e  <1fba029148b43d50890e5de9087a9e14> /usr/lib/system/liblaunch.dylib
0x1ebb68000 - 0x1ebb6dfff libmacho.dylib arm64e  <54bfbb1b83dc32eea96366055f020f07> /usr/lib/system/libmacho.dylib
0x1ebb6e000 - 0x1ebb6ffff libremovefile.dylib arm64e  <40bcfa0769003519bd1f7da26c112023> /usr/lib/system/libremovefile.dylib
0x1ebb70000 - 0x1ebb87fff libsystem_asl.dylib arm64e  <c2a7888285e23e23bd13b5a94995fb0c> /usr/lib/system/libsystem_asl.dylib
0x1ebb88000 - 0x1ebb88fff libsystem_blocks.dylib arm64e  <736168c0cbc137c695962e4deb6c0543> /usr/lib/system/libsystem_blocks.dylib
0x1ebb89000 - 0x1ebc08fff libsystem_c.dylib arm64e  <75ac4c9d5bee3060b6da8cfa1bddb2a2> /usr/lib/system/libsystem_c.dylib
0x1ebc09000 - 0x1ebc0dfff libsystem_configuration.dylib arm64e  <17fccf2b32293851ac6195c170891e2a> /usr/lib/system/libsystem_configuration.dylib
0x1ebc0e000 - 0x1ebc14fff libsystem_containermanager.dylib arm64e  <d18cb9ebd5303cca8e1e14a515d877b7> /usr/lib/system/libsystem_containermanager.dylib
0x1ebc15000 - 0x1ebc16fff libsystem_coreservices.dylib arm64e  <786f52436b513c9bb8d381dbf84eda2c> /usr/lib/system/libsystem_coreservices.dylib
0x1ebc17000 - 0x1ebc1dfff libsystem_darwin.dylib arm64e  <92172b82e4ec34f1a526d6cf5bdae0a5> /usr/lib/system/libsystem_darwin.dylib
0x1ebc1e000 - 0x1ebc24fff libsystem_dnssd.dylib arm64e  <88f2520e57cb379c99df505d24152464> /usr/lib/system/libsystem_dnssd.dylib
0x1ebc25000 - 0x1ebc64fff libsystem_info.dylib arm64e  <c7024de6155d3494a730f7fe5cfe7a83> /usr/lib/system/libsystem_info.dylib
0x1ebc65000 - 0x1ebc90fff libsystem_kernel.dylib arm64e  <0b4662a19e86370bbc7d38aba1840df1> /usr/lib/system/libsystem_kernel.dylib
0x1ebc91000 - 0x1ebcbefff libsystem_m.dylib arm64e  <4e6f0320da2039aa88a45474adf9aa25> /usr/lib/system/libsystem_m.dylib
0x1ebcbf000 - 0x1ebce3fff libsystem_malloc.dylib arm64e  <44a074204d4e36059619a2cc9df7b204> /usr/lib/system/libsystem_malloc.dylib
0x1ebce4000 - 0x1ebceffff libsystem_networkextension.dylib arm64e  <9c80d51b6b6e31b5aa6016daf0924624> /usr/lib/system/libsystem_networkextension.dylib
0x1ebcf0000 - 0x1ebcf6fff libsystem_notify.dylib arm64e  <2da4348cf18a3b97a2cbd802839cdf9d> /usr/lib/system/libsystem_notify.dylib
0x1ebcf7000 - 0x1ebcfdfff libsystem_platform.dylib arm64e  <b809eb906d86395b818f7cafcfb764d8> /usr/lib/system/libsystem_platform.dylib
0x1ebcfe000 - 0x1ebd08fff libsystem_pthread.dylib arm64e  <92bd43ed7fb13aa5a8697f5ebd64cf93> /usr/lib/system/libsystem_pthread.dylib
0x1ebd09000 - 0x1ebd0bfff libsystem_sandbox.dylib arm64e  <********************************> /usr/lib/system/libsystem_sandbox.dylib
0x1ebd0c000 - 0x1ebd13fff libsystem_symptoms.dylib arm64e  <8c3aca466aa53a0a850e724e407e7305> /usr/lib/system/libsystem_symptoms.dylib
0x1ebd14000 - 0x1ebd28fff libsystem_trace.dylib arm64e  <65151ab3122439489b4b6024484d2fe0> /usr/lib/system/libsystem_trace.dylib
0x1ebd29000 - 0x1ebd2efff libunwind.dylib arm64e  <252dce3fa8f63521a43d40a1fc51a4f8> /usr/lib/system/libunwind.dylib
0x1ebd2f000 - 0x1ebd5dfff libxpc.dylib arm64e  <fb445b1a85353d0ab7fe63e2ba533553> /usr/lib/system/libxpc.dylib
0x1ebd5e000 - 0x1ebfc6fff libicucore.A.dylib arm64e  <97eee2de71c5371eb1e5dc3d9a142da4> /usr/lib/libicucore.A.dylib
0x1ebfc7000 - 0x1ebfd8fff libz.1.dylib arm64e  <d682fa2534c5323cb381671a6a57b6f8> /usr/lib/libz.1.dylib
0x1ebfd9000 - 0x1ec33cfff CoreFoundation arm64e  <bf0dd002344d3cd59b1d57b944789379> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
0x1ec33d000 - 0x1ec34dfff libbsm.0.dylib arm64e  <aa07524e9de839e8bf5c7aa288151a71> /usr/lib/libbsm.0.dylib
0x1ec34e000 - 0x1ec34efff libenergytrace.dylib arm64e  <3a39bb95080836c895e79a0e0828a62b> /usr/lib/libenergytrace.dylib
0x1ec34f000 - 0x1ec3dffff IOKit arm64e  <aeffbf4e956330eeb9bef4eee0c34740> /System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x1ec3e0000 - 0x1ec4cafff libxml2.2.dylib arm64e  <bb961564beae38e0b41204158d8840f2> /usr/lib/libxml2.2.dylib
0x1ec4cb000 - 0x1ec4d8fff libbz2.1.0.dylib arm64e  <acf8d6ac354b3b878df95aa9cdb4cb16> /usr/lib/libbz2.1.0.dylib
0x1ec4d9000 - 0x1ec4f1fff liblzma.5.dylib arm64e  <6516c42e1ea1328f814285253e7357aa> /usr/lib/liblzma.5.dylib
0x1ec4f2000 - 0x1ec66bfff libsqlite3.dylib arm64e  <b5b4b1f691fb3ca891d06f245a3036eb> /usr/lib/libsqlite3.dylib
0x1ec66c000 - 0x1ec6a1fff libMobileGestalt.dylib arm64e  <c152ba19947631d799de9824256e437e> /usr/lib/libMobileGestalt.dylib
0x1ec6a2000 - 0x1eca59fff CFNetwork arm64e  <7bac5947136d32178a2c8ba7bb9ed387> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
0x1eca5a000 - 0x1ecd54fff Foundation arm64e  <94e29ad955f532de9be2aefc282a4878> /System/Library/Frameworks/Foundation.framework/Foundation
0x1ecd55000 - 0x1ece6bfff Security arm64e  <0f262a3d95d436ec88c0330791cdf46e> /System/Library/Frameworks/Security.framework/Security
0x1ece6c000 - 0x1eced5fff SystemConfiguration arm64e  <31c6354f99de3a869d508df1733535b7> /System/Library/Frameworks/SystemConfiguration.framework/SystemConfiguration
0x1eced6000 - 0x1ecf08fff libCRFSuite.dylib arm64e  <3722583ea27137f7bb2c8921db2f1bd3> /usr/lib/libCRFSuite.dylib
0x1ecf09000 - 0x1ecf1ffff libapple_nghttp2.dylib arm64e  <57bb3ef0ecfd3e1c8ffb9e1b26eadb58> /usr/lib/libapple_nghttp2.dylib
0x1ecf20000 - 0x1ecf49fff libarchive.2.dylib arm64e  <c8dba8a058583e6da6bde2eac60df62e> /usr/lib/libarchive.2.dylib
0x1ecf4a000 - 0x1ed012fff libboringssl.dylib arm64e  <a0b3725e28c23af6b329c49342d21dfc> /usr/lib/libboringssl.dylib
0x1ed013000 - 0x1ed029fff libcoretls.dylib arm64e  <e9c64531bb9a3a1599599856ec31b29d> /usr/lib/libcoretls.dylib
0x1ed02a000 - 0x1ed02bfff libcoretls_cfhelpers.dylib arm64e  <662ef556b2b63b0b9ad62a070c97fe46> /usr/lib/libcoretls_cfhelpers.dylib
0x1ed02c000 - 0x1ed02dfff liblangid.dylib arm64e  <bc67a1a2bd2a351aba8ad21929516d52> /usr/lib/liblangid.dylib
0x1ed02e000 - 0x1ed3a3fff libnetwork.dylib arm64e  <2ff76d0761b334e388292f05d5d5d4f1> /usr/lib/libnetwork.dylib
0x1ed3a4000 - 0x1ed3ddfff libpcap.A.dylib arm64e  <de784b132bc13022976fdba010f02216> /usr/lib/libpcap.A.dylib
0x1ed3de000 - 0x1ed43bfff libusrtcp.dylib arm64e  <65b120e609a6364d95959b05ffcf9340> /usr/lib/libusrtcp.dylib
0x1ed43c000 - 0x1ed448fff IOSurface arm64e  <f4300e4aec653027b3c1092180d22876> /System/Library/Frameworks/IOSurface.framework/IOSurface
0x1ed449000 - 0x1ed4fcfff libBLAS.dylib arm64e  <6ff88803523835c2a099213aad6d52bc> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libBLAS.dylib
0x1ed4fd000 - 0x1ed81afff libLAPACK.dylib arm64e  <24d822e17a3437efb6e60a333f2543ca> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libLAPACK.dylib
0x1ed81b000 - 0x1eda8afff vImage arm64e  <32162390cc613ad890791f2a1852d2b2> /System/Library/Frameworks/Accelerate.framework/Frameworks/vImage.framework/vImage
0x1eda8b000 - 0x1eda9cfff libSparseBLAS.dylib arm64e  <2cedb9acd7f83cce80d83939579da4f3> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libSparseBLAS.dylib
0x1eda9d000 - 0x1edaf7fff libvMisc.dylib arm64e  <ed9fd511d98e3b92a8c78d3582c669a4> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libvMisc.dylib
0x1edaf8000 - 0x1edb26fff libBNNS.dylib arm64e  <f713446d9f3e3c47bba5b57bd38151fe> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libBNNS.dylib
0x1edb27000 - 0x1edb3bfff libLinearAlgebra.dylib arm64e  <9578c2dcd1c33be68bd6e75f40e04d95> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libLinearAlgebra.dylib
0x1edb3c000 - 0x1edb40fff libQuadrature.dylib arm64e  <e7564e331373334786ad42b3350e82c3> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libQuadrature.dylib
0x1edb41000 - 0x1edbaffff libSparse.dylib arm64e  <839c702747ea383da7176bf32a3c1445> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libSparse.dylib
0x1edbb0000 - 0x1edc40fff libvDSP.dylib arm64e  <d24e9953f2763491b69acd2d37289b25> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/libvDSP.dylib
0x1edc41000 - 0x1edc41fff vecLib arm64e  <8b237e6d198d3b7385c7502b39f51a2b> /System/Library/Frameworks/Accelerate.framework/Frameworks/vecLib.framework/vecLib
0x1edc42000 - 0x1edc42fff Accelerate arm64e  <fdf21c13f4ce3be59604be3dd510e5b8> /System/Library/Frameworks/Accelerate.framework/Accelerate
0x1edc43000 - 0x1edc5bfff libcompression.dylib arm64e  <8a2a0856152b3810847afa93294332bc> /usr/lib/libcompression.dylib
0x1edc5c000 - 0x1ee203fff CoreGraphics arm64e  <b099f7812b1a3b0a9254f8fedbb50c69> /System/Library/Frameworks/CoreGraphics.framework/CoreGraphics
0x1ee204000 - 0x1ee209fff IOAccelerator arm64e  <b4966577622e3e3b819db89e5386e9ce> /System/Library/PrivateFrameworks/IOAccelerator.framework/IOAccelerator
0x1ee20a000 - 0x1ee20ffff libCoreFSCache.dylib arm64e  <aa231834627e3d4d9b513d3dc099b284> /System/Library/Frameworks/OpenGLES.framework/libCoreFSCache.dylib
0x1ee210000 - 0x1ee2b6fff Metal arm64e  <b7bfdf7ba07d302eaa91a555e1c3558c> /System/Library/Frameworks/Metal.framework/Metal
0x1ee2b7000 - 0x1ee2c9fff GraphicsServices arm64e  <76e795a126ce361ebb0ce1a8b674bddb> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
0x1ee2ca000 - 0x1ee2cafff MobileCoreServices arm64e  <7229b97b51143028bcfa1cff570cda2d> /System/Library/Frameworks/MobileCoreServices.framework/MobileCoreServices
0x1ee2cb000 - 0x1ee2cdfff IOSurfaceAccelerator arm64e  <82de12c3c22333f187d9ca317a191afa> /System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/IOSurfaceAccelerator
0x1ee2ce000 - 0x1ee310fff AppleJPEG arm64e  <baa80f7c968b32528fd1bf950924187a> /System/Library/PrivateFrameworks/AppleJPEG.framework/AppleJPEG
0x1ee311000 - 0x1ee8aefff ImageIO arm64e  <e2b5182a2e1034bd86ef10c5c8a86efb> /System/Library/Frameworks/ImageIO.framework/ImageIO
0x1ee8af000 - 0x1ee920fff BaseBoard arm64e  <2a899d132306318aadd2226d86a082ca> /System/Library/PrivateFrameworks/BaseBoard.framework/BaseBoard
0x1ee921000 - 0x1ee937fff AssertionServices arm64e  <703ae00378ef397ea8f9703294e3ffdb> /System/Library/PrivateFrameworks/AssertionServices.framework/AssertionServices
0x1ee938000 - 0x1ee940fff CorePhoneNumbers arm64e  <241ec29540fc3cbc897d71b03c7f0c9b> /System/Library/PrivateFrameworks/CorePhoneNumbers.framework/CorePhoneNumbers
0x1ee941000 - 0x1ee985fff AppSupport arm64e  <a484458e145a3a0a895f7429a295b208> /System/Library/PrivateFrameworks/AppSupport.framework/AppSupport
0x1ee986000 - 0x1ee99efff CrashReporterSupport arm64e  <9cc15d781f20331cb5f6180c0d15de8d> /System/Library/PrivateFrameworks/CrashReporterSupport.framework/CrashReporterSupport
0x1ee99f000 - 0x1ee9a4fff AggregateDictionary arm64e  <eaaf29c59af73b5589a7ec2eac824066> /System/Library/PrivateFrameworks/AggregateDictionary.framework/AggregateDictionary
0x1ee9a5000 - 0x1eea1ffff libTelephonyUtilDynamic.dylib arm64e  <3b91d893c3c534a4b907a97579693cd7> /usr/lib/libTelephonyUtilDynamic.dylib
0x1eea20000 - 0x1eea3ffff ProtocolBuffer arm64e  <a58ff0d767cf36a1ad5507134976a6ae> /System/Library/PrivateFrameworks/ProtocolBuffer.framework/ProtocolBuffer
0x1eea40000 - 0x1eea6ffff MobileKeyBag arm64e  <8fdc867fd22b38d084d0f883bffe463e> /System/Library/PrivateFrameworks/MobileKeyBag.framework/MobileKeyBag
0x1eea70000 - 0x1eeaacfff BackBoardServices arm64e  <50caea0e732e385898a0c5e3db050c61> /System/Library/PrivateFrameworks/BackBoardServices.framework/BackBoardServices
0x1eeaad000 - 0x1eeb13fff FrontBoardServices arm64e  <8a9e835d767b3c1487524934faa1ba97> /System/Library/PrivateFrameworks/FrontBoardServices.framework/FrontBoardServices
0x1eeb14000 - 0x1eeb5bfff SpringBoardServices arm64e  <a146a6a954fb39588eb23bca8fccaba6> /System/Library/PrivateFrameworks/SpringBoardServices.framework/SpringBoardServices
0x1eeb5c000 - 0x1eeb6ffff PowerLog arm64e  <7f7bd99ead8d38c1bd08e8b431d55e81> /System/Library/PrivateFrameworks/PowerLog.framework/PowerLog
0x1eeb70000 - 0x1eeb8bfff CommonUtilities arm64e  <73b67139aff13eefb4ec719d83ae65ae> /System/Library/PrivateFrameworks/CommonUtilities.framework/CommonUtilities
0x1eeb8c000 - 0x1eeb97fff liblockdown.dylib arm64e  <f3e841874d9035d0a4d47e89caf13fd5> /usr/lib/liblockdown.dylib
0x1eeb98000 - 0x1eeec3fff CoreData arm64e  <2f8299a3a0e936018e9e0e30d4acbfce> /System/Library/Frameworks/CoreData.framework/CoreData
0x1eeec4000 - 0x1eeecbfff TCC arm64e  <746c0c6d677b3173bb4813e8e249f4e1> /System/Library/PrivateFrameworks/TCC.framework/TCC
0x1eeecc000 - 0x1eeed3fff libcupolicy.dylib arm64e  <0a2cba3c8b7b3d1199c57ba4526e178b> /usr/lib/libcupolicy.dylib
0x1eeed4000 - 0x1eefa8fff CoreTelephony arm64e  <d8c67cf2c5ca3aa4b5fa5beaacaa2016> /System/Library/Frameworks/CoreTelephony.framework/CoreTelephony
0x1eefa9000 - 0x1ef003fff Accounts arm64e  <0b3736b079663044b21f3ceb65fe0013> /System/Library/Frameworks/Accounts.framework/Accounts
0x1ef004000 - 0x1ef027fff AppleSauce arm64e  <30b809ae1fc23bccb8febd30e5e0d0c5> /System/Library/PrivateFrameworks/AppleSauce.framework/AppleSauce
0x1ef028000 - 0x1ef032fff DataMigration arm64e  <2d3681c1451f3ab584c08142bf95b664> /System/Library/PrivateFrameworks/DataMigration.framework/DataMigration
0x1ef033000 - 0x1ef039fff Netrb arm64e  <0f9f8ff92a703e2a8af3381f54a4723e> /System/Library/PrivateFrameworks/Netrb.framework/Netrb
0x1ef03a000 - 0x1ef06bfff PersistentConnection arm64e  <c0321267fba63012921eac5153add9ed> /System/Library/PrivateFrameworks/PersistentConnection.framework/PersistentConnection
0x1ef06c000 - 0x1ef07bfff libmis.dylib arm64e  <74dde10eca5737afa801cdddff5aedc3> /usr/lib/libmis.dylib
0x1ef07c000 - 0x1ef187fff ManagedConfiguration arm64e  <2e91bf3143a23aec95b3a02d46822519> /System/Library/PrivateFrameworks/ManagedConfiguration.framework/ManagedConfiguration
0x1ef188000 - 0x1ef18dfff libReverseProxyDevice.dylib arm64e  <78bf6665adc73eab9aa384ec38a6abe7> /usr/lib/libReverseProxyDevice.dylib
0x1ef18e000 - 0x1ef1a0fff libamsupport.dylib arm64e  <c2af8caeae86313a8b2163e78342fc70> /usr/lib/libamsupport.dylib
0x1ef1a1000 - 0x1ef1a6fff libCoreVMClient.dylib arm64e  <dc02efe560aa3cc193f906acc8da1a0d> /System/Library/Frameworks/OpenGLES.framework/libCoreVMClient.dylib
0x1ef1a7000 - 0x1ef1a8fff libCVMSPluginSupport.dylib arm64e  <0941df7a72283c7ea2b01137aefea12e> /System/Library/Frameworks/OpenGLES.framework/libCVMSPluginSupport.dylib
0x1ef1a9000 - 0x1ef1acfff libutil.dylib arm64e  <e4155176ec533f1b98d511d54a846ecc> /usr/lib/libutil.dylib
0x1ef1ad000 - 0x1ef1ebfff libGLImage.dylib arm64e  <42a3d36513903a35b8c242e8b5b585b1> /System/Library/Frameworks/OpenGLES.framework/libGLImage.dylib
0x1ef1ec000 - 0x1ef26afff APFS arm64e  <9ba7be23cd4e32108cb099b5dc1a8ea4> /System/Library/PrivateFrameworks/APFS.framework/APFS
0x1ef26b000 - 0x1ef29cfff MediaKit arm64e  <1afd6f3dfa313567b14078419c799996> /System/Library/PrivateFrameworks/MediaKit.framework/MediaKit
0x1ef29d000 - 0x1ef2b7fff libSERestoreInfo.dylib arm64e  <48b8d23b59893ca3b1d9c2a2d9d8cc33> /usr/lib/updaters/libSERestoreInfo.dylib
0x1ef2be000 - 0x1ef2fcfff DiskImages arm64e  <63af3a3388c83ce4a2a1456902ea2985> /System/Library/PrivateFrameworks/DiskImages.framework/DiskImages
0x1ef2fd000 - 0x1ef306fff libGFXShared.dylib arm64e  <5e8b64457df93dfaad3d667563e80bb2> /System/Library/Frameworks/OpenGLES.framework/libGFXShared.dylib
0x1ef307000 - 0x1ef355fff libauthinstall.dylib arm64e  <2e0e71d413453828a0884d19306b2328> /usr/lib/libauthinstall.dylib
0x1ef356000 - 0x1ef35ffff IOMobileFramebuffer arm64e  <4103eff15ec53d82979ab91a19a74829> /System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/IOMobileFramebuffer
0x1ef360000 - 0x1ef36cfff OpenGLES arm64e  <258fbe63eb6c34dd8a38b4f4e8de0f76> /System/Library/Frameworks/OpenGLES.framework/OpenGLES
0x1ef36d000 - 0x1ef48efff ColorSync arm64e  <dd6a34c853aa367282bf61dde4e3a91f> /System/Library/PrivateFrameworks/ColorSync.framework/ColorSync
0x1ef48f000 - 0x1ef4bffff CoreVideo arm64e  <3de0fcd917cf322aaa19fd05382cd4c5> /System/Library/Frameworks/CoreVideo.framework/CoreVideo
0x1ef4c0000 - 0x1ef4c1fff libCTGreenTeaLogger.dylib arm64e  <f243e496ce9a39edaa891434e4dda0f6> /usr/lib/libCTGreenTeaLogger.dylib
0x1ef4c2000 - 0x1ef619fff CoreAudio arm64e  <1860e225ebd13c4aaa5d48387439cfdf> /System/Library/Frameworks/CoreAudio.framework/CoreAudio
0x1ef61a000 - 0x1ef639fff CoreAnalytics arm64e  <d7fd0f6ceb243503809784e79c01ed7f> /System/Library/PrivateFrameworks/CoreAnalytics.framework/CoreAnalytics
0x1ef63a000 - 0x1ef63dfff UserFS arm64e  <89b1af872c553882b9743191b8a5a91b> /System/Library/PrivateFrameworks/UserFS.framework/UserFS
0x1ef63e000 - 0x1ef7f8fff CoreMedia arm64e  <3d73a904ec113a50b94dd18e2135afd2> /System/Library/Frameworks/CoreMedia.framework/CoreMedia
0x1ef7f9000 - 0x1ef80bfff libprotobuf-lite.dylib arm64e  <b7e34815f2e538b1889f2c4da70600f4> /usr/lib/libprotobuf-lite.dylib
0x1ef80c000 - 0x1ef86bfff libprotobuf.dylib arm64e  <a07e09ee0b813cc9bf6070f42a78511b> /usr/lib/libprotobuf.dylib
0x1ef86c000 - 0x1efb8ffff libAWDSupportFramework.dylib arm64e  <bdeb5f841ab238e399440aa9488b5db3> /usr/lib/libAWDSupportFramework.dylib
0x1efb90000 - 0x1efbd3fff WirelessDiagnostics arm64e  <83846187b38637908f80d915db813e45> /System/Library/PrivateFrameworks/WirelessDiagnostics.framework/WirelessDiagnostics
0x1efbd4000 - 0x1efc94fff VideoToolbox arm64e  <********************************> /System/Library/Frameworks/VideoToolbox.framework/VideoToolbox
0x1efc95000 - 0x1efd9efff libFontParser.dylib arm64e  <16ffc99738e43b1791854aabcfe7b779> /System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x1efd9f000 - 0x1efd9ffff FontServices arm64e  <c27e7c3e13a43c13bc4226965e11a063> /System/Library/PrivateFrameworks/FontServices.framework/FontServices
0x1efda0000 - 0x1efef6fff CoreText arm64e  <63ba549bd48d3c68b538ce1c133b09fa> /System/Library/Frameworks/CoreText.framework/CoreText
0x1efef7000 - 0x1eff05fff IntlPreferences arm64e  <5bba0caddaed3aa98060a3b2bbc20cae> /System/Library/PrivateFrameworks/IntlPreferences.framework/IntlPreferences
0x1eff06000 - 0x1eff0ffff RTCReporting arm64e  <9b4eac2889f13051a6322139de754cea> /System/Library/PrivateFrameworks/RTCReporting.framework/RTCReporting
0x1eff10000 - 0x1effc2fff CoreBrightness arm64e  <b92362b2b9ec3f3f8afd0656d807e40a> /System/Library/PrivateFrameworks/CoreBrightness.framework/CoreBrightness
0x1effc3000 - 0x1effccfff libAudioStatistics.dylib arm64e  <f5d72a08a1813d45be00af5d9f004890> /usr/lib/libAudioStatistics.dylib
0x1effcd000 - 0x1f058dfff AudioToolbox arm64e  <********************************> /System/Library/Frameworks/AudioToolbox.framework/AudioToolbox
0x1f058e000 - 0x1f07cefff QuartzCore arm64e  <3a1757dd2c1439e0bb2dc06a2bbd26eb> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
0x1f07cf000 - 0x1f07d9fff MediaAccessibility arm64e  <249446c0942d39349ea9092d7b10e97b> /System/Library/Frameworks/MediaAccessibility.framework/MediaAccessibility
0x1f07da000 - 0x1f08ccfff libiconv.2.dylib arm64e  <e395d41f51e035c38fbdb390a5e0ae40> /usr/lib/libiconv.2.dylib
0x1f08cd000 - 0x1f08e8fff NetworkStatistics arm64e  <a4cd9c69cb4235408f69465f4c8a769a> /System/Library/PrivateFrameworks/NetworkStatistics.framework/NetworkStatistics
0x1f08e9000 - 0x1f0907fff MPSCore arm64e  <ddcd9b0a72a5362a940381eab940a8cc> /System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSCore.framework/MPSCore
0x1f0908000 - 0x1f097bfff MPSImage arm64e  <e41e165d42e03e54b8967444fe327759> /System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSImage.framework/MPSImage
0x1f097c000 - 0x1f09a0fff MPSMatrix arm64e  <ce48ded6771a3edd8133c8b21dc5d25a> /System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSMatrix.framework/MPSMatrix
0x1f09a1000 - 0x1f09affff CoreAUC arm64e  <459c1906b41c385db18a4c513898984d> /System/Library/PrivateFrameworks/CoreAUC.framework/CoreAUC
0x1f09b0000 - 0x1f1048fff MediaToolbox arm64e  <********************************> /System/Library/Frameworks/MediaToolbox.framework/MediaToolbox
0x1f1049000 - 0x1f11b7fff MPSNeuralNetwork arm64e  <45d43e38039c35d1864c16226d3ac436> /System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSNeuralNetwork.framework/MPSNeuralNetwork
0x1f11b8000 - 0x1f11b8fff MetalPerformanceShaders arm64e  <6c9827a523b7302d9534039391d67c21> /System/Library/Frameworks/MetalPerformanceShaders.framework/MetalPerformanceShaders
0x1f11b9000 - 0x1f15ccfff FaceCore arm64e  <e6c65040df8938208a6b88305f9f605f> /System/Library/PrivateFrameworks/FaceCore.framework/FaceCore
0x1f15cd000 - 0x1f15dafff GraphVisualizer arm64e  <b2b56a893d31377a93e099d9feffcf9c> /System/Library/PrivateFrameworks/GraphVisualizer.framework/GraphVisualizer
0x1f15db000 - 0x1f1813fff libFosl_dynamic.dylib arm64e  <00a403f33d9938bd973fa2b90c095290> /usr/lib/libFosl_dynamic.dylib
0x1f1814000 - 0x1f1ab2fff CoreImage arm64e  <b2318a54bfd83f56808d8902437cf1b6> /System/Library/Frameworks/CoreImage.framework/CoreImage
0x1f1ab3000 - 0x1f1cd6fff CoreMotion arm64e  <457d54813e293412939c0799ab6e0658> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
0x1f1cd7000 - 0x1f1d06fff CoreBluetooth arm64e  <e61000345c653f36becd563b4f8f17f9> /System/Library/Frameworks/CoreBluetooth.framework/CoreBluetooth
0x1f1d07000 - 0x1f1d29fff PlugInKit arm64e  <e83681b3c73a3d1cb8b23f6c9e2d27b7> /System/Library/PrivateFrameworks/PlugInKit.framework/PlugInKit
0x1f1d2a000 - 0x1f1fbcfff Celestial arm64e  <a18a7b24262d36da8bb77b43e405d138> /System/Library/PrivateFrameworks/Celestial.framework/Celestial
0x1f1fbd000 - 0x1f203ffff Quagga arm64e  <8c702de51b4a3670a9f2fa2823730cc8> /System/Library/PrivateFrameworks/Quagga.framework/Quagga
0x1f2040000 - 0x1f2135fff AVFAudio arm64e  <f68c9dba3bad38dfbf4a959b06253505> /System/Library/Frameworks/AVFoundation.framework/Frameworks/AVFAudio.framework/AVFAudio
0x1f2136000 - 0x1f233dfff AVFoundation arm64e  <47c1a2d5f6d132ccae91293a84bc3230> /System/Library/Frameworks/AVFoundation.framework/AVFoundation
0x1f233e000 - 0x1f235cfff CacheDelete arm64e  <7f41ebb2fdc731e69509b8df77fc01a9> /System/Library/PrivateFrameworks/CacheDelete.framework/CacheDelete
0x1f235d000 - 0x1f2396fff StreamingZip arm64e  <8e47e688d30e3106bad34514895cb500> /System/Library/PrivateFrameworks/StreamingZip.framework/StreamingZip
0x1f2397000 - 0x1f23a9fff CoreEmoji arm64e  <57b4f7bc0a843fc6bfac6570240543ec> /System/Library/PrivateFrameworks/CoreEmoji.framework/CoreEmoji
0x1f23aa000 - 0x1f23f9fff CoreLocationProtobuf arm64e  <bb92788693823329be45f375e7925ad6> /System/Library/PrivateFrameworks/CoreLocationProtobuf.framework/CoreLocationProtobuf
0x1f23fa000 - 0x1f2401fff SymptomDiagnosticReporter arm64e  <9f9bbec87f61397abe7e4044394be7de> /System/Library/PrivateFrameworks/SymptomDiagnosticReporter.framework/SymptomDiagnosticReporter
0x1f2402000 - 0x1f2e10fff GeoServices arm64e  <752c1f4a002a35f1bd5e754c84c044cd> /System/Library/PrivateFrameworks/GeoServices.framework/GeoServices
0x1f2e11000 - 0x1f2e2bfff MobileAsset arm64e  <d2d9c1dd631a3a5a95e4858a33384d3f> /System/Library/PrivateFrameworks/MobileAsset.framework/MobileAsset
0x1f2e2c000 - 0x1f2e67fff Lexicon arm64e  <1408d88fcda43400bbad0b204843c3a6> /System/Library/PrivateFrameworks/Lexicon.framework/Lexicon
0x1f2e68000 - 0x1f2e79fff libcmph.dylib arm64e  <ef86d4e45d9a3fa0a1ff49667eb0f17a> /usr/lib/libcmph.dylib
0x1f2e7a000 - 0x1f2f92fff LanguageModeling arm64e  <e2307012415f3031a118f689eaaf0ed4> /System/Library/PrivateFrameworks/LanguageModeling.framework/LanguageModeling
0x1f2faa000 - 0x1f3044fff CoreLocation arm64e  <f683b2c85ae738b287630dcc3ccb89b8> /System/Library/Frameworks/CoreLocation.framework/CoreLocation
0x1f3045000 - 0x1f3045fff PhoneNumbers arm64e  <a4afa060c935316ea9c138cb7b43c017> /System/Library/PrivateFrameworks/PhoneNumbers.framework/PhoneNumbers
0x1f3046000 - 0x1f3050fff libChineseTokenizer.dylib arm64e  <ba2b8f5c2b003edcabee0a2cbfcb0783> /usr/lib/libChineseTokenizer.dylib
0x1f3051000 - 0x1f3102fff libmecab_em.dylib arm64e  <15ac6572138d3927a9915b0f035a5e7d> /usr/lib/libmecab_em.dylib
0x1f3103000 - 0x1f3104fff libThaiTokenizer.dylib arm64e  <7d21902c4aca356b9e17fd34d0d32972> /usr/lib/libThaiTokenizer.dylib
0x1f3105000 - 0x1f3109fff libgermantok.dylib arm64e  <288da82a768e3074b07777360c25e60c> /usr/lib/libgermantok.dylib
0x1f310a000 - 0x1f316ffff CoreNLP arm64e  <55987f4058d53362a82a914c5b7a366b> /System/Library/PrivateFrameworks/CoreNLP.framework/CoreNLP
0x1f317c000 - 0x1f3343fff MobileSpotlightIndex arm64e  <b6390ee6fe0f3107912796a31408bf63> /System/Library/PrivateFrameworks/MobileSpotlightIndex.framework/MobileSpotlightIndex
0x1f3344000 - 0x1f33a6fff CoreSpotlight arm64e  <458671728e80395cb58774fdd84a1849> /System/Library/Frameworks/CoreSpotlight.framework/CoreSpotlight
0x1f33a7000 - 0x1f40dffff JavaScriptCore arm64e  <8f2ae7aacd1236018362df5ae43e0d5e> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
0x1f40e0000 - 0x1f40e5fff libheimdal-asn1.dylib arm64e  <d53f7ba3d9ae3ad9b8d7ffa550886ba3> /usr/lib/libheimdal-asn1.dylib
0x1f40e6000 - 0x1f4160fff libate.dylib arm64e  <dd50e1d08cab37a3b4e0bdbdc68236ff> /usr/lib/libate.dylib
0x1f4161000 - 0x1f4208fff TextureIO arm64e  <a1f97f28fd7b3e65b5f9dae0e88d7c98> /System/Library/PrivateFrameworks/TextureIO.framework/TextureIO
0x1f4209000 - 0x1f42cefff CoreUI arm64e  <fee19aa9b2003f2fa587b3a28daee832> /System/Library/PrivateFrameworks/CoreUI.framework/CoreUI
0x1f42cf000 - 0x1f42dcfff MobileIcons arm64e  <7d13f6b4d39e3f58b2cd887762434326> /System/Library/PrivateFrameworks/MobileIcons.framework/MobileIcons
0x1f42dd000 - 0x1f42ebfff AppleFSCompression arm64e  <e017aa209f143424b12489a924e985a1> /System/Library/PrivateFrameworks/AppleFSCompression.framework/AppleFSCompression
0x1f42ec000 - 0x1f4356fff TextInput arm64e  <0276b2164ca939b7ae9bd7682c43b8fb> /System/Library/PrivateFrameworks/TextInput.framework/TextInput
0x1f4380000 - 0x1f43b3fff DataDetectorsCore arm64e  <72cba66a1c0e30e79f1130f4f9e79e79> /System/Library/PrivateFrameworks/DataDetectorsCore.framework/DataDetectorsCore
0x1f43b4000 - 0x1f4448fff FileProvider arm64e  <5a6ed142c3ce382080a29cb6c1bad50f> /System/Library/Frameworks/FileProvider.framework/FileProvider
0x1f4449000 - 0x1f453ffff NLP arm64e  <1ef2f8d3a2733946b78176250e5da3f9> /System/Library/PrivateFrameworks/NLP.framework/NLP
0x1f4540000 - 0x1f4616fff ProofReader arm64e  <005d1624382d3c8099c6ab39af856bb2> /System/Library/PrivateFrameworks/ProofReader.framework/ProofReader
0x1f4617000 - 0x1f462dfff libAccessibility.dylib arm64e  <de6ff9a4eb93338bb95544811417484b> /usr/lib/libAccessibility.dylib
0x1f462e000 - 0x1f4b34fff libwebrtc.dylib arm64e  <05b503012b7f3cd0968df493cc2f3538> /System/Library/PrivateFrameworks/WebCore.framework/Frameworks/libwebrtc.dylib
0x1f4b35000 - 0x1f4b9efff ContactsFoundation arm64e  <7c3c903e33b1340a9fa3ace08dffdf93> /System/Library/PrivateFrameworks/ContactsFoundation.framework/ContactsFoundation
0x1f4b9f000 - 0x1f65f9fff WebCore arm64e  <1763a71221433e1a8220577707d6f002> /System/Library/PrivateFrameworks/WebCore.framework/WebCore
0x1f65fa000 - 0x1f6795fff WebKitLegacy arm64e  <e4495f41783d3d3197cf4954bc3875bc> /System/Library/PrivateFrameworks/WebKitLegacy.framework/WebKitLegacy
0x1f6796000 - 0x1f67c5fff DataAccessExpress arm64e  <6e4ddb315b38365bbea092647e05206c> /System/Library/PrivateFrameworks/DataAccessExpress.framework/DataAccessExpress
0x1f67c6000 - 0x1f6860fff AddressBookLegacy arm64e  <60809440211035bea4015e6d2fb45fc1> /System/Library/PrivateFrameworks/AddressBookLegacy.framework/AddressBookLegacy
0x1f6861000 - 0x1f68bafff ProtectedCloudStorage arm64e  <d0ab58921b1737b5bdbcb7e04afd3257> /System/Library/PrivateFrameworks/ProtectedCloudStorage.framework/ProtectedCloudStorage
0x1f68bb000 - 0x1f68eefff UserNotifications arm64e  <ecb4a471bf3836478649d3463f458752> /System/Library/Frameworks/UserNotifications.framework/UserNotifications
0x1f68ef000 - 0x1f68fafff AppleIDAuthSupport arm64e  <a18a6722e51b313698ac333ebf422420> /System/Library/PrivateFrameworks/AppleIDAuthSupport.framework/AppleIDAuthSupport
0x1f68fb000 - 0x1f6952fff AuthKit arm64e  <d491893c311d34faa6cfbe5f548d2674> /System/Library/PrivateFrameworks/AuthKit.framework/AuthKit
0x1f6992000 - 0x1f6992fff UIKit arm64e  <4bdb3fc3dfc23d42823d175921eb6425> /System/Library/Frameworks/UIKit.framework/UIKit
0x1f6993000 - 0x1f69a6fff DocumentManagerCore arm64e  <4eed45c79527323ca651054d1a694761> /System/Library/PrivateFrameworks/DocumentManagerCore.framework/DocumentManagerCore
0x1f69a7000 - 0x1f69b5fff HangTracer arm64e  <34e906fbcc0f3d8b81076de27d21b923> /System/Library/PrivateFrameworks/HangTracer.framework/HangTracer
0x1f69b6000 - 0x1f6a04fff PhysicsKit arm64e  <4a3e9bf9c4493a55a0226588d30bf30a> /System/Library/PrivateFrameworks/PhysicsKit.framework/PhysicsKit
0x1f6a05000 - 0x1f6a09fff StudyLog arm64e  <19c1b4d2d4653933b7ad38d4da7d58e0> /System/Library/PrivateFrameworks/StudyLog.framework/StudyLog
0x1f6a0a000 - 0x1f6af7fff UIFoundation arm64e  <118d809c111f3f41be5312b00e78d6fc> /System/Library/PrivateFrameworks/UIFoundation.framework/UIFoundation
0x1f6af8000 - 0x1f6c1dfff CloudKit arm64e  <959fd74afe2534168c19cac4d535574d> /System/Library/Frameworks/CloudKit.framework/CloudKit
0x1f6c1e000 - 0x1f6c25fff IntentsFoundation arm64e  <0848b5818dc43093b0d713342493eb9b> /System/Library/PrivateFrameworks/IntentsFoundation.framework/IntentsFoundation
0x1f6c26000 - 0x1f6f19fff Intents arm64e  <1908332e89613d0e83e0a351507268da> /System/Library/Frameworks/Intents.framework/Intents
0x1f6f1a000 - 0x1f6f32fff libresolv.9.dylib arm64e  <913b85324a9e3335bf0d0584420aa41e> /usr/lib/libresolv.9.dylib
0x1f6f33000 - 0x1f6f35fff CoreDuetDebugLogging arm64e  <0c79960033b43ec4829366bdd90e680e> /System/Library/PrivateFrameworks/CoreDuetDebugLogging.framework/CoreDuetDebugLogging
0x1f6f36000 - 0x1f6f68fff libtidy.A.dylib arm64e  <c3cddd9b8f8a3c04bda0969a7f061190> /usr/lib/libtidy.A.dylib
0x1f6f69000 - 0x1f712cfff CoreDuet arm64e  <04eb4c904fd9365eba05fa37a1c7209e> /System/Library/PrivateFrameworks/CoreDuet.framework/CoreDuet
0x1f712d000 - 0x1f714efff CoreDuetContext arm64e  <2a020272cb5d341eb34e949909195e5a> /System/Library/PrivateFrameworks/CoreDuetContext.framework/CoreDuetContext
0x1f714f000 - 0x1f7161fff CoreDuetDaemonProtocol arm64e  <b2e1df0d2b6430d58177b41db1f5d4a2> /System/Library/PrivateFrameworks/CoreDuetDaemonProtocol.framework/CoreDuetDaemonProtocol
0x1f7162000 - 0x1f71cafff IMFoundation arm64e  <32ce0dfd49953b81b844187b1559175f> /System/Library/PrivateFrameworks/IMFoundation.framework/IMFoundation
0x1f71cb000 - 0x1f71fdfff vCard arm64e  <e7deefa6775e3c2db27c898a58cc1638> /System/Library/PrivateFrameworks/vCard.framework/vCard
0x1f71fe000 - 0x1f7317fff Contacts arm64e  <88b41f50e27e3827b9957e436f529ca1> /System/Library/Frameworks/Contacts.framework/Contacts
0x1f7318000 - 0x1f7319fff DiagnosticLogCollection arm64e  <626fbcfba20631529411d9487b26b460> /System/Library/PrivateFrameworks/DiagnosticLogCollection.framework/DiagnosticLogCollection
0x1f731a000 - 0x1f731bfff Marco arm64e  <b3ede2b5dbac3b788b5c073ef025dfd0> /System/Library/PrivateFrameworks/Marco.framework/Marco
0x1f731c000 - 0x1f7323fff MessageProtection arm64e  <01e30b1760b1348688f553c888740b84> /System/Library/PrivateFrameworks/MessageProtection.framework/MessageProtection
0x1f7324000 - 0x1f761ffff StoreServices arm64e  <419fdade3cdd30f3b1203ca6222498ae> /System/Library/PrivateFrameworks/StoreServices.framework/StoreServices
0x1f7620000 - 0x1f7636fff Engram arm64e  <d17ee215532834148efe1918d8bd7415> /System/Library/PrivateFrameworks/Engram.framework/Engram
0x1f7637000 - 0x1f7749fff IDSFoundation arm64e  <0b48c821b4d53e6eae5b3559ea9893b4> /System/Library/PrivateFrameworks/IDSFoundation.framework/IDSFoundation
0x1f774a000 - 0x1f7754fff CaptiveNetwork arm64e  <aa9e39ca69413748b324c948908b7b6b> /System/Library/PrivateFrameworks/CaptiveNetwork.framework/CaptiveNetwork
0x1f7755000 - 0x1f7784fff EAP8021X arm64e  <f581ba4858943553b86da69d5c51ca3a> /System/Library/PrivateFrameworks/EAP8021X.framework/EAP8021X
0x1f7785000 - 0x1f77c3fff MobileWiFi arm64e  <2b313fc8970c3cc3bad5dea0890daf58> /System/Library/PrivateFrameworks/MobileWiFi.framework/MobileWiFi
0x1f77c4000 - 0x1f77c6fff OAuth arm64e  <30f52decdd5035f4ab54aaeb174b7994> /System/Library/PrivateFrameworks/OAuth.framework/OAuth
0x1f77c7000 - 0x1f77c9fff CommonAuth arm64e  <d5377ea90cac377a9b28eaf2a8dab9da> /System/Library/PrivateFrameworks/CommonAuth.framework/CommonAuth
0x1f77ca000 - 0x1f783afff Heimdal arm64e  <224fac41acd53d93bf1b25d99a0292c6> /System/Library/PrivateFrameworks/Heimdal.framework/Heimdal
0x1f783b000 - 0x1f7863fff GSS arm64e  <3c628d3b70393dfeaa00ed648bd29401> /System/Library/Frameworks/GSS.framework/GSS
0x1f7864000 - 0x1f787bfff ApplePushService arm64e  <088bfeb258bf34db829a07d376194e79> /System/Library/PrivateFrameworks/ApplePushService.framework/ApplePushService
0x1f787c000 - 0x1f790dfff AccountsDaemon arm64e  <d60817e9c65e3a8c9089bb6d359fb2ce> /System/Library/PrivateFrameworks/AccountsDaemon.framework/AccountsDaemon
0x1f790e000 - 0x1f792ffff AppleIDSSOAuthentication arm64e  <edac072a66823709b5ab7a1e49b14551> /System/Library/PrivateFrameworks/AppleIDSSOAuthentication.framework/AppleIDSSOAuthentication
0x1f7930000 - 0x1f79b2fff AppleAccount arm64e  <7568b591f82c3c3f9e009f5f7d2bda3f> /System/Library/PrivateFrameworks/AppleAccount.framework/AppleAccount
0x1f79b3000 - 0x1f7b2efff CoreUtils arm64e  <1203f63a34ad3fc183f41f03a2778aaa> /System/Library/PrivateFrameworks/CoreUtils.framework/CoreUtils
0x1f7b2f000 - 0x1f7c2efff IDS arm64e  <f50cb65fb3f93280bdebd11cefcc767e> /System/Library/PrivateFrameworks/IDS.framework/IDS
0x1f7c2f000 - 0x1f7c55fff MediaServices arm64e  <98438ab3fbe036679a11fe4e17ca4f56> /System/Library/PrivateFrameworks/MediaServices.framework/MediaServices
0x1f7c56000 - 0x1f7e35fff MediaRemote arm64e  <216ad7bdf5953b0784ce56604c6fc169> /System/Library/PrivateFrameworks/MediaRemote.framework/MediaRemote
0x1f7e36000 - 0x1f7e50fff UserManagement arm64e  <fe89dc2c577c351fa1c2ca4fdbc5af80> /System/Library/PrivateFrameworks/UserManagement.framework/UserManagement
0x1f7e62000 - 0x1f7e92fff Bom arm64e  <fda06f97e29037248d3ed3f4c8e4bf3d> /System/Library/PrivateFrameworks/Bom.framework/Bom
0x1f7e93000 - 0x1f7e97fff CommunicationsFilter arm64e  <e282dc7186763642a4e95a30b2455b34> /System/Library/PrivateFrameworks/CommunicationsFilter.framework/CommunicationsFilter
0x1f7f55000 - 0x1f7fa4fff ChunkingLibrary arm64e  <183ce12ecab136a184fdf1a9e15fb4f7> /System/Library/PrivateFrameworks/ChunkingLibrary.framework/ChunkingLibrary
0x1f7fa5000 - 0x1f7fb3fff libnetworkextension.dylib arm64e  <f8d72fcd87c135f383e19314358deb0c> /usr/lib/libnetworkextension.dylib
0x1f8d5e000 - 0x1f8f01fff NetworkExtension arm64e  <5c1d1e8777573bdeba7e28e864e0c75b> /System/Library/Frameworks/NetworkExtension.framework/NetworkExtension
0x1f93d9000 - 0x1f9408fff GLKit arm64e  <3acc216c90a633869de63171d8c6d78c> /System/Library/Frameworks/GLKit.framework/GLKit
0x1f953b000 - 0x1f9559fff AssetCacheServices arm64e  <f2b6a140be1c3983af0768568aae06f7> /System/Library/PrivateFrameworks/AssetCacheServices.framework/AssetCacheServices
0x1f955a000 - 0x1f960cfff NetworkServiceProxy arm64e  <f0c3b9aee88c3729942891f1f5de3050> /System/Library/PrivateFrameworks/NetworkServiceProxy.framework/NetworkServiceProxy
0x1f960d000 - 0x1f96e4fff MMCS arm64e  <acc34bf329c33882a279be8e6490240e> /System/Library/PrivateFrameworks/MMCS.framework/MMCS
0x1f9799000 - 0x1f97a6fff PersonaKit arm64e  <eb5d1326d3473ee5903e3ad556f79956> /System/Library/PrivateFrameworks/PersonaKit.framework/PersonaKit
0x1f9800000 - 0x1f982efff PhotosFormats arm64e  <af40585917203b6ab4be6bee95608652> /System/Library/PrivateFrameworks/PhotosFormats.framework/PhotosFormats
0x1f9925000 - 0x1f9a8afff CloudPhotoLibrary arm64e  <b23c88adf38737959123e281eeafdabe> /System/Library/PrivateFrameworks/CloudPhotoLibrary.framework/CloudPhotoLibrary
0x1f9b75000 - 0x1f9ba8fff AssetsLibraryServices arm64e  <bb557f3eee76333c90e0e3d57db60a11> /System/Library/PrivateFrameworks/AssetsLibraryServices.framework/AssetsLibraryServices
0x1f9bde000 - 0x1f9c07fff DCIMServices arm64e  <1d92eb13c2e03cb1be17e64546e92133> /System/Library/PrivateFrameworks/DCIMServices.framework/DCIMServices
0x1f9c08000 - 0x1f9d2cfff CoreMediaStream arm64e  <e018b7e972213883853466c0d1a60560> /System/Library/PrivateFrameworks/CoreMediaStream.framework/CoreMediaStream
0x1f9d2d000 - 0x1f9d34fff XPCKit arm64e  <c03d1d5b6afa330db4f8b917e64228d1> /System/Library/PrivateFrameworks/XPCKit.framework/XPCKit
0x1f9e1a000 - 0x1f9e34fff CloudPhotoServices arm64e  <bda36fa506ba322486704a2f1270daa3> /System/Library/PrivateFrameworks/CloudPhotoServices.framework/CloudPhotoServices
0x1f9e35000 - 0x1f9e40fff CoreRecents arm64e  <6789a130542632bf88c7104bba3e83b1> /System/Library/PrivateFrameworks/CoreRecents.framework/CoreRecents
0x1f9e41000 - 0x1f9e5efff MediaStream arm64e  <13ddb7e9c0e935d7b841fad9d7c74869> /System/Library/PrivateFrameworks/MediaStream.framework/MediaStream
0x1f9e5f000 - 0x1fa378fff PhotoLibraryServices arm64e  <61c533ea9ecc3ac19873d093ee73bcf0> /System/Library/PrivateFrameworks/PhotoLibraryServices.framework/PhotoLibraryServices
0x1fa379000 - 0x1fa395fff PrototypeTools arm64e  <87be4fdb8b2635b892bbd82596bec735> /System/Library/PrivateFrameworks/PrototypeTools.framework/PrototypeTools
0x1fa396000 - 0x1fa419fff CoreSymbolication arm64e  <4b33d74dee233114a799315f883f056f> /System/Library/PrivateFrameworks/CoreSymbolication.framework/CoreSymbolication
0x1fa41a000 - 0x1fa56bfff SearchFoundation arm64e  <ebcc95f97b953720b7400d538344883f> /System/Library/PrivateFrameworks/SearchFoundation.framework/SearchFoundation
0x1fa56c000 - 0x1fa571fff IncomingCallFilter arm64e  <835e9ff134ba363e9c570407ae44acd1> /System/Library/PrivateFrameworks/IncomingCallFilter.framework/IncomingCallFilter
0x1fa572000 - 0x1fa629fff iTunesStore arm64e  <15890847fbd23c7abec874bbfd7fbc37> /System/Library/PrivateFrameworks/iTunesStore.framework/iTunesStore
0x1fa785000 - 0x1fa78dfff CoreTime arm64e  <bd99044e4f2b3accbb6f9eff1dfb50fc> /System/Library/PrivateFrameworks/CoreTime.framework/CoreTime
0x1fa78e000 - 0x1fa7dffff CoreAppleCVA arm64e  <56734f25c7933cba9dcaa69f6d3b41b7> /System/Library/PrivateFrameworks/CoreAppleCVA.framework/CoreAppleCVA
0x1fa820000 - 0x1fa9c5fff AppleCVA arm64e  <34c32eff8fe738f4ba7a037c38ca1c03> /System/Library/PrivateFrameworks/AppleCVA.framework/AppleCVA
0x1fa9c6000 - 0x1faa80fff Montreal arm64e  <15489af4763f3f7bb156262ef5f3ff7e> /System/Library/PrivateFrameworks/Montreal.framework/Montreal
0x1faa81000 - 0x1fad4cfff Espresso arm64e  <ae9c995202cf3a09986d527f438c6e13> /System/Library/PrivateFrameworks/Espresso.framework/Espresso
0x1fad4d000 - 0x1fad53fff MobileSystemServices arm64e  <e699fe0cc0a63955a980f4064b27ceb1> /System/Library/PrivateFrameworks/MobileSystemServices.framework/MobileSystemServices
0x1fad54000 - 0x1faf4bfff Photos arm64e  <615f7d28546c3450a51f2abf2665a0b9> /System/Library/Frameworks/Photos.framework/Photos
0x1faf58000 - 0x1fb179fff CoreML arm64e  <94cd8e725faa3696b893853a7eefbb01> /System/Library/Frameworks/CoreML.framework/CoreML
0x1fb17a000 - 0x1fb17dfff CoreOptimization arm64e  <72b66d5a3b2d346d9a4f648141661efb> /System/Library/PrivateFrameworks/CoreOptimization.framework/CoreOptimization
0x1fb1dd000 - 0x1fb232fff CorePrediction arm64e  <50ae36ba73333be5b65e293a43a88dec> /System/Library/PrivateFrameworks/CorePrediction.framework/CorePrediction
0x1fb233000 - 0x1fb333fff Navigation arm64e  <a58b0fddf6893692bdd055c48c35fc5d> /System/Library/PrivateFrameworks/Navigation.framework/Navigation
0x1fb334000 - 0x1fb34cfff ContactsDonation arm64e  <6d45d1dde327389596885aabc30176d7> /System/Library/PrivateFrameworks/ContactsDonation.framework/ContactsDonation
0x1fb34d000 - 0x1fb379fff Futhark arm64e  <67c4f50a2ec73e74966d67f932600d91> /System/Library/PrivateFrameworks/Futhark.framework/Futhark
0x1fb37a000 - 0x1fb3edfff NanoRegistry arm64e  <65678e1bb2893ea7916121a49b545337> /System/Library/PrivateFrameworks/NanoRegistry.framework/NanoRegistry
0x1fb460000 - 0x1fb4bafff ContactsUICore arm64e  <0f1c76d5f8aa376bb729b1a7c8b49bc7> /System/Library/PrivateFrameworks/ContactsUICore.framework/ContactsUICore
0x1fb4bb000 - 0x1fb634fff ContactsUI arm64e  <290d625c2dd7365e912bd001de47888c> /System/Library/Frameworks/ContactsUI.framework/ContactsUI
0x1fb635000 - 0x1fb700fff CorePDF arm64e  <492f607abfd03016a997e8499b33fa77> /System/Library/PrivateFrameworks/CorePDF.framework/CorePDF
0x1fb701000 - 0x1fb993fff Vision arm64e  <12a83bd31d60391db446ef5f779e36dc> /System/Library/Frameworks/Vision.framework/Vision
0x1fb994000 - 0x1fbedffff WebKit arm64e  <d85f6643797e3e45bbbeb941d4504cbd> /System/Library/Frameworks/WebKit.framework/WebKit
0x1fbf7a000 - 0x1fbf7ffff ConstantClasses arm64e  <5a76f3a0cf263770a7267ec6ae54c0a1> /System/Library/PrivateFrameworks/ConstantClasses.framework/ConstantClasses
0x1fc07e000 - 0x1fc10bfff MediaPlatform arm64e  <ca979071a0cd3e11a88e9b6aad4c3464> /System/Library/PrivateFrameworks/MediaPlatform.framework/MediaPlatform
0x1fc16e000 - 0x1fc178fff DAAPKit arm64e  <f3aab8c97ca039c6962e4264737105f4> /System/Library/PrivateFrameworks/DAAPKit.framework/DAAPKit
0x1fc273000 - 0x1fc50ffff MediaLibraryCore arm64e  <e015629ac4363106a0991b60d74e9aa7> /System/Library/PrivateFrameworks/MediaLibraryCore.framework/MediaLibraryCore
0x1fc510000 - 0x1fc511fff AdSupport arm64e  <9c487a1416c532a4b3033eb45b0f93b9> /System/Library/Frameworks/AdSupport.framework/AdSupport
0x1fc512000 - 0x1fc78dfff MusicLibrary arm64e  <e74492d63add33bfaadbcb7da18cc027> /System/Library/PrivateFrameworks/MusicLibrary.framework/MusicLibrary
0x1fc78e000 - 0x1fcda3fff VectorKit arm64e  <41f69250bd4d3b6da95c1d555b2a89ab> /System/Library/PrivateFrameworks/VectorKit.framework/VectorKit
0x1fcda4000 - 0x1fcffbfff MapKit arm64e  <d3c20cd4f17531d094c8f12cc0b9ba84> /System/Library/Frameworks/MapKit.framework/MapKit
0x1fcffc000 - 0x1fd1cffff iTunesCloud arm64e  <fd249b28347239aab35f99c7ece50272> /System/Library/PrivateFrameworks/iTunesCloud.framework/iTunesCloud
0x1fd1d0000 - 0x1fd26cfff HomeSharing arm64e  <4fe8ce24bda83f108e338acc0eff8e32> /System/Library/PrivateFrameworks/HomeSharing.framework/HomeSharing
0x1fd3ff000 - 0x1fd83bfff MediaPlayer arm64e  <6dfc188ffb74396d88b6b1f2a074d5c5> /System/Library/Frameworks/MediaPlayer.framework/MediaPlayer
0x1fd83c000 - 0x1fd860fff MobileInstallation arm64e  <fcd4b6371f0b3c7e8d7dfef31bbe667d> /System/Library/PrivateFrameworks/MobileInstallation.framework/MobileInstallation
0x1fd938000 - 0x1fda02fff TelephonyUtilities arm64e  <424546a829f439229907337b8c72b8e4> /System/Library/PrivateFrameworks/TelephonyUtilities.framework/TelephonyUtilities
0x1fdab4000 - 0x1fdac6fff AssetsLibrary arm64e  <cecc403903153d0aa15ed1782aebd731> /System/Library/Frameworks/AssetsLibrary.framework/AssetsLibrary
0x1fdd9e000 - 0x1fde3bfff Social arm64e  <ae0f57d60bb632c79ddca66ea0cd6e10> /System/Library/Frameworks/Social.framework/Social
0x1fded4000 - 0x1fdeecfff CoreFollowUp arm64e  <d88501a9ec3436879ab35ce9830329bb> /System/Library/PrivateFrameworks/CoreFollowUp.framework/CoreFollowUp
0x1febd9000 - 0x1febe7fff SetupAssistantSupport arm64e  <5c23c3e9a61f38a8a6aba163dbbd3586> /System/Library/PrivateFrameworks/SetupAssistantSupport.framework/SetupAssistantSupport
0x1febe8000 - 0x1fec1bfff SetupAssistant arm64e  <3165a18e3e7032198308fda89537610f> /System/Library/PrivateFrameworks/SetupAssistant.framework/SetupAssistant
0x1fec6a000 - 0x1fec73fff MobileStorage arm64e  <0218e703a5e73929bbb186b4a3381520> /System/Library/PrivateFrameworks/MobileStorage.framework/MobileStorage
0x1fec74000 - 0x1fecb4fff ContentIndex arm64e  <fba5bbcee36f36ac9e1761832773e9b2> /System/Library/PrivateFrameworks/ContentIndex.framework/ContentIndex
0x1fecb5000 - 0x1fed12fff ImageCapture arm64e  <bdb6b5fa9d1b31049db107755fb4e800> /System/Library/PrivateFrameworks/ImageCapture.framework/ImageCapture
0x1feed1000 - 0x1feee6fff iPhotoMigrationSupport arm64e  <c799119cbab13209b2690e50e04f0608> /System/Library/PrivateFrameworks/iPhotoMigrationSupport.framework/iPhotoMigrationSupport
0x1feee7000 - 0x1feef8fff DiagnosticExtensions arm64e  <1d27918ce2c93519998c7946574d4693> /System/Library/PrivateFrameworks/DiagnosticExtensions.framework/DiagnosticExtensions
0x1ff34a000 - 0x1ff3c5fff PhotoLibrary arm64e  <501a2f7d9db2390f8eee849b99df2835> /System/Library/PrivateFrameworks/PhotoLibrary.framework/PhotoLibrary
0x1ff3c6000 - 0x1ff906fff PhotosUICore arm64e  <21aa0b1c52b43c799a8c77d5ad103b53> /System/Library/PrivateFrameworks/PhotosUICore.framework/PhotosUICore
0x1ff9e9000 - 0x1ff9eefff LinguisticData arm64e  <84c1c694155a3d8ebdf94e73732b4d3e> /System/Library/PrivateFrameworks/LinguisticData.framework/LinguisticData
0x1ffa08000 - 0x1ffa91fff PhotoEditSupport arm64e  <3f56e281e6463f658d4a0ff4843d4157> /System/Library/PrivateFrameworks/PhotoEditSupport.framework/PhotoEditSupport
0x1ffaa6000 - 0x1fff79fff PhotosUI arm64e  <114c1e8acfc0346f97bde371cf962a48> /System/Library/Frameworks/PhotosUI.framework/PhotosUI
0x200083000 - 0x2000bffff StoreKit arm64e  <ccc964563aff3bf99493a8bac937ddb1> /System/Library/Frameworks/StoreKit.framework/StoreKit
0x200287000 - 0x2002a1fff MetalKit arm64e  <67dd259943c43e93954052d109371b04> /System/Library/Frameworks/MetalKit.framework/MetalKit
0x200d1b000 - 0x200de3fff PDFKit arm64e  <c1c817b124af315f8fd526bf043ec6b5> /System/Library/Frameworks/PDFKit.framework/PDFKit
0x20116b000 - 0x20117dfff MobileDeviceLink arm64e  <ccde142340423ea5980ec1a5a248b8ec> /System/Library/PrivateFrameworks/MobileDeviceLink.framework/MobileDeviceLink
0x20125f000 - 0x2012b2fff MobileBackup arm64e  <6d5fc17bd2303ab095a8beba4a3de59a> /System/Library/PrivateFrameworks/MobileBackup.framework/MobileBackup
0x2012b3000 - 0x2012fdfff SafariSafeBrowsing arm64e  <bfb03677955b336c8193e7033187b58e> /System/Library/PrivateFrameworks/SafariSafeBrowsing.framework/SafariSafeBrowsing
0x203237000 - 0x203266fff OpenAL arm64e  <0f3b56502bd03b21a3ed283dd9dbc6d3> /System/Library/Frameworks/OpenAL.framework/OpenAL
0x203b0e000 - 0x203b60fff LoggingSupport arm64e  <86600a11b22737b29cf58313b177ba1b> /System/Library/PrivateFrameworks/LoggingSupport.framework/LoggingSupport
0x205763000 - 0x205787fff AppSupportUI arm64e  <8caaa91ca08b353dbf866b55791d34f5> /System/Library/PrivateFrameworks/AppSupportUI.framework/AppSupportUI
0x206459000 - 0x20645efff kperf arm64e  <c22ef002dc65318e82200a9fa22ed87d> /System/Library/PrivateFrameworks/kperf.framework/kperf
0x2066b3000 - 0x2066d5fff CellularPlanManager arm64e  <510fc205371e38d7b34c6b67214ac36a> /System/Library/PrivateFrameworks/CellularPlanManager.framework/CellularPlanManager
0x20689c000 - 0x2068a4fff kperfdata arm64e  <15e8f9145b9d3bbab0a1069ce54d6cc4> /System/Library/PrivateFrameworks/kperfdata.framework/kperfdata
0x2068e7000 - 0x2068eefff libdscsym.dylib arm64e  <8402125be9cf3dd88442ff2126a3c655> /usr/lib/libdscsym.dylib
0x206c95000 - 0x206ccdfff ktrace arm64e  <240d4a07db5d38b3bfb6208bb6a3e823> /System/Library/PrivateFrameworks/ktrace.framework/ktrace
0x208ee2000 - 0x208ee4fff DeviceCheck arm64e  <921979bb0f763b3b9ae8bdbf1aa760e4> /System/Library/Frameworks/DeviceCheck.framework/DeviceCheck
0x208ff5000 - 0x209004fff CTCarrierSpace arm64e  <b88c1d00b4683aad84884c0a7da66aa9> /System/Library/PrivateFrameworks/CTCarrierSpace.framework/CTCarrierSpace
0x209471000 - 0x209494fff DeviceIdentity arm64e  <9fb6b8c696953247adc8af65f080a903> /System/Library/PrivateFrameworks/DeviceIdentity.framework/DeviceIdentity
0x20b4a2000 - 0x20b51dfff Rapport arm64e  <baa7044e682e34e99b714f522e6d7926> /System/Library/PrivateFrameworks/Rapport.framework/Rapport
0x20b55b000 - 0x20b59efff SignpostSupport arm64e  <8558fa5954e63fcbbe5df6a04ea09c4a> /System/Library/PrivateFrameworks/SignpostSupport.framework/SignpostSupport
0x20c4d9000 - 0x20c4e0fff libMatch.1.dylib arm64e  <ffcc937da35e33ac961bdeea22828edb> /usr/lib/libMatch.1.dylib
0x20c529000 - 0x20c53efff libtailspin.dylib arm64e  <0373cbea823536a2b413a43aa155303f> /usr/lib/libtailspin.dylib
0x20f99d000 - 0x20f9affff libGSFontCache.dylib arm64e  <9aaac879ee983443bc51231ec41a14d2> /System/Library/PrivateFrameworks/FontServices.framework/libGSFontCache.dylib
0x211494000 - 0x211498fff InternationalSupport arm64e  <f85416b47de83d418128376b453dc78c> /System/Library/PrivateFrameworks/InternationalSupport.framework/InternationalSupport
0x21287e000 - 0x21288afff PersonaUI arm64e  <8c2ed63a4bc1387facbfee59bd13680a> /System/Library/PrivateFrameworks/PersonaUI.framework/PersonaUI
0x212cc1000 - 0x212ccbfff SignpostCollection arm64e  <c03e5c565acb318aba8cdad82dbb032a> /System/Library/PrivateFrameworks/SignpostCollection.framework/SignpostCollection
0x21371c000 - 0x21371ffff XCTTargetBootstrap arm64e  <980baea803bf3ff8b92d66ceadce2cda> /System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/XCTTargetBootstrap
0x213763000 - 0x213776fff libEDR arm64e  <9b4d967c5cff37398f9ea32e932351bf> /System/Library/PrivateFrameworks/libEDR.framework/libEDR
0x214297000 - 0x214297fff libcharset.1.dylib arm64e  <c0039c72672e3882bd5de4d815d9721b> /usr/lib/libcharset.1.dylib
0x214d1d000 - 0x214d1efff libsandbox.1.dylib arm64e  <********************************> /usr/lib/libsandbox.1.dylib
0x2153ec000 - 0x21552ffff CoreServices arm64e  <3a9d5d1c76b33e8b87830b591d9b3e1b> /System/Library/Frameworks/CoreServices.framework/CoreServices
0x215559000 - 0x215572fff MPSRayIntersector arm64e  <7c8c4f911fd33864ad4aa8add98dccec> /System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSRayIntersector.framework/MPSRayIntersector
0x2155a0000 - 0x2156c6fff Network arm64e  <ef41510e8bb338bb8329de399039df77> /System/Library/Frameworks/Network.framework/Network
0x21578c000 - 0x21579afff ANEServices arm64e  <b249714c19e23486ac2a6a27ff398588> /System/Library/PrivateFrameworks/ANEServices.framework/ANEServices
0x21579f000 - 0x2157a3fff ASEProcessing arm64e  <78674a54542f31ec9c5cbacac49b21cf> /System/Library/PrivateFrameworks/ASEProcessing.framework/ASEProcessing
0x2157a4000 - 0x2157affff AXCoreUtilities arm64e  <6fd1e02cb3453b90971494c8cc641b91> /System/Library/PrivateFrameworks/AXCoreUtilities.framework/AXCoreUtilities
0x215a38000 - 0x215b8afff AppleMediaServices arm64e  <6d45106db1b436d8985dc4b7a16614ef> /System/Library/PrivateFrameworks/AppleMediaServices.framework/AppleMediaServices
0x215b8b000 - 0x215b9afff AppleNeuralEngine arm64e  <53c78de6e4b63d0a823264bb677b99fa> /System/Library/PrivateFrameworks/AppleNeuralEngine.framework/AppleNeuralEngine
0x215d1b000 - 0x215d50fff C2 arm64e  <759dd00facec31b9a427181f1ec0ce93> /System/Library/PrivateFrameworks/C2.framework/C2
0x2163b6000 - 0x21640bfff DocumentManager arm64e  <0b0c3f5a3226300e90afef2dcdcaefb2> /System/Library/PrivateFrameworks/DocumentManager.framework/DocumentManager
0x21654d000 - 0x216551fff IdleTimerServices arm64e  <33bbef2637a33b01ba2ce2a81cbbb8d9> /System/Library/PrivateFrameworks/IdleTimerServices.framework/IdleTimerServices
0x216637000 - 0x21665ffff MetadataUtilities arm64e  <c79e40f2decd346b83d95fce01137614> /System/Library/PrivateFrameworks/MetadataUtilities.framework/MetadataUtilities
0x21782f000 - 0x21787dfff OTSVG arm64e  <1083af1dd75b382180432ecaf1e0490f> /System/Library/PrivateFrameworks/OTSVG.framework/OTSVG
0x217976000 - 0x2179d3fff PhotoFoundation arm64e  <06ef60e4da62306cbd045b65253c2fef> /System/Library/PrivateFrameworks/PhotoFoundation.framework/PhotoFoundation
0x217a24000 - 0x217a66fff PhotosImagingFoundation arm64e  <ed3c6ef641d939d3b8e929c8738b6e43> /System/Library/PrivateFrameworks/PhotosImagingFoundation.framework/PhotosImagingFoundation
0x217aac000 - 0x217ab5fff PrototypeToolsUI arm64e  <ced3112ff9f03d6699516cbb1a1802da> /System/Library/PrivateFrameworks/PrototypeToolsUI.framework/PrototypeToolsUI
0x217aca000 - 0x217b17fff ROCKit arm64e  <41d671df2bb3379787580fd4da075994> /System/Library/PrivateFrameworks/ROCKit.framework/ROCKit
0x217d3c000 - 0x217d4efff RemoteTextInput arm64e  <eabc9191b92b34a8832e84ba8669ddc9> /System/Library/PrivateFrameworks/RemoteTextInput.framework/RemoteTextInput
0x217d76000 - 0x217e0cfff SampleAnalysis arm64e  <463827016b763805a6cbab1ddf00ba90> /System/Library/PrivateFrameworks/SampleAnalysis.framework/SampleAnalysis
0x217fbe000 - 0x217fc5fff StatsKit arm64e  <b60b495e45ab324381dde9d1bf799a8e> /System/Library/PrivateFrameworks/StatsKit.framework/StatsKit
0x218c2a000 - 0x219d69fff UIKitCore arm64e  <19fd9883fb333736aa548b4f3b4a1958> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
0x219d6a000 - 0x219d75fff UIKitServices arm64e  <e9d77e702eef3d90b14831a75cab5115> /System/Library/PrivateFrameworks/UIKitServices.framework/UIKitServices
0x219d76000 - 0x219d7cfff URLFormatting arm64e  <dc42482ff90e34babc97702b98f55d4f> /System/Library/PrivateFrameworks/URLFormatting.framework/URLFormatting
0x21a683000 - 0x21a694fff libswiftAVFoundation.dylib arm64e  <6fbdd9099b2e39eca1250ec28e0a0a0b> /usr/lib/swift/libswiftAVFoundation.dylib
0x21a69b000 - 0x21a69ffff libswiftAssetsLibrary.dylib arm64e  <89efdf2baf2d3d95ab4e275601f0f570> /usr/lib/swift/libswiftAssetsLibrary.dylib
0x21a6c2000 - 0x21aa28fff libswiftCore.dylib arm64e  <6898cd82576e33588811d57ffc3581c3> /usr/lib/swift/libswiftCore.dylib
0x21aa29000 - 0x21aa2ffff libswiftCoreAudio.dylib arm64e  <87e8097803ed3599b6a98d871c0cdf89> /usr/lib/swift/libswiftCoreAudio.dylib
0x21aa38000 - 0x21aa3cfff libswiftCoreFoundation.dylib arm64e  <0ff155191b983d7881790e9a3c78399a> /usr/lib/swift/libswiftCoreFoundation.dylib
0x21aa3d000 - 0x21aa4cfff libswiftCoreGraphics.dylib arm64e  <8e8e4711f55d3567bcd533e7f6d15394> /usr/lib/swift/libswiftCoreGraphics.dylib
0x21aa4d000 - 0x21aa51fff libswiftCoreImage.dylib arm64e  <b46da48cbbd73d11882443431cc15d40> /usr/lib/swift/libswiftCoreImage.dylib
0x21aa52000 - 0x21aa58fff libswiftCoreLocation.dylib arm64e  <449eae304ba338ecad272c080c364ccd> /usr/lib/swift/libswiftCoreLocation.dylib
0x21aa59000 - 0x21aa5ffff libswiftCoreMedia.dylib arm64e  <ee690cc387c33df8bb418ba9f33eb31a> /usr/lib/swift/libswiftCoreMedia.dylib
0x21aa60000 - 0x21aa69fff libswiftDarwin.dylib arm64e  <8f2ae3a68d13340c93eced97c5daf698> /usr/lib/swift/libswiftDarwin.dylib
0x21aa6a000 - 0x21aa83fff libswiftDispatch.dylib arm64e  <4032141fd59136caa9b5744605414047> /usr/lib/swift/libswiftDispatch.dylib
0x21aa84000 - 0x21ac04fff libswiftFoundation.dylib arm64e  <1408bdb98bd63211be37d17c7a5972b6> /usr/lib/swift/libswiftFoundation.dylib
0x21ac3c000 - 0x21ac42fff libswiftMetal.dylib arm64e  <c04d6f2a58953a8db0f4e8a0e5afc7bb> /usr/lib/swift/libswiftMetal.dylib
0x21ac60000 - 0x21ac83fff libswiftNetwork.dylib arm64e  <bdc62133c9ac39549c206e62bd7f36f4> /usr/lib/swift/libswiftNetwork.dylib
0x21ac84000 - 0x21ac8afff libswiftObjectiveC.dylib arm64e  <c53c4963fa95383589ed639cbe2e824e> /usr/lib/swift/libswiftObjectiveC.dylib
0x21ac8b000 - 0x21ac93fff libswiftPhotos.dylib arm64e  <8194f993609a329a968b1138121a2ebd> /usr/lib/swift/libswiftPhotos.dylib
0x21ac94000 - 0x21ac99fff libswiftQuartzCore.dylib arm64e  <1590f47cdf283fdcb7ce86852c800412> /usr/lib/swift/libswiftQuartzCore.dylib
0x21ace0000 - 0x21acf1fff libswiftUIKit.dylib arm64e  <867e80340b943b6b9c593ddbd21b6d51> /usr/lib/swift/libswiftUIKit.dylib
0x21ad02000 - 0x21ad0afff libswiftos.dylib arm64e  <d98108a0430c39c8b64a02e324039588> /usr/lib/swift/libswiftos.dylib
0x21ad0b000 - 0x21ad27fff libswiftsimd.dylib arm64e  <6da71fef543c36e797d80d933d9e0071> /usr/lib/swift/libswiftsimd.dylib

EOF
