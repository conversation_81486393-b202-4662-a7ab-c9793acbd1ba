Incident Identifier: 06183F5B-F056-429E-A96C-4ADA932A3949
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone13,2
Process:             发型测试 [69012]
Path:                /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone13,2:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [15360]

Date/Time:           2025-08-06 16:54:15.2631 +0800
Launch Time:         2025-08-06 16:53:48.5619 +0800
OS Version:          iPhone OS 18.1.1 (22B91)
Release Type:        User
Baseband Version:    5.10.01
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001a4a7bb8c
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [69012]

Triggered by Thread:  15

Last Exception Backtrace:
0   CoreFoundation                	0x19ccec7cc __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x199fbf2e4 objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1bfc5c340 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1bfc5c000 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x19f4d3da0 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1603)
5   UIKitCore                     	0x19f45d584 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2164 (UIView.m:19901)
6   QuartzCore                    	0x19e793c28 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10944)
7   QuartzCore                    	0x19e7937b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2638)
8   QuartzCore                    	0x19e7ea914 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
9   QuartzCore                    	0x19e7697c4 CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
10  QuartzCore                    	0x19e949318 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
11  libsystem_pthread.dylib       	0x2251cc258 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x2251cbfc8 _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x2251cbf74 _pthread_wqthread_exit + 56 (pthread.c:2656)
14  libsystem_pthread.dylib       	0x2251cbd04 _pthread_wqthread + 424 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x2251c8488 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001ed07e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ed081d98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ed081cb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ed081afc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ccbda84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019ccbd130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019ccbc830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001e8c9c1c4 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x000000019f822eb0 -[UIApplication _run] + 816 (UIApplication.m:3844)
9   UIKitCore                     	0x000000019f8d15b4 UIApplicationMain + 340 (UIApplication.m:5496)
10  UIKitCore                     	0x000000019fc0bfa8 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x00000001051f9130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x00000001051f9130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x00000001051f9130 main + 120
14  dyld                          	0x00000001c26aaec8 start + 2724 (dyldMain.cpp:1334)

Thread 1:
0   libsystem_pthread.dylib       	0x00000002251c8480 start_wqthread + 0 (:-1)

Thread 2 name:
Thread 2:
0   libsystem_kernel.dylib        	0x00000001ed07e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ed081d98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ed081cb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ed081afc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ccbda84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019ccbd130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019ccbc830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   Foundation                    	0x000000019b964500 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x000000019b964350 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x000000019f836358 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1241)
10  Foundation                    	0x000000019b9756c8 __NSThread__start__ + 724 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x00000002251cd37c _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x00000002251c8494 thread_start + 8 (:-1)

Thread 3:
0   libsystem_kernel.dylib        	0x00000001ed0841b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a4a1ba78 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a4a79550 sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000105a2eef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x00000002251cd37c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000002251c8494 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001ed07e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ed081d98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ed07fbfc thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x0000000105a0697c handleExceptions + 120
4   libsystem_pthread.dylib       	0x00000002251cd37c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x00000002251c8494 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001ed07e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ed081e30 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001ed081cb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ed081afc mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000105a069a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x00000002251cd37c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000002251c8494 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001ed0841b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a4a1ba78 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a4a1b990 usleep + 68 (usleep.c:52)
3   发型测试                          	0x000000010596e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000105920e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000002251cd37c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000002251c8494 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001ed0841b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a4a1ba78 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a4a1b990 usleep + 68 (usleep.c:52)
3   发型测试                          	0x000000010596e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000105920e84 thread_do + 340
5   libsystem_pthread.dylib       	0x00000002251cd37c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x00000002251c8494 thread_start + 8 (:-1)

Thread 8:
0   libsystem_pthread.dylib       	0x00000002251c8480 start_wqthread + 0 (:-1)

Thread 9:
0   libsystem_pthread.dylib       	0x00000002251c8480 start_wqthread + 0 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001ed07e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ed081d98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ed081cb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ed081afc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ccbda84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019ccbd130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019ccbc830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CFNetwork                     	0x000000019e238ee0 +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x000000019b9756c8 __NSThread__start__ + 724 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x00000002251cd37c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000002251c8494 thread_start + 8 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x00000002251c8480 start_wqthread + 0 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001ed07e688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ed081d98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ed081cb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ed081afc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ccbda84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019ccbd130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019ccbc830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CoreFoundation                	0x000000019cd27cec CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001aa14c084 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x00000002251cd37c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x00000002251c8494 thread_start + 8 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x00000002251c8480 start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x00000002251c8480 start_wqthread + 0 (:-1)

Thread 15 Crashed:
0   libsystem_c.dylib             	0x00000001a4a7bb8c __abort + 168 (abort.c:175)
1   libsystem_c.dylib             	0x00000001a4a7bae4 abort + 140 (abort.c:130)
2   libc++abi.dylib               	0x0000000224fdd5b8 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x0000000224fcbbac demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x0000000199fdae14 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x0000000105a0522c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x0000000224fdc87c std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x0000000224fdfdfc __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x0000000224fdfda4 __cxa_throw + 92 (cxa_exception.cpp:294)
9   libobjc.A.dylib               	0x0000000199fbf44c objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001bfc5c340 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001bfc5c000 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x000000019f4d3da0 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1603)
13  UIKitCore                     	0x000000019f45d584 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2164 (UIView.m:19901)
14  QuartzCore                    	0x000000019e793c28 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10944)
15  QuartzCore                    	0x000000019e7937b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2638)
16  QuartzCore                    	0x000000019e7ea914 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
17  QuartzCore                    	0x000000019e7697c4 CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
18  QuartzCore                    	0x000000019e949318 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
19  libsystem_pthread.dylib       	0x00000002251cc258 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x00000002251cbfc8 _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x00000002251cbf74 _pthread_wqthread_exit + 56 (pthread.c:2656)
22  libsystem_pthread.dylib       	0x00000002251cbd04 _pthread_wqthread + 424 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x00000002251c8488 start_wqthread + 8 (:-1)

Thread 16:
0   libsystem_pthread.dylib       	0x00000002251c8480 start_wqthread + 0 (:-1)


Thread 15 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x0000000200a8ed58  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x000000019d116bbc  x14: 0x00000000001ff800  x15: 0x00000000000007fb
   x16: 0x0000000000000030  x17: 0x00000002093a6ae0  x18: 0x0000000000000000  x19: 0x000000016b153000
   x20: 0x000000016b14e498  x21: 0x000000016b14e540  x22: 0x00000001fe9f8000  x23: 0x0000000000000060
   x24: 0x000000011a632400  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x000000020b9d98f0   fp: 0x000000016b14e4b0   lr: 0x00000001a4a7bb8c
    sp: 0x000000016b14e480   pc: 0x00000001a4a7bb8c cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x104ff0000 -         0x105dcffff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/发型测试
        0x10619c000 -         0x1061abfff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x1061c8000 -         0x1061d7fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x1061f0000 -         0x1061fbfff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x1062f8000 -         0x106303fff libobjc-trampolines.dylib arm64e  <35a44678195b39c2bdd7072893564b45> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x1063a8000 -         0x1063dffff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x106454000 -         0x106467fff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/Promises.framework/Promises
        0x106488000 -         0x106493fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x1064b8000 -         0x1064cbfff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x1064e8000 -         0x1064f7fff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x106508000 -         0x106513fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x106534000 -         0x106573fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x106670000 -         0x106797fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x10693c000 -         0x106953fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x106990000 -         0x1069affff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x106a14000 -         0x106a67fff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x106aec000 -         0x106b17fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x106c04000 -         0x106c33fff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x106c9c000 -         0x106cfffff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x106d34000 -         0x106d5ffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x106db0000 -         0x106fa7fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x10723c000 -         0x1072a7fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x107328000 -         0x1073bbfff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x107764000 -         0x107893fff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x107cfc000 -         0x107cfffff iCloudDriveFileProviderOverride arm64e  <5de9ce32cd703abca0b951643ad20971> /System/Library/Frameworks/FileProvider.framework/OverrideBundles/iCloudDriveFileProviderOverride.bundle/iCloudDriveFileProviderOverride
        0x107d2c000 -         0x107eebfff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x1081e8000 -         0x1081effff FileProviderOverride arm64e  <664c9c68fd2032c39751b24182c710a7> /System/Library/Frameworks/FileProvider.framework/OverrideBundles/FileProviderOverride.bundle/FileProviderOverride
        0x108258000 -         0x109533fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/EC446B94-DC4A-4F64-A0D1-89BF7C653AD9/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x199fa8000 -         0x199ff8d5f libobjc.A.dylib arm64e  <1608892e67db3f949fc291492b86c95f> /usr/lib/libobjc.A.dylib
        0x19b8ad000 -         0x19c5bafff Foundation arm64e  <6d0212cc3b9e32c9be2072989ce3acb8> /System/Library/Frameworks/Foundation.framework/Foundation
        0x19cc6a000 -         0x19d1acfff CoreFoundation arm64e  <1532d3d89b3b3f2fb35f55a20ddf411b> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x19e13b000 -         0x19e4fdfff CFNetwork arm64e  <999c659afc7d351fa477e97bbf2d8081> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x19e71b000 -         0x19eac0fff QuartzCore arm64e  <d8e8e86d85ac3c90b2e1940235ecaa18> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x19f450000 -         0x1a1323fff UIKitCore arm64e  <575e5140fa6a37c2b00ba4eacedfda53> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1a4a04000 -         0x1a4a83ff3 libsystem_c.dylib arm64e  <0150f750db0a3f54b23ad21c55af8824> /usr/lib/system/libsystem_c.dylib
        0x1aa13c000 -         0x1aa540fff CoreMotion arm64e  <ad76b51c2c19371888c6e6a9d73d5868> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1bfc4a000 -         0x1bfc93fff CoreAutoLayout arm64e  <ca8d12295392352ab1e2cf328996c4d3> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1c2677000 -         0x1c26fa99f dyld arm64e  <3060d36a16ce3c3a92583881459f5714> /usr/lib/dyld
        0x1e8c9b000 -         0x1e8ca3fff GraphicsServices arm64e  <8425ea11000e3e5e8abcbddf3ff3fa32> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1ed07d000 -         0x1ed0b6ff3 libsystem_kernel.dylib arm64e  <b9618c71c0cb31b6825f92a4737c890e> /usr/lib/system/libsystem_kernel.dylib
        0x224fca000 -         0x224fe4fff libc++abi.dylib arm64e  <5e1a37143fad3ad7a23d61c4be170233> /usr/lib/libc++abi.dylib
        0x2251c7000 -         0x2251d3ff3 libsystem_pthread.dylib arm64e  <3ca98e388eee3c269862c5f66aad93c0> /usr/lib/system/libsystem_pthread.dylib

EOF
