Incident Identifier: 8D9830F3-68DC-4881-BA3C-5A9BEDEE85EA
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone15,3
Process:             发型测试 [3715]
Path:                /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone15,3:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [6659]

Date/Time:           2025-08-06 21:23:46.5967 +0800
Launch Time:         2025-08-06 21:23:19.5215 +0800
OS Version:          iPhone OS 18.2.1 (22C161)
Release Type:        User
Baseband Version:    3.20.05
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001989c5bbc
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [3715]

Triggered by Thread:  9

Last Exception Backtrace:
0   CoreFoundation                	0x190b855ec __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x18e101244 objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1b444eac8 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1b445651c -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x1933f6090 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1600)
5   UIKitCore                     	0x1933af6f4 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2164 (UIView.m:19881)
6   QuartzCore                    	0x1926c0c30 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10952)
7   QuartzCore                    	0x1926c07bc CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2645)
8   QuartzCore                    	0x19271589c CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
9   QuartzCore                    	0x19268c56c CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
10  QuartzCore                    	0x192864854 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
11  libsystem_pthread.dylib       	0x21b7bcfc4 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x21b7bcd34 _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x21b7bcce0 _pthread_wqthread_exit + 56 (pthread.c:2656)
14  libsystem_pthread.dylib       	0x21b7bb708 _pthread_wqthread + 424 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x21b7b9474 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001e21b6788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e21b9e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e21b9db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e21b9bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000190bce7f4 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000190bcdea0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000190c20274 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001ddd994c0 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x000000019376677c -[UIApplication _run] + 816 (UIApplication.m:3846)
9   UIKitCore                     	0x000000019338ce64 UIApplicationMain + 340 (UIApplication.m:5503)
10  UIKitCore                     	0x0000000193ac9a7c UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000102aa9130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000102aa9130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000102aa9130 main + 120
14  dyld                          	0x00000001b6df4de8 start + 2724 (dyldMain.cpp:1338)

Thread 1:
0   libsystem_pthread.dylib       	0x000000021b7b946c start_wqthread + 0 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x000000021b7b946c start_wqthread + 0 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001e21b6788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e21b9e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e21b9db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e21b9bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000190bce7f4 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000190bcdea0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000190c20274 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   Foundation                    	0x000000018f786b48 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x000000018f8e36f4 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x00000001937f9b80 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1351)
10  Foundation                    	0x000000018f872a54 __NSThread__start__ + 724 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000021b7b97d0 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000021b7b9480 thread_start + 8 (:-1)

Thread 4:
0   libsystem_kernel.dylib        	0x00000001e21bc2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001989665cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000198966444 sleep + 52 (sleep.c:62)
3   发型测试                          	0x00000001032deef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x000000021b7b97d0 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021b7b9480 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001e21b6788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e21b9e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e21b7cfc thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x00000001032b697c handleExceptions + 120
4   libsystem_pthread.dylib       	0x000000021b7b97d0 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021b7b9480 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e21b6788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e21b9f30 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e21b9db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e21b9bfc mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001032b69a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x000000021b7b97d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021b7b9480 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e21bc2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001989665cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001989664e4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x000000010321e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x00000001031d0e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021b7b97d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021b7b9480 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001e21bc2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001989665cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001989664e4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x000000010321e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x00000001031d0e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021b7b97d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021b7b9480 thread_start + 8 (:-1)

Thread 9 Crashed:
0   libsystem_c.dylib             	0x00000001989c5bbc __abort + 168 (abort.c:175)
1   libsystem_c.dylib             	0x00000001989c5b14 abort + 140 (abort.c:130)
2   libc++abi.dylib               	0x000000021b6e65b8 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000021b6d4bac demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000018e1032c4 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x00000001032b522c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x000000021b6e587c std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000021b6e8dfc __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x000000021b6e8da4 __cxa_throw + 92 (cxa_exception.cpp:294)
9   libobjc.A.dylib               	0x000000018e1013ac objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001b444eac8 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001b445651c -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x00000001933f6090 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1600)
13  UIKitCore                     	0x00000001933af6f4 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2164 (UIView.m:19881)
14  QuartzCore                    	0x00000001926c0c30 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10952)
15  QuartzCore                    	0x00000001926c07bc CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2645)
16  QuartzCore                    	0x000000019271589c CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
17  QuartzCore                    	0x000000019268c56c CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
18  QuartzCore                    	0x0000000192864854 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
19  libsystem_pthread.dylib       	0x000000021b7bcfc4 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x000000021b7bcd34 _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x000000021b7bcce0 _pthread_wqthread_exit + 56 (pthread.c:2656)
22  libsystem_pthread.dylib       	0x000000021b7bb708 _pthread_wqthread + 424 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x000000021b7b9474 start_wqthread + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001e21bc090 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x000000021b7bbf98 _pthread_cond_wait + 1204 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001a788d034 scavenger_thread_main + 1524 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x000000021b7b97d0 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x000000021b7b9480 thread_start + 8 (:-1)

Thread 11 name:
Thread 11:
0   libsystem_kernel.dylib        	0x00000001e21b6788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e21b9e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e21b9db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e21b9bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000190bce7f4 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000190bcdea0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000190c20274 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CFNetwork                     	0x000000019213f020 +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x000000018f872a54 __NSThread__start__ + 724 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000021b7b97d0 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021b7b9480 thread_start + 8 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001e21b6788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e21b9e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e21b9db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e21b9bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000190bce7f4 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000190bcdea0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000190c20274 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CoreFoundation                	0x0000000190c33814 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x000000019e286c6c CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000021b7b97d0 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021b7b9480 thread_start + 8 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x000000021b7b946c start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x000000021b7b946c start_wqthread + 0 (:-1)

Thread 15:
0   libsystem_pthread.dylib       	0x000000021b7b946c start_wqthread + 0 (:-1)


Thread 9 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000001f9193a18  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001910059ac  x14: 0x00000000001ff800  x15: 0x00000000000007fb
   x16: 0x0000000000000030  x17: 0x0000000202098468  x18: 0x0000000000000000  x19: 0x000000016dfbf000
   x20: 0x000000016dfba498  x21: 0x000000016dfba540  x22: 0x00000001f6e80000  x23: 0x0000000000000060
   x24: 0x000000010c2edc00  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x00000002045d9418   fp: 0x000000016dfba4b0   lr: 0x00000001989c5bbc
    sp: 0x000000016dfba480   pc: 0x00000001989c5bbc cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x1028a0000 -         0x10367ffff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/发型测试
        0x103a6c000 -         0x103a77fff libobjc-trampolines.dylib arm64e  <be05652226b13a508ad193ac99fcdc9c> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x103b4c000 -         0x103b5bfff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x103b78000 -         0x103b87fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x103ba0000 -         0x103babfff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x103bcc000 -         0x103c03fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x103c78000 -         0x103c83fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x103c98000 -         0x103ca7fff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x103d40000 -         0x103d53fff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/Promises.framework/Promises
        0x103d74000 -         0x103d7ffff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x103de8000 -         0x103dfbfff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x103e6c000 -         0x103e83fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x103ec8000 -         0x103f07fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x103fac000 -         0x103ffffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x1040b0000 -         0x1041d7fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x10437c000 -         0x10439bfff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x1043fc000 -         0x104427fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x104514000 -         0x104543fff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1045ac000 -         0x10460ffff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x104644000 -         0x10466ffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1046c0000 -         0x1048b7fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x104b4c000 -         0x104bb7fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x104c38000 -         0x104ccbfff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x105074000 -         0x1051a3fff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x10563c000 -         0x1057fbfff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x105c98000 -         0x106f73fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/391C72A0-E5DB-426D-BD92-BDD8409449C0/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x18e0d0000 -         0x18e120ccf libobjc.A.dylib arm64e  <3ed8b852087d3b6ea36fceafed8e93ae> /usr/lib/libobjc.A.dylib
        0x18f75d000 -         0x19048dfff Foundation arm64e  <7274dde368d634a08e677726e1265e80> /System/Library/Frameworks/Foundation.framework/Foundation
        0x190b58000 -         0x19109bfff CoreFoundation arm64e  <6a60be13e6573beca9acba239ae29862> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x19204d000 -         0x192411fff CFNetwork arm64e  <9987879240643260b5b2779a99b9f64e> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x192630000 -         0x1929dcfff QuartzCore arm64e  <7404049cd50a3bd89c75ebae4b91379b> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x193378000 -         0x19528dfff UIKitCore arm64e  <f80c6ee450ca346f90ebbb3da9817503> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x19894e000 -         0x1989cdffb libsystem_c.dylib arm64e  <8d425c7257c93e54a1e1e243cbdfc446> /usr/lib/system/libsystem_c.dylib
        0x19e277000 -         0x19e67ffff CoreMotion arm64e  <98d9e3e27d6b3517b3df2f786ccdaa8b> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1a7790000 -         0x1a8fc1fdf JavaScriptCore arm64e  <e77cb3939382337e81ec244a393fdb35> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1b444a000 -         0x1b4493fff CoreAutoLayout arm64e  <f6485317cfd03f8c8bb4864668de35d2> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1b6dc5000 -         0x1b6e4813f dyld arm64e  <4eb7459fe23738ce82403f3e2e1ce5ab> /usr/lib/dyld
        0x1ddd98000 -         0x1ddda0fff GraphicsServices arm64e  <f4e7a885f4913721862dc57403f4d821> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e21b5000 -         0x1e21eefe3 libsystem_kernel.dylib arm64e  <e3965df1a3a3374a94eaf86739c5cc8e> /usr/lib/system/libsystem_kernel.dylib
        0x21b6d3000 -         0x21b6edfff libc++abi.dylib arm64e  <355dab547c2030eba6bad22f6d42537a> /usr/lib/libc++abi.dylib
        0x21b7b8000 -         0x21b7c4ff3 libsystem_pthread.dylib arm64e  <b2fe0dfa67de3d7282676c42073e0e8d> /usr/lib/system/libsystem_pthread.dylib

EOF
