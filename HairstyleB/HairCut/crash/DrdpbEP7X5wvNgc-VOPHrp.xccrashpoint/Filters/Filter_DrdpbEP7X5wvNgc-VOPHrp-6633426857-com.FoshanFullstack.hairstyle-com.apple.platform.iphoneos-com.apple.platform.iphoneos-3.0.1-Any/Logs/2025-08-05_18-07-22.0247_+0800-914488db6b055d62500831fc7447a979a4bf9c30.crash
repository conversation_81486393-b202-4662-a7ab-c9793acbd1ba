Incident Identifier: F425996D-E3E4-4C88-A04B-5898D17931F6
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone12,1
Process:             发型测试 [10264]
Path:                /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone12,1:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [1544]

Date/Time:           2025-08-05 18:07:22.0247 +0800
Launch Time:         2025-08-05 18:06:59.3550 +0800
OS Version:          iPhone OS 18.3.1 (22D72)
Release Type:        User
Baseband Version:    6.01.01
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000018cbc8bbc
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [10264]

Triggered by Thread:  3

Last Exception Backtrace:
0   CoreFoundation                	0x184d7e5fc __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x1822f9244 objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1a8692ac8 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1a869a51c -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x1875ee0ac -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1600)
5   UIKitCore                     	0x1875a7710 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2164 (UIView.m:19881)
6   QuartzCore                    	0x1868b9498 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10954)
7   QuartzCore                    	0x1868b9024 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2646)
8   QuartzCore                    	0x18690e0c4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
9   QuartzCore                    	0x186884d8c CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
10  QuartzCore                    	0x186a5d2a8 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
11  libsystem_pthread.dylib       	0x20fa95fc4 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x20fa95d34 _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x20fa95ce0 _pthread_wqthread_exit + 56 (pthread.c:2656)
14  libsystem_pthread.dylib       	0x20fa94708 _pthread_wqthread + 424 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x20fa92474 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001d6474788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d6477e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d6477db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d6477bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000184dc7804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000184dc6eb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000184e19284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001d20554c0 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x000000018795e674 -[UIApplication _run] + 816 (UIApplication.m:3846)
9   UIKitCore                     	0x0000000187584e88 UIApplicationMain + 340 (UIApplication.m:5503)
10  UIKitCore                     	0x0000000187cc115c UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000100449130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000100449130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000100449130 main + 120
14  dyld                          	0x00000001ab059de8 start + 2724 (dyldMain.cpp:1338)

Thread 1:
0   libsystem_pthread.dylib       	0x000000020fa9246c start_wqthread + 0 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x000000020fa9246c start_wqthread + 0 (:-1)

Thread 3 Crashed:
0   libsystem_c.dylib             	0x000000018cbc8bbc __abort + 168 (abort.c:175)
1   libsystem_c.dylib             	0x000000018cbc8b14 abort + 140 (abort.c:130)
2   libc++abi.dylib               	0x000000020f9bf5b8 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000020f9adbac demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x00000001822fb2c4 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x0000000100c5522c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x000000020f9be87c std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000020f9c1dfc __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x000000020f9c1da4 __cxa_throw + 92 (cxa_exception.cpp:294)
9   libobjc.A.dylib               	0x00000001822f93ac objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001a8692ac8 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001a869a51c -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x00000001875ee0ac -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1600)
13  UIKitCore                     	0x00000001875a7710 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2164 (UIView.m:19881)
14  QuartzCore                    	0x00000001868b9498 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10954)
15  QuartzCore                    	0x00000001868b9024 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2646)
16  QuartzCore                    	0x000000018690e0c4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
17  QuartzCore                    	0x0000000186884d8c CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
18  QuartzCore                    	0x0000000186a5d2a8 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
19  libsystem_pthread.dylib       	0x000000020fa95fc4 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x000000020fa95d34 _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x000000020fa95ce0 _pthread_wqthread_exit + 56 (pthread.c:2656)
22  libsystem_pthread.dylib       	0x000000020fa94708 _pthread_wqthread + 424 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x000000020fa92474 start_wqthread + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001d6474788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d6477e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d6477db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d6477bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000184dc7804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000184dc6eb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000184e19284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   Foundation                    	0x000000018397f0e8 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x0000000183adbbb0 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x00000001879f1a78 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1351)
10  Foundation                    	0x0000000183a6af30 __NSThread__start__ + 724 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000020fa927d0 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000020fa92480 thread_start + 8 (:-1)

Thread 5:
0   libsystem_pthread.dylib       	0x000000020fa9246c start_wqthread + 0 (:-1)

Thread 6:
0   libsystem_pthread.dylib       	0x000000020fa9246c start_wqthread + 0 (:-1)

Thread 7:
0   libsystem_kernel.dylib        	0x00000001d647a2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000018cb695cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000018cb69444 sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000100c7eef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x000000020fa927d0 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000020fa92480 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001d6474788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d6477e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d6475cfc thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x0000000100c5697c handleExceptions + 120
4   libsystem_pthread.dylib       	0x000000020fa927d0 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000020fa92480 thread_start + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001d6474788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d6477f30 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001d6477db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d6477bfc mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000100c569a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x000000020fa927d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000020fa92480 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001d647a2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000018cb695cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000018cb694e4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000100bbe580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000100b70e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000020fa927d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000020fa92480 thread_start + 8 (:-1)

Thread 11 name:
Thread 11:
0   libsystem_kernel.dylib        	0x00000001d647a2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000018cb695cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000018cb694e4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000100bbe580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000100b70e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000020fa927d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000020fa92480 thread_start + 8 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x000000020fa9246c start_wqthread + 0 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x000000020fa9246c start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x000000020fa9246c start_wqthread + 0 (:-1)

Thread 15 name:
Thread 15:
0   libsystem_kernel.dylib        	0x00000001d647a090 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x000000020fa94f98 _pthread_cond_wait + 1204 (pthread_cond.c:862)
2   JavaScriptCore                	0x000000019babc6e4 scavenger_thread_main + 1524 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x000000020fa927d0 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x000000020fa92480 thread_start + 8 (:-1)

Thread 16 name:
Thread 16:
0   libsystem_kernel.dylib        	0x00000001d6474788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d6477e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d6477db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d6477bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000184dc7804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000184dc6eb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000184e19284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CFNetwork                     	0x0000000186337c4c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x0000000183a6af30 __NSThread__start__ + 724 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000020fa927d0 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000020fa92480 thread_start + 8 (:-1)

Thread 17 name:
Thread 17:
0   libsystem_kernel.dylib        	0x00000001d6474788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d6477e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d6477db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d6477bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000184dc7804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000184dc6eb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000184e19284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CoreFoundation                	0x0000000184e2c824 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001924a5950 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000020fa927d0 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000020fa92480 thread_start + 8 (:-1)


Thread 3 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000001ed3af318  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001851fe9ac  x14: 0x00000000001ff800  x15: 0x00000000000007fb
   x16: 0x0000000000000030  x17: 0x00000001f62aa7c8  x18: 0x0000000000000000  x19: 0x000000016fe77000
   x20: 0x000000016fe72498  x21: 0x000000016fe72540  x22: 0x00000001eb084000  x23: 0x0000000000000060
   x24: 0x0000000109206800  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x00000001f88e2c60   fp: 0x000000016fe724b0   lr: 0x000000018cbc8bbc
    sp: 0x000000016fe72480   pc: 0x000000018cbc8bbc cpsr: 0x40000000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x100240000 -         0x10101ffff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/发型测试
        0x101480000 -         0x10148ffff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x101550000 -         0x10155bfff libobjc-trampolines.dylib arm64e  <4aba9420e4d03c989d62c653b259eab4> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x101600000 -         0x10160ffff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x10162c000 -         0x101637fff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x101660000 -         0x101673fff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/Promises.framework/Promises
        0x1016ac000 -         0x1016e3fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x101758000 -         0x101763fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x101788000 -         0x10179bfff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x1017b8000 -         0x1017c7fff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x1017d8000 -         0x1017e3fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x10180c000 -         0x101823fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x101860000 -         0x10187ffff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x1018c8000 -         0x1019effff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x101b94000 -         0x101bd3fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x101c78000 -         0x101ccbfff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x101dd0000 -         0x101dfbfff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x101e50000 -         0x101e7bfff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x101ee8000 -         0x101f17fff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x101f80000 -         0x101fe3fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x102094000 -         0x10228bfff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x102520000 -         0x10258bfff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x10260c000 -         0x10269ffff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x102a48000 -         0x102b77fff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x103010000 -         0x1031cffff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x1034b0000 -         0x10478bfff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/CD719B58-AD9D-484E-83D9-35075A1F1E7F/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x1822c8000 -         0x182318ccf libobjc.A.dylib arm64e  <a6a17b3c335130adaf2815a71b78f050> /usr/lib/libobjc.A.dylib
        0x183955000 -         0x184685fff Foundation arm64e  <e2f95328659e3c0197f752b5b3bb7aa5> /System/Library/Frameworks/Foundation.framework/Foundation
        0x184d51000 -         0x185294fff CoreFoundation arm64e  <0013a8b125243534b5ba681aaf18c798> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x186246000 -         0x18660afff CFNetwork arm64e  <e610c6a8da363e07910f2d4a62320985> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x186829000 -         0x186bd5fff QuartzCore arm64e  <8a08cc2400173108bea429111b40063c> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x187570000 -         0x189488fff UIKitCore arm64e  <8cc54497f7ec3903ae5aa274047c0cf1> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x18cb51000 -         0x18cbd0ffb libsystem_c.dylib arm64e  <400d888f854833fc802ff29678681197> /usr/lib/system/libsystem_c.dylib
        0x192496000 -         0x19289efff CoreMotion arm64e  <6845c463baf9365fa47cf9617091a79a> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x19b9c0000 -         0x19d1f6f3f JavaScriptCore arm64e  <933bc301d4fd36deaf22836554e78fff> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1a868e000 -         0x1a86d7fff CoreAutoLayout arm64e  <122c3b6cdcfe3ae2b112884cef86b3b5> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1ab02a000 -         0x1ab0ad137 dyld arm64e  <a770ff8c8fb93e0385fe7f26db36812b> /usr/lib/dyld
        0x1d2054000 -         0x1d205cfff GraphicsServices arm64e  <3eca7962867b3029adc8bbe100f85ba5> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1d6473000 -         0x1d64acfe3 libsystem_kernel.dylib arm64e  <881fe934759c3089b98660344cb843e3> /usr/lib/system/libsystem_kernel.dylib
        0x20f9ac000 -         0x20f9c6fff libc++abi.dylib arm64e  <93fe31d773fb338eb696211de65fd7ed> /usr/lib/libc++abi.dylib
        0x20fa91000 -         0x20fa9dff3 libsystem_pthread.dylib arm64e  <6f6e49251fb43a0b99d26bd8b7b1a148> /usr/lib/system/libsystem_pthread.dylib

EOF
