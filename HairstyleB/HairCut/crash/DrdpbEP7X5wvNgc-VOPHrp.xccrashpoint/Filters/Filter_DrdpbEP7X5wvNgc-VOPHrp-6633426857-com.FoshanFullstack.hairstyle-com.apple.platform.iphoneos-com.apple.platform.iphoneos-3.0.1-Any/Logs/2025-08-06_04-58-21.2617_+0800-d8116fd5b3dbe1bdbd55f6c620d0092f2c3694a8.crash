Incident Identifier: 0474783F-BF83-44BF-B851-508DA64B71E7
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone14,7
Process:             发型测试 [7018]
Path:                /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone14,7:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [2704]

Date/Time:           2025-08-06 04:58:21.2617 +0800
Launch Time:         2025-08-06 04:57:55.3974 +0800
OS Version:          iPhone OS 18.3.2 (22D82)
Release Type:        User
Baseband Version:    3.40.03
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000018fe24bbc
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [7018]

Triggered by Thread:  1

Last Exception Backtrace:
0   CoreFoundation                	0x187fd65fc __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x185551244 objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1ab902ac8 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1ab902ea4 -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
4   CoreAutoLayout                	0x1ab902dd4 -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
5   CoreAutoLayout                	0x1ab902b60 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
6   UIKitCore                     	0x18a803e24 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 3976 (UIView.m:19970)
7   UIKitCore                     	0x18aa6bd34 -[_UILabelLayer layoutSublayers] + 672 (_UILabelLayer.m:192)
8   QuartzCore                    	0x189b11498 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10954)
9   QuartzCore                    	0x189b11024 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2646)
10  QuartzCore                    	0x189b660c4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
11  QuartzCore                    	0x189adcd8c CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
12  QuartzCore                    	0x189cb52a8 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
13  libsystem_pthread.dylib       	0x212d61fc4 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
14  libsystem_pthread.dylib       	0x212d61d34 _pthread_exit + 84 (pthread.c:1770)
15  libsystem_pthread.dylib       	0x212d61ce0 _pthread_wqthread_exit + 56 (pthread.c:2656)
16  libsystem_pthread.dylib       	0x212d60708 _pthread_wqthread + 424 (pthread.c:2690)
17  libsystem_pthread.dylib       	0x212d5e474 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001d9704788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d9707e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d9707db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d9707bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018801f804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000018801eeb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000188071284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001d52e14c0 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x000000018abba674 -[UIApplication _run] + 816 (UIApplication.m:3846)
9   UIKitCore                     	0x000000018a7e0e88 UIApplicationMain + 340 (UIApplication.m:5503)
10  UIKitCore                     	0x000000018af1d15c UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000100a39130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000100a39130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000100a39130 main + 120
14  dyld                          	0x00000001ae2cdde8 start + 2724 (dyldMain.cpp:1338)

Thread 1 Crashed:
0   libsystem_c.dylib             	0x000000018fe24bbc __abort + 168 (abort.c:175)
1   libsystem_c.dylib             	0x000000018fe24b14 abort + 140 (abort.c:130)
2   libc++abi.dylib               	0x0000000212c8b5b8 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x0000000212c79bac demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x00000001855532c4 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x000000010124522c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x0000000212c8a87c std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x0000000212c8ddfc __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x0000000212c8dda4 __cxa_throw + 92 (cxa_exception.cpp:294)
9   libobjc.A.dylib               	0x00000001855513ac objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001ab902ac8 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001ab902ea4 -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
12  CoreAutoLayout                	0x00000001ab902dd4 -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
13  CoreAutoLayout                	0x00000001ab902b60 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
14  UIKitCore                     	0x000000018a803e24 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 3976 (UIView.m:19970)
15  UIKitCore                     	0x000000018aa6bd34 -[_UILabelLayer layoutSublayers] + 672 (_UILabelLayer.m:192)
16  QuartzCore                    	0x0000000189b11498 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10954)
17  QuartzCore                    	0x0000000189b11024 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2646)
18  QuartzCore                    	0x0000000189b660c4 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
19  QuartzCore                    	0x0000000189adcd8c CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
20  QuartzCore                    	0x0000000189cb52a8 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
21  libsystem_pthread.dylib       	0x0000000212d61fc4 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
22  libsystem_pthread.dylib       	0x0000000212d61d34 _pthread_exit + 84 (pthread.c:1770)
23  libsystem_pthread.dylib       	0x0000000212d61ce0 _pthread_wqthread_exit + 56 (pthread.c:2656)
24  libsystem_pthread.dylib       	0x0000000212d60708 _pthread_wqthread + 424 (pthread.c:2690)
25  libsystem_pthread.dylib       	0x0000000212d5e474 start_wqthread + 8 (:-1)

Thread 2 name:
Thread 2:
0   libsystem_kernel.dylib        	0x00000001d9704788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d9707e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d9707db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d9707bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018801f804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000018801eeb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000188071284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   Foundation                    	0x0000000186bd70e8 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x0000000186d33bb0 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x000000018ac4da78 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1351)
10  Foundation                    	0x0000000186cc2f30 __NSThread__start__ + 724 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x0000000212d5e7d0 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x0000000212d5e480 thread_start + 8 (:-1)

Thread 3:
0   libsystem_pthread.dylib       	0x0000000212d5e46c start_wqthread + 0 (:-1)

Thread 4:
0   libsystem_pthread.dylib       	0x0000000212d5e46c start_wqthread + 0 (:-1)

Thread 5:
0   libsystem_kernel.dylib        	0x00000001d970a2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000018fdc55cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000018fdc5444 sleep + 52 (sleep.c:62)
3   发型测试                          	0x000000010126eef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x0000000212d5e7d0 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000212d5e480 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001d9704788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d9707e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d9705cfc thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x000000010124697c handleExceptions + 120
4   libsystem_pthread.dylib       	0x0000000212d5e7d0 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000212d5e480 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001d9704788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d9707f30 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001d9707db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d9707bfc mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001012469a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x0000000212d5e7d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000212d5e480 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001d970a2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000018fdc55cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000018fdc54e4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001011ae580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000101160e84 thread_do + 340
5   libsystem_pthread.dylib       	0x0000000212d5e7d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000212d5e480 thread_start + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001d970a2b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000018fdc55cc nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000018fdc54e4 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001011ae580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000101160e84 thread_do + 340
5   libsystem_pthread.dylib       	0x0000000212d5e7d0 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000212d5e480 thread_start + 8 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x0000000212d5e46c start_wqthread + 0 (:-1)

Thread 11 name:
Thread 11:
0   libsystem_kernel.dylib        	0x00000001d970a090 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x0000000212d60f98 _pthread_cond_wait + 1204 (pthread_cond.c:862)
2   JavaScriptCore                	0x000000019ed246e4 0x19ec28000 + 1033956
3   libsystem_pthread.dylib       	0x0000000212d5e7d0 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x0000000212d5e480 thread_start + 8 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001d9704788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d9707e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d9707db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d9707bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018801f804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000018801eeb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000188071284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CFNetwork                     	0x000000018958fc4c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x0000000186cc2f30 __NSThread__start__ + 724 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x0000000212d5e7d0 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000212d5e480 thread_start + 8 (:-1)

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001d9704788 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001d9707e98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001d9707db0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001d9707bfc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000018801f804 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000018801eeb0 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000188071284 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CoreFoundation                	0x0000000188084824 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x0000000195705950 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x0000000212d5e7d0 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000212d5e480 thread_start + 8 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x0000000212d5e46c start_wqthread + 0 (:-1)

Thread 15:
0   libsystem_pthread.dylib       	0x0000000212d5e46c start_wqthread + 0 (:-1)

Thread 16:
0   libsystem_pthread.dylib       	0x0000000212d5e46c start_wqthread + 0 (:-1)


Thread 1 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x00000001f064bad8  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001884569ac  x14: 0x00000000001ff800  x15: 0x00000000000007fb
   x16: 0x0000000000000030  x17: 0x00000001f9546428  x18: 0x0000000000000000  x19: 0x000000016f7fb000
   x20: 0x000000016f7f6228  x21: 0x000000016f7f62d0  x22: 0x00000001ee320000  x23: 0x0000000000000060
   x24: 0x0000000000000000  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x00000001fb7d7fd0   fp: 0x000000016f7f6240   lr: 0x000000018fe24bbc
    sp: 0x000000016f7f6210   pc: 0x000000018fe24bbc cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x100830000 -         0x10160ffff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/发型测试
        0x1019d8000 -         0x1019e3fff libobjc-trampolines.dylib arm64e  <4aba9420e4d03c989d62c653b259eab4> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x101adc000 -         0x101aebfff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x101b08000 -         0x101b17fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x101b30000 -         0x101b3bfff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x101b64000 -         0x101b77fff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/Promises.framework/Promises
        0x101b98000 -         0x101ba3fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x101c40000 -         0x101c4ffff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x101c60000 -         0x101c6bfff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x101ce8000 -         0x101d1ffff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x101d94000 -         0x101da7fff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x101dc4000 -         0x101ddbfff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x101e18000 -         0x101e37fff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x101e74000 -         0x101eb3fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x101fb0000 -         0x1020d7fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x10227c000 -         0x1022cffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x1023d4000 -         0x1023fffff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x102454000 -         0x10247ffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1024ec000 -         0x10251bfff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x102584000 -         0x1025e7fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x102698000 -         0x10288ffff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x102b24000 -         0x102b8ffff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x102c10000 -         0x102ca3fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x10304c000 -         0x10317bfff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x103614000 -         0x1037d3fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x103b98000 -         0x104e73fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/********-CE36-4FE9-AD14-1A6F6DEB44A9/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x185520000 -         0x185570ccf libobjc.A.dylib arm64e  <a6a17b3c335130adaf2815a71b78f050> /usr/lib/libobjc.A.dylib
        0x186bad000 -         0x1878ddfff Foundation arm64e  <e2f95328659e3c0197f752b5b3bb7aa5> /System/Library/Frameworks/Foundation.framework/Foundation
        0x187fa9000 -         0x1884ecfff CoreFoundation arm64e  <0013a8b125243534b5ba681aaf18c798> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x18949e000 -         0x189862fff CFNetwork arm64e  <e610c6a8da363e07910f2d4a62320985> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x189a81000 -         0x189e2dfff QuartzCore arm64e  <8a08cc2400173108bea429111b40063c> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x18a7cc000 -         0x18c6e4fff UIKitCore arm64e  <8cc54497f7ec3903ae5aa274047c0cf1> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x18fdad000 -         0x18fe2cffb libsystem_c.dylib arm64e  <400d888f854833fc802ff29678681197> /usr/lib/system/libsystem_c.dylib
        0x1956f6000 -         0x195afefff CoreMotion arm64e  <6845c463baf9365fa47cf9617091a79a> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x19ec28000 -         0x1a045ef3f JavaScriptCore arm64e  <2952bfa6959939dbbca250e97ad38818> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1ab8fe000 -         0x1ab947fff CoreAutoLayout arm64e  <122c3b6cdcfe3ae2b112884cef86b3b5> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1ae29e000 -         0x1ae321137 dyld arm64e  <a770ff8c8fb93e0385fe7f26db36812b> /usr/lib/dyld
        0x1d52e0000 -         0x1d52e8fff GraphicsServices arm64e  <3eca7962867b3029adc8bbe100f85ba5> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1d9703000 -         0x1d973cfe3 libsystem_kernel.dylib arm64e  <881fe934759c3089b98660344cb843e3> /usr/lib/system/libsystem_kernel.dylib
        0x212c78000 -         0x212c92fff libc++abi.dylib arm64e  <93fe31d773fb338eb696211de65fd7ed> /usr/lib/libc++abi.dylib
        0x212d5d000 -         0x212d69ff3 libsystem_pthread.dylib arm64e  <6f6e49251fb43a0b99d26bd8b7b1a148> /usr/lib/system/libsystem_pthread.dylib

EOF
