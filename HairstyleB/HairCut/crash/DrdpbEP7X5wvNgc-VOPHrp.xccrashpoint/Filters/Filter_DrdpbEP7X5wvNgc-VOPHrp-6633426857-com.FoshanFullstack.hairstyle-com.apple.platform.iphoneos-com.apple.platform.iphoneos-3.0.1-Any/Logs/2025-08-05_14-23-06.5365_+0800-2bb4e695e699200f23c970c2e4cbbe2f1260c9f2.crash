Incident Identifier: DCBA8DFA-A5D5-417B-B203-8393B773EA7B
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone15,4
Process:             发型测试 [9906]
Path:                /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone15,4:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [3172]

Date/Time:           2025-08-05 14:23:06.5365 +0800
Launch Time:         2025-08-05 14:22:51.0611 +0800
OS Version:          iPhone OS 18.1.1 (22B91)
Release Type:        User
Baseband Version:    2.20.03
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001a7cc7b8c
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [9906]

Triggered by Thread:  12

Last Exception Backtrace:
0   CoreFoundation                	0x19ff387cc __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x19d20b2e4 objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1c2ea8340 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1c2ea8000 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x1a271fda0 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1603)
5   UIKitCore                     	0x1a26a9584 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2164 (UIView.m:19901)
6   QuartzCore                    	0x1a19dfc28 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10944)
7   QuartzCore                    	0x1a19df7b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2638)
8   QuartzCore                    	0x1a1a36914 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
9   QuartzCore                    	0x1a19b57c4 CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
10  QuartzCore                    	0x1a1b95318 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
11  libsystem_pthread.dylib       	0x22843c258 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x22843bfc8 _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x22843bf74 _pthread_wqthread_exit + 56 (pthread.c:2656)
14  libsystem_pthread.dylib       	0x22843bd04 _pthread_wqthread + 424 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x228438488 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001f02ca688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001f02cdd98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001f02cdcb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001f02cdafc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ff09a84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019ff09130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019ff08830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001ebee81c4 GSEventRunModal + 164 (GSEvent.c:2196)
8   UIKitCore                     	0x00000001a2a6eeb0 -[UIApplication _run] + 816 (UIApplication.m:3844)
9   UIKitCore                     	0x00000001a2b1d5b4 UIApplicationMain + 340 (UIApplication.m:5496)
10  UIKitCore                     	0x00000001a2e57fa8 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x00000001023e9130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x00000001023e9130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x00000001023e9130 main + 120
14  dyld                          	0x00000001c58f6ec8 start + 2724 (dyldMain.cpp:1334)

Thread 1:
0   libsystem_pthread.dylib       	0x0000000228438480 start_wqthread + 0 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x0000000228438480 start_wqthread + 0 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001f02ca688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001f02cdd98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001f02cdcb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001f02cdafc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ff09a84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019ff09130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019ff08830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   Foundation                    	0x000000019ebb0500 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:373)
8   Foundation                    	0x000000019ebb0350 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:420)
9   UIKitCore                     	0x00000001a2a82358 -[UIEventFetcher threadMain] + 420 (UIEventFetcher.m:1241)
10  Foundation                    	0x000000019ebc16c8 __NSThread__start__ + 724 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000022843d37c _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x0000000228438494 thread_start + 8 (:-1)

Thread 4:
0   libsystem_kernel.dylib        	0x00000001f02d01b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a7c67a78 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a7cc5550 sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000102c1eef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x000000022843d37c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000228438494 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001f02ca688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001f02cdd98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001f02cbbfc thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x0000000102bf697c handleExceptions + 120
4   libsystem_pthread.dylib       	0x000000022843d37c _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000228438494 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001f02ca688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001f02cde30 mach_msg2_internal + 232 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001f02cdcb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001f02cdafc mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000102bf69a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x000000022843d37c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000228438494 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001f02d01b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a7c67a78 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a7c67990 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000102b5e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000102b10e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000022843d37c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000228438494 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001f02d01b0 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a7c67a78 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a7c67990 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000102b5e580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000102b10e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000022843d37c _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000228438494 thread_start + 8 (:-1)

Thread 9:
0   libsystem_pthread.dylib       	0x0000000228438480 start_wqthread + 0 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x0000000228438480 start_wqthread + 0 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x0000000228438480 start_wqthread + 0 (:-1)

Thread 12 Crashed:
0   libsystem_c.dylib             	0x00000001a7cc7b8c __abort + 168 (abort.c:175)
1   libsystem_c.dylib             	0x00000001a7cc7ae4 abort + 140 (abort.c:130)
2   libc++abi.dylib               	0x000000022824d5b8 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000022823bbac demangling_terminate_handler() + 348 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000019d226e14 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x0000000102bf522c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x000000022824c87c std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000022824fdfc __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x000000022824fda4 __cxa_throw + 92 (cxa_exception.cpp:294)
9   libobjc.A.dylib               	0x000000019d20b44c objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001c2ea8340 _AssertAutoLayoutOnAllowedThreadsOnly + 320 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001c2ea8000 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x00000001a271fda0 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1603)
13  UIKitCore                     	0x00000001a26a9584 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2164 (UIView.m:19901)
14  QuartzCore                    	0x00000001a19dfc28 CA::Layer::layout_if_needed(CA::Transaction*) + 496 (CALayer.mm:10944)
15  QuartzCore                    	0x00000001a19df7b4 CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 148 (CALayer.mm:2638)
16  QuartzCore                    	0x00000001a1a36914 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 472 (CAContextInternal.mm:2613)
17  QuartzCore                    	0x00000001a19b57c4 CA::Transaction::commit() + 648 (CATransactionInternal.mm:420)
18  QuartzCore                    	0x00000001a1b95318 CA::Transaction::release_thread(void*) + 228 (CATransactionInternal.mm:618)
19  libsystem_pthread.dylib       	0x000000022843c258 _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x000000022843bfc8 _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x000000022843bf74 _pthread_wqthread_exit + 56 (pthread.c:2656)
22  libsystem_pthread.dylib       	0x000000022843bd04 _pthread_wqthread + 424 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x0000000228438488 start_wqthread + 8 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x0000000228438480 start_wqthread + 0 (:-1)

Thread 14 name:
Thread 14:
0   libsystem_kernel.dylib        	0x00000001f02cff90 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	0x000000022843aa50 _pthread_cond_wait + 1204 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001b7c7aca4 0x1b66d4000 + 22703268
3   libsystem_pthread.dylib       	0x000000022843d37c _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x0000000228438494 thread_start + 8 (:-1)

Thread 15 name:
Thread 15:
0   libsystem_kernel.dylib        	0x00000001f02ca688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001f02cdd98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001f02cdcb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001f02cdafc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ff09a84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019ff09130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019ff08830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CFNetwork                     	0x00000001a1484ee0 +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x000000019ebc16c8 __NSThread__start__ + 724 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000022843d37c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000228438494 thread_start + 8 (:-1)

Thread 16 name:
Thread 16:
0   libsystem_kernel.dylib        	0x00000001f02ca688 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001f02cdd98 mach_msg2_internal + 80 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001f02cdcb0 mach_msg_overwrite + 424 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001f02cdafc mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019ff09a84 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019ff09130 __CFRunLoopRun + 1212 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019ff08830 CFRunLoopRunSpecific + 588 (CFRunLoop.c:3434)
7   CoreFoundation                	0x000000019ff73cec CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001ad398084 CLMotionCore::runMotionThread(void*) + 1292 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000022843d37c _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000228438494 thread_start + 8 (:-1)


Thread 12 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x0000000000000023
    x8: 0x00000000ffffffe7   x9: 0x0000000203cdb318  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001a0362bbc  x14: 0x00000000ffffffa4  x15: 0x00000000000007fb
   x16: 0x0000000000000030  x17: 0x000000020c5f2ae0  x18: 0x0000000000000000  x19: 0x000000016e823000
   x20: 0x000000016e81e498  x21: 0x000000016e81e540  x22: 0x0000000201c44000  x23: 0x0000000000000060
   x24: 0x000000010ba69c00  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x000000020ec259a0   fp: 0x000000016e81e4b0   lr: 0x00000001a7cc7b8c
    sp: 0x000000016e81e480   pc: 0x00000001a7cc7b8c cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x1021e0000 -         0x102fbffff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/发型测试
        0x103388000 -         0x103393fff libobjc-trampolines.dylib arm64e  <35a44678195b39c2bdd7072893564b45> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x10348c000 -         0x10349bfff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x1034b8000 -         0x1034c7fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x1034e0000 -         0x1034ebfff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x10350c000 -         0x103543fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x1035b8000 -         0x1035c3fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x1035ec000 -         0x1035fffff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/Promises.framework/Promises
        0x103620000 -         0x103633fff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x103650000 -         0x10365ffff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x103670000 -         0x10367bfff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x1036a0000 -         0x1036bffff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x103778000 -         0x1037b7fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x10385c000 -         0x103873fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x1038b0000 -         0x1038dbfff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x103934000 -         0x103987fff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x103a40000 -         0x103b67fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x103da4000 -         0x103dd3fff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x103e3c000 -         0x103e9ffff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x103ed4000 -         0x103efffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x103f50000 -         0x104147fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x1043dc000 -         0x104447fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x1044c8000 -         0x10455bfff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x104904000 -         0x104a33fff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x104ecc000 -         0x10508bfff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x105628000 -         0x106903fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/99990BF3-FE06-43C5-9DD4-A0EEAB228C2B/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x107984000 -         0x107987fff iCloudDriveFileProviderOverride arm64e  <5de9ce32cd703abca0b951643ad20971> /System/Library/Frameworks/FileProvider.framework/OverrideBundles/iCloudDriveFileProviderOverride.bundle/iCloudDriveFileProviderOverride
        0x1079b0000 -         0x1079b7fff FileProviderOverride arm64e  <664c9c68fd2032c39751b24182c710a7> /System/Library/Frameworks/FileProvider.framework/OverrideBundles/FileProviderOverride.bundle/FileProviderOverride
        0x19d1f4000 -         0x19d244d5f libobjc.A.dylib arm64e  <1608892e67db3f949fc291492b86c95f> /usr/lib/libobjc.A.dylib
        0x19eaf9000 -         0x19f806fff Foundation arm64e  <6d0212cc3b9e32c9be2072989ce3acb8> /System/Library/Frameworks/Foundation.framework/Foundation
        0x19feb6000 -         0x1a03f8fff CoreFoundation arm64e  <1532d3d89b3b3f2fb35f55a20ddf411b> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x1a1387000 -         0x1a1749fff CFNetwork arm64e  <999c659afc7d351fa477e97bbf2d8081> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x1a1967000 -         0x1a1d0cfff QuartzCore arm64e  <d8e8e86d85ac3c90b2e1940235ecaa18> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x1a269c000 -         0x1a456ffff UIKitCore arm64e  <575e5140fa6a37c2b00ba4eacedfda53> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1a7c50000 -         0x1a7ccfff3 libsystem_c.dylib arm64e  <0150f750db0a3f54b23ad21c55af8824> /usr/lib/system/libsystem_c.dylib
        0x1ad388000 -         0x1ad78cfff CoreMotion arm64e  <ad76b51c2c19371888c6e6a9d73d5868> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1b66d4000 -         0x1b7e58fff JavaScriptCore arm64e  <c3d567d87bc136f1a811d8b12a954c96> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1c2e96000 -         0x1c2edffff CoreAutoLayout arm64e  <ca8d12295392352ab1e2cf328996c4d3> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1c58c3000 -         0x1c594699f dyld arm64e  <3060d36a16ce3c3a92583881459f5714> /usr/lib/dyld
        0x1ebee7000 -         0x1ebeeffff GraphicsServices arm64e  <8425ea11000e3e5e8abcbddf3ff3fa32> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1f02c9000 -         0x1f0302ff3 libsystem_kernel.dylib arm64e  <b9618c71c0cb31b6825f92a4737c890e> /usr/lib/system/libsystem_kernel.dylib
        0x22823a000 -         0x228254fff libc++abi.dylib arm64e  <5e1a37143fad3ad7a23d61c4be170233> /usr/lib/libc++abi.dylib
        0x228437000 -         0x228443ff3 libsystem_pthread.dylib arm64e  <3ca98e388eee3c269862c5f66aad93c0> /usr/lib/system/libsystem_pthread.dylib

EOF
