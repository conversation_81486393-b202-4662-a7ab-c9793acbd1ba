Incident Identifier: E3B643A1-6411-4C09-B6F6-61DA5704AE66
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone12,1
Process:             发型测试 [17162]
Path:                /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone12,1:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [39502]

Date/Time:           2025-08-03 22:26:35.6560 +0800
Launch Time:         2025-08-03 22:25:57.6129 +0800
OS Version:          iPhone OS 18.5 (22F76)
Release Type:        User
Baseband Version:    6.03.01
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001996fc380
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [17162]

Triggered by Thread:  11

Last Exception Backtrace:
0   CoreFoundation                	0x1917cf21c __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x18ec69abc objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1b5c3cd1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1b5c3ddec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
4   CoreAutoLayout                	0x1b5c3dd1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
5   CoreAutoLayout                	0x1b5c3daa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
6   UIKitCore                     	0x193fca688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
7   UIKitCore                     	0x1940a5b84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
8   QuartzCore                    	0x193241c14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
9   QuartzCore                    	0x19324158c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
10  QuartzCore                    	0x1932437f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
11  QuartzCore                    	0x193242cc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
12  QuartzCore                    	0x1933a6e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
13  libsystem_pthread.dylib       	0x21bdbe36c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
14  libsystem_pthread.dylib       	0x21bdbe0dc _pthread_exit + 84 (pthread.c:1770)
15  libsystem_pthread.dylib       	0x21bdc02d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
16  libsystem_pthread.dylib       	0x21bdbca94 _pthread_wqthread + 428 (pthread.c:2690)
17  libsystem_pthread.dylib       	0x21bdbcaac start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001e28d3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e28d739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e28d72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e28d7100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001916c6900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001916c51f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x00000001916c6c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001de8a5454 GSEventRunModal + 168 (GSEvent.c:2196)
8   UIKitCore                     	0x00000001940d9274 -[UIApplication _run] + 816 (UIApplication.m:3845)
9   UIKitCore                     	0x00000001940a4a28 UIApplicationMain + 336 (UIApplication.m:5540)
10  UIKitCore                     	0x0000000194186168 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000100c11130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000100c11130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000100c11130 main + 120
14  dyld                          	0x00000001b859bf08 start + 6040 (dyldMain.cpp:1450)

Thread 1:
0   libsystem_pthread.dylib       	0x000000021bdbcaa4 start_wqthread + 0 (:-1)

Thread 2 name:
Thread 2:
0   libsystem_kernel.dylib        	0x00000001e28d3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e28d739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e28d72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e28d7100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001916c6900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001916c51f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x00000001916c6c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Foundation                    	0x000000019033e79c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:375)
8   Foundation                    	0x0000000190344020 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:422)
9   UIKitCore                     	0x00000001940c356c -[UIEventFetcher threadMain] + 424 (UIEventFetcher.m:1351)
10  Foundation                    	0x00000001903a4804 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000021bdbf344 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000021bdbcab8 thread_start + 8 (:-1)

Thread 3:
0   libsystem_kernel.dylib        	0x00000001e28d9658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001996989ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001996add10 sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000101446ef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x000000021bdbf344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021bdbcab8 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001e28d3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e28d739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e28d522c thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x000000010141e97c handleExceptions + 120
4   libsystem_pthread.dylib       	0x000000021bdbf344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021bdbcab8 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001e28d3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e28d7430 mach_msg2_internal + 224 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e28d72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e28d7100 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x000000010141e9a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x000000021bdbf344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021bdbcab8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e28d9658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001996989ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000199698ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000101386580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000101338e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021bdbf344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021bdbcab8 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e28d9658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001996989ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x0000000199698ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000101386580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000101338e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021bdbf344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021bdbcab8 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001e28d3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e28d739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e28d72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e28d7100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001916c6900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001916c51f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x00000001916c6c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CFNetwork                     	0x0000000192ce630c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x00000001903a4804 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000021bdbf344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021bdbcab8 thread_start + 8 (:-1)

Thread 9:
0   libsystem_pthread.dylib       	0x000000021bdbcaa4 start_wqthread + 0 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001e28d3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e28d739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e28d72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e28d7100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x00000001916c6900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001916c51f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x00000001916c6c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CoreFoundation                	0x0000000191741674 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x000000019f011e4c CLMotionCore::runMotionThread(void*) + 1300 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000021bdbf344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021bdbcab8 thread_start + 8 (:-1)

Thread 11 Crashed:
0   libsystem_c.dylib             	0x00000001996fc380 __abort + 164 (abort.c:175)
1   libsystem_c.dylib             	0x00000001996fc2dc abort + 136 (abort.c:130)
2   libc++abi.dylib               	0x000000021bced5a0 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000021bcdbf10 demangling_terminate_handler() + 344 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000018ec6bbf8 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x000000010141d22c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x000000021bcec8b4 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000021bcefe1c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x000000021bcefdc4 __cxa_throw + 92 (cxa_exception.cpp:299)
9   libobjc.A.dylib               	0x000000018ec69c24 objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001b5c3cd1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001b5c3ddec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
12  CoreAutoLayout                	0x00000001b5c3dd1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
13  CoreAutoLayout                	0x00000001b5c3daa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
14  UIKitCore                     	0x0000000193fca688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
15  UIKitCore                     	0x00000001940a5b84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
16  QuartzCore                    	0x0000000193241c14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
17  QuartzCore                    	0x000000019324158c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
18  QuartzCore                    	0x00000001932437f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
19  QuartzCore                    	0x0000000193242cc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
20  QuartzCore                    	0x00000001933a6e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
21  libsystem_pthread.dylib       	0x000000021bdbe36c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
22  libsystem_pthread.dylib       	0x000000021bdbe0dc _pthread_exit + 84 (pthread.c:1770)
23  libsystem_pthread.dylib       	0x000000021bdc02d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
24  libsystem_pthread.dylib       	0x000000021bdbca94 _pthread_wqthread + 428 (pthread.c:2690)
25  libsystem_pthread.dylib       	0x000000021bdbcaac start_wqthread + 8 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001e28d9438 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x000000021bdbde50 _pthread_cond_wait + 984 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001a8fd5864 scavenger_thread_main + 1584 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x000000021bdbf344 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x000000021bdbcab8 thread_start + 8 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x000000021bdbcaa4 start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x000000021bdbcaa4 start_wqthread + 0 (:-1)

Thread 15:
0   libsystem_pthread.dylib       	0x000000021bdbcaa4 start_wqthread + 0 (:-1)


Thread 11 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0xffa381db4c7c44c4
    x8: 0x00000000ffffffe7   x9: 0x00000001fc3f9d50  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x0000000191b70494  x14: 0x0000000000000001  x15: 0xffffffffb00007ff
   x16: 0x0000000000000030  x17: 0x00000001fde62440  x18: 0x0000000000000000  x19: 0x000000016f7c7000
   x20: 0x000000016f7c22f0  x21: 0x000000016f7c23a0  x22: 0x00000001f9f1c000  x23: 0x00000001091bd1d8
   x24: 0x0000000000000000  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000209ed28b0   fp: 0x000000016f7c2310   lr: 0x00000001996fc380
    sp: 0x000000016f7c22e0   pc: 0x00000001996fc380 cpsr: 0x40000000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x100a08000 -         0x1017e7fff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/发型测试
        0x101bb0000 -         0x101bbbfff libobjc-trampolines.dylib arm64e  <9136d8ba22ff3f129caddfc4c6dc51de> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x101cb4000 -         0x101cc3fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x101ce0000 -         0x101ceffff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x101d08000 -         0x101d13fff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x101d34000 -         0x101d6bfff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x101f28000 -         0x101f33fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x101f5c000 -         0x101f6ffff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/Promises.framework/Promises
        0x101f90000 -         0x101fa3fff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x101fc0000 -         0x101fcffff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x101fe0000 -         0x101febfff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x102008000 -         0x102047fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x102140000 -         0x102157fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x102194000 -         0x1021b3fff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x1021f0000 -         0x102317fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x1024bc000 -         0x10250ffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x102614000 -         0x10263ffff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x102694000 -         0x1026bffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x10272c000 -         0x10275bfff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1027c4000 -         0x102827fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x1028d8000 -         0x102acffff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x102d64000 -         0x102dcffff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x102e50000 -         0x102ee3fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x10328c000 -         0x1033bbfff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x103854000 -         0x103a13fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x103dd8000 -         0x1050b3fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/7D22AA24-A84A-47DF-A7B1-62988ED05C5B/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x18ec38000 -         0x18ec89bb3 libobjc.A.dylib arm64e  <ed7c5fc7ddc734249c44db56f51b8be2> /usr/lib/libobjc.A.dylib
        0x19032f000 -         0x190fa2ddf Foundation arm64e  <34de055d8683380a9198c3347211d13d> /System/Library/Frameworks/Foundation.framework/Foundation
        0x1916b5000 -         0x191c31fff CoreFoundation arm64e  <7821f73c378b3a10be90ef526b7dba93> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x192c46000 -         0x19300bb9f CFNetwork arm64e  <a35a109c49d23986965d4ed7e0b6681e> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x19322d000 -         0x1935e769f QuartzCore arm64e  <109010da3c353e22b001939786412ee2> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x193fa4000 -         0x195ee5b5f UIKitCore arm64e  <96636f64106f30c8a78082dcebb0f443> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x199685000 -         0x1997048ef libsystem_c.dylib arm64e  <93f93d7c245f3395822dec61ffae79cf> /usr/lib/system/libsystem_c.dylib
        0x19f00b000 -         0x19f428a9f CoreMotion arm64e  <cec80db7b3f23b179d4ebcfeb020ca2d> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1a8f8a000 -         0x1aa9c575f JavaScriptCore arm64e  <e32426af64113260be0bbe0ad12e23c8> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1b5c3b000 -         0x1b5c83c3f CoreAutoLayout arm64e  <b850e010e4023e07aaa549f71cccc7fc> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1b855d000 -         0x1b85f7857 dyld arm64e  <86d5253d4fd136f3b4ab25982c90cbf4> /usr/lib/dyld
        0x1de8a4000 -         0x1de8acc7f GraphicsServices arm64e  <5ba62c226d3731999dfd0e0f7abebfa9> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e28d3000 -         0x1e290cebf libsystem_kernel.dylib arm64e  <9e195be11733345ea9bf50d0d7059647> /usr/lib/system/libsystem_kernel.dylib
        0x21bcd7000 -         0x21bcf4fff libc++abi.dylib arm64e  <a360ea66d985389394b96bba7bd8a6df> /usr/lib/libc++abi.dylib
        0x21bdbc000 -         0x21bdc83f3 libsystem_pthread.dylib arm64e  <b37430d8e3af33e481e1faed9ee26e8a> /usr/lib/system/libsystem_pthread.dylib

EOF
