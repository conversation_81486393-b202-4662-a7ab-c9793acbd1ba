Incident Identifier: 8516DEFB-B2B4-49D7-9BF1-87B1D8040590
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone15,5
Process:             发型测试 [13853]
Path:                /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone15,5:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [14741]

Date/Time:           2025-08-08 01:39:52.7846 +0800
Launch Time:         2025-08-08 01:39:26.9378 +0800
OS Version:          iPhone OS 18.4.1 (22E252)
Release Type:        User
Baseband Version:    2.52.03
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001a66e1380
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [13853]

Triggered by Thread:  1

Last Exception Backtrace:
0   CoreFoundation                	0x19e7c12ec __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x19bc45a7c objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1c282c72c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1c282cc9c -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
4   CoreAutoLayout                	0x1c282cbcc -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
5   CoreAutoLayout                	0x1c282c958 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
6   UIKitCore                     	0x1a0fac9c8 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
7   UIKitCore                     	0x1a125f490 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
8   QuartzCore                    	0x1a021f444 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
9   QuartzCore                    	0x1a021efdc CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
10  QuartzCore                    	0x1a02ebec8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
11  QuartzCore                    	0x1a026d980 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
12  QuartzCore                    	0x1a0394520 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
13  libsystem_pthread.dylib       	0x228c26eac _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
14  libsystem_pthread.dylib       	0x228c26c1c _pthread_exit + 84 (pthread.c:1770)
15  libsystem_pthread.dylib       	0x228c299c0 _pthread_wqthread_exit + 56 (pthread.c:2656)
16  libsystem_pthread.dylib       	0x228c296e8 _pthread_wqthread + 428 (pthread.c:2690)
17  libsystem_pthread.dylib       	0x228c269f8 start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001ef2a3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ef2a739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ef2a72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ef2a7100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019e7186cc __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019e717dac __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019e73c700 CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001eb27d190 GSEventRunModal + 168 (GSEvent.c:2196)
8   UIKitCore                     	0x00000001a135a240 -[UIApplication _run] + 816 (UIApplication.m:3845)
9   UIKitCore                     	0x00000001a1358470 UIApplicationMain + 336 (UIApplication.m:5540)
10  UIKitCore                     	0x00000001a17b6a30 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000100791130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000100791130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000100791130 main + 120
14  dyld                          	0x00000001c513fad8 start + 5964 (dyldMain.cpp:1443)

Thread 1 Crashed:
0   libsystem_c.dylib             	0x00000001a66e1380 __abort + 164 (abort.c:175)
1   libsystem_c.dylib             	0x00000001a66e12dc abort + 136 (abort.c:130)
2   libc++abi.dylib               	0x0000000228b565a0 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x0000000228b44f10 demangling_terminate_handler() + 344 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000019bc47bb8 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x0000000100f9d22c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x0000000228b558b4 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x0000000228b58e1c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x0000000228b58dc4 __cxa_throw + 92 (cxa_exception.cpp:299)
9   libobjc.A.dylib               	0x000000019bc45be4 objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001c282c72c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001c282cc9c -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
12  CoreAutoLayout                	0x00000001c282cbcc -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
13  CoreAutoLayout                	0x00000001c282c958 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
14  UIKitCore                     	0x00000001a0fac9c8 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
15  UIKitCore                     	0x00000001a125f490 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
16  QuartzCore                    	0x00000001a021f444 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
17  QuartzCore                    	0x00000001a021efdc CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
18  QuartzCore                    	0x00000001a02ebec8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
19  QuartzCore                    	0x00000001a026d980 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
20  QuartzCore                    	0x00000001a0394520 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
21  libsystem_pthread.dylib       	0x0000000228c26eac _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
22  libsystem_pthread.dylib       	0x0000000228c26c1c _pthread_exit + 84 (pthread.c:1770)
23  libsystem_pthread.dylib       	0x0000000228c299c0 _pthread_wqthread_exit + 56 (pthread.c:2656)
24  libsystem_pthread.dylib       	0x0000000228c296e8 _pthread_wqthread + 428 (pthread.c:2690)
25  libsystem_pthread.dylib       	0x0000000228c269f8 start_wqthread + 8 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x0000000228c269f0 start_wqthread + 0 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001ef2a3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ef2a739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ef2a72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ef2a7100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019e7186cc __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019e717dac __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019e73c700 CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Foundation                    	0x000000019d37e8a8 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:375)
8   Foundation                    	0x000000019d37d6d8 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:422)
9   UIKitCore                     	0x00000001a144d5ec -[UIEventFetcher threadMain] + 424 (UIEventFetcher.m:1351)
10  Foundation                    	0x000000019d41fcf8 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x0000000228c26afc _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x0000000228c26a04 thread_start + 8 (:-1)

Thread 4:
0   libsystem_pthread.dylib       	0x0000000228c269f0 start_wqthread + 0 (:-1)

Thread 5:
0   libsystem_pthread.dylib       	0x0000000228c269f0 start_wqthread + 0 (:-1)

Thread 6:
0   libsystem_pthread.dylib       	0x0000000228c269f0 start_wqthread + 0 (:-1)

Thread 7:
0   libsystem_kernel.dylib        	0x00000001ef2a9658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a66863e8 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a6692d10 sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000100fc6ef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x0000000228c26afc _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000228c26a04 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001ef2a3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ef2a739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ef2a522c thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x0000000100f9e97c handleExceptions + 120
4   libsystem_pthread.dylib       	0x0000000228c26afc _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000228c26a04 thread_start + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001ef2a3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ef2a7430 mach_msg2_internal + 224 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001ef2a72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ef2a7100 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x0000000100f9e9a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x0000000228c26afc _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000228c26a04 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001ef2a9658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a66863e8 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a6686300 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000100f06580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000100eb8e84 thread_do + 340
5   libsystem_pthread.dylib       	0x0000000228c26afc _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000228c26a04 thread_start + 8 (:-1)

Thread 11 name:
Thread 11:
0   libsystem_kernel.dylib        	0x00000001ef2a9658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a66863e8 nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a6686300 usleep + 68 (usleep.c:52)
3   发型测试                          	0x0000000100f06580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000100eb8e84 thread_do + 340
5   libsystem_pthread.dylib       	0x0000000228c26afc _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000228c26a04 thread_start + 8 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x0000000228c269f0 start_wqthread + 0 (:-1)

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001ef2a9438 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x0000000228c282fc _pthread_cond_wait + 984 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001b5d8a524 scavenger_thread_main + 1584 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x0000000228c26afc _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x0000000228c26a04 thread_start + 8 (:-1)

Thread 14 name:
Thread 14:
0   libsystem_kernel.dylib        	0x00000001ef2a3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ef2a739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ef2a72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ef2a7100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019e7186cc __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019e717dac __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019e73c700 CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CFNetwork                     	0x000000019fd21eec +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x000000019d41fcf8 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x0000000228c26afc _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000228c26a04 thread_start + 8 (:-1)

Thread 15 name:
Thread 15:
0   libsystem_kernel.dylib        	0x00000001ef2a3ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001ef2a739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001ef2a72b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001ef2a7100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019e7186cc __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019e717dac __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019e73c700 CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CoreFoundation                	0x000000019e73a0b0 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001abfd2b50 0x1abfc9000 + 39760
9   libsystem_pthread.dylib       	0x0000000228c26afc _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000228c26a04 thread_start + 8 (:-1)

Thread 16:
0   libsystem_pthread.dylib       	0x0000000228c269f0 start_wqthread + 0 (:-1)


Thread 1 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x768ca0e67557a40d
    x8: 0x00000000ffffffe7   x9: 0x000000020a1d1510  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x000000019eb624cc  x14: 0x0000000000000001  x15: 0xffffffffb00007ff
   x16: 0x0000000000000030  x17: 0x000000020bc94948  x18: 0x0000000000000000  x19: 0x000000016f98b000
   x20: 0x000000016f9862f0  x21: 0x000000016f9863a0  x22: 0x0000000207cd4000  x23: 0x00000001063b4e58
   x24: 0x0000000000000000  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000218837be0   fp: 0x000000016f986310   lr: 0x00000001a66e1380
    sp: 0x000000016f9862e0   pc: 0x00000001a66e1380 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x100588000 -         0x101367fff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/发型测试
        0x101734000 -         0x10173ffff libobjc-trampolines.dylib arm64e  <7b2292ed23143e4f929b2831fc172b43> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x101834000 -         0x101843fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x101860000 -         0x10186ffff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x101888000 -         0x101893fff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x1018b4000 -         0x1018ebfff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x101960000 -         0x10196bfff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x101994000 -         0x1019a7fff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/Promises.framework/Promises
        0x1019c8000 -         0x1019dbfff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x1019f8000 -         0x101a07fff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x101a18000 -         0x101a23fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x101a4c000 -         0x101a63fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x101c08000 -         0x101d2ffff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x101ed4000 -         0x101f13fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x101fb8000 -         0x10200bfff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x102090000 -         0x1020affff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x102110000 -         0x10213bfff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x102228000 -         0x102257fff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1022c0000 -         0x102323fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x102358000 -         0x102383fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1023d4000 -         0x1025cbfff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x102860000 -         0x1028cbfff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x10294c000 -         0x1029dffff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x102d88000 -         0x102eb7fff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x103350000 -         0x10350ffff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x1037f0000 -         0x104acbfff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/C7AC6447-B3B6-41F6-BF55-CD0ED09391BD/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x19bc14000 -         0x19bc65beb libobjc.A.dylib arm64e  <8b2413f2456037f3b0815ccd6236f050> /usr/lib/libobjc.A.dylib
        0x19d30a000 -         0x19df935ff Foundation arm64e  <2bd7eb532f573300b8de478229e49115> /System/Library/Frameworks/Foundation.framework/Foundation
        0x19e6a7000 -         0x19ec23fff CoreFoundation arm64e  <0551baa3c6e93423b0e2be69719c1d7f> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x19fc36000 -         0x19fffbaff CFNetwork arm64e  <b25d7953680a30a5a2a00f07e5117c91> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x1a021d000 -         0x1a05d745f QuartzCore arm64e  <aa60096595aa3af4853aa2c99d41e348> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x1a0f90000 -         0x1a2ed0a1f UIKitCore arm64e  <05197341f99832e5a847da98bb8c68bf> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1a666a000 -         0x1a66e98b7 libsystem_c.dylib arm64e  <027de04c2929357bb6a3701405aab6be> /usr/lib/system/libsystem_c.dylib
        0x1abfc9000 -         0x1ac3e4a3f CoreMotion arm64e  <028099438cc030089beb025eca667e1a> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1b5cda000 -         0x1b75c5f3f JavaScriptCore arm64e  <e169eaa1dcef34a5ba5368004ae2c6b0> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1c2829000 -         0x1c2871cbf CoreAutoLayout arm64e  <24db916cd60634d89d0050d462237e80> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1c512f000 -         0x1c51c9013 dyld arm64e  <189fe4805d5b3b89928958bc88624420> /usr/lib/dyld
        0x1eb27c000 -         0x1eb284c5f GraphicsServices arm64e  <c620bafa568e3bcbb31776c829cbdd46> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1ef2a3000 -         0x1ef2dcb77 libsystem_kernel.dylib arm64e  <9d196db4701331768c025b4c68701c92> /usr/lib/system/libsystem_kernel.dylib
        0x1ffd1c000 -         0x200049aff libANGLE-shared.dylib arm64e  <02d77464b908384b8c4d38bd5f9dbe99> /System/Library/PrivateFrameworks/WebCore.framework/Frameworks/libANGLE-shared.dylib
        0x228b40000 -         0x228b5dfff libc++abi.dylib arm64e  <2dbdca052baa3bfe9ef315a157d84058> /usr/lib/libc++abi.dylib
        0x228c25000 -         0x228c313fb libsystem_pthread.dylib arm64e  <00306a1f11183f8690bdd18b5ed5409f> /usr/lib/system/libsystem_pthread.dylib

EOF
