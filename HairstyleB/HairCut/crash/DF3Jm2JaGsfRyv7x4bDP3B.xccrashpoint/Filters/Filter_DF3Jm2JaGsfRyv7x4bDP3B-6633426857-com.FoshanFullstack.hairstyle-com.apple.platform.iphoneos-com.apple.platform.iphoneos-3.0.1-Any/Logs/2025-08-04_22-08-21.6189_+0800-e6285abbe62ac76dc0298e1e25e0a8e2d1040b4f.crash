Incident Identifier: 48526718-7B58-4A3B-921D-2E142228C757
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone15,4
Process:             发型测试 [30650]
Path:                /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone15,4:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [4277]

Date/Time:           2025-08-04 22:08:21.6189 +0800
Launch Time:         2025-08-04 22:08:05.1954 +0800
OS Version:          iPhone OS 18.5 (22F76)
Release Type:        User
Baseband Version:    2.60.02
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000019d158380
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [30650]

Triggered by Thread:  10

Last Exception Backtrace:
0   CoreFoundation                	0x19522b21c __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x1926c5abc objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1b9698d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1b9698a54 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x197a7b248 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1600)
5   UIKitCore                     	0x197a25f6c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2180 (UIView.m:19897)
6   QuartzCore                    	0x196c9dc14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
7   QuartzCore                    	0x196c9d58c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
8   QuartzCore                    	0x196c9f7f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
9   QuartzCore                    	0x196c9ecc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
10  QuartzCore                    	0x196e02e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
11  libsystem_pthread.dylib       	0x21f83636c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x21f8360dc _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x21f8382d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
14  libsystem_pthread.dylib       	0x21f834a94 _pthread_wqthread + 428 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x21f834aac start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001e632fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e633339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e63332b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e6333100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000195122900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001951211f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000195122c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001e2301454 GSEventRunModal + 168 (GSEvent.c:2196)
8   UIKitCore                     	0x0000000197b35274 -[UIApplication _run] + 816 (UIApplication.m:3845)
9   UIKitCore                     	0x0000000197b00a28 UIApplicationMain + 336 (UIApplication.m:5540)
10  UIKitCore                     	0x0000000197be2168 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000101055130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000101055130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000101055130 main + 120
14  dyld                          	0x00000001bbff7f08 start + 6040 (dyldMain.cpp:1450)

Thread 1:
0   libsystem_pthread.dylib       	0x000000021f834aa4 start_wqthread + 0 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x000000021f834aa4 start_wqthread + 0 (:-1)

Thread 3:
0   libsystem_pthread.dylib       	0x000000021f834aa4 start_wqthread + 0 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001e632fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e633339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e63332b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e6333100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000195122900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001951211f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000195122c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Foundation                    	0x0000000193d9a79c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:375)
8   Foundation                    	0x0000000193da0020 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:422)
9   UIKitCore                     	0x0000000197b1f56c -[UIEventFetcher threadMain] + 424 (UIEventFetcher.m:1351)
10  Foundation                    	0x0000000193e00804 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000021f837344 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000021f834ab8 thread_start + 8 (:-1)

Thread 5:
0   libsystem_kernel.dylib        	0x00000001e6335658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019d0f49ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019d109d10 sleep + 52 (sleep.c:62)
3   发型测试                          	0x000000010188aef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x000000021f837344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021f834ab8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e632fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e633339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e633122c thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x000000010186297c handleExceptions + 120
4   libsystem_pthread.dylib       	0x000000021f837344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021f834ab8 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e632fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e6333430 mach_msg2_internal + 224 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e63332b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e6333100 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001018629a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x000000021f837344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021f834ab8 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001e6335658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019d0f49ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019d0f4ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001017ca580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x000000010177ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021f837344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021f834ab8 thread_start + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001e6335658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019d0f49ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019d0f4ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001017ca580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x000000010177ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021f837344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021f834ab8 thread_start + 8 (:-1)

Thread 10 Crashed:
0   libsystem_c.dylib             	0x000000019d158380 __abort + 164 (abort.c:175)
1   libsystem_c.dylib             	0x000000019d1582dc abort + 136 (abort.c:130)
2   libc++abi.dylib               	0x000000021f7655a0 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000021f753f10 demangling_terminate_handler() + 344 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x00000001926c7bf8 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x000000010186122c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x000000021f7648b4 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000021f767e1c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x000000021f767dc4 __cxa_throw + 92 (cxa_exception.cpp:299)
9   libobjc.A.dylib               	0x00000001926c5c24 objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001b9698d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001b9698a54 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x0000000197a7b248 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1600)
13  UIKitCore                     	0x0000000197a25f6c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2180 (UIView.m:19897)
14  QuartzCore                    	0x0000000196c9dc14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
15  QuartzCore                    	0x0000000196c9d58c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
16  QuartzCore                    	0x0000000196c9f7f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
17  QuartzCore                    	0x0000000196c9ecc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
18  QuartzCore                    	0x0000000196e02e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
19  libsystem_pthread.dylib       	0x000000021f83636c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x000000021f8360dc _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x000000021f8382d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
22  libsystem_pthread.dylib       	0x000000021f834a94 _pthread_wqthread + 428 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x000000021f834aac start_wqthread + 8 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x000000021f834aa4 start_wqthread + 0 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x000000021f834aa4 start_wqthread + 0 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x000000021f834aa4 start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x000000021f834aa4 start_wqthread + 0 (:-1)

Thread 15 name:
Thread 15:
0   libsystem_kernel.dylib        	0x00000001e6335438 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x000000021f835e50 _pthread_cond_wait + 984 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001aca31864 scavenger_thread_main + 1584 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x000000021f837344 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x000000021f834ab8 thread_start + 8 (:-1)

Thread 16 name:
Thread 16:
0   libsystem_kernel.dylib        	0x00000001e632fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e633339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e63332b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e6333100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000195122900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001951211f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000195122c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CFNetwork                     	0x000000019674230c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x0000000193e00804 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000021f837344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021f834ab8 thread_start + 8 (:-1)

Thread 17 name:
Thread 17:
0   libsystem_kernel.dylib        	0x00000001e632fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e633339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e63332b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e6333100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000195122900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001951211f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000195122c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CoreFoundation                	0x000000019519d674 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001a2a6de4c CLMotionCore::runMotionThread(void*) + 1300 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000021f837344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021f834ab8 thread_start + 8 (:-1)


Thread 10 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x262943f2e1958451
    x8: 0x00000000ffffffe7   x9: 0x00000001ffe56810  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001955cc494  x14: 0x0000000000000001  x15: 0xffffffffb00007ff
   x16: 0x0000000000000030  x17: 0x00000002018be440  x18: 0x0000000000000000  x19: 0x000000016f63f000
   x20: 0x000000016f63a490  x21: 0x000000016f63a540  x22: 0x00000001fd978000  x23: 0x000000010ab8e458
   x24: 0x0000000117f84400  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x000000020e135910   fp: 0x000000016f63a4b0   lr: 0x000000019d158380
    sp: 0x000000016f63a480   pc: 0x000000019d158380 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x100e4c000 -         0x101c2bfff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/发型测试
        0x102000000 -         0x10200bfff libobjc-trampolines.dylib arm64e  <9136d8ba22ff3f129caddfc4c6dc51de> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x1020f8000 -         0x102107fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x102124000 -         0x102133fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x10214c000 -         0x102157fff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x102178000 -         0x1021affff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x102224000 -         0x10222ffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x102244000 -         0x10224ffff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x102398000 -         0x1023abfff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/Promises.framework/Promises
        0x1023cc000 -         0x1023dffff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x1023fc000 -         0x10240bfff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x102478000 -         0x1024b7fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x10255c000 -         0x102573fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x1025b0000 -         0x1025cffff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x102660000 -         0x102787fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x10292c000 -         0x10297ffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x102a04000 -         0x102a2ffff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x102b1c000 -         0x102b4bfff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x102bb4000 -         0x102c17fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x102c4c000 -         0x102c77fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x102cc8000 -         0x102ebffff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x103154000 -         0x1031bffff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x103240000 -         0x1032d3fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x10367c000 -         0x1037abfff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x103c44000 -         0x103e03fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x104248000 -         0x105523fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/119C0539-162B-463B-B4FE-0C58DEBAADB7/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x192694000 -         0x1926e5bb3 libobjc.A.dylib arm64e  <ed7c5fc7ddc734249c44db56f51b8be2> /usr/lib/libobjc.A.dylib
        0x193d8b000 -         0x1949feddf Foundation arm64e  <34de055d8683380a9198c3347211d13d> /System/Library/Frameworks/Foundation.framework/Foundation
        0x195111000 -         0x19568dfff CoreFoundation arm64e  <7821f73c378b3a10be90ef526b7dba93> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x1966a2000 -         0x196a67b9f CFNetwork arm64e  <a35a109c49d23986965d4ed7e0b6681e> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x196c89000 -         0x19704369f QuartzCore arm64e  <109010da3c353e22b001939786412ee2> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x197a00000 -         0x199941b5f UIKitCore arm64e  <96636f64106f30c8a78082dcebb0f443> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x19d0e1000 -         0x19d1608ef libsystem_c.dylib arm64e  <93f93d7c245f3395822dec61ffae79cf> /usr/lib/system/libsystem_c.dylib
        0x1a2a67000 -         0x1a2e84a9f CoreMotion arm64e  <cec80db7b3f23b179d4ebcfeb020ca2d> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1ac9e6000 -         0x1ae42175f JavaScriptCore arm64e  <e32426af64113260be0bbe0ad12e23c8> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1b9697000 -         0x1b96dfc3f CoreAutoLayout arm64e  <b850e010e4023e07aaa549f71cccc7fc> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1bbfb9000 -         0x1bc053857 dyld arm64e  <86d5253d4fd136f3b4ab25982c90cbf4> /usr/lib/dyld
        0x1e2300000 -         0x1e2308c7f GraphicsServices arm64e  <5ba62c226d3731999dfd0e0f7abebfa9> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e632f000 -         0x1e6368ebf libsystem_kernel.dylib arm64e  <9e195be11733345ea9bf50d0d7059647> /usr/lib/system/libsystem_kernel.dylib
        0x21f74f000 -         0x21f76cfff libc++abi.dylib arm64e  <a360ea66d985389394b96bba7bd8a6df> /usr/lib/libc++abi.dylib
        0x21f834000 -         0x21f8403f3 libsystem_pthread.dylib arm64e  <b37430d8e3af33e481e1faed9ee26e8a> /usr/lib/system/libsystem_pthread.dylib

EOF
