Incident Identifier: 6AA27523-7A7C-48EA-93A3-9110E51B387F
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone16,2
Process:             发型测试 [87594]
Path:                /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone16,2:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [37499]

Date/Time:           2025-08-08 06:50:53.7652 +0800
Launch Time:         2025-08-08 06:50:28.8478 +0800
OS Version:          iPhone OS 18.5 (22F76)
Release Type:        User
Baseband Version:    2.60.02
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000019124c380
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [87594]

Triggered by Thread:  8

Last Exception Backtrace:
0   CoreFoundation                	0x18931f21c __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x1867b9abc objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1ad78cd1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1ad78ca54 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
4   UIKitCore                     	0x18bb6f248 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1600)
5   UIKitCore                     	0x18bb19f6c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2180 (UIView.m:19897)
6   QuartzCore                    	0x18ad91c14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
7   QuartzCore                    	0x18ad9158c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
8   QuartzCore                    	0x18ad937f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
9   QuartzCore                    	0x18ad92cc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
10  QuartzCore                    	0x18aef6e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
11  libsystem_pthread.dylib       	0x21392e36c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
12  libsystem_pthread.dylib       	0x21392e0dc _pthread_exit + 84 (pthread.c:1770)
13  libsystem_pthread.dylib       	0x2139302d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
14  libsystem_pthread.dylib       	0x21392ca94 _pthread_wqthread + 428 (pthread.c:2690)
15  libsystem_pthread.dylib       	0x21392caac start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001da423ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da42739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da4272b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da427100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000189216900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001892151f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000189216c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001d63f5454 GSEventRunModal + 168 (GSEvent.c:2196)
8   UIKitCore                     	0x000000018bc29274 -[UIApplication _run] + 816 (UIApplication.m:3845)
9   UIKitCore                     	0x000000018bbf4a28 UIApplicationMain + 336 (UIApplication.m:5540)
10  UIKitCore                     	0x000000018bcd6168 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000102d41130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000102d41130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000102d41130 main + 120
14  dyld                          	0x00000001b00ebf08 start + 6040 (dyldMain.cpp:1450)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001da423ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da42739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da4272b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da427100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000189216900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001892151f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000189216c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Foundation                    	0x0000000187e8e79c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:375)
8   Foundation                    	0x0000000187e94020 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:422)
9   UIKitCore                     	0x000000018bc1356c -[UIEventFetcher threadMain] + 424 (UIEventFetcher.m:1351)
10  Foundation                    	0x0000000187ef4804 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000021392f344 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000021392cab8 thread_start + 8 (:-1)

Thread 2:
0   libsystem_kernel.dylib        	0x00000001da429658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001911e89ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001911fdd10 sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000103576ef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x000000021392f344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021392cab8 thread_start + 8 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001da423ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da42739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da42522c thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x000000010354e97c handleExceptions + 120
4   libsystem_pthread.dylib       	0x000000021392f344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021392cab8 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001da423ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da427430 mach_msg2_internal + 224 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001da4272b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da427100 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x000000010354e9a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x000000021392f344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021392cab8 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001da429658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001911e89ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001911e8ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001034b6580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000103468e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021392f344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021392cab8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001da429658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001911e89ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001911e8ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001034b6580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000103468e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021392f344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021392cab8 thread_start + 8 (:-1)

Thread 7:
0   libsystem_pthread.dylib       	0x000000021392caa4 start_wqthread + 0 (:-1)

Thread 8 Crashed:
0   libsystem_c.dylib             	0x000000019124c380 __abort + 164 (abort.c:175)
1   libsystem_c.dylib             	0x000000019124c2dc abort + 136 (abort.c:130)
2   libc++abi.dylib               	0x000000021385d5a0 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000021384bf10 demangling_terminate_handler() + 344 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x00000001867bbbf8 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x000000010354d22c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x000000021385c8b4 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000021385fe1c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x000000021385fdc4 __cxa_throw + 92 (cxa_exception.cpp:299)
9   libobjc.A.dylib               	0x00000001867b9c24 objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001ad78cd1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001ad78ca54 -[NSISEngine withBehaviors:performModifications:] + 36 (NSISEngine.m:1982)
12  UIKitCore                     	0x000000018bb6f248 -[UIView _resetLayoutEngineHostConstraints] + 112 (NSLayoutConstraint_UIKitAdditions.m:1600)
13  UIKitCore                     	0x000000018bb19f6c -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 2180 (UIView.m:19897)
14  QuartzCore                    	0x000000018ad91c14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
15  QuartzCore                    	0x000000018ad9158c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
16  QuartzCore                    	0x000000018ad937f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
17  QuartzCore                    	0x000000018ad92cc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
18  QuartzCore                    	0x000000018aef6e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
19  libsystem_pthread.dylib       	0x000000021392e36c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
20  libsystem_pthread.dylib       	0x000000021392e0dc _pthread_exit + 84 (pthread.c:1770)
21  libsystem_pthread.dylib       	0x00000002139302d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
22  libsystem_pthread.dylib       	0x000000021392ca94 _pthread_wqthread + 428 (pthread.c:2690)
23  libsystem_pthread.dylib       	0x000000021392caac start_wqthread + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001da423ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da42739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da4272b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da427100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000189216900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001892151f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000189216c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CFNetwork                     	0x000000018a83630c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x0000000187ef4804 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000021392f344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021392cab8 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001da423ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001da42739c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001da4272b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001da427100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000189216900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001892151f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000189216c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CoreFoundation                	0x0000000189291674 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x0000000196b61e4c CLMotionCore::runMotionThread(void*) + 1300 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000021392f344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021392cab8 thread_start + 8 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x000000021392caa4 start_wqthread + 0 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x000000021392caa4 start_wqthread + 0 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x000000021392caa4 start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x000000021392caa4 start_wqthread + 0 (:-1)


Thread 8 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0xfc1e106b028f6c8c
    x8: 0x00000000ffffffe7   x9: 0x00000001f3f4a950  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001896c0494  x14: 0x0000000000000001  x15: 0xffffffffb00007ff
   x16: 0x0000000000000030  x17: 0x00000001f59b2440  x18: 0x0000000000000000  x19: 0x000000016dc9b000
   x20: 0x000000016dc96490  x21: 0x000000016dc96540  x22: 0x00000001f1a6c000  x23: 0x000000010e19dcd8
   x24: 0x000000010e22d400  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000202229970   fp: 0x000000016dc964b0   lr: 0x000000019124c380
    sp: 0x000000016dc96480   pc: 0x000000019124c380 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x102b38000 -         0x103917fff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/发型测试
        0x103ce0000 -         0x103ceffff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x103e6c000 -         0x103e77fff libobjc-trampolines.dylib arm64e  <9136d8ba22ff3f129caddfc4c6dc51de> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x103ed0000 -         0x103ee3fff GAXClient arm64e  <8e9f05236ba938078b1272ed92c1754a> /System/Library/AccessibilityBundles/GAXClient.bundle/GAXClient
        0x103f44000 -         0x103f53fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x103f70000 -         0x103f7bfff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x103fa4000 -         0x103fb7fff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/Promises.framework/Promises
        0x103ff0000 -         0x104027fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x10409c000 -         0x1040a7fff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x1040cc000 -         0x1040dffff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x1040fc000 -         0x10410bfff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x10411c000 -         0x104127fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x104150000 -         0x104167fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x1041a4000 -         0x1041c3fff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x10420c000 -         0x104333fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x1044d8000 -         0x104517fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x1045bc000 -         0x10460ffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x104714000 -         0x10473ffff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x104794000 -         0x1047bffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x10482c000 -         0x10485bfff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1048c4000 -         0x104927fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x1049d8000 -         0x104bcffff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x104e64000 -         0x104ecffff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x104f50000 -         0x104fe3fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x10538c000 -         0x1054bbfff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x105954000 -         0x105b13fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x105df4000 -         0x1070cffff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/60AA8574-9BA5-40E4-A57E-B06F19C7CDF7/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x186788000 -         0x1867d9bb3 libobjc.A.dylib arm64e  <ed7c5fc7ddc734249c44db56f51b8be2> /usr/lib/libobjc.A.dylib
        0x187e7f000 -         0x188af2ddf Foundation arm64e  <34de055d8683380a9198c3347211d13d> /System/Library/Frameworks/Foundation.framework/Foundation
        0x189205000 -         0x189781fff CoreFoundation arm64e  <7821f73c378b3a10be90ef526b7dba93> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x18a796000 -         0x18ab5bb9f CFNetwork arm64e  <a35a109c49d23986965d4ed7e0b6681e> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x18ad7d000 -         0x18b13769f QuartzCore arm64e  <109010da3c353e22b001939786412ee2> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x18baf4000 -         0x18da35b5f UIKitCore arm64e  <96636f64106f30c8a78082dcebb0f443> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1911d5000 -         0x1912548ef libsystem_c.dylib arm64e  <93f93d7c245f3395822dec61ffae79cf> /usr/lib/system/libsystem_c.dylib
        0x196b5b000 -         0x196f78a9f CoreMotion arm64e  <cec80db7b3f23b179d4ebcfeb020ca2d> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1ad78b000 -         0x1ad7d3c3f CoreAutoLayout arm64e  <b850e010e4023e07aaa549f71cccc7fc> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1b00ad000 -         0x1b0147857 dyld arm64e  <86d5253d4fd136f3b4ab25982c90cbf4> /usr/lib/dyld
        0x1d63f4000 -         0x1d63fcc7f GraphicsServices arm64e  <5ba62c226d3731999dfd0e0f7abebfa9> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1da423000 -         0x1da45cebf libsystem_kernel.dylib arm64e  <9e195be11733345ea9bf50d0d7059647> /usr/lib/system/libsystem_kernel.dylib
        0x213847000 -         0x213864fff libc++abi.dylib arm64e  <a360ea66d985389394b96bba7bd8a6df> /usr/lib/libc++abi.dylib
        0x21392c000 -         0x2139383f3 libsystem_pthread.dylib arm64e  <b37430d8e3af33e481e1faed9ee26e8a> /usr/lib/system/libsystem_pthread.dylib

EOF
