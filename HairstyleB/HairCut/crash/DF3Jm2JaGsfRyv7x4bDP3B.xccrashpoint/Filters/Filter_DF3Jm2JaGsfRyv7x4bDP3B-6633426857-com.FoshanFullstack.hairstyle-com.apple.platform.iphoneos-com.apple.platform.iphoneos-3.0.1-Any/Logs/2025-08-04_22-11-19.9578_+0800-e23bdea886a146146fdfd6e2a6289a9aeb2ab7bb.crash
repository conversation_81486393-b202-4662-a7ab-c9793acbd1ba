Incident Identifier: 7332F1E6-077B-4138-8C30-65FD42325B6B
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone13,3
Process:             发型测试 [1863]
Path:                /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone13,3:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [1348]

Date/Time:           2025-08-04 22:11:19.9578 +0800
Launch Time:         2025-08-04 22:10:40.8406 +0800
OS Version:          iPhone OS 18.5 (22F76)
Release Type:        User
Baseband Version:    5.51.03
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000019c468380
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [1863]

Triggered by Thread:  7

Last Exception Backtrace:
0   CoreFoundation                	0x19453b21c __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x1919d5abc objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1b89a8d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1b89a9dec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
4   CoreAutoLayout                	0x1b89a9d1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
5   CoreAutoLayout                	0x1b89a9aa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
6   UIKitCore                     	0x196d36688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
7   UIKitCore                     	0x196e11b84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
8   QuartzCore                    	0x195fadc14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
9   QuartzCore                    	0x195fad58c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
10  QuartzCore                    	0x195faf7f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
11  QuartzCore                    	0x195faecc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
12  QuartzCore                    	0x196112e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
13  libsystem_pthread.dylib       	0x21eb3236c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
14  libsystem_pthread.dylib       	0x21eb320dc _pthread_exit + 84 (pthread.c:1770)
15  libsystem_pthread.dylib       	0x21eb342d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
16  libsystem_pthread.dylib       	0x21eb30a94 _pthread_wqthread + 428 (pthread.c:2690)
17  libsystem_pthread.dylib       	0x21eb30aac start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001e563fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e564339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e56432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e5643100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000194432900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001944311f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000194432c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001e1611454 GSEventRunModal + 168 (GSEvent.c:2196)
8   UIKitCore                     	0x0000000196e45274 -[UIApplication _run] + 816 (UIApplication.m:3845)
9   UIKitCore                     	0x0000000196e10a28 UIApplicationMain + 336 (UIApplication.m:5540)
10  UIKitCore                     	0x0000000196ef2168 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000100b75130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000100b75130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000100b75130 main + 120
14  dyld                          	0x00000001bb307f08 start + 6040 (dyldMain.cpp:1450)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001e563fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e564339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e56432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e5643100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000194432900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001944311f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000194432c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Foundation                    	0x00000001930aa79c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:375)
8   Foundation                    	0x00000001930b0020 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:422)
9   UIKitCore                     	0x0000000196e2f56c -[UIEventFetcher threadMain] + 424 (UIEventFetcher.m:1351)
10  Foundation                    	0x0000000193110804 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000021eb33344 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000021eb30ab8 thread_start + 8 (:-1)

Thread 2:
0   libsystem_kernel.dylib        	0x00000001e5645658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019c4049ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019c419d10 sleep + 52 (sleep.c:62)
3   发型测试                          	0x00000001013aaef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x000000021eb33344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021eb30ab8 thread_start + 8 (:-1)

Thread 3 name:
Thread 3:
0   libsystem_kernel.dylib        	0x00000001e563fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e564339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e564122c thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x000000010138297c handleExceptions + 120
4   libsystem_pthread.dylib       	0x000000021eb33344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021eb30ab8 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001e563fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e5643430 mach_msg2_internal + 224 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e56432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e5643100 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001013829a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x000000021eb33344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021eb30ab8 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001e5645658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019c4049ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019c404ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001012ea580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x000000010129ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021eb33344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021eb30ab8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e5645658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019c4049ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019c404ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001012ea580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x000000010129ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021eb33344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021eb30ab8 thread_start + 8 (:-1)

Thread 7 Crashed:
0   libsystem_c.dylib             	0x000000019c468380 __abort + 164 (abort.c:175)
1   libsystem_c.dylib             	0x000000019c4682dc abort + 136 (abort.c:130)
2   libc++abi.dylib               	0x000000021ea615a0 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000021ea4ff10 demangling_terminate_handler() + 344 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x00000001919d7bf8 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x000000010138122c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x000000021ea608b4 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000021ea63e1c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x000000021ea63dc4 __cxa_throw + 92 (cxa_exception.cpp:299)
9   libobjc.A.dylib               	0x00000001919d5c24 objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001b89a8d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001b89a9dec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
12  CoreAutoLayout                	0x00000001b89a9d1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
13  CoreAutoLayout                	0x00000001b89a9aa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
14  UIKitCore                     	0x0000000196d36688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
15  UIKitCore                     	0x0000000196e11b84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
16  QuartzCore                    	0x0000000195fadc14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
17  QuartzCore                    	0x0000000195fad58c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
18  QuartzCore                    	0x0000000195faf7f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
19  QuartzCore                    	0x0000000195faecc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
20  QuartzCore                    	0x0000000196112e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
21  libsystem_pthread.dylib       	0x000000021eb3236c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
22  libsystem_pthread.dylib       	0x000000021eb320dc _pthread_exit + 84 (pthread.c:1770)
23  libsystem_pthread.dylib       	0x000000021eb342d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
24  libsystem_pthread.dylib       	0x000000021eb30a94 _pthread_wqthread + 428 (pthread.c:2690)
25  libsystem_pthread.dylib       	0x000000021eb30aac start_wqthread + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001e563fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e564339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e56432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e5643100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000194432900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001944311f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000194432c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CFNetwork                     	0x0000000195a5230c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x0000000193110804 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000021eb33344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021eb30ab8 thread_start + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001e563fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e564339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e56432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e5643100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000194432900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001944311f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000194432c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CoreFoundation                	0x00000001944ad674 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001a1d7de4c CLMotionCore::runMotionThread(void*) + 1300 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000021eb33344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021eb30ab8 thread_start + 8 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x000000021eb30aa4 start_wqthread + 0 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x000000021eb30aa4 start_wqthread + 0 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001e5645438 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x000000021eb31e50 _pthread_cond_wait + 984 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001abd41864 scavenger_thread_main + 1584 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x000000021eb33344 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x000000021eb30ab8 thread_start + 8 (:-1)

Thread 13:
0   libsystem_pthread.dylib       	0x000000021eb30aa4 start_wqthread + 0 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x000000021eb30aa4 start_wqthread + 0 (:-1)

Thread 15:
0   libsystem_pthread.dylib       	0x000000021eb30aa4 start_wqthread + 0 (:-1)

Thread 16:
0   libsystem_pthread.dylib       	0x000000021eb30aa4 start_wqthread + 0 (:-1)


Thread 7 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0x429e2befab00d5f3
    x8: 0x00000000ffffffe7   x9: 0x00000001ff166250  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001948dc494  x14: 0x0000000000000001  x15: 0xffffffffb00007ff
   x16: 0x0000000000000030  x17: 0x0000000200bce440  x18: 0x0000000000000000  x19: 0x000000016fe67000
   x20: 0x000000016fe622f0  x21: 0x000000016fe623a0  x22: 0x00000001fcc88000  x23: 0x0000000126e24e58
   x24: 0x0000000000000000  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x000000020d43e910   fp: 0x000000016fe62310   lr: 0x000000019c468380
    sp: 0x000000016fe622e0   pc: 0x000000019c468380 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x10096c000 -         0x10174bfff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/发型测试
        0x101b14000 -         0x101b1ffff libobjc-trampolines.dylib arm64e  <9136d8ba22ff3f129caddfc4c6dc51de> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x101c18000 -         0x101c27fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x101c44000 -         0x101c53fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x101c6c000 -         0x101c77fff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x101c98000 -         0x101ccffff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x101d44000 -         0x101d4ffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x101d78000 -         0x101d8bfff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/Promises.framework/Promises
        0x101dac000 -         0x101dbffff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x101ddc000 -         0x101debfff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x101dfc000 -         0x101e07fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x101e30000 -         0x101e47fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x101fc0000 -         0x101ffffff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x1020a4000 -         0x1020f7fff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x10217c000 -         0x10219bfff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x1021fc000 -         0x102227fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x102288000 -         0x1023affff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x1025ec000 -         0x10261bfff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x102684000 -         0x1026e7fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x10271c000 -         0x102747fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x102798000 -         0x10298ffff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x102c24000 -         0x102c8ffff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x102d10000 -         0x102da3fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x10314c000 -         0x10327bfff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x103714000 -         0x1038d3fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x103e70000 -         0x10514bfff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/E9B4D062-D115-4555-9254-6CCEA5DE9F3D/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x1919a4000 -         0x1919f5bb3 libobjc.A.dylib arm64e  <ed7c5fc7ddc734249c44db56f51b8be2> /usr/lib/libobjc.A.dylib
        0x19309b000 -         0x193d0eddf Foundation arm64e  <34de055d8683380a9198c3347211d13d> /System/Library/Frameworks/Foundation.framework/Foundation
        0x194421000 -         0x19499dfff CoreFoundation arm64e  <7821f73c378b3a10be90ef526b7dba93> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x1959b2000 -         0x195d77b9f CFNetwork arm64e  <a35a109c49d23986965d4ed7e0b6681e> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x195f99000 -         0x19635369f QuartzCore arm64e  <109010da3c353e22b001939786412ee2> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x196d10000 -         0x198c51b5f UIKitCore arm64e  <96636f64106f30c8a78082dcebb0f443> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x19c3f1000 -         0x19c4708ef libsystem_c.dylib arm64e  <93f93d7c245f3395822dec61ffae79cf> /usr/lib/system/libsystem_c.dylib
        0x1a1d77000 -         0x1a2194a9f CoreMotion arm64e  <cec80db7b3f23b179d4ebcfeb020ca2d> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1abcf6000 -         0x1ad73175f JavaScriptCore arm64e  <e32426af64113260be0bbe0ad12e23c8> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1b89a7000 -         0x1b89efc3f CoreAutoLayout arm64e  <b850e010e4023e07aaa549f71cccc7fc> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1bb2c9000 -         0x1bb363857 dyld arm64e  <86d5253d4fd136f3b4ab25982c90cbf4> /usr/lib/dyld
        0x1e1610000 -         0x1e1618c7f GraphicsServices arm64e  <5ba62c226d3731999dfd0e0f7abebfa9> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e563f000 -         0x1e5678ebf libsystem_kernel.dylib arm64e  <9e195be11733345ea9bf50d0d7059647> /usr/lib/system/libsystem_kernel.dylib
        0x21ea4b000 -         0x21ea68fff libc++abi.dylib arm64e  <a360ea66d985389394b96bba7bd8a6df> /usr/lib/libc++abi.dylib
        0x21eb30000 -         0x21eb3c3f3 libsystem_pthread.dylib arm64e  <b37430d8e3af33e481e1faed9ee26e8a> /usr/lib/system/libsystem_pthread.dylib

EOF
