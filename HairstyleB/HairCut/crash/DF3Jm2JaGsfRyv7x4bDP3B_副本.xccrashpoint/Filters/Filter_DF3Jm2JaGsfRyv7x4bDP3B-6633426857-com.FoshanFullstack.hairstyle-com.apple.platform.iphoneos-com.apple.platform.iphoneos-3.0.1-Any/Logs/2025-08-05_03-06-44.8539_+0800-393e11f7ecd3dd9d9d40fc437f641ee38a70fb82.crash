Incident Identifier: 6CC5FF06-7251-4857-B5FC-EF7097D36829
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone14,8
Process:             发型测试 [41431]
Path:                /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone14,8:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [2790]

Date/Time:           2025-08-05 03:06:44.8539 +0800
Launch Time:         2025-08-05 03:06:14.7483 +0800
OS Version:          iPhone OS 18.5 (22F76)
Release Type:        User
Baseband Version:    3.60.02
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x00000001a5c68380
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [41431]

Triggered by Thread:  5

Last Exception Backtrace:
0   CoreFoundation                	0x19dd3b21c __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x19b1d5abc objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1c21a8d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1c21a9dec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
4   CoreAutoLayout                	0x1c21a9d1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
5   CoreAutoLayout                	0x1c21a9aa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
6   UIKitCore                     	0x1a0536688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
7   UIKitCore                     	0x1a0611b84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
8   QuartzCore                    	0x19f7adc14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
9   QuartzCore                    	0x19f7ad58c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
10  QuartzCore                    	0x19f7af7f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
11  QuartzCore                    	0x19f7aecc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
12  QuartzCore                    	0x19f912e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
13  libsystem_pthread.dylib       	0x22834236c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
14  libsystem_pthread.dylib       	0x2283420dc _pthread_exit + 84 (pthread.c:1770)
15  libsystem_pthread.dylib       	0x2283442d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
16  libsystem_pthread.dylib       	0x228340a94 _pthread_wqthread + 428 (pthread.c:2690)
17  libsystem_pthread.dylib       	0x228340aac start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001eee3fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001eee4339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001eee432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001eee43100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019dc32900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019dc311f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019dc32c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001eae11454 GSEventRunModal + 168 (GSEvent.c:2196)
8   UIKitCore                     	0x00000001a0645274 -[UIApplication _run] + 816 (UIApplication.m:3845)
9   UIKitCore                     	0x00000001a0610a28 UIApplicationMain + 336 (UIApplication.m:5540)
10  UIKitCore                     	0x00000001a06f2168 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000102c61130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000102c61130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000102c61130 main + 120
14  dyld                          	0x00000001c4b07f08 start + 6040 (dyldMain.cpp:1450)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001eee3fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001eee4339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001eee432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001eee43100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019dc32900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019dc311f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019dc32c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Foundation                    	0x000000019c8aa79c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:375)
8   Foundation                    	0x000000019c8b0020 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:422)
9   UIKitCore                     	0x00000001a062f56c -[UIEventFetcher threadMain] + 424 (UIEventFetcher.m:1351)
10  Foundation                    	0x000000019c910804 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x0000000228343344 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x0000000228340ab8 thread_start + 8 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x0000000228340aa4 start_wqthread + 0 (:-1)

Thread 3:
0   libsystem_pthread.dylib       	0x0000000228340aa4 start_wqthread + 0 (:-1)

Thread 4:
0   libsystem_pthread.dylib       	0x0000000228340aa4 start_wqthread + 0 (:-1)

Thread 5 Crashed:
0   libsystem_c.dylib             	0x00000001a5c68380 __abort + 164 (abort.c:175)
1   libsystem_c.dylib             	0x00000001a5c682dc abort + 136 (abort.c:130)
2   libc++abi.dylib               	0x00000002282715a0 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000022825ff10 demangling_terminate_handler() + 344 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000019b1d7bf8 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x000000010346d22c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x00000002282708b4 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x0000000228273e1c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x0000000228273dc4 __cxa_throw + 92 (cxa_exception.cpp:299)
9   libobjc.A.dylib               	0x000000019b1d5c24 objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001c21a8d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001c21a9dec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
12  CoreAutoLayout                	0x00000001c21a9d1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
13  CoreAutoLayout                	0x00000001c21a9aa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
14  UIKitCore                     	0x00000001a0536688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
15  UIKitCore                     	0x00000001a0611b84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
16  QuartzCore                    	0x000000019f7adc14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
17  QuartzCore                    	0x000000019f7ad58c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
18  QuartzCore                    	0x000000019f7af7f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
19  QuartzCore                    	0x000000019f7aecc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
20  QuartzCore                    	0x000000019f912e78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
21  libsystem_pthread.dylib       	0x000000022834236c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
22  libsystem_pthread.dylib       	0x00000002283420dc _pthread_exit + 84 (pthread.c:1770)
23  libsystem_pthread.dylib       	0x00000002283442d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
24  libsystem_pthread.dylib       	0x0000000228340a94 _pthread_wqthread + 428 (pthread.c:2690)
25  libsystem_pthread.dylib       	0x0000000228340aac start_wqthread + 8 (:-1)

Thread 6:
0   libsystem_kernel.dylib        	0x00000001eee45658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a5c049ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a5c19d10 sleep + 52 (sleep.c:62)
3   发型测试                          	0x0000000103496ef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x0000000228343344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000228340ab8 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001eee3fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001eee4339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001eee4122c thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x000000010346e97c handleExceptions + 120
4   libsystem_pthread.dylib       	0x0000000228343344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x0000000228340ab8 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001eee3fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001eee43430 mach_msg2_internal + 224 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001eee432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001eee43100 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x000000010346e9a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x0000000228343344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000228340ab8 thread_start + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001eee45658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a5c049ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a5c04ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001033d6580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000103388e84 thread_do + 340
5   libsystem_pthread.dylib       	0x0000000228343344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000228340ab8 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001eee45658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x00000001a5c049ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x00000001a5c04ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001033d6580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000103388e84 thread_do + 340
5   libsystem_pthread.dylib       	0x0000000228343344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x0000000228340ab8 thread_start + 8 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x0000000228340aa4 start_wqthread + 0 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001eee45438 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x0000000228341e50 _pthread_cond_wait + 984 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001b5541864 scavenger_thread_main + 1584 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x0000000228343344 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x0000000228340ab8 thread_start + 8 (:-1)

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001eee3fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001eee4339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001eee432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001eee43100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019dc32900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019dc311f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019dc32c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CFNetwork                     	0x000000019f25230c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x000000019c910804 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x0000000228343344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000228340ab8 thread_start + 8 (:-1)

Thread 14:
0   libsystem_pthread.dylib       	0x0000000228340aa4 start_wqthread + 0 (:-1)

Thread 15 name:
Thread 15:
0   libsystem_kernel.dylib        	0x00000001eee3fce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001eee4339c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001eee432b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001eee43100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019dc32900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x000000019dc311f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019dc32c3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CoreFoundation                	0x000000019dcad674 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001ab57de4c CLMotionCore::runMotionThread(void*) + 1300 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x0000000228343344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x0000000228340ab8 thread_start + 8 (:-1)


Thread 5 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0xe695a1f9ef8cebe6
    x8: 0x00000000ffffffe7   x9: 0x0000000208966510  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x000000019e0dc494  x14: 0x0000000000000001  x15: 0xffffffffb00007ff
   x16: 0x0000000000000030  x17: 0x000000020a3ce440  x18: 0x0000000000000000  x19: 0x000000016d88f000
   x20: 0x000000016d88a2f0  x21: 0x000000016d88a3a0  x22: 0x0000000206488000  x23: 0x000000010877ce58
   x24: 0x0000000000000000  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x0000000216c3e970   fp: 0x000000016d88a310   lr: 0x00000001a5c68380
    sp: 0x000000016d88a2e0   pc: 0x00000001a5c68380 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x102a58000 -         0x103837fff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/发型测试
        0x103c08000 -         0x103c13fff libobjc-trampolines.dylib arm64e  <9136d8ba22ff3f129caddfc4c6dc51de> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x103d04000 -         0x103d13fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x103d30000 -         0x103d3ffff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x103d58000 -         0x103d63fff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x103d8c000 -         0x103d9ffff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/Promises.framework/Promises
        0x103dc0000 -         0x103dcbfff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x103f1c000 -         0x103f53fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x103fc8000 -         0x103fd7fff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x103ff8000 -         0x10400bfff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x104028000 -         0x104033fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x1040a8000 -         0x1040e7fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x10418c000 -         0x1041a3fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x1041e4000 -         0x10430bfff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x1044b0000 -         0x104503fff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x104588000 -         0x1045b3fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x104608000 -         0x104627fff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x1046f0000 -         0x10471ffff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x104788000 -         0x1047ebfff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x104820000 -         0x10484bfff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x10489c000 -         0x104a93fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x104d28000 -         0x104d93fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x104e14000 -         0x104ea7fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x105250000 -         0x10537ffff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x105818000 -         0x1059d7fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x105dcc000 -         0x1070a7fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/CD85E502-E2B9-4BD8-AEA3-1459A24A01CD/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x19b1a4000 -         0x19b1f5bb3 libobjc.A.dylib arm64e  <ed7c5fc7ddc734249c44db56f51b8be2> /usr/lib/libobjc.A.dylib
        0x19c89b000 -         0x19d50eddf Foundation arm64e  <34de055d8683380a9198c3347211d13d> /System/Library/Frameworks/Foundation.framework/Foundation
        0x19dc21000 -         0x19e19dfff CoreFoundation arm64e  <7821f73c378b3a10be90ef526b7dba93> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x19f1b2000 -         0x19f577b9f CFNetwork arm64e  <a35a109c49d23986965d4ed7e0b6681e> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x19f799000 -         0x19fb5369f QuartzCore arm64e  <109010da3c353e22b001939786412ee2> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x1a0510000 -         0x1a2451b5f UIKitCore arm64e  <96636f64106f30c8a78082dcebb0f443> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x1a5bf1000 -         0x1a5c708ef libsystem_c.dylib arm64e  <93f93d7c245f3395822dec61ffae79cf> /usr/lib/system/libsystem_c.dylib
        0x1ab577000 -         0x1ab994a9f CoreMotion arm64e  <cec80db7b3f23b179d4ebcfeb020ca2d> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1b54f6000 -         0x1b6f3175f JavaScriptCore arm64e  <e32426af64113260be0bbe0ad12e23c8> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1c21a7000 -         0x1c21efc3f CoreAutoLayout arm64e  <b850e010e4023e07aaa549f71cccc7fc> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1c4ac9000 -         0x1c4b63857 dyld arm64e  <86d5253d4fd136f3b4ab25982c90cbf4> /usr/lib/dyld
        0x1eae10000 -         0x1eae18c7f GraphicsServices arm64e  <5ba62c226d3731999dfd0e0f7abebfa9> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1eee3f000 -         0x1eee78ebf libsystem_kernel.dylib arm64e  <9e195be11733345ea9bf50d0d7059647> /usr/lib/system/libsystem_kernel.dylib
        0x22825b000 -         0x228278fff libc++abi.dylib arm64e  <a360ea66d985389394b96bba7bd8a6df> /usr/lib/libc++abi.dylib
        0x228340000 -         0x22834c3f3 libsystem_pthread.dylib arm64e  <b37430d8e3af33e481e1faed9ee26e8a> /usr/lib/system/libsystem_pthread.dylib

EOF
