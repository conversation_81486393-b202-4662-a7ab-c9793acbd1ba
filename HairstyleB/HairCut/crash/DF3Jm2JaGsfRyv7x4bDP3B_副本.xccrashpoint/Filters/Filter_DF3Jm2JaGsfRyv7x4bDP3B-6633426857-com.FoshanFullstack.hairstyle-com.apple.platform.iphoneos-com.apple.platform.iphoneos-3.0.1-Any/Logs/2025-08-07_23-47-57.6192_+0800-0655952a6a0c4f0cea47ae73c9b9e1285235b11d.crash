Incident Identifier: E6275CD4-22DC-41EF-BC74-D1E2226E2C9E
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone17,2
Process:             发型测试 [11471]
Path:                /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone17,2:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [3135]

Date/Time:           2025-08-07 23:47:57.6192 +0800
Launch Time:         2025-08-07 23:47:26.6192 +0800
OS Version:          iPhone OS 18.5 (22F76)
Release Type:        User
Baseband Version:    1.60.02
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000019d0d0380
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [11471]

Triggered by Thread:  4

Last Exception Backtrace:
0   CoreFoundation                	0x1951a321c __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x19263dabc objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1b9610d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1b9611dec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
4   CoreAutoLayout                	0x1b9611d1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
5   CoreAutoLayout                	0x1b9611aa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
6   UIKitCore                     	0x19799e688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
7   UIKitCore                     	0x197a79b84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
8   QuartzCore                    	0x196c15c14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
9   QuartzCore                    	0x196c1558c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
10  QuartzCore                    	0x196c177f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
11  QuartzCore                    	0x196c16cc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
12  QuartzCore                    	0x196d7ae78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
13  libsystem_pthread.dylib       	0x21f7b236c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
14  libsystem_pthread.dylib       	0x21f7b20dc _pthread_exit + 84 (pthread.c:1770)
15  libsystem_pthread.dylib       	0x21f7b42d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
16  libsystem_pthread.dylib       	0x21f7b0a94 _pthread_wqthread + 428 (pthread.c:2690)
17  libsystem_pthread.dylib       	0x21f7b0aac start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001e62a7ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e62ab39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e62ab2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e62ab100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019509a900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001950991f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019509ac3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001e2279454 GSEventRunModal + 168 (GSEvent.c:2196)
8   UIKitCore                     	0x0000000197aad274 -[UIApplication _run] + 816 (UIApplication.m:3845)
9   UIKitCore                     	0x0000000197a78a28 UIApplicationMain + 336 (UIApplication.m:5540)
10  UIKitCore                     	0x0000000197b5a168 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000102c25130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000102c25130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000102c25130 main + 120
14  dyld                          	0x00000001bbf6ff08 start + 6040 (dyldMain.cpp:1450)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001e62a7ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e62ab39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e62ab2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e62ab100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019509a900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001950991f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019509ac3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Foundation                    	0x0000000193d1279c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:375)
8   Foundation                    	0x0000000193d18020 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:422)
9   UIKitCore                     	0x0000000197a9756c -[UIEventFetcher threadMain] + 424 (UIEventFetcher.m:1351)
10  Foundation                    	0x0000000193d78804 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000021f7b3344 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000021f7b0ab8 thread_start + 8 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x000000021f7b0aa4 start_wqthread + 0 (:-1)

Thread 3:
0   libsystem_pthread.dylib       	0x000000021f7b0aa4 start_wqthread + 0 (:-1)

Thread 4 Crashed:
0   libsystem_c.dylib             	0x000000019d0d0380 __abort + 164 (abort.c:175)
1   libsystem_c.dylib             	0x000000019d0d02dc abort + 136 (abort.c:130)
2   libc++abi.dylib               	0x000000021f6e15a0 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000021f6cff10 demangling_terminate_handler() + 344 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x000000019263fbf8 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x000000010343122c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x000000021f6e08b4 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000021f6e3e1c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x000000021f6e3dc4 __cxa_throw + 92 (cxa_exception.cpp:299)
9   libobjc.A.dylib               	0x000000019263dc24 objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001b9610d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001b9611dec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
12  CoreAutoLayout                	0x00000001b9611d1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
13  CoreAutoLayout                	0x00000001b9611aa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
14  UIKitCore                     	0x000000019799e688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
15  UIKitCore                     	0x0000000197a79b84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
16  QuartzCore                    	0x0000000196c15c14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
17  QuartzCore                    	0x0000000196c1558c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
18  QuartzCore                    	0x0000000196c177f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
19  QuartzCore                    	0x0000000196c16cc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
20  QuartzCore                    	0x0000000196d7ae78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
21  libsystem_pthread.dylib       	0x000000021f7b236c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
22  libsystem_pthread.dylib       	0x000000021f7b20dc _pthread_exit + 84 (pthread.c:1770)
23  libsystem_pthread.dylib       	0x000000021f7b42d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
24  libsystem_pthread.dylib       	0x000000021f7b0a94 _pthread_wqthread + 428 (pthread.c:2690)
25  libsystem_pthread.dylib       	0x000000021f7b0aac start_wqthread + 8 (:-1)

Thread 5:
0   libsystem_kernel.dylib        	0x00000001e62ad658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019d06c9ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019d081d10 sleep + 52 (sleep.c:62)
3   发型测试                          	0x000000010345aef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x000000021f7b3344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021f7b0ab8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e62a7ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e62ab39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e62a922c thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x000000010343297c handleExceptions + 120
4   libsystem_pthread.dylib       	0x000000021f7b3344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021f7b0ab8 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e62a7ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e62ab430 mach_msg2_internal + 224 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e62ab2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e62ab100 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x00000001034329a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x000000021f7b3344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021f7b0ab8 thread_start + 8 (:-1)

Thread 8 name:
Thread 8:
0   libsystem_kernel.dylib        	0x00000001e62a7c78 semaphore_timedwait_trap + 8 (:-1)
1   libdispatch.dylib             	0x000000019d049198 _dispatch_sema4_timedwait + 64 (lock.c:154)
2   libdispatch.dylib             	0x000000019d016e58 _dispatch_semaphore_wait_slow + 76 (semaphore.c:116)
3   发型测试                          	0x000000010338d7a0 hevc_decoder_close1_::SemaphoreToWait(NSObject<OS_dispatch_semaphore>*, int, int volatile*, TTHttpTask*, NSURLSessionTask*) + 120
4   发型测试                          	0x000000010338eb58 hevc_decoder_close1_::write_sample_table_box_::mc_luma_v2_pass2_m_neon_(tt265_release_frame_::tt265_dec_destroy_<tt265_default_param_::tt265_alloc_frame_>, tt265_release_frame_::tt265_dec_destroy_<... + 2116
5   发型测试                          	0x000000010338c0a4 hevc_decoder_close1_::write_sample_table_box_::SampleTableBox_release_(tt265_release_frame_::tt265_dec_destroy_<tt265_default_param_::tt265_alloc_frame_>, tt265_release_frame_::tt265_dec_destroy_<t... + 104
6   发型测试                          	0x00000001033a5f44 hevc_decoder_close1_::out_of_range_::_function_() + 5664
7   发型测试                          	0x00000001033552a4 hevc_decoder_close1_::HevcConfigurationBox_release_::EnableRiskInspectModule()::$_2::operator()(void*) const + 140
8   发型测试                          	0x000000010335520c hevc_decoder_close1_::HevcConfigurationBox_release_::EnableRiskInspectModule()::$_2::__invoke(void*) + 12
9   发型测试                          	0x000000010339a484 hevc_decoder_close1_::worker_thread(void*) + 744
10  发型测试                          	0x000000010334ce84 thread_do + 340
11  libsystem_pthread.dylib       	0x000000021f7b3344 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000021f7b0ab8 thread_start + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001e62ad658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019d06c9ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019d06cae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x000000010339a580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x000000010334ce84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021f7b3344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021f7b0ab8 thread_start + 8 (:-1)

Thread 10:
0   libsystem_pthread.dylib       	0x000000021f7b0aa4 start_wqthread + 0 (:-1)

Thread 11:
0   libsystem_pthread.dylib       	0x000000021f7b0aa4 start_wqthread + 0 (:-1)

Thread 12 name:
Thread 12:
0   libsystem_kernel.dylib        	0x00000001e62ad438 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x000000021f7b1e50 _pthread_cond_wait + 984 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001ac9a9864 scavenger_thread_main + 1584 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x000000021f7b3344 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x000000021f7b0ab8 thread_start + 8 (:-1)

Thread 13 name:
Thread 13:
0   libsystem_kernel.dylib        	0x00000001e62a7ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e62ab39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e62ab2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e62ab100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019509a900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001950991f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019509ac3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CFNetwork                     	0x00000001966ba30c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x0000000193d78804 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000021f7b3344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021f7b0ab8 thread_start + 8 (:-1)

Thread 14 name:
Thread 14:
0   libsystem_kernel.dylib        	0x00000001e62a7ce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e62ab39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e62ab2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e62ab100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x000000019509a900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x00000001950991f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x000000019509ac3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CoreFoundation                	0x0000000195115674 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001a29e5e4c CLMotionCore::runMotionThread(void*) + 1300 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000021f7b3344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021f7b0ab8 thread_start + 8 (:-1)

Thread 15:
0   libsystem_pthread.dylib       	0x000000021f7b0aa4 start_wqthread + 0 (:-1)

Thread 16:
0   libsystem_pthread.dylib       	0x000000021f7b0aa4 start_wqthread + 0 (:-1)


Thread 4 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0xd38f5e79ccb0d6e9
    x8: 0x00000000ffffffe7   x9: 0x00000001ffdce950  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x0000000195544494  x14: 0x0000000000000001  x15: 0xffffffffb00007ff
   x16: 0x0000000000000030  x17: 0x0000000201836440  x18: 0x0000000000000000  x19: 0x000000016d957000
   x20: 0x000000016d9522f0  x21: 0x000000016d9523a0  x22: 0x00000001fd8f0000  x23: 0x0000000108bb51d8
   x24: 0x0000000000000000  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x000000020dca69d0   fp: 0x000000016d952310   lr: 0x000000019d0d0380
    sp: 0x000000016d9522e0   pc: 0x000000019d0d0380 cpsr: 0x40000000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x102a1c000 -         0x1037fbfff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/发型测试
        0x103bc8000 -         0x103bd3fff libobjc-trampolines.dylib arm64e  <9136d8ba22ff3f129caddfc4c6dc51de> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x103c2c000 -         0x103c3ffff GAXClient arm64e  <8e9f05236ba938078b1272ed92c1754a> /System/Library/AccessibilityBundles/GAXClient.bundle/GAXClient
        0x103cc8000 -         0x103cd7fff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x103cf4000 -         0x103d03fff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x103d1c000 -         0x103d27fff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x103d48000 -         0x103d7ffff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x103df4000 -         0x103dfffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x103e28000 -         0x103e3bfff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/Promises.framework/Promises
        0x103e5c000 -         0x103e6ffff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x103e8c000 -         0x103e9bfff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x103fe8000 -         0x103ff3fff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x10403c000 -         0x104053fff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x1040c8000 -         0x104107fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x1041ac000 -         0x1041fffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x1042b0000 -         0x1043d7fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x10457c000 -         0x10459bfff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x1045fc000 -         0x104627fff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x104714000 -         0x104743fff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1047ac000 -         0x10480ffff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x104844000 -         0x10486ffff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x1048c0000 -         0x104ab7fff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x104d4c000 -         0x104db7fff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x104e38000 -         0x104ecbfff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x105274000 -         0x1053a3fff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x10583c000 -         0x1059fbfff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x105e98000 -         0x107173fff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/C438B28D-E1FA-4A17-AC6C-881412E9F648/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x19260c000 -         0x19265dbb3 libobjc.A.dylib arm64e  <ed7c5fc7ddc734249c44db56f51b8be2> /usr/lib/libobjc.A.dylib
        0x193d03000 -         0x194976ddf Foundation arm64e  <34de055d8683380a9198c3347211d13d> /System/Library/Frameworks/Foundation.framework/Foundation
        0x195089000 -         0x195605fff CoreFoundation arm64e  <7821f73c378b3a10be90ef526b7dba93> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x19661a000 -         0x1969dfb9f CFNetwork arm64e  <a35a109c49d23986965d4ed7e0b6681e> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x196c01000 -         0x196fbb69f QuartzCore arm64e  <109010da3c353e22b001939786412ee2> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x197978000 -         0x1998b9b5f UIKitCore arm64e  <96636f64106f30c8a78082dcebb0f443> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x19d013000 -         0x19d058b1f libdispatch.dylib arm64e  <395da84f715d334e8d41a16cd93fc83c> /usr/lib/system/libdispatch.dylib
        0x19d059000 -         0x19d0d88ef libsystem_c.dylib arm64e  <93f93d7c245f3395822dec61ffae79cf> /usr/lib/system/libsystem_c.dylib
        0x1a29df000 -         0x1a2dfca9f CoreMotion arm64e  <cec80db7b3f23b179d4ebcfeb020ca2d> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1ac95e000 -         0x1ae39975f JavaScriptCore arm64e  <e32426af64113260be0bbe0ad12e23c8> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1b960f000 -         0x1b9657c3f CoreAutoLayout arm64e  <b850e010e4023e07aaa549f71cccc7fc> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1bbf31000 -         0x1bbfcb857 dyld arm64e  <86d5253d4fd136f3b4ab25982c90cbf4> /usr/lib/dyld
        0x1e2278000 -         0x1e2280c7f GraphicsServices arm64e  <5ba62c226d3731999dfd0e0f7abebfa9> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e62a7000 -         0x1e62e0ebf libsystem_kernel.dylib arm64e  <9e195be11733345ea9bf50d0d7059647> /usr/lib/system/libsystem_kernel.dylib
        0x21f6cb000 -         0x21f6e8fff libc++abi.dylib arm64e  <a360ea66d985389394b96bba7bd8a6df> /usr/lib/libc++abi.dylib
        0x21f7b0000 -         0x21f7bc3f3 libsystem_pthread.dylib arm64e  <b37430d8e3af33e481e1faed9ee26e8a> /usr/lib/system/libsystem_pthread.dylib

EOF
