Incident Identifier: F0AE538B-AD14-4525-82D9-F90FF75B02F1
Distributor ID:      com.apple.AppStore
Hardware Model:      iPhone14,5
Process:             发型测试 [18489]
Path:                /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/发型测试
Identifier:          com.FoshanFullstack.hairstyle
Version:             3.0.1 (6)
AppStoreTools:       16F7
AppVariant:          1:iPhone14,5:18
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           com.FoshanFullstack.hairstyle [17244]

Date/Time:           2025-08-04 07:09:35.0829 +0800
Launch Time:         2025-08-04 07:08:54.6628 +0800
OS Version:          iPhone OS 18.5 (22F76)
Release Type:        User
Baseband Version:    4.60.01
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000019bf84380
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [18489]

Triggered by Thread:  8

Last Exception Backtrace:
0   CoreFoundation                	0x19405721c __exceptionPreprocess + 164 (NSException.m:249)
1   libobjc.A.dylib               	0x1914f1abc objc_exception_throw + 88 (objc-exception.mm:356)
2   CoreAutoLayout                	0x1b84c4d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
3   CoreAutoLayout                	0x1b84c5dec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
4   CoreAutoLayout                	0x1b84c5d1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
5   CoreAutoLayout                	0x1b84c5aa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
6   UIKitCore                     	0x196852688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
7   UIKitCore                     	0x19692db84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
8   QuartzCore                    	0x195ac9c14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
9   QuartzCore                    	0x195ac958c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
10  QuartzCore                    	0x195acb7f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
11  QuartzCore                    	0x195acacc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
12  QuartzCore                    	0x195c2ee78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
13  libsystem_pthread.dylib       	0x21e64e36c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
14  libsystem_pthread.dylib       	0x21e64e0dc _pthread_exit + 84 (pthread.c:1770)
15  libsystem_pthread.dylib       	0x21e6502d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
16  libsystem_pthread.dylib       	0x21e64ca94 _pthread_wqthread + 428 (pthread.c:2690)
17  libsystem_pthread.dylib       	0x21e64caac start_wqthread + 8 (:-1)

Thread 0 name:
Thread 0:
0   libsystem_kernel.dylib        	0x00000001e515bce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e515f39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e515f2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e515f100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000193f4e900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000193f4d1f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000193f4ec3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   GraphicsServices              	0x00000001e112d454 GSEventRunModal + 168 (GSEvent.c:2196)
8   UIKitCore                     	0x0000000196961274 -[UIApplication _run] + 816 (UIApplication.m:3845)
9   UIKitCore                     	0x000000019692ca28 UIApplicationMain + 336 (UIApplication.m:5540)
10  UIKitCore                     	0x0000000196a0e168 UIApplicationMain(_:_:_:_:) + 104 (UIKit.swift:565)
11  发型测试                          	0x0000000100e71130 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ0013oorqAgjBzrDBa03AppB0C_Tgm5 + 28 (/<compiler-generated>:12)
12  发型测试                          	0x0000000100e71130 static AppDelegate.$main() + 28 (AppDelegate.swift:0)
13  发型测试                          	0x0000000100e71130 main + 120
14  dyld                          	0x00000001bae23f08 start + 6040 (dyldMain.cpp:1450)

Thread 1 name:
Thread 1:
0   libsystem_kernel.dylib        	0x00000001e515bce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e515f39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e515f2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e515f100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000193f4e900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000193f4d1f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000193f4ec3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   Foundation                    	0x0000000192bc679c -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 212 (NSRunLoop.m:375)
8   Foundation                    	0x0000000192bcc020 -[NSRunLoop(NSRunLoop) runUntilDate:] + 64 (NSRunLoop.m:422)
9   UIKitCore                     	0x000000019694b56c -[UIEventFetcher threadMain] + 424 (UIEventFetcher.m:1351)
10  Foundation                    	0x0000000192c2c804 __NSThread__start__ + 732 (NSThread.m:991)
11  libsystem_pthread.dylib       	0x000000021e64f344 _pthread_start + 136 (pthread.c:931)
12  libsystem_pthread.dylib       	0x000000021e64cab8 thread_start + 8 (:-1)

Thread 2:
0   libsystem_pthread.dylib       	0x000000021e64caa4 _pthread_wqthread + 444 (pthread.c:2676)

Thread 3:
0   libsystem_kernel.dylib        	0x00000001e5161658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019bf209ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019bf35d10 sleep + 52 (sleep.c:62)
3   发型测试                          	0x00000001016a6ef8 monitorCachedData + 692
4   libsystem_pthread.dylib       	0x000000021e64f344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021e64cab8 thread_start + 8 (:-1)

Thread 4 name:
Thread 4:
0   libsystem_kernel.dylib        	0x00000001e515bce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e515f39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e515d22c thread_suspend + 108 (thread_actUser.c:1036)
3   发型测试                          	0x000000010167e97c handleExceptions + 120
4   libsystem_pthread.dylib       	0x000000021e64f344 _pthread_start + 136 (pthread.c:931)
5   libsystem_pthread.dylib       	0x000000021e64cab8 thread_start + 8 (:-1)

Thread 5 name:
Thread 5:
0   libsystem_kernel.dylib        	0x00000001e515bce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e515f430 mach_msg2_internal + 224 (mach_msg.c:244)
2   libsystem_kernel.dylib        	0x00000001e515f2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e515f100 mach_msg + 24 (mach_msg.c:323)
4   发型测试                          	0x000000010167e9a8 handleExceptions + 164
5   libsystem_pthread.dylib       	0x000000021e64f344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021e64cab8 thread_start + 8 (:-1)

Thread 6 name:
Thread 6:
0   libsystem_kernel.dylib        	0x00000001e5161658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019bf209ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019bf20ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001015e6580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000101598e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021e64f344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021e64cab8 thread_start + 8 (:-1)

Thread 7 name:
Thread 7:
0   libsystem_kernel.dylib        	0x00000001e5161658 __semwait_signal + 8 (:-1)
1   libsystem_c.dylib             	0x000000019bf209ac nanosleep + 220 (nanosleep.c:104)
2   libsystem_c.dylib             	0x000000019bf20ae0 usleep + 68 (usleep.c:52)
3   发型测试                          	0x00000001015e6580 hevc_decoder_close1_::worker_thread(void*) + 996
4   发型测试                          	0x0000000101598e84 thread_do + 340
5   libsystem_pthread.dylib       	0x000000021e64f344 _pthread_start + 136 (pthread.c:931)
6   libsystem_pthread.dylib       	0x000000021e64cab8 thread_start + 8 (:-1)

Thread 8 Crashed:
0   libsystem_c.dylib             	0x000000019bf84380 __abort + 164 (abort.c:175)
1   libsystem_c.dylib             	0x000000019bf842dc abort + 136 (abort.c:130)
2   libc++abi.dylib               	0x000000021e57d5a0 abort_message + 132 (abort_message.cpp:78)
3   libc++abi.dylib               	0x000000021e56bf10 demangling_terminate_handler() + 344 (cxa_default_handlers.cpp:77)
4   libobjc.A.dylib               	0x00000001914f3bf8 _objc_terminate() + 156 (objc-exception.mm:496)
5   发型测试                          	0x000000010167d22c CPPExceptionTerminate() + 176
6   libc++abi.dylib               	0x000000021e57c8b4 std::__terminate(void (*)()) + 16 (cxa_handlers.cpp:59)
7   libc++abi.dylib               	0x000000021e57fe1c __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 88 (cxa_exception.cpp:152)
8   libc++abi.dylib               	0x000000021e57fdc4 __cxa_throw + 92 (cxa_exception.cpp:299)
9   libobjc.A.dylib               	0x00000001914f1c24 objc_exception_throw + 448 (objc-exception.mm:385)
10  CoreAutoLayout                	0x00000001b84c4d1c _AssertAutoLayoutOnAllowedThreadsOnly + 316 (NSISEngine.m:0)
11  CoreAutoLayout                	0x00000001b84c5dec -[NSISEngine _optimizeWithoutRebuilding] + 72 (NSISEngine.m:1716)
12  CoreAutoLayout                	0x00000001b84c5d1c -[NSISEngine optimize] + 96 (NSISEngine.m:1690)
13  CoreAutoLayout                	0x00000001b84c5aa8 -[NSISEngine performPendingChangeNotifications] + 104 (NSISEngine.m:684)
14  UIKitCore                     	0x0000000196852688 -[UIView(CALayerDelegate) layoutSublayersOfLayer:] + 4000 (UIView.m:19986)
15  UIKitCore                     	0x000000019692db84 -[_UILabelLayer layoutSublayers] + 60 (_UILabelLayer.m:193)
16  QuartzCore                    	0x0000000195ac9c14 CA::Layer::layout_if_needed(CA::Transaction*) + 488 (CALayer.mm:10954)
17  QuartzCore                    	0x0000000195ac958c CA::Layer::layout_and_display_if_needed(CA::Transaction*) + 156 (CALayer.mm:2646)
18  QuartzCore                    	0x0000000195acb7f8 CA::Context::commit_transaction(CA::Transaction*, double, double*) + 476 (CAContextInternal.mm:2613)
19  QuartzCore                    	0x0000000195acacc0 CA::Transaction::commit() + 644 (CATransactionInternal.mm:420)
20  QuartzCore                    	0x0000000195c2ee78 CA::Transaction::release_thread(void*) + 232 (CATransactionInternal.mm:618)
21  libsystem_pthread.dylib       	0x000000021e64e36c _pthread_tsd_cleanup + 620 (pthread_tsd.c:416)
22  libsystem_pthread.dylib       	0x000000021e64e0dc _pthread_exit + 84 (pthread.c:1770)
23  libsystem_pthread.dylib       	0x000000021e6502d4 _pthread_wqthread_exit + 56 (pthread.c:2656)
24  libsystem_pthread.dylib       	0x000000021e64ca94 _pthread_wqthread + 428 (pthread.c:2690)
25  libsystem_pthread.dylib       	0x000000021e64caac start_wqthread + 8 (:-1)

Thread 9 name:
Thread 9:
0   libsystem_kernel.dylib        	0x00000001e5161438 __psynch_cvwait + 8 (:-1)
1   libsystem_pthread.dylib       	0x000000021e64de50 _pthread_cond_wait + 984 (pthread_cond.c:862)
2   JavaScriptCore                	0x00000001ab85d864 scavenger_thread_main + 1584 (pas_scavenger.c:347)
3   libsystem_pthread.dylib       	0x000000021e64f344 _pthread_start + 136 (pthread.c:931)
4   libsystem_pthread.dylib       	0x000000021e64cab8 thread_start + 8 (:-1)

Thread 10 name:
Thread 10:
0   libsystem_kernel.dylib        	0x00000001e515bce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e515f39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e515f2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e515f100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000193f4e900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000193f4d1f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000193f4ec3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CFNetwork                     	0x000000019556e30c +[__CFN_CoreSchedulingSetRunnable _run:] + 416 (CoreSchedulingSet.mm:1473)
8   Foundation                    	0x0000000192c2c804 __NSThread__start__ + 732 (NSThread.m:991)
9   libsystem_pthread.dylib       	0x000000021e64f344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021e64cab8 thread_start + 8 (:-1)

Thread 11 name:
Thread 11:
0   libsystem_kernel.dylib        	0x00000001e515bce4 mach_msg2_trap + 8 (:-1)
1   libsystem_kernel.dylib        	0x00000001e515f39c mach_msg2_internal + 76 (mach_msg.c:201)
2   libsystem_kernel.dylib        	0x00000001e515f2b8 mach_msg_overwrite + 428 (mach_msg.c:0)
3   libsystem_kernel.dylib        	0x00000001e515f100 mach_msg + 24 (mach_msg.c:323)
4   CoreFoundation                	0x0000000193f4e900 __CFRunLoopServiceMachPort + 160 (CFRunLoop.c:2637)
5   CoreFoundation                	0x0000000193f4d1f0 __CFRunLoopRun + 1208 (CFRunLoop.c:3021)
6   CoreFoundation                	0x0000000193f4ec3c CFRunLoopRunSpecific + 572 (CFRunLoop.c:3434)
7   CoreFoundation                	0x0000000193fc9674 CFRunLoopRun + 64 (CFRunLoop.c:3460)
8   CoreMotion                    	0x00000001a1899e4c CLMotionCore::runMotionThread(void*) + 1300 (CLMotionCore.mm:376)
9   libsystem_pthread.dylib       	0x000000021e64f344 _pthread_start + 136 (pthread.c:931)
10  libsystem_pthread.dylib       	0x000000021e64cab8 thread_start + 8 (:-1)

Thread 12:
0   libsystem_pthread.dylib       	0x000000021e64caa4 _pthread_wqthread + 444 (pthread.c:2676)

Thread 13:
0   libsystem_pthread.dylib       	0x000000021e64caa4 _pthread_wqthread + 444 (pthread.c:2676)


Thread 8 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000001
    x4: 0x0000000000000000   x5: 0x0000000000989680   x6: 0x000000000000006e   x7: 0xbc741901c39eb43e
    x8: 0x00000000ffffffe7   x9: 0x00000001fec82350  x10: 0x00000000000003e8  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001943f8494  x14: 0x0000000000000001  x15: 0xffffffffb00007ff
   x16: 0x0000000000000030  x17: 0x00000002006ea440  x18: 0x0000000000000000  x19: 0x000000016fbf7000
   x20: 0x000000016fbf22f0  x21: 0x000000016fbf23a0  x22: 0x00000001fc7a4000  x23: 0x00000001095ed558
   x24: 0x0000000000000000  x25: 0x0000000000000000  x26: 0x0000000000000000  x27: 0x0000000000000000
   x28: 0x000000020c35a950   fp: 0x000000016fbf2310   lr: 0x000000019bf84380
    sp: 0x000000016fbf22e0   pc: 0x000000019bf84380 cpsr: 0x40001000
   esr: 0xf2000001 (Breakpoint) brk 1


Binary Images:
        0x100c68000 -         0x101a47fff 发型测试 arm64  <4d8a6f2a416e3e7d8b1789c20ba835df> /var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/发型测试
        0x101e34000 -         0x101e3ffff libobjc-trampolines.dylib arm64e  <9136d8ba22ff3f129caddfc4c6dc51de> /private/preboot/Cryptexes/OS/usr/lib/libobjc-trampolines.dylib
        0x102044000 -         0x10204ffff TTSDKReachability arm64  <6277d61f35993e26bac541ae15c4fe40> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/TTSDKReachability.framework/TTSDKReachability
        0x102070000 -         0x10207ffff FBLPromises arm64  <0f600f055e203461a33acc9580f9ae26> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/FBLPromises.framework/FBLPromises
        0x10209c000 -         0x1020abfff Masonry arm64  <3505d9b1765f3d4f92adedf29b63365d> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/Masonry.framework/Masonry
        0x1020c4000 -         0x1020cffff Reachability arm64  <5d095ae5a5fc311991e90f3b9ddfef7d> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/Reachability.framework/Reachability
        0x1020f0000 -         0x102127fff BSImagePicker arm64  <ba54dcaa0a7a38738b9418900f2ba204> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/BSImagePicker.framework/BSImagePicker
        0x10219c000 -         0x1021abfff TTSDKStrategyLite arm64  <7a312a4bc9f4310391786763fc4128b2> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/TTSDKStrategyLite.framework/TTSDKStrategyLite
        0x1021d0000 -         0x1021e3fff Promises arm64  <c2bc955f550b344c884bde0bf095c301> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/Promises.framework/Promises
        0x102204000 -         0x102217fff SVProgressHUD arm64  <0cf8553e31a730e4b90c99afe74e36a4> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/SVProgressHUD.framework/SVProgressHUD
        0x102234000 -         0x10223ffff TTSDKTTFFmpegLiveLite arm64  <915f7b581ce1314ca45bf4c55a8f66a9> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/TTSDKTTFFmpegLiveLite.framework/TTSDKTTFFmpegLiveLite
        0x102288000 -         0x10229ffff SnapKit arm64  <cc7a2ac98d2035a88a4e1096f3790407> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/SnapKit.framework/SnapKit
        0x10230c000 -         0x102433fff Alamofire arm64  <8f56a39e73913d0e87222bb86117ed26> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/Alamofire.framework/Alamofire
        0x1025d8000 -         0x102617fff JXSegmentedView arm64  <66f0af0eec293520b20d4eb7060a4f5d> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/JXSegmentedView.framework/JXSegmentedView
        0x1026bc000 -         0x10270ffff SDWebImage arm64  <f863348cb05d31e29252de7cfb05629b> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/SDWebImage.framework/SDWebImage
        0x102794000 -         0x1027b3fff SwiftyJSON arm64  <7bf8ffaf730332df936faf021e96c092> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/SwiftyJSON.framework/SwiftyJSON
        0x102814000 -         0x10283ffff Starscream arm64  <6f69efb81d753e48a06ce227a15604e8> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/Starscream.framework/Starscream
        0x10292c000 -         0x10295bfff TTSDKCore arm64  <c2b6c8e330c63e0f8df03a447371540a> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/TTSDKCore.framework/TTSDKCore
        0x1029c4000 -         0x102a27fff TTSDKTools arm64  <a63dbac1cc2836818cf8e5570e616cb0> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/TTSDKTools.framework/TTSDKTools
        0x102a5c000 -         0x102a87fff ttboringssl arm64  <fec2624d5c433a7a8958c6054dbae9ad> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/ttboringssl.framework/ttboringssl
        0x102ad8000 -         0x102ccffff TTFFmpeg arm64  <34b943cbd49531a5a387b07c7417a14e> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/TTFFmpeg.framework/TTFFmpeg
        0x102f64000 -         0x102fcffff ttcrypto arm64  <********************************> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/ttcrypto.framework/ttcrypto
        0x103050000 -         0x1030e3fff TTSDKLiveBase arm64  <782e91c627be37489b0e0bca8a8f7690> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/TTSDKLiveBase.framework/TTSDKLiveBase
        0x10348c000 -         0x1035bbfff TTSDKLivePlayerLite arm64  <db0c614959523f03bca27486c0666da8> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/TTSDKLivePlayerLite.framework/TTSDKLivePlayerLite
        0x103a54000 -         0x103c13fff TTSDKPlayerCoreLiveLite arm64  <48580ac295bd3944a75c0e00a1939fa2> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/TTSDKPlayerCoreLiveLite.framework/TTSDKPlayerCoreLiveLite
        0x103ef4000 -         0x1051cffff FURenderKit arm64  <91ae2cdf3a683955893e1fd1d96bd766> /private/var/containers/Bundle/Application/35673CD8-496D-46D8-B6F6-78B3714A1949/发型测试.app/Frameworks/FURenderKit.framework/FURenderKit
        0x1914c0000 -         0x191511bb3 libobjc.A.dylib arm64e  <ed7c5fc7ddc734249c44db56f51b8be2> /usr/lib/libobjc.A.dylib
        0x192bb7000 -         0x19382addf Foundation arm64e  <34de055d8683380a9198c3347211d13d> /System/Library/Frameworks/Foundation.framework/Foundation
        0x193f3d000 -         0x1944b9fff CoreFoundation arm64e  <7821f73c378b3a10be90ef526b7dba93> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
        0x1954ce000 -         0x195893b9f CFNetwork arm64e  <a35a109c49d23986965d4ed7e0b6681e> /System/Library/Frameworks/CFNetwork.framework/CFNetwork
        0x195ab5000 -         0x195e6f69f QuartzCore arm64e  <109010da3c353e22b001939786412ee2> /System/Library/Frameworks/QuartzCore.framework/QuartzCore
        0x19682c000 -         0x19876db5f UIKitCore arm64e  <96636f64106f30c8a78082dcebb0f443> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
        0x19bf0d000 -         0x19bf8c8ef libsystem_c.dylib arm64e  <93f93d7c245f3395822dec61ffae79cf> /usr/lib/system/libsystem_c.dylib
        0x1a1893000 -         0x1a1cb0a9f CoreMotion arm64e  <cec80db7b3f23b179d4ebcfeb020ca2d> /System/Library/Frameworks/CoreMotion.framework/CoreMotion
        0x1ab812000 -         0x1ad24d75f JavaScriptCore arm64e  <e32426af64113260be0bbe0ad12e23c8> /System/Library/Frameworks/JavaScriptCore.framework/JavaScriptCore
        0x1b84c3000 -         0x1b850bc3f CoreAutoLayout arm64e  <b850e010e4023e07aaa549f71cccc7fc> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/CoreAutoLayout
        0x1bade5000 -         0x1bae7f857 dyld arm64e  <86d5253d4fd136f3b4ab25982c90cbf4> /usr/lib/dyld
        0x1e112c000 -         0x1e1134c7f GraphicsServices arm64e  <5ba62c226d3731999dfd0e0f7abebfa9> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
        0x1e515b000 -         0x1e5194ebf libsystem_kernel.dylib arm64e  <9e195be11733345ea9bf50d0d7059647> /usr/lib/system/libsystem_kernel.dylib
        0x21e567000 -         0x21e584fff libc++abi.dylib arm64e  <a360ea66d985389394b96bba7bd8a6df> /usr/lib/libc++abi.dylib
        0x21e64c000 -         0x21e6583f3 libsystem_pthread.dylib arm64e  <b37430d8e3af33e481e1faed9ee26e8a> /usr/lib/system/libsystem_pthread.dylib

EOF
