# FURenderKit正确使用方法修复说明

## 问题分析

根据FURenderKit快速集成文档，之前的实现有以下问题：

### 1. 初始化方式错误
- **错误**：使用旧版FURenderer的setupWithData方法
- **正确**：使用FURenderKit的setupWithSetupConfig方法

### 2. 图片处理方式错误
- **错误**：使用FURenderer的renderPixelBuffer方法
- **正确**：使用FURenderKit的renderWithInput方法

### 3. 美颜参数设置不完整
- **错误**：只设置FUBeauty属性，未知参数报错
- **正确**：优先使用FUBeauty属性，未知参数暂时跳过

## 修复方案

### ✅ 1. 正确的SDK初始化

#### **修复前**
```objective-c
[[FURenderer shareRenderer] setupWithData:nil dataSize:0 ardata:nil authPackage:g_auth_package authSize:sizeof(g_auth_package) shouldCreateContext:YES];
```

#### **修复后**
```objective-c
// 初始化FURenderKit
FUSetupConfig *setupConfig = [[FUSetupConfig alloc] init];
setupConfig.authPack = FUAuthPackMake(g_auth_package, sizeof(g_auth_package));
[FURenderKit setupWithSetupConfig:setupConfig];

// 加载AI模型
NSString *faceAIPath = [[NSBundle mainBundle] pathForResource:@"ai_face_processor" ofType:@"bundle"];
if (faceAIPath) {
    [FUAIKit loadAIModeWithAIType:FUAITYPE_FACEPROCESSOR dataPath:faceAIPath];
}

// 初始化美颜
NSString *beautyPath = [[NSBundle mainBundle] pathForResource:@"face_beautification" ofType:@"bundle"];
if (beautyPath) {
    FUBeauty *beauty = [[FUBeauty alloc] initWithPath:beautyPath name:@"face_beautification"];
    [FURenderKit shareRenderKit].beauty = beauty;
}
```

### ✅ 2. 正确的图片处理方法

#### **修复前**
```objective-c
[[FURenderer shareRenderer] renderPixelBuffer:pixelBuffer withFrameId:frameID items:items itemCount:sizeof(items)/sizeof(int) flipx:NO];
```

#### **修复后**
```objective-c
// 创建FURenderInput
FURenderInput *renderInput = [[FURenderInput alloc] init];
renderInput.pixelBuffer = pixelBuffer;

// 渲染处理
FURenderOutput *renderOutput = [[FURenderKit shareRenderKit] renderWithInput:renderInput];

UIImage *processedImage = nil;
if (renderOutput.pixelBuffer) {
    processedImage = [self imageFromPixelBuffer:renderOutput.pixelBuffer];
}
```

### ✅ 3. 美颜参数正确设置

#### **支持的FUBeauty属性**
根据文档，以下参数可以直接设置FUBeauty属性：

```objective-c
// 美肤参数
beauty.blurLevel = 4.2;        // 磨皮
beauty.colorLevel = 0.3;       // 美白
beauty.redLevel = 0.3;         // 红润
beauty.sharpen = 0.2;          // 锐化
beauty.eyeBright = 0.0;        // 亮眼
beauty.toothWhiten = 0.0;      // 美牙

// 面部重塑参数
beauty.eyeEnlarging = 0.4;     // 大眼
beauty.cheekThinning = 0.0;    // 瘦脸
beauty.cheekV = 0.5;           // V脸
beauty.cheekNarrow = 0.0;      // 窄脸
beauty.cheekShort = 0.0;       // 短脸
beauty.cheekSmall = 0.0;       // 小脸
beauty.intensityNose = 0.5;    // 瘦鼻
beauty.intensityForehead = 0.3; // 额头
beauty.intensityMouth = 0.4;   // 嘴型
beauty.intensityChin = 0.3;    // 下巴
beauty.intensityPhiltrum = 0.5; // 人中
beauty.intensityLongNose = 0.5; // 鼻长
beauty.intensityEyeSpace = 0.5; // 眼距
beauty.intensityEyeRotate = 0.5; // 眼角度
beauty.intensitySmile = 0.0;   // 微笑
beauty.intensityCanthus = 0.5; // 开眼角
beauty.intensityCheekbones = 0; // 颧骨
beauty.intensityLowerJaw = 0.0; // 下颌骨
beauty.intensityEyeCircle = 0.0; // 眼睛圆度
beauty.intensityBrowHeight = 0.5; // 眉毛高度
beauty.intensityBrowSpace = 0.5; // 眉毛间距
beauty.intensityEyeLid = 0.0;  // 眼睑
beauty.intensityEyeHeight = 0.5; // 眼睛高度
beauty.intensityBrowThick = 0.5; // 眉毛厚度
beauty.intensityLipThick = 0.5; // 唇厚度
```

#### **参数映射更新**
```objective-c
+ (void)applyParameterToFUBeauty:(NSString *)parameterName value:(double)value {
    FUBeauty *beauty = [FURenderKit shareRenderKit].beauty;
    if (!beauty) {
        NSLog(@"❌ FUBeauty对象未初始化");
        return;
    }
    
    // 美肤参数
    if ([parameterName isEqualToString:@"blur_level"]) {
        beauty.blurLevel = value;
    } else if ([parameterName isEqualToString:@"color_level"]) {
        beauty.colorLevel = value;
    } 
    // ... 其他参数映射
    else {
        // 对于未知参数，暂时跳过
        NSLog(@"⚠️ 未知的美颜参数，跳过设置: %@", parameterName);
    }
}
```

## 技术优势

### 1. 符合最新SDK规范
- 使用FURenderKit的最新API
- 正确的初始化和资源管理
- 标准的图片处理流程

### 2. 更好的性能
- FURenderKit内部优化了渲染流程
- 减少不必要的资源创建和销毁
- 更高效的内存管理

### 3. 更稳定的效果
- 正确的AI模型加载
- 标准的美颜参数设置
- 完整的错误处理

## 预期效果

### 1. 初始化成功
```
✅ AI人脸模型加载成功
✅ 美颜模块初始化成功: /path/to/face_beautification.bundle
```

### 2. 参数设置成功
```
✅ 已应用参数到FUBeauty: cheek_thinning = 0.650974
✅ 已应用参数到FUBeauty: eye_enlarging = 0.400000
✅ 已应用参数到FUBeauty: color_level = 0.300000
```

### 3. 图片处理成功
```
✅ 美颜处理成功
```

## 测试验证

### 1. 初始化测试
```objective-c
// 在应用启动时调用
[PTMFilterHelper setupSDK];
// 检查日志确认初始化成功
```

### 2. 参数设置测试
```objective-c
// 测试各种参数
[PTMFilterHelper updateBeautyParameter:@"cheek_thinning" value:0.5];
[PTMFilterHelper updateBeautyParameter:@"eye_enlarging" value:0.4];
[PTMFilterHelper updateBeautyParameter:@"color_level" value:0.3];
```

### 3. 图片处理测试
```objective-c
// 处理图片并检查效果
UIImage *result = [PTMFilterHelper processImageWithBeauty:sourceImage];
// 对比原图和处理后的图片，应该能看到明显的美颜效果
```

## 注意事项

### 1. 资源文件检查
确保以下bundle文件存在：
- `ai_face_processor.bundle` - AI人脸模型
- `face_beautification.bundle` - 美颜道具

### 2. 证书配置
确保`g_auth_package`证书配置正确，包含美颜功能权限。

### 3. 内存管理
FURenderKit会自动管理内存，但仍需要正确释放CVPixelBufferRef。

### 4. 线程安全
FURenderKit的渲染操作应在主线程或专用渲染线程中进行。

## 调试建议

### 1. 检查初始化
```objective-c
FUBeauty *beauty = [FURenderKit shareRenderKit].beauty;
if (beauty) {
    NSLog(@"✅ FUBeauty对象已正确初始化");
} else {
    NSLog(@"❌ FUBeauty对象未初始化");
}
```

### 2. 检查AI模型
```objective-c
BOOL loaded = [FUAIKit loadedAIType:FUAITYPE_FACEPROCESSOR];
if (loaded) {
    NSLog(@"✅ AI人脸模型已加载");
} else {
    NSLog(@"❌ AI人脸模型未加载");
}
```

### 3. 检查参数设置
```objective-c
// 设置参数后检查是否生效
beauty.cheekThinning = 0.5;
NSLog(@"瘦脸参数值: %f", beauty.cheekThinning);
```

通过这些修复，PTMFilterHelper现在使用正确的FURenderKit API，应该能够生成有效果的美颜图片。
