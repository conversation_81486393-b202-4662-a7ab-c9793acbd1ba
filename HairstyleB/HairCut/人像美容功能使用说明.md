# 人像美容功能使用说明

## 功能概述

人像美容功能提供了完整的人像美化解决方案，包括美肤、面部重塑和发型编辑等功能。用户可以通过简单的操作对人像照片进行专业级的美化处理。

## 页面结构

### 1. 人像美容入口页面 (PortraitBeautyVC)

**页面设计**
- 背景颜色：#F9F9F9
- 顶部白色导航栏：包含返回按钮和保存按钮
- 中间图片显示区域：展示当前处理的图片
- 底部悬浮白色工具栏：包含三个功能按钮

**UI布局**
- 顶部导航栏高度：44pt
- 保存按钮：黄色背景(#FFEC53)，圆角15pt
- 底部工具栏：左右边距16pt，底部距离安全区域16pt，高度87pt
- 三个功能按钮：使用ZYEButtonPositionAndSpace扩展，图片在上文字在下

**功能按钮**
1. **美肤** - 跳转到美肤编辑页面
2. **面部重塑** - 跳转到面部重塑页面（开发中）
3. **发型编辑** - 跳转到发型编辑页面（开发中）

### 2. 美肤编辑页面 (SkinBeautyEditVC)

**页面设计**
- 背景颜色：#F9F9F9
- 顶部白色导航栏：返回和保存按钮
- 图片显示区域：正方形显示，支持实时预览
- 底部参数调节区域：滚动视图包含所有美肤参数

**美肤参数**
- **磨皮** (blur_level): 0.0-6.0，默认6.0
- **美白** (color_level): 0.0-1.0，默认0.0
- **红润** (red_level): 0.0-1.0，默认0.0
- **法令纹** (remove_nasolabial_folds_strength): 0.0-1.0，默认0.0
- **亮眼** (eye_bright): 0.0-1.0，默认0.0
- **黑眼圈** (remove_pouch_strength): 0.0-1.0，默认0.0
- **美牙** (tooth_whiten): 0.0-1.0，默认0.0

**交互方式**
- 滑块调节：实时调节参数值
- 实时预览：参数变化时立即更新图片效果
- 参数保存：自动保存用户调节的参数值

## 技术实现

### 1. 美颜引擎
- 基于FaceUnity SDK的PTMFilterHelper
- 支持31个美颜参数的实时调节
- 纯Objective-C实现，避免混编问题

### 2. 配置管理
- JSON配置文件：beauty.json（项目内置默认配置）
- 用户配置：沙盒存储，支持参数持久化
- BeautyConfigManager：统一管理配置加载和保存

### 3. UI组件
- ZYEButtonPositionAndSpace：按钮图片文字布局
- SnapKit：自动布局约束
- 自定义滑块：支持主题色和实时反馈

## 使用流程

### 1. 从首页进入
```swift
// 在HomeVC中
func portraitBeautyClickAction() {
    MobClick.event("HOME", attributes: ["source": "人像美容"])
    self.showPortraitBeautyActionSheet() // 弹出选择拍照或相册的ActionSheet
}
```

### 2. 选择图片来源
用户可以选择：
- **拍照**：使用相机拍摄新照片
- **相册**：从相册中选择已有照片
- **取消**：取消操作

选择完成后，系统会自动跳转到人像美容页面，并传入选择的图片。

### 3. 进入美肤编辑
```swift
// 在PortraitBeautyVC中
@objc private func skinButtonTapped() {
    let skinBeautyEditVC = SkinBeautyEditVC()
    skinBeautyEditVC.sourceImage = currentImage // 传递当前图片
    skinBeautyEditVC.hidesBottomBarWhenPushed = true
    self.navigationController?.pushViewController(skinBeautyEditVC as UIViewController, animated: true)
}
```

### 4. 美颜参数调节
```swift
// 滑块值变化时
@objc private func sliderValueChanged(_ slider: UISlider) {
    let parameter = beautyParameters[slider.tag]
    let newValue = Double(slider.value)
    
    // 更新参数
    PTMFilterHelper.updateBeautyParameter(parameter.name, value: newValue)
    
    // 应用效果
    applyBeautyEffect()
}
```

### 5. 图片处理
```swift
private func applyBeautyEffect() {
    guard let originalImage = originalImage else { return }
    
    DispatchQueue.global(qos: .userInitiated).async { [weak self] in
        // 应用美颜效果
        let processedImage = PTMFilterHelper.processImageWithBeauty(originalImage)
        
        DispatchQueue.main.async {
            self?.currentImage = processedImage ?? originalImage
            self?.imageView.image = self?.currentImage
        }
    }
}
```

## 资源文件

### 图片资源
- `美肤.imageset` - 美肤按钮图标
- `面部重塑.imageset` - 面部重塑按钮图标
- `发型编辑.imageset` - 发型编辑按钮图标
- `返回.imageset` - 返回按钮图标

### 配置文件
- `beauty.json` - 美颜参数默认配置
- `user_beauty_config.json` - 用户自定义配置（沙盒）

## 测试

### 功能测试
```swift
// 运行完整测试
PortraitBeautyFlowTest.testCompleteFlow()

// 测试页面跳转
PortraitBeautyFlowTest.runTestsInViewController(self)
```

### 测试内容
- ViewController创建和属性设置
- 美颜参数加载和配置
- 图片处理和效果应用
- UI组件创建和布局

## 注意事项

### 1. 性能优化
- 图片处理在后台线程进行
- 大图片自动缩放到2048像素以内
- 参数调节时使用防抖机制

### 2. 内存管理
- 及时释放处理后的图片
- 避免循环引用
- 合理使用weak引用

### 3. 用户体验
- 实时预览效果
- 流畅的动画过渡
- 直观的参数调节界面

### 4. 错误处理
- 图片加载失败的降级处理
- 美颜SDK初始化失败的提示
- 参数保存失败的重试机制

## 扩展功能

### 1. 面部重塑
- 脸型调整：v脸、瘦脸、小脸等
- 五官调整：大眼、瘦鼻、嘴型等
- 实时预览和参数保存

### 2. 发型编辑
- 发型更换
- 发色调整
- 发型匹配推荐

### 3. 滤镜效果
- 内置滤镜库
- 自定义滤镜强度
- 滤镜组合效果

## 开发计划

1. **Phase 1** ✅ - 基础框架和美肤功能
2. **Phase 2** - 面部重塑功能完善
3. **Phase 3** - 发型编辑功能集成
4. **Phase 4** - 滤镜系统和高级功能
