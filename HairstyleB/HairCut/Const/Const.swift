//
//  HttpConst.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/7/29.
//

import Foundation

struct HttpConst {
    public static let authLink = "https://ukey.godimage.mobi:8886/authkey/query"
    public static let privacyAgreementLink = "https://spc283igik.feishu.cn/docx/Ddw2df39eo3GiaxQ1g7cu7tfnKj?from=from_copylink"
    public static let termOfUseLink = "https://www.freeprivacypolicy.com/live/************************************"
    
    
    public static let superBaseWeb = "dl.biggerlens.cn:9132"
    public static let hairCutUploadImage = "https://\(superBaseWeb)/upload/image"
    public static let hairCutStartQueen = "https://\(superBaseWeb)/prompt"
    public static let hairCutStatus = "wss://\(superBaseWeb)/ws"
    public static let hairCutGetResult = "https://\(superBaseWeb)/history/"
    
    
    public static let badiduToken = "https://aip.baidubce.com/oauth/2.0/token"
    public static let faceTestV3 = "https://aip.baidubce.com/rest/2.0/face/v3/detect"
    public static let faceTestResultDomain = "https://qz-hairstyle.oss-cn-guangzhou.aliyuncs.com/"
}

struct BaiduTokenConst {
    public static let grantType = "client_credentials"
    public static let apiKey = "PN9aYC5Az8zhu9qyIHoLFEm4"
    public static let secrectKey = "Xtz9nOVV0T58mjHZ937nsC5c3B1vNckK"
}

struct FaceTestConst {
    public static let eyeDistance = 255.0
    public static let hairWidth = 1701.0
    public static let hairHeight = 2268.0
    public static let eyeProportionY = 903.0
    public static let cellHeight = 110.0
}

struct UserDefaultsConst {
    public static let firstUse = "firstUse"
    public static let firstInternetRequest = "firstInternetRequest"
}

struct HDColorPickerConst {
    public static let colorPickerWidth = 257.0
    public static let colorPickerHeight = 209.0
    public static let colorPickerGradientWidth = 235.0
    public static let colorPickerGradientHeight = 110.0
    public static let colorPickerGradientBtnWidth = 20.0
    public static let colorPickerSliderWidth = 235.0
    public static let colorPickerSliderHeight = 8.0
    public static let colorPickerSliderBtnWidth = 20.0
    public static let colopPickerColorHexData = "colorHexData"
}

struct AppSetting {
    public static let appIconName = "icon4"
}

struct AdsCNSetting {
    public static let appId = "5651546" //应用ID
    public static let bannerId = "963248418" //banner广告ID
    public static let bannerHeight = 75.0 //banner高度
    public static let insertFullViewId = "963248420" //新插屏广告ID
    public static let rewardId = "964342868"
}
