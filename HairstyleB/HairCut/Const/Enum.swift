//
//  Enum.swift
//  HairCut
//
//  Created by Bigger on 2024/7/31.
//

import Foundation

// MARK: - 订阅枚举
enum SubscribePlan {
    case month //连续包月
    case year //连续包年
    case forever //永久
}

// MARK: - 外部打开URL枚举
enum UrlType {
    case termsOfUse //使用条款
    case privacyAgreement //隐私政策
}

// MARK: - 脸形
enum FaceShape: String {
    case noChoose = "" //没有选择
    case oval = "oval" //鹅蛋脸
    case heart = "heart" //心形脸
    case triangle = "triangle" //菱形脸
    case round = "round" //圆形
    case square = "square" //方形脸
}

//// MARK: - 性别
enum Gender: Int {
    case none = -1
    case Female = 0
    case Male
}

// MARK: - 发色
enum HairColor: String {
    case black = "black" //黑色
    case brown = "brown" //棕色
    case blue = "blue" //蓝色
    case green = "green" //绿色
    case purple = "purple" //紫色
    case red = "red" //红色
    case white = "white" //白色
    case silver = "silver" //银色
    case grey = "grey" //灰色
    case blonde = "blonde" //金色
}

// MARK: - 男性刘海


// MARK: - 男性长度
enum MaleHairLength: String {
    case long = "( long hair:1.2)" //长发
    case short = "(very short hair:1.2)" //短发
}
    
// MARK: - 男性发型
enum MaleHairType: String {
    case buzzCut = "buzz cut" //短寸头
    case combOver = "((comb over:1.2))" //油头
    case spiked = "spiked hair" //刺猬头
    case dreadlocks = "dreadlocks" //脏辫
    case ringlets = "ringlets" //小卷发
    case longCurly = "Long Curly" //卷发
    case afr = "Afr" //鸟窝头
    case manBun = "Man Bun" //男士发髻
    case Dreadlocks = "Dreadlocks" //长发绺
    case quiffWithSidePart = "Quiff With Side Part" //侧分
    case pompadour = "pompadour" //蓬帕杜发型
}

// MARK: - 女性发型
enum FeMaleHairType: String {
    case wavy = "(wavy hair:1.2)" //波浪发型发
    case ringlets = "(ringlets:1.2)" //小卷发
    case dreadlocks = "(dreadlocks:1.2)" //脏辫
    case twinBraids = "(twin braids:1.2)" //双麻花辫
    case twintails = "(twintails:1.2)" //双马尾
    case ponytail = "(ponytail:1.2)" //马尾
    case himeCut = "((hime cut:1.2))" //公主切
    case doubleBun = "(double bun:1.2)" //双丸子头
    case hairBun = "(hair bun:1.2)" //丸子头
    case crownBraid = "(crown braid:1.2)" //法式冠编发
    case bobcut = "(bobcut:1.2)" //波波头
    case halfUpdo = "(half updo:1.2)" //半盘发型
    case frenchBraid = "(french braid:1.2)" //法式辫
    case straightHair = "straight hair" //长直发
}

// MARK: - 女性刘海


// MARK: - 女性长度
enum FeMaleHairLength: String {
    case long = "(long hair:1.2)" //长发
    case short = "((very short hair:1.2))" //短发
}

// MARK: - 男性热门发型模板
enum MalePopularTemplate: String {
    /// 羊毛卷
    case WoolRoll = "classic,highres,an extremely delicate and beautiful,ultra-detailed,best quality,realistic, high quality, change one's hairstyle,cloud hair,((curly hair:1.2)),messy hair,white hair,no face:1.5, no body:1.5, hair only:1.5, close-up of hair:1.4, hair details:1.4, no person:1.5, sharp edges, clean outlines, smooth gradients, soft background blur, minimalist background, pastel tones, studio lighting, high-quality, ultra-sharp focus, no blur, no distortion, no artifacts, no noise, perfect composition, professional photo"
    /// 纹理烫
    case GrainPressing = "(men's layered hairstyle:1.5), (textured hair:1.4), (voluminous hair:1.3), (soft waves:1.2), modern look, stylish look, (natural texture:1.3), elegant style, masculine style, highly detailed, realistic hair rendering, no face:1.5, no body:1.5, hair only:1.5, close-up of hair:1.4, hair details:1.4, no person:1.5, sharp edges, clean outlines, smooth gradients, soft background blur, minimalist background, pastel tones, studio lighting, high-quality, ultra-sharp focus, no blur, no distortion, no artifacts, no noise, perfect composition, professional photo"
    ///  爆炸头
    case ExplosiveHead = "classic,highres,an extremely delicate and beautiful,ultra-detailed,best quality,realistic, high quality, change one's hairstyle,((afro:1.2)),no face:1.5, no body:1.5, hair only:1.5, close-up of hair:1.4, hair details:1.4, no person:1.5, sharp edges, clean outlines, smooth gradients, soft background blur, minimalist background, pastel tones, studio lighting, high-quality, ultra-sharp focus, no blur, no distortion, no artifacts, no noise, perfect composition, professional photo"
}

// MARK: - 女性热门发型模板
enum FeMalePopularTemplate: String {
    /// 洛丽塔发型
    case Lolita = "change one's hairstyle,very long hair,(lolita fashion),black hair,crown braid,((lolita hairband)),(hair flower),(hair bow), no face:1.5, no body:1.5, hair only:1.5, close-up of hair:1.4, hair details:1.4, no person:1.5, sharp edges, clean outlines, smooth gradients, soft background blur, minimalist background, pastel tones, studio lighting, high-quality, ultra-sharp focus, no blur, no distortion, no artifacts, no noise, perfect composition, professional photo"
    /// 灰色渐变卷发
    case GreyGradientCurl = "change one's hairstyle,very long hair,((grey hair:1.2)),((gradient hair:1.2)),((wavy hair:1.2)),(curly hair)，parted bangs,no face:1.5, no body:1.5, hair only:1.5, close-up of hair:1.4, hair details:1.4, no person:1.5, sharp edges, clean outlines, smooth gradients, soft background blur, minimalist background, pastel tones, studio lighting, high-quality, ultra-sharp focus, no blur, no distortion, no artifacts, no noise, perfect composition, professional photo"
    ///  挑染编织发
    case BraidedWithHighlights = "change one's hairstyle,very long hair,((streaked hair:1.2)),((crown braid)),no face:1.5, no body:1.5, hair only:1.5, close-up of hair:1.4, hair details:1.4, no person:1.5, sharp edges, clean outlines, smooth gradients, soft background blur, minimalist background, pastel tones, studio lighting, high-quality, ultra-sharp focus, no blur, no distortion, no artifacts, no noise, perfect composition, professional photo"
}
// MARK: - 脸形测试性别
enum FaceTestGender: String {
    case none = ""
    case male = "male"
    case female = "female"
}

// MARK: - 脸形测试眼镜
enum FaceTestGlasses: String {
    case none = "none" //无眼镜
    case common = "common" //普通眼镜
    case sun = "sun" //墨镜
}

// MARK: - 脸形测试表情
enum FaceTestExpression: String {
    case none = "none" //不笑
    case smile = "smile" //微笑
    case laugh = "laugh" //大笑
}

// MARK: - 语言
enum Language: String {
    case english = "en"
    case chineseSimplified = "zh-Hans"
    case chineseTranditional = "zh-Hant"
    case vietnamese = "vi"
}
