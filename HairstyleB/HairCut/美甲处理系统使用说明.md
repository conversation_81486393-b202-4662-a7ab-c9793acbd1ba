# 美甲处理系统使用说明

## 🎨 系统概述

本美甲处理系统集成了火山引擎图生图API，提供完整的美甲样式选择和处理功能。用户可以上传手部照片，选择喜欢的美甲样式，系统会自动生成美甲效果图。

## 🔧 核心组件

### 1. VolcanoEngineAPI.swift
火山引擎API集成工具类
- **功能**: 调用火山引擎图生图API
- **配置**: 
  - Access Key ID: `AKLTY2U1NWMwNmU2M2I3NDdmMzg5MjRhM2M1YTI5N2ZjYzk`
  - Secret Access Key: `TjJGall6bGxOV1U0TXpNeE5ESXdPR0UzWXpJNVpXVXhNMkV5TkRSak5qSQ==`
  - Scale: 1.0

```swift
// 使用示例
VolcanoEngineAPI.processNailWithModel(
    image: userImage,
    nailModel: selectedNailModel
) { result in
    switch result {
    case .success(let processedImage):
        // 处理成功
    case .failure(let error):
        // 处理失败
    }
}
```

### 2. NailProcessViewController.swift
美甲处理主界面
- **功能**: 显示用户图片和美甲样式选择
- **特性**: 
  - 横向滚动的美甲样式选择
  - 实时预览选中状态
  - 处理进度显示
  - 结果展示和保存

### 3. NailProcessHelper.swift
美甲处理助手类
- **功能**: 提供便捷的处理方法
- **特性**:
  - 图片验证和预处理
  - 批量处理支持
  - 快速处理模式

### 4. NailModel.swift & NailDataManager.swift
数据模型和管理
- **数据源**: `https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/nailsmodel.json`
- **功能**: 美甲样式数据获取、缓存、筛选

## 🚀 使用方法

### 基础使用

```swift
// 1. 启动美甲处理界面
let image = UIImage(named: "user_hand")!
self.startNailProcess(with: image)

// 2. 快速处理（使用默认样式）
self.quickNailProcess(with: image)

// 3. 直接API调用
NailProcessHelper.processNailDirectly(
    image: image,
    nailModel: nailModel
) { result in
    // 处理结果
}
```

### 集成到相机功能

```swift
// 在相机拍照完成后
func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
    guard let image = UIImage(data: photo.fileDataRepresentation()!) else { return }
    
    // 显示选择：直接使用或美甲处理
    showImageActionSheet(with: image)
}
```

### 批量处理

```swift
NailProcessHelper.batchProcessNails(
    image: userImage,
    nailModels: selectedModels,
    progressCallback: { current, total in
        print("进度: \(current)/\(total)")
    }
) { results, errors in
    print("完成: \(results.count) 成功, \(errors.count) 失败")
}
```

## 📱 界面流程

1. **图片选择/拍摄**
   - 从相册选择
   - 相机拍摄
   - 图片验证和预处理

2. **美甲样式选择**
   - 横向滚动展示所有样式
   - 支持VIP标识
   - 实时选中状态反馈

3. **处理执行**
   - 显示处理进度
   - 调用火山引擎API
   - 错误处理和重试

4. **结果展示**
   - 原图和处理结果对比
   - 保存到相册功能
   - 分享功能（可扩展）

## 🔧 API参数说明

### 火山引擎请求参数
```json
{
    "req_key": "img2img_anime",
    "binary_data_base64": ["base64编码的图片"],
    "prompt": "美甲样式描述",
    "scale": 1.0,
    "strength": 0.8,
    "steps": 20,
    "return_url": false
}
```

### 美甲数据格式
```json
{
    "id": "2",
    "image": "样式预览图URL",
    "name": "大理石纹美甲",
    "isVip": false,
    "type": 1,
    "sex": 0,
    "prompt": "半透明裸色底油上晕染着优雅的灰白色大理石纹理...",
    "name_en": "Marble Nail Art"
}
```

## 🧪 测试和调试

### 运行测试
```swift
// 测试API连接
TestNailAPI.runAllTests()

// 测试数据解析
NailModelTest.runAllTests()

// 演示完整流程
let demoVC = NailProcessDemoViewController()
present(demoVC, animated: true)
```

### 调试信息
- 网络请求日志
- 图片处理状态
- API响应解析
- 错误详细信息

## ⚠️ 注意事项

### 图片要求
- **尺寸**: 200x200 到 4096x4096 像素
- **格式**: JPEG, PNG
- **内容**: 清晰的手部照片
- **大小**: 建议小于5MB

### API限制
- **并发**: 建议不超过5个并发请求
- **频率**: 每分钟不超过60次请求
- **超时**: 请求超时时间15秒

### 性能优化
- 图片自动压缩和缩放
- SDWebImage缓存美甲样式图片
- 异步处理避免UI阻塞

## 🔮 扩展功能

### 已实现
- ✅ 基础美甲处理
- ✅ 样式选择界面
- ✅ 图片预处理
- ✅ 结果保存

### 可扩展
- 🔄 实时预览（AR效果）
- 🔄 自定义美甲设计
- 🔄 社交分享功能
- 🔄 用户收藏和历史
- 🔄 美甲教程集成

## 📞 技术支持

### 常见问题
1. **API调用失败**: 检查网络连接和密钥配置
2. **图片处理慢**: 确保图片尺寸合适
3. **样式加载失败**: 检查数据源URL可访问性

### 错误代码
- `10000`: 成功
- `10001`: 参数错误
- `10002`: 图片格式不支持
- `10003`: 处理超时

### 联系方式
- 开发团队: [联系信息]
- 技术文档: [文档链接]
- 问题反馈: [反馈渠道]

---

*最后更新: 2025-07-14*
