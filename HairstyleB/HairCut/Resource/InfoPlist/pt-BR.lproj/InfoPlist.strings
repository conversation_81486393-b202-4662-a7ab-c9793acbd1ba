/* 
  InfoPlist.strings
  HairCut

  Created by <PERSON> on 2024/8/4.
  
*/
NSPhotoLibraryAddUsageDescription = "App needs your permission to access albums to add hairstyles to photos and save editing results";
NSPhotoLibraryUsageDescription = "To save and edit files, the app needs your permission to access the album
";
NSCameraUsageDescription = "App needs your permission to access the camera so that you can take photos to use our hair test feature";
CFBundleDisplayName = "Cabelo Curto";
NSUserTrackingUsageDescription = "Need to get the ad identifier for your device in order to provide a better ad service";
