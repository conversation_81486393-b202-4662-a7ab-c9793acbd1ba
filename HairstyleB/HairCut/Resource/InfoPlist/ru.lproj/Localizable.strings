"detection_results" = "Detection Results";
"attractiveness_rating" = "Attractiveness Rating:";
"gender" = "Gender:";
"age" = "Age:";
"expression" = "Expression:";
"glassess" = "Glasses:";
"your_face_shape_is" = "Your Face Shape is:";
"male" = "Male";
"female" = "Female";
"not_smiling" = "Not Smiling";
"laughing" = "Laughing";
"smiling" = "Smiling";
"no_glassess" = "No Glasses";
"sunglassess" = "Sunglasses";
"regular_glassess" = "Regular Glasses";
"oval" = "Oval";
"heart" = "Heart";
"diamond" = "Diamond";
"round" = "Round";
"square" = "Square";
"upload_image" = "Upload Image";
"custom_parameters" = "Custom Parameters";
"recomended_templates" = "Recommended Templates";
"hairstyle" = "Hairstyle";
"men_hairstyles" = "Men's Hairstyles";
"women_hairstyles" = "Women's Hairstyles";
"bangs" = "Bangs";
"length" = "Length";
"hair_color" = "Hair Color";
"hairstyle_test" = "Прическа";//只改名字，其他为英语
"face_shape_test&hairstyle_change" = "Face Shape Test & Hairstyle Change";
"auto_renewal_subscription" = "Auto-Renewal Subscription";
"start_now" = "Start Now";
"three_day_free" = "3 Days Free, then ¥X.XX/Month";
"subscriotion_plan" = "Subscription Plan";
"long_subscription" = "Free 3-day trial to experience XXXXXXX with all features. You can use all features without restriction during the trial period. If unsatisfied, cancel anytime before the trial period ends. After the first 3 days, ¥X.XX/Month, auto-renewal, cancel anytime.";
"recommendations" = "Recommendations";
"continuous_monthly" = "Continuous Monthly";
"continuous_annual" = "Continuous Annual";
"permanent_membership" = "Permanent Membership";
"change_main_screen_icon" = "Change Main Screen Icon";
"app_filling_number" = "App Filing Number: XXXXXXXXXXXXXXX\nXXXXX Copyright";
"popular_templates" = "Popular Templates";
"ai_hairstyle_change" = "AI Hairstyle Change";
"face_shape_test" = "Face Shape Test";
"lolita_haristyle" = "Lolita Hairstyle";
"gray_ombre_curls" = "Gray Ombre Curls";
"highlighted_braided_hair" = "Highlighted Braided Hair";
"woolly_curls" = "Woolly Curls";
"textured_perm" = "Textured Perm";
"afro_hairstyle" = "Afro Hairstyle";
"home" = "Home";
"setting" = "Setting";
"about_us" = "About Us";
"terms_of_use" = "Terms of Use";
"privacy_policy" = "Privacy Policy";
"camera" = "Camera";
"photo_library" = "Photo Library";
"cancel" = "Cancel";
"change_hairstyle_fail" = "Failed to change hairstyle, please try again later";
"generate" = "Generate";
"not_all_options_selected" = "Not all options selected";
"buzzCut" = "Buzz Cut";
"combOver" = "Comb Over";
"spiked" = "Spiked";
"dreadlocks" = "Dreadlocks";
"ringlets" = "Ringlets";
"longCurly" = "Long Curly";
"afr" = "Afro";
"manBun" = "Man Bun";
"Dreadlocks" = "Long Dreadlocks";
"quiffWithSidePart" ="Quiff with Side Part";
"pompadour" = "Pompadour";
"wavy" = "Wavy Hair";
"twinBraids" = "Twin Braids";
"twintails" = "Twin Tails";
"ponytail" = "Ponytail";
"himeCut" = "Hime Cut";
"doubleBun" = "Double Buns";
"hairBun" = "Bun";
"crownBraid" = "Crown Braid";
"bobcut" = "Bob Cut";
"halfUpdo" = "Half Updo";
"frenchBraid" = "French Braid";
"straightHair" = "Straight Hair";
"parted" = "Parted Bangs";
"asymmetrical" = "Asymmetrical Bangs";
"blunt" = "Blunt Bangs";
"bluntBangs" = "Blunt Bangs";
"partedBangs" = "Parted Bangs";
"asymmetricalBangs" = "Asymmetrical Bangs";
"long_hair" = "Long Hair";
"short_hair" = "Short Hair";
"download" = "Download";
"rebuilad_fail" = "Regeneration failed, please try again later";
"one_more" = "One More";
"try_again" = "Regenerate";
"save" = "Save";
"face_test_fail" = "Face shape detection failed";
"view_matching_hairstyles" = "View Matching Hairstyles";
"continue" = "continue";
"restore_purchase" = "Restore Purchase";
"limited_time_offer" = "Limited Time Offer";
"original_price_year" = "Original Price XX/Year";
"original_price" = "Original Price XX";
"confirm" = "Confirm";
"save_success" = "Save successful";
"save_fail" = "Save failed";
"version" = "version";
"complete" = "Complete";
"return" = "Return";
"internet_fail" = "Network connection failed";
"image_upload_fail" = "Image upload failed";
"loading" = "Loading";
"change_picture" = "Change image";
"tip_camera_permissions" = "Please go to settings and allow camera permissions";
"tip_alum_permissions" = "Please go to settings and allow photo album permissions";
"loading_tips" = "The function involves complex calculations, please be patient.";
"hairstyle_top_male" = "Men’s Hair";
"hairstyle_top_female" = "Women’s Hair";
"hairstyle_hide_hair" = "HairHide";
"hairstyle_color_change" = "Recolor";
"recover" = "Recover";
"append" = "Append";
"retry" = "Retry";
"hairstyle_fail" = "Failed to retrieve data from the hairstyle library";
"no_face" = "No face was detected in the image";
"hairstyle" = "HairStyle";
