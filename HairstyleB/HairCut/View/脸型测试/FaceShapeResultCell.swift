//
//  FaceShapeResultCell.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/8/3.
//

import Foundation
import UIKit
import SnapKit
import SDWebImage

class FaceShapeResultCell: UICollectionViewCell {
    static let identifier = "FaceShapeResultCell"
    
    private var imageView = UIImageView()
    private var hairView = UIImageView()
    public var cellData: FaceTestCellData? {
        willSet {
            self.buildHairTarget(cellData: newValue)
        }
    }
    
    override init(frame: CGRect) {
        self.cellData = nil
        super.init(frame: frame)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.layer.cornerRadius = 12
        self.layer.masksToBounds = true
        self.imageView.frame = self.frame
        self.addSubview(self.imageView)
    }
    
    private func buildHairTarget(cellData: FaceTestCellData?) {
        DispatchQueue.main.async {
            guard let data = cellData, let pImage = data.image, let pLeftEye = data.leftEye, let pRightEye = data.rightEye, let pRotation = data.rotation, let hairUrlString = data.hairImageUrlString  else {
                return
            }
     
            self.hairView.transform = .identity
            if FaceTestConst.cellHeight * pImage.size.width / pImage.size.height <= self.frame.size.width {
                self.imageView.frame = CGRectMake(0, 0, self.frame.size.height * (pImage.size.width  / pImage.size.height), self.frame.size.height)
            } else {
                self.imageView.frame = CGRectMake(0, 0, self.frame.size.width, self.frame.size.width * (pImage.size.height / pImage.size.width))
            }
            
            self.imageView.center = self.convert(self.center, from: self.superview)
            
            let cellWidth = self.imageView.frame.width
            let cellHeight = self.imageView.frame.height
            var leftEyeCenter = CGPointMake(pLeftEye.x * cellWidth / pImage.size.width , pLeftEye.y * cellHeight / pImage.size.height)
            var rightEyeCenter = CGPointMake(pRightEye.x * cellWidth / pImage.size.width, pRightEye.y * cellHeight / pImage.size.height)
            leftEyeCenter = self.convert(leftEyeCenter, from: self.imageView)
            rightEyeCenter = self.convert(rightEyeCenter, from: self.imageView)
            
            let x = ((leftEyeCenter.x + rightEyeCenter.x) / 2.0 )
            let y = ((leftEyeCenter.y + rightEyeCenter.y) / 2.0 )
            if (abs(leftEyeCenter.x - rightEyeCenter.x) == 0) {
                self.hairView.frame = CGRectMake(x, y, cellWidth / 3.0, cellWidth / 3.0 * FaceTestConst.hairHeight / FaceTestConst.hairWidth)
                self.addSubview(self.hairView)
                self.hairView.center = CGPointMake(x, y)
            } else {
                let width = fabs(leftEyeCenter.x - rightEyeCenter.x) / (FaceTestConst.eyeDistance / FaceTestConst.hairWidth)
                let height = fabs(leftEyeCenter.x - rightEyeCenter.x) / (FaceTestConst.eyeDistance / FaceTestConst.hairWidth) * FaceTestConst.hairHeight / FaceTestConst.hairWidth
                self.addSubview(self.hairView)
                self.hairView.frame = CGRectMake(x, y, width, height)
                self.layoutIfNeeded()
                let centerX = (leftEyeCenter.x + rightEyeCenter.x) / 2
                let centerY1 = (leftEyeCenter.y + rightEyeCenter.y) / 2.0
                let centerY2 = (fabs(leftEyeCenter.x - rightEyeCenter.x) / (FaceTestConst.eyeDistance / FaceTestConst.hairWidth) * FaceTestConst.hairHeight / FaceTestConst.hairWidth) * (0.5 - (FaceTestConst.eyeProportionY / FaceTestConst.hairHeight))
                let centerY = centerY1 + centerY2
                
                let hairCenter = CGPointMake(centerX, centerY)
                self.hairView.center = hairCenter
            }
            
            if pRotation != 0 {
                let eyeCenter = CGPoint(x: ((leftEyeCenter.x + rightEyeCenter.x) / 2.0), y: ((leftEyeCenter.y + rightEyeCenter.y) / 2.0))

                let eyeProportionX = 837.0
                let op = CGPointMake(self.hairView.frame.size.width * eyeProportionX / FaceTestConst.hairWidth, self.hairView.frame.size.height * eyeProportionX / FaceTestConst.hairHeight)
                let angle = Double(pRotation) * Double.pi / 180
                self.hairView.transform = CGAffineTransformMakeRotation(angle)
                let point = self.layer.convert(op, from: self.hairView.layer)
                self.hairView.transform = CGAffineTransformTranslate(self.hairView.transform, eyeCenter.x - point.x, eyeCenter.y - point.y)
            }

            self.hairView.sd_setImage(with: URL(string: hairUrlString)) {[weak self]image, error, cacgeType, url in
    //            printLog(message: "success:\(url)")
                self?.imageView.image = pImage
            }
        }
    }
}

