//
//  FaceShapeTestView.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/2.
//

import Foundation
import UIKit
import SnapKit

protocol FaceShapeTestViewDelegate: NSObjectProtocol {
    func resultButtonAction()
    func updateFaceShape(faceShape: FaceShape)
}

class FaceShapeTestView: UIView {
    private let imageHeight = UIScreen.main.bounds.height * 363 / 812
    public let imageActionView = UIView()
    public let imageView = UIImageView()
    public let hairView = UIImageView()
    public let resultButton = UIButton()
    private let whiteBackGroundView = UIView()
    public let faceShapeTestResultView = FaceShapeTestResultView()
    
    public var faceTestData: FaceTestData? {
        willSet {
            self.faceShapeTestResultView.faceTestData = newValue
        }
    }
    
    public var sourceImage: UIImage? {
        willSet {
            self.refreshView(image: newValue)
        }
    }
    
    public var faceHairUrlString: String? {
        willSet {
            self.combineHair(hairUrlString: newValue)
        }
    }
    
    private var hairPinOriginW = 0.0
    private var hairPinOriginH = 0.0
    
    weak public var delegate: FaceShapeTestViewDelegate?
    
    init() {
        super.init(frame: .zero)
        self.backgroundColor = UIColor(valueRGB: 0xF7F7F7)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.addSubview(self.imageActionView)
        self.imageActionView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.equalTo(UIScreen.main.bounds.width)
            make.height.equalTo(self.imageHeight)
        }
        self.imageView.backgroundColor = UIColor(valueRGB: 0xD9D9D9)
        self.imageActionView.addSubview(self.imageView)
        self.imageView.frame = CGRectMake(0, 0, UIScreen.main.bounds.width, self.imageHeight)
        
        self.whiteBackGroundView.backgroundColor = UIColor.white
        self.whiteBackGroundView.isUserInteractionEnabled = true
        self.addSubview(whiteBackGroundView)
        self.whiteBackGroundView.snp.makeConstraints { make in
            make.top.equalTo(self.imageActionView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        self.resultButton.layer.cornerRadius = 25
        self.resultButton.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        self.resultButton.setTitle("view_matching_hairstyles".localized, for: .normal)
        self.resultButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        self.resultButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 15)
        self.resultButton.addTarget(self, action: #selector(resultButtonAction), for: .touchUpInside)
        self.whiteBackGroundView.addSubview(self.resultButton)
        self.resultButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0 > 0 ? -47 : -10)
            make.centerX.equalToSuperview()
            make.width.greaterThanOrEqualTo(260)
            make.height.equalTo(50)
        }
        
        self.faceShapeTestResultView.delegate = self
        self.whiteBackGroundView.addSubview(self.faceShapeTestResultView)
        self.faceShapeTestResultView.isUserInteractionEnabled = true
        self.faceShapeTestResultView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.bottom.equalTo(self.resultButton.snp.top).offset(-42)
        }
    }
    
    @objc func resultButtonAction() {
        MobClick.event("Face_shape_test", attributes: ["source": "查看发型"])
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.resultButtonAction)) != nil) {
            self.delegate?.resultButtonAction()
        }
    }
    
    private func refreshView(image: UIImage?) {
        guard let pImage = image else {
            return
        }
        DispatchQueue.main.async {
            self.imageView.image = pImage
            self.layoutIfNeeded()
            if self.imageHeight * pImage.size.width / pImage.size.height <= self.imageActionView.frame.width {
                self.imageView.frame = CGRectMake(0, 0, self.imageActionView.frame.height * (pImage.size.width  / pImage.size.height), self.imageActionView.frame.height)
            } else {
                self.imageView.frame = CGRectMake(0, 0, self.imageActionView.frame.width, self.imageActionView.frame.width * (pImage.size.height / pImage.size.width))
            }
//            printLog(message: "-----after-frame:\(self.imageView.frame)")
            self.imageView.center = self.convert(self.imageActionView.center, from: self.imageActionView)
//            printLog(message: "-----after-center:\(self.imageView.frame)")
        }
    }
    
    private func combineHair(hairUrlString: String?) {
        guard let hairUrl = hairUrlString, let data = self.faceTestData, let pImage = self.imageView.image else {
            return
        }
        self.hairView.transform = .identity
        
        let imageWidth = self.imageView.frame.width
        let imageHeight = self.imageView.frame.height
        let pLeftEye = data.letfEyeCenter
        let pRightEye = data.rightEyeCenter
        let pRotation = data.rotation
        var leftEyeCenter = CGPointMake(pLeftEye.x * imageWidth / pImage.size.width , pLeftEye.y * imageHeight / pImage.size.height)
        var rightEyeCenter = CGPointMake(pRightEye.x * imageWidth / pImage.size.width, pRightEye.y * imageHeight / pImage.size.height)
        leftEyeCenter = self.convert(leftEyeCenter, from: self.imageView)
        rightEyeCenter = self.convert(rightEyeCenter, from: self.imageView)
        
        let x = ((leftEyeCenter.x + rightEyeCenter.x) / 2.0 )
        let y = ((leftEyeCenter.y + rightEyeCenter.y) / 2.0 )
        
        if (abs(leftEyeCenter.x - rightEyeCenter.x) == 0) {
            self.hairView.frame = CGRectMake(x, y, imageWidth / 3.0, imageWidth / 3.0 * FaceTestConst.hairHeight / FaceTestConst.hairWidth)
            self.imageActionView.addSubview(self.hairView)
            self.hairView.center = CGPointMake(x, y)
        } else {
            let width = fabs(leftEyeCenter.x - rightEyeCenter.x) / (FaceTestConst.eyeDistance / FaceTestConst.hairWidth)
            let height = fabs(leftEyeCenter.x - rightEyeCenter.x) / (FaceTestConst.eyeDistance / FaceTestConst.hairWidth) * FaceTestConst.hairHeight / FaceTestConst.hairWidth
            self.imageActionView.addSubview(self.hairView)
            self.hairView.frame = CGRectMake(x, y, width, height)
            self.layoutIfNeeded()
            let centerX = (leftEyeCenter.x + rightEyeCenter.x) / 2
            let centerY1 = (leftEyeCenter.y + rightEyeCenter.y) / 2.0
            let centerY2 = (fabs(leftEyeCenter.x - rightEyeCenter.x) / (FaceTestConst.eyeDistance / FaceTestConst.hairWidth) * FaceTestConst.hairHeight / FaceTestConst.hairWidth) * (0.5 - (FaceTestConst.eyeProportionY / FaceTestConst.hairHeight))
            let centerY = centerY1 + centerY2
            
            let hairCenter = CGPointMake(centerX, centerY)
            self.hairView.center = hairCenter
        }
        
        if pRotation != 0 {
            let eyeCenter = CGPoint(x: ((leftEyeCenter.x + rightEyeCenter.x) / 2.0), y: ((leftEyeCenter.y + rightEyeCenter.y) / 2.0))

            let eyeProportionX = 837.0
            let op = CGPointMake(self.hairView.frame.size.width * eyeProportionX / FaceTestConst.hairWidth, self.hairView.frame.size.height * eyeProportionX / FaceTestConst.hairHeight)
            let angle = Double(pRotation) * Double.pi / 180
            self.hairView.transform = CGAffineTransformMakeRotation(angle)
            let point = self.layer.convert(op, from: self.hairView.layer)
            self.hairView.transform = CGAffineTransformTranslate(self.hairView.transform, eyeCenter.x - point.x, eyeCenter.y - point.y)
        }

        self.hairView.sd_setImage(with: URL(string: hairUrl)) {[weak self]image, error, cacgeType, url in
//            printLog(message: "success:\(url)")
            self?.imageView.image = pImage
        }
        
        self.hairPinOriginW = self.hairView.bounds.size.width
        self.hairPinOriginH = self.hairView.bounds.size.height
        
        self.addHairViewGesture()
    }
    

}

typealias FaceShapeTestViewAction = FaceShapeTestView
extension FaceShapeTestViewAction: FaceShapeTestResultViewDelegate,UIGestureRecognizerDelegate {
    func updateFaceShape(faceShape: FaceShape) {
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("updateFaceShape:"))) != nil) {
            self.delegate?.updateFaceShape(faceShape: faceShape)
        }
    }
    
    private func addHairViewGesture() {
        //添加手势前先删除已有手势，不删除会造成叠加效果
        if let gestures = self.hairView.gestureRecognizers {
            for gesture in gestures {
                self.hairView.removeGestureRecognizer(gesture)
            }
        }
        //移动手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(panGesture(_ :)))
        panGesture.delegate = self
        panGesture.maximumNumberOfTouches = 2
        self.hairView.isUserInteractionEnabled = true
        self.hairView.addGestureRecognizer(panGesture)
        //缩放手势
        let pinGesture = UIPinchGestureRecognizer(target: self, action: #selector(pinGesture(_ :)))
        pinGesture.delegate = self
        self.hairView.addGestureRecognizer(pinGesture)
        //旋转手势
        let rotationGesture = UIRotationGestureRecognizer(target: self, action: #selector(rotationGesture(_ :)))
        rotationGesture.delegate = self
        self.hairView.addGestureRecognizer(rotationGesture)
    }
    
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
    
    @objc func rotationGesture(_ gesture: UIRotationGestureRecognizer) {
        if gesture.state == .began || gesture.state == .changed {
            self.hairView.transform = CGAffineTransformRotate(self.hairView.transform, gesture.rotation)
            gesture.rotation = 0
        }
    }
    
    @objc func panGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: self.hairView.superview)
        let center = CGPoint(x: self.hairView.center.x + translation.x,
                             y: self.hairView.center.y + translation.y)
        self.hairView.center = center
        gesture.setTranslation(.zero, in: self.hairView.superview)
    }
    
    @objc func pinGesture(_ gesture: UIPinchGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        let pinchCenter = gesture.location(in: view)
        
        if gesture.state == .began || gesture.state == .changed {
            //根据缩放因子scale去计算出transform
            let scale = gesture.scale
            let transform = view.transform.scaledBy(x: scale, y: scale)
            view.transform = transform
            //因为锚点位置变化会导致位移,计算相对于视图中心的x/y偏移量，重新赋值中心点
            let newCenter = CGPoint(x: view.center.x + (pinchCenter.x - view.bounds.size.width / 2) * (scale - 1),y: view.center.y + (pinchCenter.y - view.bounds.size.height / 2) * (scale - 1))
            view.center = newCenter
            gesture.scale = 1.0
        }
    }
}
