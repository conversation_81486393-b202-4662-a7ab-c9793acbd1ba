//
//  FaceShapeTestCell.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/2.
//

import Foundation
import UIKit
import SnapKit

class FaceShapeTestCell: UICollectionViewCell {
    static let identifier = "FaceShapeTestCell"
    
    private var imageBubbleView = UIImageView()
    private var imageBubbleTitle = UILabel()
    private var resultBackgroundView = UIView()
    private var imageView = UIImageView()
    private var titleLabel = UILabel()
    public var shape: FaceShape {
        willSet {
            switch newValue {
            case .noChoose:
                return
            case .oval:
                self.imageView.image = UIImage(named: "faceshape_oval_off")
                self.titleLabel.text = "oval".localized
            case .heart:
                self.imageView.image = UIImage(named: "faceshape_heart_off")
                self.titleLabel.text = "heart".localized
            case .triangle:
                self.imageView.image = UIImage(named: "faceshape_rhomboid_off")
                self.titleLabel.text = "diamond".localized
            case .round:
                self.imageView.image = UIImage(named: "faceshape_roundness_off")
                self.titleLabel.text = "round".localized
            case .square:
                self.imageView.image = UIImage(named: "faceshape_quadrate_off")
                self.titleLabel.text = "square".localized
            }
        }
    }
    
    public var titleString: String? {
        willSet {
            self.titleLabel.text = newValue ?? ""
        }
    }
    
    public var isSelectedItem: Bool = false {
        willSet {
            self.resultBackgroundView.backgroundColor = newValue == true ? UIColor(valueRGB: 0xFFF8C5) : UIColor(valueRGB: 0xF7F7F7)
        }
    }
    
    public var isTestResult: Bool = false {
        willSet {
            self.imageBubbleView.isHidden = newValue == true ? false : true
        }
    }
    
    override init(frame: CGRect) {
        self.shape = .noChoose
        super.init(frame: frame)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.resultBackgroundView.layer.cornerRadius = 10
        self.resultBackgroundView.backgroundColor = UIColor(valueRGB: 0xF7F7F7)
        self.addSubview(self.resultBackgroundView)
        self.resultBackgroundView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(17)
        }
        
        self.imageView.contentMode = .scaleAspectFit
        self.resultBackgroundView.addSubview(self.imageView)
        self.imageView.snp.makeConstraints { make in
            make.width.equalTo(24)
            make.height.equalTo(24)
            make.centerX.equalToSuperview()
            make.top.equalTo(10)
        }
        
        self.titleLabel.text = self.titleString
        self.titleLabel.adjustsFontSizeToFitWidth = true
        self.titleLabel.textColor = UIColor(valueRGB: 0x333333)
        self.titleLabel.font = UIFont.systemFont(ofSize: 11)
        self.titleLabel.textAlignment = .center
        self.resultBackgroundView.addSubview(self.titleLabel)
        self.titleLabel.snp.makeConstraints { make in
            make.top.equalTo(self.imageView.snp.bottom).offset(8)
            make.left.equalTo(5)
            make.right.equalTo(-5)
            make.height.greaterThanOrEqualTo(15)
        }
        
        self.imageBubbleView.image = UIImage(named: "faceshape_bubble")
        self.imageBubbleView.contentMode = .scaleAspectFit
        self.addSubview(self.imageBubbleView)
        self.imageBubbleView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.equalTo(52)
            make.height.equalTo(24)
        }
        self.imageBubbleView.isHidden = true
        
        self.imageBubbleTitle.text = "detection_results".localized
        self.imageBubbleTitle.adjustsFontSizeToFitWidth = true
        self.imageBubbleTitle.textColor = UIColor(valueRGB: 0x333333)
        self.imageBubbleTitle.font = UIFont.systemFont(ofSize: 10)
        self.imageBubbleTitle.textAlignment = .center
        self.imageBubbleView.addSubview(self.imageBubbleTitle)
        self.imageBubbleTitle.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(5)
            make.right.equalTo(-5)
        }
        self.layoutIfNeeded()
        self.imageBubbleTitle.sizeToFit()
    }
}
