//
//  FaceShapeSliderResultView.swift
//  HairCut
//
//  Created by Clarence on 2024/8/3.
//

import Foundation
import UIKit
import SnapKit

protocol FaceShapeSliderResultViewDelegate: NSObjectProtocol {
    func selectHair(hairUrlString: String)
}

class FaceShapeSliderResultView: UIView {
    public var resultImageArr: [FaceTestCellData] = [] {
        didSet {
            self.resultView?.reloadData()
        }
    }
    private let resultBackgroundView = UIView()
    private var resultView: UICollectionView? = nil
    private let closeButton = UIButton()
    
    weak public var delegate: FaceShapeSliderResultViewDelegate? = nil
    
    init() {
        super.init(frame: .zero)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.backgroundColor = UIColor(valueRGB: 0x000000).withAlphaComponent(0.5)
        self.buildResultCollectionView()
        
        self.closeButton.setImage(UIImage(named: "faceshape_close"), for: .normal)
        self.closeButton.addTarget(self, action: #selector(closeView), for: .touchUpInside)
        self.closeButton.adjustsImageWhenHighlighted = false
        self.addSubview(self.closeButton)
        let top = UIScreen.main.bounds.height/2 - 40
        self.closeButton.snp.makeConstraints { make in
            make.right.equalTo(self.resultBackgroundView.snp.left).offset(0)
            make.width.equalTo(32)
            make.height.equalTo(80)
            make.top.equalTo(top)
        }
    }
    
    @objc func closeView() {
        self.removeFromSuperview()
    }
}

typealias FaceShapeSliderResultViewCollectionDelegate = FaceShapeSliderResultView
extension FaceShapeSliderResultViewCollectionDelegate: UICollectionViewDelegate, UICollectionViewDataSource {
    private func buildResultCollectionView() {
        self.resultBackgroundView.backgroundColor = UIColor.white
        self.addSubview(self.resultBackgroundView)
        self.resultBackgroundView.snp.makeConstraints { make in
            make.left.equalTo(53)
            make.top.right.bottom.equalToSuperview()
        }
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.itemSize = CGSize(width: 90, height: 110)
        layout.minimumLineSpacing = 5
        layout.minimumInteritemSpacing = 5
        self.resultView = UICollectionView(frame: CGRect(x: 0, y: 16, width: UIScreen.main.bounds.width - 53, height: UIScreen.main.bounds.height), collectionViewLayout: layout)
        self.resultView?.showsVerticalScrollIndicator = false
        self.resultBackgroundView.addSubview(self.resultView ?? UICollectionView())
        self.resultView?.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.right.equalTo(-16)
            make.top.equalTo(44)
            make.bottom.equalTo(-44)
        }
        self.resultView?.collectionViewLayout = layout
        self.resultView?.delegate = self
        self.resultView?.dataSource = self
        self.resultView?.backgroundColor = UIColor.white
        self.resultView?.register(FaceShapeResultCell.self, forCellWithReuseIdentifier: FaceShapeResultCell.identifier)
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return resultImageArr.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FaceShapeResultCell.identifier, for: indexPath) as? FaceShapeResultCell else {
            return UICollectionViewCell()
        }

        cell.cellData = self.resultImageArr[indexPath.row]
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("selectHair:"))) != nil) {
            self.delegate?.selectHair(hairUrlString: self.resultImageArr[indexPath.row].hairImageUrlString ?? "")
        }
    }
}
