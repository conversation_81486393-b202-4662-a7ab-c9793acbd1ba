//
//  FaceShapeTestResultView.swift
//  HairCut
//
//  Created by Clarence on 2024/8/3.
//

import Foundation
import UIKit
import SnapKit

protocol FaceShapeTestResultViewDelegate: NSObjectProtocol {
    func updateFaceShape(faceShape: FaceShape)
}

class FaceShapeTestResultView: UIView {
    private let scrollView = UIScrollView()
    private let resultTitleLabel = UILabel()
    private let appearanceLevelRateLabel = UILabel()
    private let resultView = UIView()
    private let genderLabel = UILabel()
    private let ageLabel = UILabel()
    private let emoteLabel = UILabel()
    private let glassessLabel = UILabel()
    public let faceshapeTitleLabel = UILabel()
    public var faceTypeCollectionView: UICollectionView? = nil
    
    public var shapeArr = [FaceShape]()
    
    public var faceTestData: FaceTestData? {
        willSet {
            self.freshView(newValue: newValue)
        }
    }
    
    public var chooseFaceShapeIndex: Int = -1
    
    weak public var delegate: FaceShapeTestResultViewDelegate? = nil
    
    init() {
        super.init(frame: .zero)
        self.initView()
        self.addCollectionView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.addSubview(self.scrollView)
        self.scrollView.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
        
        self.resultTitleLabel.text = "detection_results".localized
        self.resultTitleLabel.adjustsFontSizeToFitWidth = true
        self.resultTitleLabel.textColor = UIColor(valueRGB: 0x333333)
        self.resultTitleLabel.font = UIFont.boldSystemFont(ofSize: 15)
        self.resultTitleLabel.textAlignment = .left
        self.scrollView.addSubview(self.resultTitleLabel)
        self.resultTitleLabel.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.top.equalTo(14)
            make.width.greaterThanOrEqualTo(75)
            make.height.equalTo(20)
        }
        
        self.resultView.backgroundColor = UIColor(valueRGB: 0xF7F7F7)
        self.resultView.layer.cornerRadius = 5
        self.scrollView.addSubview(self.resultView)
        self.resultView.snp.makeConstraints { make in
            make.top.equalTo(self.resultTitleLabel.snp.bottom).offset(14)
            make.left.equalTo(self.resultTitleLabel)
            make.width.equalTo(UIScreen.main.bounds.width - 32)
            make.height.greaterThanOrEqualTo(51)
        }
        
        self.appearanceLevelRateLabel.text = "attractiveness_rating".localized
        self.appearanceLevelRateLabel.adjustsFontSizeToFitWidth = true
        self.appearanceLevelRateLabel.textColor = UIColor(valueRGB: 0x333333)
        self.appearanceLevelRateLabel.font = UIFont.systemFont(ofSize: 15)
        self.appearanceLevelRateLabel.textAlignment = .right
        self.scrollView.addSubview(self.appearanceLevelRateLabel)
        self.appearanceLevelRateLabel.snp.makeConstraints { make in
            make.right.equalTo(self.resultView.snp.right)
            make.top.equalTo(self.resultTitleLabel)
            make.width.greaterThanOrEqualTo(87)
            make.height.equalTo(20)
        }
        
        let labelWidth = (UIScreen.main.bounds.width - 32 - 5 * 14) / 4
        
        self.genderLabel.text = "gender".localized
        self.genderLabel.numberOfLines = 0
        self.genderLabel.lineBreakMode = .byWordWrapping
        self.genderLabel.textColor = UIColor(valueRGB: 0x333333)
        self.genderLabel.font = UIFont.systemFont(ofSize: 13)
        self.genderLabel.textAlignment = .left
        self.resultView.addSubview(self.genderLabel)
        self.genderLabel.snp.makeConstraints { make in
            make.left.equalTo(14)
            make.top.equalTo(17)
            make.width.equalTo(labelWidth - 15)
            make.height.greaterThanOrEqualTo(18)
            make.bottom.equalTo(-16)
        }
        
        self.ageLabel.text = "age".localized
        self.ageLabel.numberOfLines = 0
        self.ageLabel.lineBreakMode = .byWordWrapping
        self.ageLabel.textColor = UIColor(valueRGB: 0x333333)
        self.ageLabel.font = UIFont.systemFont(ofSize: 13)
        self.ageLabel.textAlignment = .left
        self.resultView.addSubview(self.ageLabel)
        self.ageLabel.snp.makeConstraints { make in
            make.left.equalTo(self.genderLabel.snp.right).offset(17)
            make.top.equalTo(self.genderLabel)
            make.width.equalTo(labelWidth - 10)
            make.height.greaterThanOrEqualTo(18)
            make.bottom.equalTo(-16)
        }
        
        self.emoteLabel.text = "expression".localized
        self.emoteLabel.numberOfLines = 0
        self.emoteLabel.lineBreakMode = .byWordWrapping
        self.emoteLabel.textColor = UIColor(valueRGB: 0x333333)
        self.emoteLabel.font = UIFont.systemFont(ofSize: 13)
        self.emoteLabel.textAlignment = .left
        self.resultView.addSubview(self.emoteLabel)
        self.emoteLabel.snp.makeConstraints { make in
            make.left.equalTo(self.ageLabel.snp.right).offset(14)
            make.top.equalTo(self.ageLabel)
            make.width.equalTo(labelWidth + 10)
            make.height.greaterThanOrEqualTo(18)
            make.bottom.equalTo(-16)
        }
        
        self.glassessLabel.text = "glassess".localized
        self.glassessLabel.numberOfLines = 0
        self.glassessLabel.lineBreakMode = .byWordWrapping
        self.glassessLabel.textColor = UIColor(valueRGB: 0x333333)
        self.glassessLabel.font = UIFont.systemFont(ofSize: 13)
        self.glassessLabel.textAlignment = .left
        self.resultView.addSubview(self.glassessLabel)
        self.glassessLabel.snp.makeConstraints { make in
            make.left.equalTo(self.emoteLabel.snp.right).offset(14)
            make.top.equalTo(self.genderLabel)
            make.width.equalTo(labelWidth + 15)
            make.height.greaterThanOrEqualTo(18)
            make.bottom.equalTo(-16)
        }
        
        self.faceshapeTitleLabel.text = "your_face_shape_is".localized
        self.faceshapeTitleLabel.adjustsFontSizeToFitWidth = true
        self.faceshapeTitleLabel.textColor = UIColor(valueRGB: 0x333333)
        self.faceshapeTitleLabel.font = UIFont.systemFont(ofSize: 15)
        self.faceshapeTitleLabel.textAlignment = .left
        self.addSubview(self.faceshapeTitleLabel)
        self.faceshapeTitleLabel.snp.makeConstraints { make in
            make.left.equalTo(self.resultTitleLabel)
            make.right.equalTo(self.appearanceLevelRateLabel)
            make.top.equalTo(self.resultView.snp.bottom).offset(20)
            make.height.equalTo(20)
        }
    }
    
    private func freshView(newValue: FaceTestData?) {
        MobClick.event("Face_shape_test", attributes: ["source": "切换发型"])
        self.appearanceLevelRateLabel.text = "attractiveness_rating".localized + "\(String(newValue?.beauty ?? 0))"
        switch newValue?.faceshape {
            case .noChoose,.none: self.chooseFaceShapeIndex = -1
            case .oval: self.chooseFaceShapeIndex = 0
            case .heart: self.chooseFaceShapeIndex = 1
            case .triangle: self.chooseFaceShapeIndex = 4
            case .round: self.chooseFaceShapeIndex = 2
            case .square: self.chooseFaceShapeIndex = 3
        }
        if newValue?.gender == .male {
            self.genderLabel.text = "gender".localized + "male".localized
        } else if newValue?.gender == .female {
            self.genderLabel.text = "gender".localized + "female".localized
        }
        self.ageLabel.text = "age".localized + "\(String(newValue?.age ?? 0))"
        if newValue?.expression == FaceTestExpression.none {
            self.emoteLabel.text = "expression".localized + "not_smiling".localized
        } else if newValue?.expression == .laugh {
            self.emoteLabel.text = "expression".localized + "laughing".localized
        } else if newValue?.expression == .smile {
            self.emoteLabel.text = "expression".localized + "smiling".localized
        }
        if newValue?.glassess == FaceTestGlasses.none {
            self.glassessLabel.text = "glassess".localized + "no_glassess".localized
        } else if newValue?.glassess == .sun {
            self.glassessLabel.text = "glassess".localized + "sunglassess".localized
        } else if newValue?.glassess == .common {
            self.glassessLabel.text = "glassess".localized + "regular_glassess".localized
        }
        guard let collectionview = self.faceTypeCollectionView else {
            return
        }
        collectionview.reloadData()
    }
}


typealias FaceShapeTestResultViewCollectionView = FaceShapeTestResultView
extension FaceShapeTestResultViewCollectionView: UICollectionViewDelegate,UICollectionViewDataSource {
    func addCollectionView() {
        self.shapeArr = [FaceShape.oval,FaceShape.heart,FaceShape.round,FaceShape.square,FaceShape.triangle]
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.itemSize = CGSize(width: 52, height: 85)
        layout.minimumInteritemSpacing = (UIScreen.main.bounds.width - 292) / 4
        layout.minimumLineSpacing = 1
        self.faceTypeCollectionView = UICollectionView(frame: CGRect(x: 0, y: 16, width: UIScreen.main.bounds.width - 32, height: 326), collectionViewLayout: layout)
        self.faceTypeCollectionView?.showsVerticalScrollIndicator = false
        self.scrollView.addSubview(self.faceTypeCollectionView ?? UICollectionView())
        self.faceTypeCollectionView?.snp.makeConstraints { make in
            make.top.equalTo(self.faceshapeTitleLabel.snp.bottom).offset(0)
            make.left.equalTo(16)
            make.width.equalTo(UIScreen.main.bounds.width - 32)
            make.height.equalTo(85)
        }
        self.faceTypeCollectionView?.isScrollEnabled = false
        self.faceTypeCollectionView?.collectionViewLayout = layout
        self.faceTypeCollectionView?.delegate = self
        self.faceTypeCollectionView?.dataSource = self
        self.faceTypeCollectionView?.backgroundColor = UIColor.white
        self.faceTypeCollectionView?.register(FaceShapeTestCell.self, forCellWithReuseIdentifier: FaceShapeTestCell.identifier)
        self.faceTypeCollectionView?.bounces = false
        
        self.layoutIfNeeded()
        self.scrollView.contentSize = CGSizeMake(UIScreen.main.bounds.width, CGRectGetMaxY(self.faceTypeCollectionView!.frame) + 20)
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.shapeArr.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FaceShapeTestCell.identifier, for: indexPath) as? FaceShapeTestCell else {
            return UICollectionViewCell()
        }
        cell.shape = self.shapeArr[indexPath.row]
        if self.chooseFaceShapeIndex != -1 {
            cell.isSelectedItem = cell.shape == self.shapeArr[self.chooseFaceShapeIndex]
        } else {
            cell.isSelectedItem = false
        }
        
        cell.isTestResult = cell.shape == self.faceTestData?.faceshape
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let index = indexPath.row
        if self.chooseFaceShapeIndex == index {
            return
        }
        var arr = [IndexPath]()
        if self.chooseFaceShapeIndex != -1 {
            arr.append(IndexPath(row: self.chooseFaceShapeIndex, section: 0))
        }
        self.chooseFaceShapeIndex = index
        arr.append(IndexPath(row: index, section: 0))
        //一次刷新两个item，不能单个刷2遍，因为是异步操作
        self.faceTypeCollectionView?.reloadItems(at: arr)

        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("updateFaceShape:"))) != nil) {
            self.delegate?.updateFaceShape(faceShape: self.shapeArr[self.chooseFaceShapeIndex])
        }
    }
}
