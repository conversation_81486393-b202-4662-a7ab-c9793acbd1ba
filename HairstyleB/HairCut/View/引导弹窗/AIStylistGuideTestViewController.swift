//
//  AIStylistGuideTestViewController.swift
//  HairCut
//
//  Created by AI Assistant on 2025/01/21.
//

import UIKit
import SnapKit

/// AI造型师引导弹窗测试页面
class AIStylistGuideTestViewController: UIViewController {
    
    private let showPopupButton = UIButton(type: .system)
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    private func setupUI() {
        view.backgroundColor = .white
        title = "引导弹窗测试"
        
        // 显示弹窗按钮
        showPopupButton.setTitle("显示AI造型师引导弹窗", for: .normal)
        showPopupButton.backgroundColor = UIColor(red: 255/255.0, green: 236/255.0, blue: 83/255.0, alpha: 1.0)
        showPopupButton.setTitleColor(.black, for: .normal)
        showPopupButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        showPopupButton.layer.cornerRadius = 8
        view.addSubview(showPopupButton)
    }
    
    private func setupConstraints() {
        showPopupButton.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(50)
        }
    }
    
    private func setupActions() {
        showPopupButton.addTarget(self, action: #selector(showPopupButtonTapped), for: .touchUpInside)
    }
    
    @objc private func showPopupButtonTapped() {
        let popupView = AIStylistGuidePopupView()

        // 设置回调
        popupView.onHairStyleButtonTapped = { [weak self] in
            print("换发型按钮点击")
            self?.showAlert(title: "AI换发型/换衣服", message: "跳转到换发型页面\n一键生成多款发型")
        }

        popupView.onNailArtButtonTapped = { [weak self] in
            print("美甲按钮点击")
            self?.showAlert(title: "AI美甲", message: "跳转到美甲页面\n可爱美甲随心试")
        }

        popupView.onDismiss = {
            print("弹窗关闭")
        }

        // 显示弹窗
        popupView.show()
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}
