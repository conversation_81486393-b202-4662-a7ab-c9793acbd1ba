//
//  AIStylistGuidePopupView.swift
//  HairCut
//
//  Created by AI Assistant on 2025/01/21.
//

import UIKit
import SnapKit

/// AI造型师引导弹窗
class AIStylistGuidePopupView: UIView {

    // MARK: - 回调
    var onHairStyleButtonTapped: (() -> Void)?
    var onNailArtButtonTapped: (() -> Void)?
    var onDismiss: (() -> Void)?

    // MARK: - UI组件
    // 顶部次数区域
    private let topCountLabel = UILabel()
    private let topAddButton = UIButton(type: .custom)
    private let topDescLabel = UILabel()

    // 换发型相关
    private let hairStyleImageView = UIImageView()
    private let hairStyleMaskView = UIView()
    private let hairStyleTitleLabel = UILabel()
    private let hairStyleButton = UIButton(type: .custom)

    // 美甲相关
    private let nailArtImageView = UIImageView()
    private let nailArtMaskView = UIView()
    private let nailArtTitleLabel = UILabel()
    private let nailArtButton = UIButton(type: .custom)

    // 背景手势识别器
    private var backgroundTapGesture: UITapGestureRecognizer!

    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupActions()

        // 监听次数和订阅状态更新通知
        NotificationCenter.default.addObserver(self, selector: #selector(handleUsageCountUpdate), name: .usageCountDidUpdate, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleSubscriptionStatusUpdate), name: .subscriptionStatusDidUpdate, object: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - UI设置
    private func setupUI() {
        // 全屏背景
        backgroundColor = UIColor.black.withAlphaComponent(0.8)

        // 顶部次数标签 - 获取真实次数
        updateUsageCountDisplay()
        topCountLabel.textColor = .white
        topCountLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        addSubview(topCountLabel)

        // 顶部添加按钮
        topAddButton.setImage(UIImage(named: "添加次数按钮"), for: .normal)
        topAddButton.backgroundColor = UIColor(red: 255/255.0, green: 236/255.0, blue: 83/255.0, alpha: 1.0)
        topAddButton.layer.cornerRadius = 15
        addSubview(topAddButton)

        // 顶部描述标签
        topDescLabel.text = local("会员每月畅享20次高品质AI工具")
        topDescLabel.textColor = .white
        topDescLabel.font = UIFont.systemFont(ofSize: 14)
        addSubview(topDescLabel)

        // 换发型图片 - 272*200比例
        hairStyleImageView.image = UIImage(named: "引导页换发型")
        hairStyleImageView.contentMode = .scaleAspectFill
        hairStyleImageView.layer.cornerRadius = 12
        hairStyleImageView.layer.masksToBounds = true
        addSubview(hairStyleImageView)

        // 换发型遮罩层
        hairStyleMaskView.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        hairStyleMaskView.layer.cornerRadius = 12
        hairStyleMaskView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
        hairStyleImageView.addSubview(hairStyleMaskView)

        // 换发型标题（在图片底部遮罩上）
        hairStyleTitleLabel.text = local("AI换发型/换衣服")
        hairStyleTitleLabel.textColor = .white
        hairStyleTitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        hairStyleTitleLabel.textAlignment = .center
        hairStyleMaskView.addSubview(hairStyleTitleLabel)

        // 换发型点击按钮（透明覆盖整个图片区域）
        hairStyleButton.backgroundColor = .clear
        addSubview(hairStyleButton)

        // 美甲图片 - 272*200比例
        nailArtImageView.image = UIImage(named: "引导页美甲")
        nailArtImageView.contentMode = .scaleAspectFill
        nailArtImageView.layer.cornerRadius = 12
        nailArtImageView.layer.masksToBounds = true
        addSubview(nailArtImageView)

        // 美甲遮罩层
        nailArtMaskView.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        nailArtMaskView.layer.cornerRadius = 12
        nailArtMaskView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
        nailArtImageView.addSubview(nailArtMaskView)

        // 美甲标题（在图片底部遮罩上）
        nailArtTitleLabel.text = local("AI美甲")
        nailArtTitleLabel.textColor = .white
        nailArtTitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        nailArtTitleLabel.textAlignment = .center
        nailArtMaskView.addSubview(nailArtTitleLabel)

        // 美甲点击按钮（透明覆盖整个图片区域）
        nailArtButton.backgroundColor = .clear
        addSubview(nailArtButton)
    }
    
    private func setupConstraints() {
        // 顶部次数标签
        topCountLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(100)
            make.centerX.equalToSuperview().offset(-20)
        }

        // 顶部添加按钮
        topAddButton.snp.makeConstraints { make in
            make.centerY.equalTo(topCountLabel)
            make.left.equalTo(topCountLabel.snp.right).offset(20)
            make.width.height.equalTo(18)
        }

        // 顶部描述标签
        topDescLabel.snp.makeConstraints { make in
            make.top.equalTo(topCountLabel.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
        }

        // 换发型图片约束 - 272*200比例
        hairStyleImageView.snp.makeConstraints { make in
            make.top.equalTo(topDescLabel.snp.bottom).offset(40)
            make.centerX.equalToSuperview()
            make.width.equalTo(272)
            make.height.equalTo(200)
        }

        // 换发型遮罩层
        hairStyleMaskView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(38)
        }

        // 换发型标题约束（在遮罩层上）
        hairStyleTitleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        // 换发型点击按钮约束
        hairStyleButton.snp.makeConstraints { make in
            make.edges.equalTo(hairStyleImageView)
        }

        // 美甲图片约束 - 272*200比例
        nailArtImageView.snp.makeConstraints { make in
            make.top.equalTo(hairStyleImageView.snp.bottom).offset(40)
            make.centerX.equalToSuperview()
            make.width.equalTo(272)
            make.height.equalTo(200)
        }

        // 美甲遮罩层
        nailArtMaskView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(38)
        }

        // 美甲标题约束（在遮罩层上）
        nailArtTitleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        // 美甲点击按钮约束
        nailArtButton.snp.makeConstraints { make in
            make.edges.equalTo(nailArtImageView)
        }
    }
    
    private func setupActions() {
        // 背景点击隐藏
        backgroundTapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        addGestureRecognizer(backgroundTapGesture)

        // 按钮点击事件
        hairStyleButton.addTarget(self, action: #selector(hairStyleButtonTapped), for: .touchUpInside)
        nailArtButton.addTarget(self, action: #selector(nailArtButtonTapped), for: .touchUpInside)

        // 添加次数按钮点击事件
        topAddButton.addTarget(self, action: #selector(topAddButtonTapped), for: .touchUpInside)
    }
    
    // MARK: - 公共方法

    /// 显示弹窗 - 直接添加到keyWindow最上层
    func show() {
        // 设置视图大小为全屏
        frame = UIScreen.main.bounds
        
        // 直接添加到keyWindow，确保在最上层（包括tabbar之上）
        if let keyWindow = UIApplication.shared.keyWindow {
            keyWindow.addSubview(self)
            
            // 添加显示动画
            alpha = 0
            transform = CGAffineTransform(scaleX: 0.3, y: 0.3)
            UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut, animations: {
                self.alpha = 1
                self.transform = CGAffineTransform.identity
            })
        }
    }

    /// 隐藏弹窗
    func hide() {
        // 添加隐藏动画，然后移除视图
        UIView.animate(withDuration: 0.25, delay: 0, options: .curveEaseIn, animations: {
            self.alpha = 0
            self.transform = CGAffineTransform(scaleX: 0.3, y: 0.3)
        }, completion: { _ in
            self.removeFromSuperview()
            self.onDismiss?()
        })
    }
    
    // MARK: - 事件处理
    
    @objc private func backgroundTapped() {
        hide()
    }
    
    @objc private func hairStyleButtonTapped() {
        hide()
        onHairStyleButtonTapped?()
    }
    
    @objc private func nailArtButtonTapped() {
        hide()
        onNailArtButtonTapped?()
    }

    @objc private func topAddButtonTapped() {
        // 使用PopView弹出购买次数弹窗
        showUsagePurchasePopup()
    }

    // MARK: - 私有方法

    /// 更新次数显示
    private func updateUsageCountDisplay() {
        let remaining = VolcanoUsageManager.shared.getRemainingCount()
        topCountLabel.text = local("剩余次数: ") + "\(remaining)"
    }

    /// 显示购买次数弹窗
    private func showUsagePurchasePopup() {
        // 暂时禁用引导弹窗的背景点击
        self.isUserInteractionEnabled = false
        backgroundTapGesture.isEnabled = false

        let popupView = UsagePurchasePopupView()

        // 获取次数购买产品价格
        let purchasePrice = UserDefaults.standard.string(forKey: "com.Fengyin.Camera.Frequency.package") ?? "¥10"
        popupView.updatePurchasePrice(purchasePrice)

        // 检查是否为VIP用户，更新布局
        let isVIP = APPMakeStoreIAPManager.featureVip()
        popupView.updateLayoutForVIPStatus(isVIP)

        // 设置回调
        popupView.onPurchaseButtonTapped = { [weak self] in
            // 执行次数购买
            VolcanoEngineAPI.purchaseUsagePackage { success in
                if success {
                    // 友盟埋点：10次购买成功
                    MobClick.event("Subscribe", attributes: ["result": "成功结果10次购买成功"])
                    popupView.hide()
                    // 次数更新通过通知自动处理，无需手动调用
                } else {
                    // 友盟埋点：10次购买失败
                    MobClick.event("Subscribe", attributes: ["result": "成功结果10次购买失败"])
                }
            }
        }

        popupView.onSubscribeButtonTapped = {
            popupView.hide()
            // 友盟埋点：进入订阅页面
            MobClick.event("Subscribe", attributes: ["source": "AI换发（入口进入订阅页算一次）"])
            VolcanoEngineAPI.showSubscriptionPage(from: "引导弹窗次数不足")
        }

        // 购买弹窗关闭时恢复引导弹窗的交互
        popupView.onDismiss = { [weak self] in
            self?.isUserInteractionEnabled = true
            self?.backgroundTapGesture.isEnabled = true
        }

        // 使用PopView正常弹出购买弹窗
        popupView.show()
    }

    /// 处理次数更新通知
    @objc private func handleUsageCountUpdate() {
        DispatchQueue.main.async {
            self.updateUsageCountDisplay()
        }
    }

    /// 处理订阅状态更新通知
    @objc private func handleSubscriptionStatusUpdate() {
        DispatchQueue.main.async {
            self.updateUsageCountDisplay()
        }
    }
}
