//
//  NailStyleViewController.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import UIKit
import SnapKit
import SVProgressHUD
import SDWebImage

class NailStyleViewController: UIViewController {
    
    // MARK: - Properties
    private var nailModels: [NailModel] = []
    private var filteredModels: [NailModel] = []
    private let dataManager = NailDataManager.shared
    
    // MARK: - UI Elements
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 15
        layout.minimumInteritemSpacing = 10
        layout.sectionInset = UIEdgeInsets(top: 20, left: 15, bottom: 20, right: 15)
        
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .white
        cv.delegate = self
        cv.dataSource = self
        cv.register(NailStyleCell.self, forCellWithReuseIdentifier: "NailStyleCell")
        return cv
    }()
    
    private lazy var segmentedControl: UISegmentedControl = {
        let items = ["全部", "女性", "男性", "VIP", "免费"]
        let sc = UISegmentedControl(items: items)
        sc.selectedSegmentIndex = 0
        sc.addTarget(self, action: #selector(segmentChanged(_:)), for: .valueChanged)
        return sc
    }()
    
    private lazy var searchBar: UISearchBar = {
        let sb = UISearchBar()
        sb.placeholder = "搜索美甲样式..."
        sb.delegate = self
        return sb
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "美甲样式"
        view.backgroundColor = .white
        
        view.addSubview(searchBar)
        view.addSubview(segmentedControl)
        view.addSubview(collectionView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        searchBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        segmentedControl.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom).offset(10)
            make.left.equalToSuperview().offset(15)
            make.right.equalToSuperview().offset(-15)
            make.height.equalTo(32)
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(segmentedControl.snp.bottom).offset(10)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Data Loading
    private func loadData() {
        SVProgressHUD.show(withStatus: "加载中...")
        
        dataManager.loadNailData { [weak self] result in
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()
                
                switch result {
                case .success(let models):
                    self?.nailModels = models
                    self?.filteredModels = models
                    self?.collectionView.reloadData()
                    print("✅ 美甲数据加载成功，共 \(models.count) 个样式")
                    
                case .failure(let error):
                    print("❌ 美甲数据加载失败: \(error.localizedDescription)")
                    SVProgressHUD.showError(withStatus: "加载失败")
                }
            }
        }
    }
    
    // MARK: - Actions
    @objc private func segmentChanged(_ sender: UISegmentedControl) {
        switch sender.selectedSegmentIndex {
        case 0: // 全部
            filteredModels = nailModels
        case 1: // 女性
            filteredModels = dataManager.getModelsBySex(0)
        case 2: // 男性
            filteredModels = dataManager.getModelsBySex(1)
        case 3: // VIP
            filteredModels = dataManager.getVipModels()
        case 4: // 免费
            filteredModels = dataManager.getFreeModels()
        default:
            filteredModels = nailModels
        }
        
        collectionView.reloadData()
    }
}

// MARK: - UICollectionViewDataSource
extension NailStyleViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return filteredModels.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "NailStyleCell", for: indexPath) as! NailStyleCell
        let model = filteredModels[indexPath.item]
        cell.configure(with: model)
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension NailStyleViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let model = filteredModels[indexPath.item]
        print("选择了美甲样式: \(model.name)")
        
        // 这里可以添加选择美甲样式后的处理逻辑
        // 比如跳转到美甲处理页面
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension NailStyleViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (collectionView.frame.width - 40) / 2 // 两列布局，减去边距
        return CGSize(width: width, height: width * 1.3) // 高度稍大一些
    }
}

// MARK: - UISearchBarDelegate
extension NailStyleViewController: UISearchBarDelegate {
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {
        if searchText.isEmpty {
            // 根据当前选中的分段重新筛选
            segmentChanged(segmentedControl)
        } else {
            filteredModels = dataManager.searchModels(keyword: searchText)
            collectionView.reloadData()
        }
    }
    
    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
    }
}

// MARK: - NailStyleCell
class NailStyleCell: UICollectionViewCell {
    
    private let imageView = UIImageView()
    private let nameLabel = UILabel()
    private let vipBadge = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.backgroundColor = .white
        contentView.layer.cornerRadius = 8
        contentView.layer.shadowColor = UIColor.black.cgColor
        contentView.layer.shadowOffset = CGSize(width: 0, height: 2)
        contentView.layer.shadowOpacity = 0.1
        contentView.layer.shadowRadius = 4
        
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 6
        
        nameLabel.font = UIFont.systemFont(ofSize: 14)
        nameLabel.textAlignment = .center
        nameLabel.numberOfLines = 2
        
        vipBadge.text = "VIP"
        vipBadge.font = UIFont.boldSystemFont(ofSize: 10)
        vipBadge.textColor = .white
        vipBadge.backgroundColor = .systemOrange
        vipBadge.textAlignment = .center
        vipBadge.layer.cornerRadius = 8
        vipBadge.clipsToBounds = true
        vipBadge.isHidden = true
        
        contentView.addSubview(imageView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(vipBadge)
        
        imageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(8)
            make.height.equalTo(imageView.snp.width).multipliedBy(1.2)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(8)
            make.bottom.equalToSuperview().inset(8)
        }
        
        vipBadge.snp.makeConstraints { make in
            make.top.right.equalTo(imageView).inset(4)
            make.width.equalTo(30)
            make.height.equalTo(16)
        }
    }
    
    func configure(with model: NailModel) {
        nameLabel.text = model.name
        vipBadge.isHidden = !model.isVip

        // 使用SDWebImage加载图片
        if let url = URL(string: model.image) {
            imageView.sd_setImage(with: url, placeholderImage: UIImage(named: "placeholder_nail")) { [weak self] image, error, cacheType, url in
                if let error = error {
                    print("图片加载失败: \(error.localizedDescription)")
                    // 可以设置一个默认图片
                    self?.imageView.image = UIImage(named: "default_nail")
                }
            }
        } else {
            imageView.image = UIImage(named: "default_nail")
        }
    }
}
