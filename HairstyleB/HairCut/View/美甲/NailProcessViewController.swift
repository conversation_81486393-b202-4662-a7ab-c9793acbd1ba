//
//  NailProcessViewController.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import UIKit
import SnapKit
import SVProgressHUD
import SDWebImage
import UMCommon

@available(iOS 13.0, *)
class NailProcessViewController: UIViewController {
    
    // MARK: - Properties
    private var originalImage: UIImage  // 保存原始图片
    private var userImage: UIImage      // 当前显示的图片
    private var nailModels: [NailModel] = []
    private var selectedNailModel: NailModel?
    private var selectedNailShapeIndex: Int = 0  // 默认选中第一个甲型
    private var processedImage: UIImage?
    private var preselectedNailId: String? // 预选的美甲ID

    // 添加次数按钮
    private var addUsageButton: UIButton!

    // 主题颜色
    private let themeColor = UIColor.hex(string: "#FFEC53")
    private let selectedShapeColor = UIColor.hex(string: "#FFF8C5")
    private let unselectedColor = UIColor.hex(string: "#F7F7F7")
    private let selectedTextColor = UIColor.hex(string: "#333333")
    private let unselectedTextColor = UIColor.hex(string: "#999999")

    // 甲型数据
    private let nailShapes = [
        ("杏形", "杏形美甲"),
        ("圆形", "圆形美甲"),
        ("尖形", "尖形美甲"),
        ("方形", "方形美甲")
    ]
    
    // MARK: - UI Elements
    // 返回按钮
    private lazy var backButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "返回"), for: .normal)
        btn.tintColor = .black
        btn.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        return btn
    }()

    // 剩余次数标签
    private lazy var remainingCountLabel: UILabel = {
        let label = UILabel()
        label.text = local("剩余次数: ") + "0"
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = unselectedTextColor
        label.textAlignment = .left
        return label
    }()

    // 重拍按钮
    private lazy var retakeButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "重拍"), for: .normal)
        btn.addTarget(self, action: #selector(retakeButtonTapped), for: .touchUpInside)
        return btn
    }()

    // 保存按钮
    private lazy var saveButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle(local("保存"), for: .normal)
        btn.setTitleColor(.black, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        btn.backgroundColor = themeColor
        btn.layer.cornerRadius = 15
        btn.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        return btn
    }()

    // 图片容器视图
    private lazy var imageContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.hex(string: "#F7F7F7")
        view.layer.cornerRadius = 12
        view.clipsToBounds = true
        return view
    }()

    private lazy var userImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFit
        iv.clipsToBounds = true
        return iv
    }()

    // 颜色标题
    private lazy var colorTitleLabel: UILabel = {
        let label = UILabel()
        label.text = local("颜色：")
        label.font = UIFont.boldSystemFont(ofSize: 16)
        label.textColor = selectedTextColor
        return label
    }()

    // 美甲类型集合视图
    private lazy var nailTypeCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        layout.sectionInset = UIEdgeInsets(top: 0, left: 20, bottom: 0, right: 20)

        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .clear
        cv.delegate = self
        cv.dataSource = self
        cv.showsHorizontalScrollIndicator = false
        cv.register(NailTypeCell.self, forCellWithReuseIdentifier: "NailTypeCell")
        return cv
    }()

    // 甲型标题
    private lazy var shapeTitleLabel: UILabel = {
        let label = UILabel()
        label.text = local("甲型：")
        label.font = UIFont.boldSystemFont(ofSize: 16)
        label.textColor = selectedTextColor
        return label
    }()

    // 甲型选择容器
    private lazy var shapeSelectionView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var processButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle(local("开始"), for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.backgroundColor = themeColor
        btn.layer.cornerRadius = 25
        btn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        btn.addTarget(self, action: #selector(processButtonTapped), for: .touchUpInside)
        btn.isEnabled = false
        btn.alpha = 0.5
        return btn
    }()



    // 甲型按钮数组
    private var shapeButtons: [UIButton] = []
    
    // MARK: - Initialization
    init(userImage: UIImage, preselectedNailId: String? = nil) {
        self.userImage = userImage
        self.originalImage = userImage  // 保存原始图片
        self.preselectedNailId = preselectedNailId
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupAddUsageButton()
        setupUI()

        updateUsageCountDisplay()
        loadNailData()

        // 监听次数和订阅状态更新通知
        NotificationCenter.default.addObserver(self, selector: #selector(handleUsageCountUpdate), name: .usageCountDidUpdate, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleSubscriptionStatusUpdate), name: .subscriptionStatusDidUpdate, object: nil)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 隐藏导航栏
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 恢复导航栏显示
        navigationController?.setNavigationBarHidden(false, animated: animated)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = .white

        // 隐藏导航栏
        navigationController?.setNavigationBarHidden(true, animated: false)

        // 设置用户图片
        userImageView.image = userImage

        // 添加子视图
        view.addSubview(backButton)
        view.addSubview(remainingCountLabel)
        view.addSubview(addUsageButton)
        view.addSubview(retakeButton)
        view.addSubview(saveButton)
        view.addSubview(imageContainerView)
        imageContainerView.addSubview(userImageView)
        view.addSubview(colorTitleLabel)
        view.addSubview(nailTypeCollectionView)
        view.addSubview(shapeTitleLabel)
        view.addSubview(shapeSelectionView)
        view.addSubview(processButton)

        setupShapeButtons()
        setupConstraints()
    }
    
    private func setupShapeButtons() {
        for (index, shape) in nailShapes.enumerated() {
            let button = UIButton(type: .custom)
            button.tag = index
            button.addTarget(self, action: #selector(shapeButtonTapped(_:)), for: .touchUpInside)

            // 使用ZYEButtonPositionAndSpace设置上图下文布局
            button.setTitle(local(shape.0), for: .normal)
            button.setTitleColor(index == 0 ? selectedTextColor : unselectedTextColor, for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 12)
            button.backgroundColor = index == 0 ? selectedShapeColor : unselectedColor
            button.layer.cornerRadius = 8

            // 设置图片
            let normalImageName = "甲型\(index + 1)_normal"
            let selectedImageName = "甲型\(index + 1)_select"
            button.setImage(UIImage(named: index == 0 ? selectedImageName : normalImageName), for: .normal)

            // 设置图片在上，文字在下的布局
            button.imageView?.contentMode = .scaleAspectFit
            button.setImagePosition(with: .top, spacing: 5)

            shapeSelectionView.addSubview(button)
            shapeButtons.append(button)
        }
    }

    private func setupConstraints() {
        // 计算图片显示区域尺寸 - 为小屏幕优化
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        let isIPad = UIDevice.current.userInterfaceIdiom == .pad
        let isSmallScreen = screenHeight <= 667 // iPhone SE, iPhone 8及以下

        let imageSize: CGFloat
        if isIPad {
            imageSize = screenWidth * 0.7
        } else if isSmallScreen {
            imageSize = screenWidth * 0.75 // 小屏幕使用更小的图片
        } else {
            imageSize = screenWidth * 0.85 // 大屏幕稍微小一点
        }

        // 返回按钮
        backButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(10)
            make.left.equalToSuperview().offset(16)
            make.width.height.equalTo(30)
        }

        // 剩余次数标签
        remainingCountLabel.snp.makeConstraints { make in
            make.centerY.equalTo(backButton)
            make.left.equalTo(backButton.snp.right).offset(15)
            make.height.equalTo(20)
        }

        // 添加次数按钮
        addUsageButton.snp.makeConstraints { make in
            make.centerY.equalTo(remainingCountLabel)
            make.left.equalTo(remainingCountLabel.snp.right).offset(8)
            make.width.height.equalTo(24)
        }

        // 重拍按钮
        retakeButton.snp.makeConstraints { make in
            make.centerY.equalTo(backButton)
            make.right.equalTo(saveButton.snp.left).offset(-15)
            make.width.height.equalTo(30)
        }

        // 保存按钮
        saveButton.snp.makeConstraints { make in
            make.centerY.equalTo(backButton)
            make.right.equalToSuperview().offset(-20)
            make.width.equalTo(60)
            make.height.equalTo(30)
        }

        // 图片容器视图
        imageContainerView.snp.makeConstraints { make in
            make.top.equalTo(backButton.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(imageSize)
        }

        userImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(10)
        }

        // 颜色标题
        colorTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageContainerView.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(20)
            make.height.equalTo(20)
        }

        // 美甲类型集合视图
        nailTypeCollectionView.snp.makeConstraints { make in
            make.top.equalTo(colorTitleLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview()
            make.height.equalTo(80)
        }

        // 甲型标题
        shapeTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(nailTypeCollectionView.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(20)
            make.height.equalTo(20)
        }

        // 甲型选择视图
        shapeSelectionView.snp.makeConstraints { make in
            make.top.equalTo(shapeTitleLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(80)
        }

        // 甲型按钮布局
        let buttonWidth = (UIScreen.main.bounds.width - 80) / 4  // 减去左右边距和间距
        for (index, button) in shapeButtons.enumerated() {
            button.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(CGFloat(index) * (buttonWidth + 10))
                make.top.equalToSuperview()
                make.width.equalTo(buttonWidth)
                make.height.equalTo(80)
            }
        }

        // 开始按钮 - 布局到底部safearea.bottom-12
        processButton.snp.makeConstraints { make in
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-12)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(50)
        }
    }
    
    // MARK: - Data Loading
    private func loadNailData() {
        SVProgressHUD.show(withStatus: local("加载美甲样式..."))
        
        NailModel.fetchNailData { [weak self] result in
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()
                
                switch result {
                case .success(let models):
                    self?.nailModels = models
                    self?.nailTypeCollectionView.reloadData()

                    // 如果有预选的美甲ID，自动选中对应的美甲类型
                    if let preselectedId = self?.preselectedNailId {
                        self?.selectNailModelById(preselectedId)
                    }

                    print("✅ 加载了 \(models.count) 个美甲样式")
                    
                case .failure(let error):
                    print("❌ 加载美甲样式失败: \(error.localizedDescription)")
                    SVProgressHUD.showError(withStatus: local("加载失败"))
                }
            }
        }
    }

    // 根据ID选中美甲类型
    private func selectNailModelById(_ nailId: String) {
        // 在美甲模型中查找匹配的ID
        if let matchingIndex = nailModels.firstIndex(where: { $0.id == nailId }) {
            let matchingModel = nailModels[matchingIndex]
            selectedNailModel = matchingModel
            nailTypeCollectionView.reloadData()
            updateProcessButton()

            // 滚动到对应的cell位置，确保选中的cell可见
            DispatchQueue.main.async {
                let indexPath = IndexPath(item: matchingIndex, section: 0)
                self.nailTypeCollectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
            }

            print("✅ 自动选中了美甲样式: \(matchingModel.name)，索引: \(matchingIndex)")
        } else {
            print("⚠️ 未找到ID为 \(nailId) 的美甲样式")
        }
    }

    // MARK: - Actions
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }

    @objc private func saveButtonTapped() {
        MobClick.event("AI_Nail_art", attributes: ["Source": "保存"])

        guard let currentImage = userImageView.image else {
            SVProgressHUD.showError(withStatus: local("没有可保存的图片"))
            return
        }

        // 保存当前显示的图片到相册
        UIImageWriteToSavedPhotosAlbum(currentImage, self, #selector(image(_:didFinishSavingWithError:contextInfo:)), nil)
    }

    @objc private func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            SVProgressHUD.showError(withStatus: local("保存失败: ") + "\(error.localizedDescription)")
        } else {
            SVProgressHUD.showSuccess(withStatus: local("保存成功"))
        }
    }

    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }

    @objc private func retakeButtonTapped() {
        MobClick.event("AI_Nail_art", attributes: ["Source": "切换相机"])

        // 调用拍照页面 - 直接present，不使用导航控制器
        let cameraVC = NailCameraViewController()
        cameraVC.delegate = self
        cameraVC.modalPresentationStyle = .fullScreen
        present(cameraVC, animated: true)
    }

    @objc private func shapeButtonTapped(_ sender: UIButton) {
        let newIndex = sender.tag

        // 更新选中状态
        for (index, button) in shapeButtons.enumerated() {
            let isSelected = index == newIndex
            button.backgroundColor = isSelected ? selectedShapeColor : unselectedColor
            button.setTitleColor(isSelected ? selectedTextColor : unselectedTextColor, for: .normal)

            // 更新图片
            let imageName = isSelected ? "甲型\(index + 1)_select" : "甲型\(index + 1)_normal"
            button.setImage(UIImage(named: imageName), for: .normal)
        }

        selectedNailShapeIndex = newIndex
        print("选择了甲型: \(nailShapes[newIndex].0)")
    }
    
    @objc private func processButtonTapped() {
        MobClick.event("AI_Nail_art", attributes: ["Source": "点击生成"])

        guard let selectedModel = selectedNailModel else {
            SVProgressHUD.showError(withStatus: local("请先选择美甲样式"))
            return
        }

        // 直接开始处理，用户可以多次点击来更换生成结果
        startNailProcessing(with: selectedModel)
    }

    private func startNailProcessing(with selectedModel: NailModel) {
        // 先检查次数，如果不足则直接弹出购买弹窗，不显示HUD
        guard VolcanoUsageManager.shared.canUse() else {
            // 次数不足，直接显示购买弹窗
            VolcanoEngineAPI.showUsagePurchasePopup(from: "AI美甲") {
                // 购买成功后可以重新尝试
            }
            return
        }

        // 显示处理进度
        SVProgressHUD.show(withStatus: local("正在提交任务..."))
        SVProgressHUD.setDefaultMaskType(.black) // 防止用户操作

        // 禁用处理按钮防止重复点击
        processButton.isEnabled = false
        processButton.alpha = 0.5

        // 开始处理流程
        startProcessingWithProgressUpdates(selectedModel: selectedModel)
    }

    private func startProcessingWithProgressUpdates(selectedModel: NailModel) {
        // 模拟进度更新
        updateProgressStatus("正在提交任务...")

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.updateProgressStatus("任务已提交，正在处理...")
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            self.updateProgressStatus("AI正在生成美甲效果...")
        }

        // 构建prompt
        var finalPrompt = selectedModel.prompt
        if selectedNailShapeIndex != 0 {
            // 如果不是默认甲型，添加甲型描述
            let shapeDescription = nailShapes[selectedNailShapeIndex].1
            finalPrompt = "\(shapeDescription)。\(selectedModel.prompt)"
        }

        // 创建临时模型用于处理
        var processModel = selectedModel
        processModel.prompt = finalPrompt

        VolcanoEngineAPI.processNailWithModel(
            image: originalImage,  // 使用原始图片
            nailModel: processModel
        ) { [weak self] result in
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()

                // 恢复按钮状态
                self?.processButton.isEnabled = true
                self?.processButton.alpha = 1.0

                switch result {
                case .success(let processedImage):
                    // 处理成功，更新次数显示
                    self?.updateUsageCountDisplay()
                    self?.showProcessedResult(processedImage)

                case .failure(let error):
                    print("❌ 美甲处理失败: \(error.localizedDescription)")

                    // 检查是否为次数不足错误
                    if let nsError = error as? NSError, nsError.domain == "VolcanoUsageLimit" {
                        // 次数不足时不显示错误HUD，购买弹窗会延迟显示
                        print("💡 次数不足，购买弹窗即将显示")
                    } else {
                        // 其他错误才显示错误提示
                        let errorMessage = self?.getErrorMessage(from: error) ?? local("处理失败，请重试")
                        SVProgressHUD.showError(withStatus: errorMessage)
                    }
                }
            }
        }
    }

    private func updateProgressStatus(_ message: String) {
        SVProgressHUD.show(withStatus: message)
    }

    private func getErrorMessage(from error: Error) -> String {
        let errorDescription = error.localizedDescription

        if errorDescription.contains("TimeoutError") {
            return local("处理超时，请重试")
        } else if errorDescription.contains("ImageError") {
            return local("图片格式错误")
        } else if errorDescription.contains("APIError") {
            return local("服务暂时不可用")
        } else if errorDescription.contains("网络") || errorDescription.contains("Network") {
            return local("网络连接失败")
        } else {
            return local("处理失败，请重试")
        }
    }
    
    

    
    
    // MARK: - Helper Methods
    private func showProcessedResult(_ image: UIImage) {
        processedImage = image

        // 将处理结果显示在图片视图中
        userImageView.image = image

        // 显示保存按钮
        saveButton.isHidden = false

        // 添加美甲生成成功埋点
        let nailName = selectedNailModel?.name ?? "未知美甲"
        MobClick.event("AI_Nail_art", attributes: ["Generated_successfully": "\(nailName)生成成功"])

        // 显示成功提示
        SVProgressHUD.showSuccess(withStatus: local("美甲处理完成！"))

        // 可选：添加一个轻微的动画效果
        UIView.transition(with: userImageView, duration: 0.3, options: .transitionCrossDissolve, animations: {
            // 动画已在上面设置image时完成
        }, completion: nil)
    }
    
    private func updateProcessButton() {
        let isEnabled = selectedNailModel != nil
        processButton.isEnabled = isEnabled
        processButton.alpha = isEnabled ? 1.0 : 0.5
    }
}

// MARK: - UICollectionViewDataSource
extension NailProcessViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return nailModels.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "NailTypeCell", for: indexPath) as! NailTypeCell
        let model = nailModels[indexPath.item]
        let isSelected = selectedNailModel?.id == model.id
        cell.configure(with: model, isSelected: isSelected, themeColor: themeColor)
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension NailProcessViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedNailModel = nailModels[indexPath.item]
        nailTypeCollectionView.reloadData()
        updateProcessButton()

        print("选择了美甲样式: \(selectedNailModel?.name ?? "")")
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension NailProcessViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // 按照972:672的比例计算，高度60，宽度约为87
        let height: CGFloat = 60
        let width = height * (972.0 / 672.0) // 约为87
        return CGSize(width: width, height: height)
    }
}

// MARK: - NailCameraViewControllerDelegate
extension NailProcessViewController: NailCameraViewControllerDelegate {
    func nailCameraDidSelectImage(_ image: UIImage) {
        // 更新原始图片和显示图片
        originalImage = image
        userImage = image
        userImageView.image = image

        // 隐藏保存按钮，重置处理状态
        saveButton.isHidden = true
        processedImage = nil

        print("重新选择了图片")
    }

    func nailCameraDidCancel() {
        // 相机取消，不需要特殊处理
    }
}

// MARK: - NailTypeCell
class NailTypeCell: UICollectionViewCell {

    private let imageView = UIImageView()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        contentView.backgroundColor = .clear
        contentView.layer.cornerRadius = 8
        contentView.clipsToBounds = true

        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8

        contentView.addSubview(imageView)

        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    func configure(with model: NailModel, isSelected: Bool, themeColor: UIColor) {
        // 使用SDWebImage加载图片
        if let url = URL(string: model.image) {
            imageView.sd_setImage(with: url, placeholderImage: UIImage(named: "placeholder_nail"))
        } else {
            imageView.image = UIImage(named: "default_nail")
        }

        // 选中状态的边框
        contentView.layer.borderWidth = isSelected ? 3 : 0
        contentView.layer.borderColor = isSelected ? themeColor.cgColor : UIColor.clear.cgColor
    }
}

// MARK: - 次数显示相关
@available(iOS 13.0, *)
extension NailProcessViewController {

    /// 设置添加次数按钮
    private func setupAddUsageButton() {
        // 创建添加次数按钮
        addUsageButton = UIButton(type: .custom)
        addUsageButton.setImage(UIImage(named: "添加次数按钮"), for: .normal)
        addUsageButton.addTarget(self, action: #selector(addUsageButtonTapped), for: .touchUpInside)
    }

    /// 更新次数显示
    private func updateUsageCountDisplay() {
        let remaining = VolcanoUsageManager.shared.getRemainingCount()
        remainingCountLabel.text = local("剩余次数: ") + "\(remaining)"
    }

    /// 添加次数按钮点击
    @objc private func addUsageButtonTapped() {
        VolcanoEngineAPI.showUsagePurchasePopup(from: "AI美甲") {
            // 次数更新通过通知自动处理，无需手动调用
        }
    }

    /// 处理次数更新通知
    @objc private func handleUsageCountUpdate() {
        DispatchQueue.main.async {
            self.updateUsageCountDisplay()
        }
    }

    /// 处理订阅状态更新通知
    @objc private func handleSubscriptionStatusUpdate() {
        DispatchQueue.main.async {
            self.updateUsageCountDisplay()
        }
    }
}
