//
//  BeautyParameterCell.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import UIKit
import SnapKit

class BeautyParameterCell: UICollectionViewCell {
    
    // MARK: - UI Components
    private let imageView = UIImageView()
    private let titleLabel = UILabel()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .white
        layer.cornerRadius = 8
        
        // Setup image view
        imageView.contentMode = .scaleAspectFit
        contentView.addSubview(imageView)
        
        // Setup title label
        titleLabel.textAlignment = .center
        titleLabel.font = UIFont.systemFont(ofSize: 12)
        titleLabel.textColor = UIColor.hex(string: "#999999")
        contentView.addSubview(titleLabel)
    }
    
    private func setupConstraints() {
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(4)
            make.bottom.equalToSuperview().offset(-8)
        }
    }
    
    // MARK: - Configuration
    func configure(with parameter: BeautyParameter, isSelected: Bool) {
        titleLabel.text = parameter.cnname

        // 设置图片
        let imageName = isSelected ? "\(parameter.cnname) 1" : parameter.cnname
        imageView.image = UIImage(named: imageName)

        // 设置选中状态的样式
        if isSelected {
            backgroundColor = UIColor.hex(string: "#FFF8C5")
            titleLabel.textColor = UIColor.hex(string: "#333333")
        } else {
            backgroundColor = .white
            titleLabel.textColor = UIColor.hex(string: "#999999")
        }

        // 检查是否有临时参数值，如果有则显示不同的视觉效果
        if let tempValue = BeautyFilterManager.shared.getTemporaryParameter(parameter.name),
           tempValue != parameter.currentValue {
            // 有临时修改，可以添加一个小的指示器
            layer.borderWidth = 1
            layer.borderColor = UIColor.hex(string: "#FFEC53").cgColor
        } else {
            layer.borderWidth = 0
            layer.borderColor = UIColor.clear.cgColor
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
        titleLabel.text = nil
        backgroundColor = .white
        titleLabel.textColor = UIColor.hex(string: "#999999")
    }
}
