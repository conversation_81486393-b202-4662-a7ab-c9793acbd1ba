//
//  HairFunctionCell.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import UIKit
import SnapKit

class HairFunctionCell: UICollectionViewCell {
    
    // MARK: - UI Components
    private let imageView = UIImageView()
    private let nameLabel = UILabel()
    
    // MARK: - Properties
    private var hairFunction: HairEditFunction?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .clear
        layer.cornerRadius = 10
        layer.masksToBounds = true

        // Setup image view
        imageView.backgroundColor = .clear
        imageView.layer.cornerRadius = 10
        imageView.clipsToBounds = true
        contentView.addSubview(imageView)

        // Setup name label
        nameLabel.textAlignment = .center
        nameLabel.font = UIFont.systemFont(ofSize: 12)
        nameLabel.textColor = .white
        nameLabel.numberOfLines = 1
        nameLabel.backgroundColor = .black.withAlphaComponent(0.5)
        imageView.addSubview(nameLabel)
    }

    private func setupConstraints() {
        // 约束会在 configure 方法中根据类型动态设置
    }
    
    // MARK: - Configuration
    func configure(with hairFunction: HairEditFunction, isSelected: Bool) {
        self.hairFunction = hairFunction

        // 清除之前的约束
        imageView.snp.removeConstraints()
        nameLabel.snp.removeConstraints()

        // 设置图片
        imageView.image = UIImage(named: hairFunction.imageName)

        // 根据是否是原图按钮设置不同的样式和布局
        if hairFunction.isOriginal {
            // 原图按钮：46*95，#F7F7F7背景，24*24图标居中
            nameLabel.isHidden = true
            backgroundColor = UIColor.hex(string: "#F7F7F7")
            imageView.contentMode = .center

            // 图片约束：24*24居中
            imageView.snp.makeConstraints { make in
                make.center.equalToSuperview()
                make.width.height.equalTo(24)
            }

        } else {
            // 其他功能按钮：80*95，图片80*75，文字label 80*20贴着图片底边
            nameLabel.isHidden = false
            nameLabel.text = hairFunction.name
            backgroundColor = .clear
            imageView.contentMode = .scaleAspectFill

            // 图片约束：80*75，顶部对齐
            imageView.snp.makeConstraints { make in
                make.top.left.right.equalToSuperview()
                make.height.equalTo(95)
            }

            // 文字约束：80*20，贴着图片底边
            nameLabel.snp.makeConstraints { make in
                make.bottom.left.right.equalToSuperview()
                make.height.equalTo(20)
            }
        }

        // 设置选中状态
        updateSelectionState(isSelected: isSelected)
    }

    private func updateSelectionState(isSelected: Bool) {
        if isSelected {
            // 选中时显示黄色边框，1.5宽
            layer.borderWidth = 1.5
            layer.borderColor = UIColor.hex(string: "#FFEC53").cgColor
        } else {
            // 未选中时无边框
            layer.borderWidth = 0
            layer.borderColor = UIColor.clear.cgColor
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
        nameLabel.text = nil
        nameLabel.isHidden = false
        backgroundColor = .clear
        layer.borderWidth = 0
        layer.borderColor = UIColor.clear.cgColor

        // 清除约束，准备重新设置
        imageView.snp.removeConstraints()
        nameLabel.snp.removeConstraints()
    }
}
