//
//  DeveloperAppCell.swift
//  HairCut
//
//  Created by AI Assistant on 2025/01/21.
//

import Foundation
import UIKit
import SnapKit
import SDWebImage

class DeveloperAppCell: UICollectionViewCell {
    static let identifier = "DeveloperAppCell"
    
    // MARK: - UI Elements
    private let logoImageView = UIImageView()
    private let nameLabel = UILabel()
    
    // MARK: - Properties
    var app: DeveloperApp? {
        didSet {
            configureCell()
        }
    }
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        backgroundColor = UIColor.white
        layer.cornerRadius = 12
        layer.shadowColor = UIColor.black.withAlphaComponent(0.05).cgColor
        layer.shadowOffset = CGSize(width: 0, height: 1)
        layer.shadowRadius = 2
        layer.shadowOpacity = 1

        // Logo图片 - 左侧
        logoImageView.contentMode = .scaleAspectFit
        logoImageView.layer.cornerRadius = 8
        logoImageView.clipsToBounds = true
        logoImageView.backgroundColor = UIColor(valueRGB: 0xF5F5F5)
        contentView.addSubview(logoImageView)

        // 应用名称 - 右侧
        nameLabel.font = UIFont.systemFont(ofSize: 14)
        nameLabel.textColor = UIColor(valueRGB: 0x333333)
        nameLabel.textAlignment = .left
        nameLabel.numberOfLines = 2
        contentView.addSubview(nameLabel)

        // 设置约束 - 横向布局
        logoImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
        }

        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(logoImageView.snp.right).offset(12)
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
        }
    }
    
    // MARK: - Configuration
    private func configureCell() {
        guard let app = app else { return }

        // 设置应用名称（根据当前语言环境显示）
        nameLabel.text = app.displayName
        
        // 加载logo图片
        if let logoURL = URL(string: app.logo) {
            logoImageView.sd_setImage(
                with: logoURL,
                placeholderImage: UIImage(named: "app_placeholder"),
                options: [.refreshCached],
                completed: nil
            )
        } else {
            logoImageView.image = UIImage(named: "app_placeholder")
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        logoImageView.image = nil
        nameLabel.text = nil
    }
}
