//
//  SettingView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/7/30.
//

import Foundation
import UIKit
import SnapKit
import SDWebImage

protocol SettingViewDelgate: NSObjectProtocol {
    func aboutUsClickAction()
    func termsOfUseClickAction()
    func privacyAgreementClickAction()
    func changeIconImage(imageString: String)
}

class SettingView: UIView {
    public let settingView = UIView()
    private let aboutUsButtonView = SettingButtonView("about_us".localized)
    private let termsOfUseButtonView = SettingButtonView("terms_of_use".localized)
    private let privacyAgreementView = SettingButtonView("privacy_policy".localized)
    public let iconView = UIView()
    public let iconTitltLebel = UILabel()
    private var iconCollectionView: UICollectionView? = nil

    // 更多应用相关
    private let moreAppsView = UIView()
    private let moreAppsTitleLabel = UILabel()
    private var moreAppsCollectionView: UICollectionView? = nil
    private var developerApps: [DeveloperApp] = []
    
    weak public var delegate: SettingViewDelgate?
    private var iconImageArr = ["AppIcon 1","AppIcon 2","AppIcon 3","AppIcon 4","AppIcon 5"]
    
    init() {
        super.init(frame: .zero)
        self.initView()
        self.buildIconCollectionView()
        self.buildMoreAppsView()
        self.loadDeveloperApps()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.backgroundColor = UIColor(valueRGB: 0xF9F9F9)
        
        self.settingView.layer.cornerRadius = 18
        self.settingView.backgroundColor = UIColor.white
        self.addSubview(self.settingView)
        self.settingView.snp.makeConstraints { make in
            make.top.equalTo(98)
            make.left.equalTo(18)
            make.right.equalTo(-18)
            make.height.equalTo(216)
        }

        self.aboutUsButtonView.layer.cornerRadius = 18
        self.aboutUsButtonView.isUserInteractionEnabled = true
        self.settingView.addSubview(self.aboutUsButtonView)
        self.aboutUsButtonView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(72)
        }
        let aboutUsTapGestureRecognizer = UITapGestureRecognizer()
        aboutUsTapGestureRecognizer.addTarget(self, action: #selector(aboutUsClickAction))
        self.aboutUsButtonView.addGestureRecognizer(aboutUsTapGestureRecognizer)
        
        self.termsOfUseButtonView.layer.cornerRadius = 18
        self.termsOfUseButtonView.isUserInteractionEnabled = true
        self.settingView.addSubview(self.termsOfUseButtonView)
        self.termsOfUseButtonView.snp.makeConstraints { make in
            make.top.equalTo(self.aboutUsButtonView.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(72)
        }
        let termsOfUseTapGestureRecognizer = UITapGestureRecognizer()
        termsOfUseTapGestureRecognizer.addTarget(self, action: #selector(termsOfUseClickAction))
        self.termsOfUseButtonView.addGestureRecognizer(termsOfUseTapGestureRecognizer)
        
        self.privacyAgreementView.layer.cornerRadius = 18
        self.privacyAgreementView.isUserInteractionEnabled = true
        self.settingView.addSubview(self.privacyAgreementView)
        self.privacyAgreementView.snp.makeConstraints { make in
            make.top.equalTo(self.termsOfUseButtonView.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(72)
        }
        let privacyAgreementTapGestureRecognizer = UITapGestureRecognizer()
        privacyAgreementTapGestureRecognizer.addTarget(self, action: #selector(privacyAgreementClickAction))
        self.privacyAgreementView.addGestureRecognizer(privacyAgreementTapGestureRecognizer)
        
        self.iconView.layer.cornerRadius = 18
        self.iconView.backgroundColor = UIColor.white
        self.iconView.isUserInteractionEnabled = true
        self.addSubview(self.iconView)
        self.iconView.snp.makeConstraints { make in
            make.top.equalTo(self.settingView.snp.bottom).offset(12)
            make.left.equalTo(18)
            make.right.equalTo(-18)
            make.height.equalTo(138)
        }
        
        self.iconTitltLebel.text = "change_main_screen_icon".localized
        self.iconTitltLebel.adjustsFontSizeToFitWidth = true
        self.iconTitltLebel.textColor = UIColor(valueRGB: 0x333333)
        self.iconTitltLebel.textAlignment = .left
        self.iconTitltLebel.numberOfLines = 0
        self.iconTitltLebel.font = UIFont.boldSystemFont(ofSize: 15)
        self.iconView.addSubview(self.iconTitltLebel)
        self.iconTitltLebel.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(22)
            make.width.greaterThanOrEqualTo(150)
            make.height.greaterThanOrEqualTo(21)
        }

        // 更多应用视图
        self.moreAppsView.layer.cornerRadius = 18
        self.moreAppsView.backgroundColor = UIColor.white
        self.moreAppsView.isUserInteractionEnabled = true
        self.addSubview(self.moreAppsView)
        self.moreAppsView.snp.makeConstraints { make in
            make.top.equalTo(self.iconView.snp.bottom).offset(12)
            make.left.equalTo(18)
            make.right.equalTo(-18)
            make.bottom.equalToSuperview().offset(-20)
        }

        // 更多应用标题
        self.moreAppsTitleLabel.text = local("更多来自此开发者的应用")
        self.moreAppsTitleLabel.adjustsFontSizeToFitWidth = true
        self.moreAppsTitleLabel.textColor = UIColor(valueRGB: 0x333333)
        self.moreAppsTitleLabel.textAlignment = .left
        self.moreAppsTitleLabel.numberOfLines = 0
        self.moreAppsTitleLabel.font = UIFont.boldSystemFont(ofSize: 15)
        self.moreAppsView.addSubview(self.moreAppsTitleLabel)
        self.moreAppsTitleLabel.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(22)
            make.width.greaterThanOrEqualTo(200)
            make.height.greaterThanOrEqualTo(21)
        }
    }
    
    @objc func aboutUsClickAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.aboutUsClickAction)) != nil) {
            self.delegate?.aboutUsClickAction()
        }
    }
    
    @objc func termsOfUseClickAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.termsOfUseClickAction)) != nil) {
            self.delegate?.termsOfUseClickAction()
        }
    }
    
    @objc func privacyAgreementClickAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.privacyAgreementClickAction)) != nil) {
            self.delegate?.privacyAgreementClickAction()
        }
    }

    // MARK: - 更多应用相关方法

    /// 构建更多应用CollectionView
    private func buildMoreAppsView() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 0

        // 每个Cell占满宽度，高度为60
        let cellWidth = UIScreen.main.bounds.width - 76
        layout.itemSize = CGSize(width: cellWidth, height: 60)

        self.moreAppsCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        self.moreAppsView.addSubview(self.moreAppsCollectionView!)

        self.moreAppsCollectionView?.snp.makeConstraints { make in
            make.top.equalTo(self.moreAppsTitleLabel.snp.bottom).offset(15)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.bottom.equalTo(-20)
        }

        self.moreAppsCollectionView?.showsVerticalScrollIndicator = false
        self.moreAppsCollectionView?.backgroundColor = UIColor.white
        self.moreAppsCollectionView?.delegate = self
        self.moreAppsCollectionView?.dataSource = self
        self.moreAppsCollectionView?.register(DeveloperAppCell.self, forCellWithReuseIdentifier: DeveloperAppCell.identifier)
    }

    /// 加载开发者应用数据
    private func loadDeveloperApps() {
        DeveloperAppManager.shared.fetchDeveloperApps { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let apps):
                    self?.developerApps = apps
                    self?.moreAppsCollectionView?.reloadData()
                case .failure(let error):
                    print("❌ 加载开发者应用失败: \(error.localizedDescription)")
                }
            }
        }
    }


}

class SettingButtonView: UIView {
    public let titleLabel = UILabel()
    public let titleImageView = UIImageView()
    
    init(_ titleString: String) {
        super.init(frame: .zero)
        self.initView(titleString)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView(_ titleString: String) {
        self.titleLabel.text = titleString
        self.titleLabel.textColor = UIColor(valueRGB: 0x333333)
        self.titleLabel.font = UIFont.boldSystemFont(ofSize: 15)
        self.titleLabel.adjustsFontSizeToFitWidth = true
        self.titleLabel.textAlignment = .left
        self.addSubview(self.titleLabel)
        self.titleLabel.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
            make.width.lessThanOrEqualTo(200)
        }
        
        self.titleImageView.image = UIImage(named: "right_arrow")
        self.titleImageView.contentMode = .scaleAspectFit
        self.addSubview(self.titleImageView)
        self.titleImageView.snp.makeConstraints { make in
            make.right.equalTo(-14)
            make.width.height.equalTo(24)
            make.centerY.equalToSuperview()
        }
    }
}

typealias SettingVCIconImage = SettingView
extension SettingVCIconImage: UICollectionViewDelegate,UICollectionViewDataSource {
    private func buildIconCollectionView() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 58, height: 58)
        layout.minimumLineSpacing = 11
        self.iconCollectionView = UICollectionView(frame: CGRect(x: 0, y: 16, width: self.settingView.frame.width, height: 58), collectionViewLayout: layout)
        self.iconCollectionView?.showsHorizontalScrollIndicator = false
        self.iconView.addSubview(self.iconCollectionView ?? UICollectionView())
        self.iconCollectionView?.snp.makeConstraints { make in
            make.top.equalTo(self.iconTitltLebel.snp.bottom).offset(14)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.height.equalTo(60)
            make.bottom.equalTo(-20)
        }
        self.iconCollectionView?.collectionViewLayout = layout
        self.iconCollectionView?.delegate = self
        self.iconCollectionView?.dataSource = self
        self.iconCollectionView?.backgroundColor = UIColor.white
        self.iconCollectionView?.register(SettingIconViewCell.self, forCellWithReuseIdentifier: SettingIconViewCell.identifier)
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == iconCollectionView {
            return iconImageArr.count
        } else if collectionView == moreAppsCollectionView {
            return developerApps.count
        }
        return 0
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if collectionView == iconCollectionView {
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: SettingIconViewCell.identifier, for: indexPath) as? SettingIconViewCell else {
                return UICollectionViewCell()
            }
            cell.imageUrlString = iconImageArr[indexPath.row]
            return cell
        } else if collectionView == moreAppsCollectionView {
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: DeveloperAppCell.identifier, for: indexPath) as? DeveloperAppCell else {
                return UICollectionViewCell()
            }
            cell.app = developerApps[indexPath.row]
            return cell
        }
        return UICollectionViewCell()
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if collectionView == iconCollectionView {
            if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("changeIconImage:"))) != nil) {
                self.delegate?.changeIconImage(imageString: iconImageArr[indexPath.row])
            }
        } else if collectionView == moreAppsCollectionView {
            let app = developerApps[indexPath.row]
            DeveloperAppManager.shared.openAppStore(appId: app.appid)
        }
    }
}
