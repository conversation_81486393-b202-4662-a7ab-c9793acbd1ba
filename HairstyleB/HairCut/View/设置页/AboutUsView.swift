//
//  AboutUsView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/7/31.
//

import Foundation
import UIKit
import SnapKit

class AboutUsView: UIView {
    private let iconImage = UIImageView()
    private let appNameLabel = UILabel()
    private let appMessageLabel = UILabel()
    private let appPlateMessageLabel = UILabel()
    
    init() {
        super.init(frame: .zero)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.setGradientBackground(colors: [UIColor(valueRGB: 0xFFF396), UIColor(valueRGB: 0xFFFFFF)], resizeBound: UIScreen.main.bounds)
        if let appIcon = UIApplication.shared.alternateIconName {
            self.iconImage.image = UIImage(named: appIcon)
        } else {
            self.iconImage.image = UIImage(named: AppSetting.appIconName)
        }
        self.iconImage.layer.cornerRadius = 18
        self.iconImage.layer.masksToBounds = true
        self.addSubview(self.iconImage)
        self.iconImage.snp.makeConstraints { make in
            make.width.equalTo(80)
            make.height.equalTo(80)
            make.centerX.equalToSuperview()
            make.top.equalTo(UIScreen.main.bounds.height * 269 / 812)
        }
        
        self.appNameLabel.text = "hairstyle_test".localized
        self.appNameLabel.textAlignment = .center
        self.appNameLabel.textColor = UIColor(valueRGB: 0x333333)
        self.appNameLabel.font = UIFont.systemFont(ofSize: 20)
        self.appNameLabel.numberOfLines = 0
        self.addSubview(self.appNameLabel)
        self.appNameLabel.snp.makeConstraints { make in
            make.top.equalTo(self.iconImage.snp.bottom).offset(16)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.height.greaterThanOrEqualTo(28)
        }
        
        if let infoDict = Bundle.main.infoDictionary,
           let version = infoDict["CFBundleShortVersionString"] as? String {
            self.appMessageLabel.text = "version".localized + "\(version)"
        }
        self.appMessageLabel.textAlignment = .center
        self.appMessageLabel.textColor = UIColor(valueRGB: 0x999999)
        self.appMessageLabel.font = UIFont.systemFont(ofSize: 14)
        self.appMessageLabel.numberOfLines = 0
        self.addSubview(self.appMessageLabel)
        self.appMessageLabel.snp.makeConstraints { make in
            make.top.equalTo(self.appNameLabel.snp.bottom).offset(6)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.height.greaterThanOrEqualTo(18)
        }
        
        self.appPlateMessageLabel.textAlignment = .center
        self.appPlateMessageLabel.text = "app_filling_number".localized
        self.appPlateMessageLabel.textColor = UIColor(valueRGB: 0x999999)
        self.appPlateMessageLabel.font = UIFont.systemFont(ofSize: 12)
        self.appPlateMessageLabel.numberOfLines = 0
        self.addSubview(self.appPlateMessageLabel)
        self.appPlateMessageLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-52)
            make.centerX.equalToSuperview()
            make.height.greaterThanOrEqualTo(44)
            make.left.equalTo(10)
            make.right.equalTo(-10)
        }
        //暂时屏蔽，等待备案号下来
        self.appPlateMessageLabel.isHidden = true
    }
    

}
