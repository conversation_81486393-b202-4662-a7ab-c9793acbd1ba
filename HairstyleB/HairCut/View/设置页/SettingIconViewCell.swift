//
//  SettingIconViewCell.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/1.
//

import Foundation
import UIKit
import SnapKit

class SettingIconViewCell: UICollectionViewCell {
    static let identifier = "SettingIconViewCell"
    
    private var imageView = UIImageView()
    public var imageUrlString: String? {
        willSet {
            if newValue != nil && newValue?.count ?? 0 > 0 {
                self.imageView.image = UIImage(named: newValue ?? "")
            } else {
                self.imageView.backgroundColor = UIColor(valueRGB: 0xD9D9D9)
            }
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.imageView.layer.cornerRadius = 12
        self.imageView.layer.masksToBounds = true
        self.imageView.isUserInteractionEnabled = true
        self.imageView.contentMode = .scaleAspectFit
        self.addSubview(self.imageView)
        self.imageView.snp.makeConstraints { make in
            make.width.equalTo(58)
            make.height.equalTo(58)
            make.left.right.equalToSuperview()
            make.top.equalTo(0)
        }
    }
}
