//
//  HomeView.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/7/30.
//

import Foundation
import UIKit
import SnapKit
import Alamofire
// 引入JXSegmentedView模块
import JXSegmentedView

protocol HomeViewDelegate: NSObjectProtocol {
    func aiStylistClickAction()
    func faceTextClickAction()
    func portraitBeautyClickAction()
    func popularHairCutDataAction(type: AIHairModel)
    func bannerClickAction(index: Int)
    func nailTypeClickAction(nailId: String) // 新增美甲类型点击事件
}

class HomeView: UIView {
    private let vipImageView = UIImageView()
    private let bannerScrollView = UIScrollView()
    private let bannerContainerView = UIView()
    private var bannerImageViews: [UIImageView] = []
    private var bannerTimer: Timer?
    private let aiStylistButton = UIButton(type: .custom)
    private let faceShapeTestButton = UIButton(type: .custom)
    private let portraitBeautyButton = UIButton(type: .custom)
    private let buttonsStackView = UIStackView()
    private let hotModelView = UIView()
    
    // JXSegmentedView相关成员
    private var segmentedView: JXSegmentedView!
    private var listContainerView: JXSegmentedListContainerView!
    private var segmentedDataSource: JXSegmentedTitleDataSource!
    private var tagBasedManager = TagBasedHairModelManager()
    
    weak public var delegate: HomeViewDelegate?
    public var sourceArr = [AIHairModel]()
    
    private var hotModelTopConstraint: Constraint?
    private var panGesture: UIPanGestureRecognizer!
    private var hotModelOriginalTop: CGFloat = 0
    private let hotModelMaxTop: CGFloat = 0 // 顶部位置
    private var isHotModelAtTop = false
    
    init() {
        super.init(frame: .zero)
        self.initView()
        self.setupGesture()
        self.fetchHairData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        bannerTimer?.invalidate()
    }
    
    private func setupGesture() {
        panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        panGesture.delegate = self
        hotModelView.addGestureRecognizer(panGesture)
    }
    
    private func setupBannerScrollView() {
        // 设置滚动视图
        bannerScrollView.isPagingEnabled = true
        bannerScrollView.showsHorizontalScrollIndicator = false
        bannerScrollView.showsVerticalScrollIndicator = false
        bannerScrollView.bounces = false
        bannerScrollView.alwaysBounceVertical = false
        bannerScrollView.alwaysBounceHorizontal = false
        bannerScrollView.isDirectionalLockEnabled = true
        bannerScrollView.delegate = self
        self.addSubview(bannerScrollView)

        safeConstraints(self.bannerScrollView) { make in
            make.top.equalTo(0)
            make.left.width.right.equalToSuperview()

            make.height.equalTo(SCREEN_WIDTH * 342 / 375)

        }

        // 设置容器视图
        bannerContainerView.backgroundColor = .clear
        self.bannerScrollView.addSubview(self.bannerContainerView)
        safeConstraints(self.bannerContainerView) { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(SCREEN_WIDTH * 342 / 375)
            make.width.equalTo(UIScreen.main.bounds.width) // 初始宽度，后续会更新
        }
    }

    private func setupBannerImages() {
        // 准备banner图片数组
        let bannerNames = [
            LanguageTool.currentLanguage() == .chineseSimplified ? "home_banner" : "home_banner_en",
            LanguageTool.currentLanguage() == .chineseSimplified ?"AI美甲banner": "AI美甲banner_en"
        ]

        // 为了实现无限循环，我们需要创建 原始数组 + 第一张图片 的布局
        // 这样当滚动到最后一张时，可以无缝切换到第一张
        let totalBanners = bannerNames.count + 1 // 多加一张用于循环

        // 设置容器宽度
        safeUpdateConstraints(self.bannerContainerView) { make in
            make.width.equalTo(UIScreen.main.bounds.width * CGFloat(totalBanners))
        }

        // 创建图片视图
        for i in 0..<totalBanners {
            let imageView = UIImageView()
            let imageName = i < bannerNames.count ? bannerNames[i] : bannerNames[0] // 最后一张使用第一张图片
            imageView.image = UIImage(named: imageName)
            imageView.contentMode = .scaleAspectFill
            imageView.clipsToBounds = true
            imageView.isUserInteractionEnabled = true
            imageView.tag = i < bannerNames.count ? i : 0 // 设置tag用于识别点击的banner

            // 添加点击手势
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(bannerTapped(_:)))
            imageView.addGestureRecognizer(tapGesture)

            self.bannerContainerView.addSubview(imageView)
            safeConstraints(imageView) { make in
                make.top.equalToSuperview()
                make.left.equalTo(UIScreen.main.bounds.width * CGFloat(i))
                make.width.equalTo(UIScreen.main.bounds.width)
                make.height.equalTo(SCREEN_WIDTH * 342 / 375)
            }

            self.bannerImageViews.append(imageView)
        }

        // 启动自动滚动
        startAutoScroll()
    }

    private func startAutoScroll() {
        bannerTimer?.invalidate()
        bannerTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { [weak self] _ in
            self?.scrollToNextBanner()
        }
    }

    private func scrollToNextBanner() {
        let currentPage = Int(bannerScrollView.contentOffset.x / UIScreen.main.bounds.width)
        let nextPage = currentPage + 1

        if nextPage >= bannerImageViews.count {
            // 如果到达最后一张（复制的第一张），无缝切换到真正的第一张
            bannerScrollView.setContentOffset(CGPoint(x: 0, y: 0), animated: false)
        } else {
            let nextOffset = CGPoint(x: UIScreen.main.bounds.width * CGFloat(nextPage), y: 0)
            bannerScrollView.setContentOffset(nextOffset, animated: true)
        }
    }

    private func initView() {
        setupBannerScrollView()
        setupBannerImages()

        self.vipImageView.image = UIImage(named: "home_vip")
        self.vipImageView.contentMode = .scaleAspectFit
        self.addSubview(self.vipImageView)
        safeConstraints(self.vipImageView) { make in
            make.width.height.equalTo(36)
            make.left.equalTo(13)
            make.top.equalTo(48)
        }
        //没付费功能，暂隐藏图标
        self.vipImageView.isHidden = true
        
        // 设置按钮容器 - 浮在scrollview上面
        buttonsStackView.axis = .horizontal
        buttonsStackView.distribution = .fillEqually
        buttonsStackView.spacing = 12
        buttonsStackView.alignment = .fill
        self.addSubview(buttonsStackView)
        safeConstraints(self.buttonsStackView) { make in
            make.bottom.equalTo(self.bannerScrollView.snp.bottom).offset(-12-48)
            make.height.equalTo(80)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
        }
        
        // 设置AI造型师按钮
        setupButton(aiStylistButton, title: "AI造型师".localized, imageName: "AI造型师", action: #selector(aiStylistClickAction))
        
        // 设置脸型测试按钮  
        setupButton(faceShapeTestButton, title: "脸型测试".localized, imageName: "脸型测试", action: #selector(faceTextClickAction))
        
        // 设置人像美容按钮
        setupButton(portraitBeautyButton, title: "人像美容".localized, imageName: "人像美容", action: #selector(portraitBeautyClickAction))
        
        // 添加到容器
        buttonsStackView.addArrangedSubview(aiStylistButton)
        buttonsStackView.addArrangedSubview(faceShapeTestButton)
        buttonsStackView.addArrangedSubview(portraitBeautyButton)
        
        self.hotModelView.layer.cornerRadius = 30
        self.hotModelView.backgroundColor = UIColor.white
        self.hotModelView.isUserInteractionEnabled = true
        self.addSubview(self.hotModelView)
        safeConstraints(self.hotModelView) { make in
            self.hotModelTopConstraint = make.top.equalTo(self.bannerScrollView.snp.bottom).offset(-48).constraint
            make.left.right.bottom.equalToSuperview()
        }

        let line = UIView()
        self.addSubview(line)
        line.backgroundColor = UIColor(valueRGB: 0xDBDBDB)
        safeConstraints(line) { make in
            make.left.right.equalToSuperview()
            make.bottom.equalTo(-1)
            make.height.equalTo(1)
        }
        
        self.bannerScrollView.superview?.layoutIfNeeded()
        self.hotModelOriginalTop = self.bannerScrollView.frame.maxY - 48
    }
    
    // 设置按钮的通用方法
    private func setupButton(_ button: UIButton, title: String, imageName: String, action: Selector) {
        button.backgroundColor = UIColor.white
        button.layer.cornerRadius = 12
        button.setTitle(title, for: .normal)
        button.setImage(UIImage(named: imageName), for: .normal)
        button.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 13)
        button.titleLabel?.adjustsFontSizeToFitWidth = true
        button.titleLabel?.numberOfLines = 0
        button.titleLabel?.textAlignment = .center

        // 添加阴影效果
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowOpacity = 0.1
        button.layer.shadowRadius = 4

        // 使用自定义扩展设置图片在上，文字在下
        button.setImagePosition(with: .top, spacing: 4)

        button.addTarget(self, action: action, for: .touchUpInside)
    }
    
    // 配置JXSegmentedView
    private func setupSegmentedView() {
        // 首先移除旧的视图（如果存在）
        segmentedView?.removeFromSuperview()
        listContainerView?.removeFromSuperview()
        
        // 确保有标签和模型
        if tagBasedManager.getTagCount() == 0 {
            return
        }
        
        // 创建数据源
        segmentedDataSource = JXSegmentedTitleDataSource()
        segmentedDataSource.titles = tagBasedManager.getAllTags()

        // 设置选中状态样式
        segmentedDataSource.titleSelectedColor = UIColor(red: 51/255.0, green: 51/255.0, blue: 51/255.0, alpha: 1.0)
        segmentedDataSource.titleSelectedFont =  UIFont.systemFont(ofSize: 16, weight: .medium)

        // 设置未选中状态样式
        segmentedDataSource.titleNormalColor = UIColor(red: 153/255.0, green: 153/255.0, blue: 153/255.0, alpha: 1.0)
        segmentedDataSource.titleNormalFont = UIFont(name: "PingFang SC", size: 16) ?? UIFont.systemFont(ofSize: 16)

        segmentedDataSource.isItemSpacingAverageEnabled = false // 改为false，不均匀分布
        segmentedDataSource.itemSpacing = 15 // 标签间距
        segmentedDataSource.itemWidthIncrement = 0 // 设置为0，使用内容宽度
        
        // 创建JXSegmentedView
        segmentedView = JXSegmentedView()
        segmentedView.backgroundColor = .clear
        segmentedView.dataSource = segmentedDataSource
        segmentedView.delegate = self
        
        // 配置指示器
        let indicator = JXSegmentedIndicatorLineView()
        indicator.indicatorColor = UIColor(valueRGB: 0xFFEC53)
        indicator.indicatorWidth = JXSegmentedViewAutomaticDimension
        indicator.lineStyle = .normal
        indicator.indicatorHeight = 2
        indicator.verticalOffset = 6 // 设置指示器距离文字的距离为2
        segmentedView.indicators = [indicator]
        
        self.hotModelView.addSubview(self.segmentedView)
        // 调整segmentedView位置，距离superview的top是20
        safeConstraints(self.segmentedView) { make in
            make.top.equalTo(15)
            make.left.equalTo(14)
            make.right.equalTo(-14)
            make.height.equalTo(40)
        }
        
        // 创建列表容器
        listContainerView = JXSegmentedListContainerView(dataSource: self)
        listContainerView.backgroundColor = .white
        // 启用滑动切换页面功能
        listContainerView.scrollView?.isScrollEnabled = true
        // 优化滚动体验
        listContainerView.scrollView?.decelerationRate = .fast
        listContainerView.scrollView?.bounces = true
        // 减少翻页阻力
        listContainerView.scrollView?.delaysContentTouches = false
        
        // 关联segmentedView和listContainerView
        segmentedView.listContainer = listContainerView
        
        self.hotModelView.addSubview(self.listContainerView)
        safeConstraints(self.listContainerView) { make in
            make.top.equalTo(self.segmentedView.snp.bottom).offset(5)
            make.left.equalTo(0)
            make.right.equalTo(0)
            make.bottom.equalTo(-3)
        }
        
        // 确保所有子视图刷新布局
        self.hotModelView.setNeedsLayout()
        self.hotModelView.layoutIfNeeded()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        // 确保原始位置正确
        hotModelOriginalTop = bannerScrollView.frame.maxY - 48
    }
    
    @objc func aiStylistClickAction() {
        // 防重复点击
        if aiStylistButton.ignoreEvent { return }
        aiStylistButton.antiDoubleClick()

        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.aiStylistClickAction)) != nil) {
            self.delegate?.aiStylistClickAction()
        }
    }

    @objc func portraitBeautyClickAction() {
        // 防重复点击
        if portraitBeautyButton.ignoreEvent { return }
        portraitBeautyButton.antiDoubleClick()

        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.portraitBeautyClickAction)) != nil) {
            self.delegate?.portraitBeautyClickAction()
        }
    }

    @objc func faceTextClickAction() {
        // 防重复点击
        if faceShapeTestButton.ignoreEvent { return }
        faceShapeTestButton.antiDoubleClick()

        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.faceTextClickAction)) != nil) {
            self.delegate?.faceTextClickAction()
        }
    }

    @objc func bannerTapped(_ gesture: UITapGestureRecognizer) {
        guard let imageView = gesture.view as? UIImageView else { return }

        // 防重复点击
        if imageView.ignoreEvent { return }
        imageView.antiDoubleClick()

        let bannerIndex = imageView.tag
        self.delegate?.bannerClickAction(index: bannerIndex)
    }
    
    var changeIS: CGFloat = 0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        // 获取滑动方向
        let velocity = gesture.velocity(in: self)
        
        // 如果是明显的水平滑动，不处理垂直滑动逻辑
        if abs(velocity.x) > abs(velocity.y) * 1.5 {
            return
        }
        
        switch gesture.state {
        case .changed:
            let translation = gesture.translation(in: self).y
            
            // 顶部内容滚动到顶部时才处理
            let contentOffsetY = getActiveCollectionView()?.contentOffset.y ?? 0
            if contentOffsetY <= 0 {
                // 计算新的Y坐标（限制在最大最小范围之间）
                let validChange = changeIS + CGFloat(translation)
                changeIS = max(min(validChange, -48), -(bannerScrollView.frame.maxY-48))

                // 实时更新约束
                hotModelTopConstraint?.update(offset: changeIS)
                self.layoutIfNeeded()
            }
            
            gesture.setTranslation(.zero, in: self)
            
            let newY = bannerScrollView.frame.maxY-48+changeIS
            if newY <= hotModelMaxTop {
                // 到达顶部位置
                hotModelTopConstraint?.update(offset: -(bannerScrollView.frame.maxY-48))
                self.isHotModelAtTop = true
                
                // 只有当内容偏移量为0时，手势才会影响内容滚动
                if contentOffsetY <= 0 {
                    getActiveCollectionView()?.contentOffset.y = 0
                }
            }
        case .ended, .cancelled:
            // 处理手势结束时的动画
            let targetY: CGFloat
            
            // 计算手势的速度来决定是展开还是收起
            let velocity = gesture.velocity(in: self).y
            
            if abs(velocity) > 500 {
                // 高速滑动时，根据方向决定
                targetY = velocity > 0 ? -48 : -(bannerScrollView.frame.maxY-48)
            } else {
                // 低速滑动时，根据当前位置决定
                let threshold = -(bannerScrollView.frame.maxY-48) / 2
                targetY = changeIS < threshold ? -(bannerScrollView.frame.maxY-48) : -48
            }
            
            UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
                self.hotModelTopConstraint?.update(offset: targetY)
                self.layoutIfNeeded()
            } completion: { _ in
                // 更新状态
                self.changeIS = targetY
                self.isHotModelAtTop = targetY == -(self.bannerScrollView.frame.maxY-48)
                
                // 根据位置更新子视图状态
                if self.isHotModelAtTop {
                    // 如果在顶部，允许内容滚动
                    // 获取当前所有显示的列表视图
                    for (_, list) in self.listContainerView?.validListDict ?? [:] {
                        if let categoryVC = list as? CategoryListViewController {
                            categoryVC.collectionView.isScrollEnabled = true
                        }
                    }
                    
                    // 确保水平滑动可用
                    self.listContainerView?.scrollView?.isScrollEnabled = true
                } else {
                    // 如果不在顶部，重置所有列表滚动位置并禁止垂直滚动
                    // 但保留水平滑动功能
                    for (_, list) in self.listContainerView?.validListDict ?? [:] {
                        if let categoryVC = list as? CategoryListViewController {
                            // categoryVC.collectionView.contentOffset = .zero
//                            categoryVC.collectionView.isScrollEnabled = false
                        }
                    }
                    
                    // 确保水平滑动仍然可用
                    self.listContainerView?.scrollView?.isScrollEnabled = true
                }
            }
        default:
            break
        }
    }
    
    // 获取当前活动的CollectionView
    private func getActiveCollectionView() -> UICollectionView? {
        guard let currentVC = listContainerView?.validListDict.values.first as? CategoryListViewController else {
            return nil
        }
        return currentVC.collectionView
    }
    
    // 网络请求逻辑
    func fetchHairData() {
        SVProgressHUD.show()
        let url = "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/hotmodel.json" // 替换为你的实际 API 地址
        
        AIHairModel.fetchHairData(from: url, parameters: nil) { [weak self] result in
            guard let self = self else { return }
            
            SVProgressHUD.dismiss()
            switch result {
            case .success(let data):
                self.sourceArr = data
                print("数据加载成功，共 \(data.count) 条")
                
                // 使用标签管理器处理数据
                self.tagBasedManager.updateModels(data)
                // 不需要再单独添加"全部"分类，已在TagBasedHairModelManager中处理
                
                // 在主线程更新UI
                DispatchQueue.main.async {
                    self.setupSegmentedView()
                }
                
            case .failure(let error):
                print("请求失败: \(error.localizedDescription)")
                SVProgressHUD.showError(withStatus: "获取失败".localized)
            }
        }
    }
}

// MARK: - UIGestureRecognizerDelegate
extension HomeView: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer,
                         shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // 如果是我们的垂直滑动手势panGesture
        if gestureRecognizer == panGesture {
            // 获取垂直滑动手势的移动方向
            let velocity = panGesture.velocity(in: self)
            
            // 对于明显的水平滑动（水平分量大于垂直分量的2倍），不要同时识别垂直手势
            if abs(velocity.x) > abs(velocity.y) * 2 {
                return false
            }
            
            // 如果热门模型视图不在顶部，优先处理垂直手势
            if !isHotModelAtTop {
                return true
            }
            
            // 如果内容已经滚动到顶部且继续向下滑动，允许垂直手势
            if let scrollView = getActiveCollectionView(), scrollView.contentOffset.y <= 0 && velocity.y > 0 {
                return true
            }
        }
        
        // 默认行为：允许同时识别
        return true
    }
}

// MARK: - JXSegmentedViewDelegate
extension HomeView: JXSegmentedViewDelegate {
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        // 可以在这里处理点击某个分类的事件
    }
}

// MARK: - JXSegmentedListContainerViewDataSource
extension HomeView: JXSegmentedListContainerViewDataSource {
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return tagBasedManager.getTagCount()
    }
    
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        if let tag = tagBasedManager.getTag(at: index) {
            let listVC = CategoryListViewController()
            listVC.models = tagBasedManager.getModels(forTag: tag)
            listVC.delegate = self
            return listVC
        }
        return CategoryListViewController()
    }
}

// MARK: - CategoryListViewControllerDelegate
extension HomeView: CategoryListViewControllerDelegate {
    func didSelectModel(_ model: AIHairModel) {
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("popularHairCutDataAction:"))) != nil) {
            self.delegate?.popularHairCutDataAction(type: model)
        }
    }

    func didSelectNailModel(_ model: AIHairModel) {
        // 处理美甲类型点击，传递模型的ID
        print("💅 HomeView: 收到美甲模型点击 - name: \(model.name), ID: \(model.ID)")
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("nailTypeClickAction:"))) != nil) {
            print("💅 HomeView: 调用 delegate.nailTypeClickAction，ID: \(model.ID)")
            self.delegate?.nailTypeClickAction(nailId: model.ID)
        } else {
            print("❌ HomeView: delegate 为 nil 或不响应 nailTypeClickAction")
        }
    }
}

class HomeButtonView: UIView {
    let titleLabel = UILabel()
    let imageView = UIImageView()
    let titleString = String()
    
    init(titleString: String, imageName: String) {
        super.init(frame: .zero)
        self.backgroundColor = UIColor(valueRGB: 0xFAC218)
        
        self.imageView.image = UIImage(named: imageName)
        self.imageView.contentMode = .scaleAspectFit
        self.addSubview(self.imageView)
        safeConstraints(self.imageView) { make in
            make.width.height.equalTo(30)
            make.right.equalTo(-16)
            make.top.equalTo(13)
            make.centerY.equalToSuperview()
        }
        
        self.titleLabel.text = titleString
        self.titleLabel.textColor = UIColor.white
        self.titleLabel.textAlignment = .center
        self.titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        self.titleLabel.adjustsFontSizeToFitWidth = true
        self.titleLabel.numberOfLines = 0
        self.addSubview(self.titleLabel)
        safeConstraints(self.titleLabel) { make in
            make.left.equalTo(20)
            make.centerY.equalToSuperview()
            make.width.lessThanOrEqualTo(80)
            make.height.equalTo(self)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

// MARK: - UIScrollViewDelegate
extension HomeView: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        if scrollView == bannerScrollView {
            // 强制保持垂直位置为0，只允许水平滚动
            if scrollView.contentOffset.y != 0 {
                scrollView.contentOffset = CGPoint(x: scrollView.contentOffset.x, y: 0)
            }
        }
    }

    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        if scrollView == bannerScrollView {
            handleBannerScrollEnd()
            // 减速结束后重新启动自动滚动
            startAutoScroll()
        }
    }

    func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        if scrollView == bannerScrollView {
            handleBannerScrollEnd()
        }
    }

    private func handleBannerScrollEnd() {
        let currentPage = Int(bannerScrollView.contentOffset.x / UIScreen.main.bounds.width)

        // 如果滚动到了最后一张（复制的第一张），无缝切换到真正的第一张
        if currentPage >= bannerImageViews.count - 1 {
            bannerScrollView.setContentOffset(CGPoint(x: 0, y: 0), animated: false)
        }
    }

    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        if scrollView == bannerScrollView {
            // 用户开始手动滑动时，暂停自动滚动
            bannerTimer?.invalidate()
        }
    }

    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if scrollView == bannerScrollView && !decelerate {
            // 用户手动滑动结束且没有减速时，重新启动自动滚动
            startAutoScroll()
        }
    }
}
