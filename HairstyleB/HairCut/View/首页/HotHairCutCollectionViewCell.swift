//
//  HotHairCutCollectionViewCell.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/7/31.
//

import Foundation
import UIKit
import SnapKit

class HotHairCutCollectionViewCell: UICollectionViewCell {
    static let identifier = "HotHairCutCollectionViewCell"
    
    private var imageView = UIImageView()
    private var titleLabel = UILabel()
    public var imageString: String? {
        willSet {
            if newValue != nil && newValue?.count ?? 0 > 0 {
                
                self.imageView.sd_setImage(with: NSURL(string: newValue ?? "") as URL?, placeholderImage: UIImage(named: ""))
            }
        }
    }
    
    public var titleString: String? {
        willSet {
            self.titleLabel.text = newValue ?? ""
        }
    }
    
    public var isSelectedItem: Bool {
        willSet {
            self.contentView.backgroundColor = newValue == true ?  UIColor.init(valueRGB: 0xFFEC53) : UIColor(valueRGB: 0xF7F7F7)
        }
    }
    
    public var aspectRatio: CGFloat = 1.0 {
        didSet {
            updateImageConstraints()
        }
    }
    
    override init(frame: CGRect) {
        self.isSelectedItem = false
        super.init(frame: frame)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.contentView.layer.cornerRadius = 4
        self.contentView.layer.masksToBounds = true
        self.contentView.backgroundColor = UIColor(valueRGB: 0xF7F7F7)
        
        self.imageView.layer.cornerRadius = 4
        self.imageView.layer.masksToBounds = true
        self.imageView.contentMode = .scaleAspectFill
        self.contentView.addSubview(self.imageView)
        updateImageConstraints()
        
        self.titleLabel.text = self.titleString
        self.titleLabel.textColor = UIColor(valueRGB: 0x333333)
        self.titleLabel.font = UIFont.systemFont(ofSize: 14)
        self.titleLabel.textAlignment = .center
        self.titleLabel.adjustsFontSizeToFitWidth = false  // 改为false，让文字正常换行
        self.titleLabel.numberOfLines = 0
        self.titleLabel.lineBreakMode = .byWordWrapping  // 设置换行模式
        self.contentView.addSubview(self.titleLabel)
        self.titleLabel.snp.makeConstraints { make in
            make.top.equalTo(self.imageView.snp.bottom).offset(8)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.bottom.equalTo(-8)  // 添加底部约束，确保cell高度自适应
        }
        self.layoutIfNeeded()
    }

    private func updateImageConstraints() {
        self.imageView.snp.remakeConstraints { make in
            make.left.equalTo(2)
            make.right.equalTo(-2)
            make.top.equalTo(2)
            // height = width * aspectRatio
            make.height.equalTo(self.imageView.snp.width).multipliedBy(aspectRatio)
        }
    }


}
