//
//  SubscribeView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/7/30.
//

import Foundation
import UIKit
import SnapKit
import SVProgressHUD

// MARK: - 订阅回调欢迎页协议
protocol SubscribeViewDelegate: NSObjectProtocol {
    // 订阅条款点击事件
    func termOfUseClcikAction()
    func privacyAgreementClickAction()
    func rePurchaseClickAction()
    // 这些方法暂时保留以保持向后兼容
    func paymentGestureClick()
}

// MARK: - 订阅主视图
class SubscribeView: UIView, SubscribeChooseViewDelegate {
    // 背景
    private let backgroundView = UIView()
    // 背景图片(只占上半部分)
    private let backgroundImageView = UIImageView()
    // 关闭按钮
    private let closeBtn = UIButton()
    
    // 会员权益简介(白色背景)
    private let benefitsContainer = UIView()
    private let benefitsLabel = UILabel()
    
    // 3天免费试用开关容器
    private let freeTrialContainer = UIView()
    private let freeTrialLabel = UILabel()
    private let freeTrialSwitch = UISwitch()

    // 订阅按钮和价格
    private let subscribeButton = UIButton()
    private let priceLabel = UILabel()
    
    // 底部链接
    private let termsButton = UIButton()
    private let privacyButton = UIButton()
    private let restoreButton = UIButton()
    
    weak public var delegate: SubscribeViewDelegate?
    
    init() {
        super.init(frame: .zero)
        self.initView()
        self.setupPriceInfo()
        
        // 注册价格更新通知
        NotificationCenter.default.addObserver(self, selector: #selector(updatePriceInfo), name: NSNotification.Name("refreshPrice"), object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        // 背景设置为白色
        self.backgroundColor = UIColor.white
        MobClick.event("Subscribe", attributes: ["source": "三连屏"])
        // 背景视图
        backgroundView.backgroundColor = UIColor(valueRGB: 0xE9FFA5)
        self.addSubview(backgroundView)
        backgroundView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(UIScreen.main.bounds.height * 0.5) // 只占半屏
        }
        
        // 钻石背景图片(包含"成为会员"和"解锁全部功能"文字)
        backgroundImageView.image = UIImage(named: "subscribe_background")
        backgroundImageView.contentMode = .scaleAspectFit
        backgroundView.addSubview(backgroundImageView)
        backgroundImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview()
            make.height.equalTo(UIScreen.main.bounds.width * 1.2) // 保持合适比例
        }
        
        // 关闭按钮
        closeBtn.setImage(UIImage(named: "subscribe_close"), for: .normal)
        closeBtn.addTarget(self, action: #selector(closeBtnClick), for: .touchUpInside)
        self.addSubview(closeBtn)
        closeBtn.snp.makeConstraints { make in
            make.width.height.equalTo(36)
            make.left.equalTo(18)
            make.top.equalTo(50)
        }
        
        // 会员权益简介(白色背景) - 高度自适应
        benefitsContainer.backgroundColor = UIColor.white
        benefitsContainer.layer.cornerRadius = 10
        benefitsContainer.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        benefitsContainer.layer.shadowOffset = CGSize(width: 0, height: 2)
        benefitsContainer.layer.shadowRadius = 5
        benefitsContainer.layer.shadowOpacity = 0.8
        self.addSubview(benefitsContainer)
        benefitsContainer.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(backgroundView.snp.bottom).offset(-20)
            make.width.equalTo(UIScreen.main.bounds.width - 40)
        }

        // 会员权益完整文字(包含所有说明)
        let fullText = local("会员权益：每月畅享20次高品质AI工具+其他功能无限使用\n为了专注呈现每一次换发的最佳效果(流畅、精准)我们设定了使用次数，这是我们对高品质体验的坚持，也是对技术资源的优化配置，确保每位会员都能获得满意的服务。")
        benefitsLabel.text = fullText
        benefitsLabel.textColor = UIColor(valueRGB: 0x333333)
        benefitsLabel.font = UIFont.systemFont(ofSize: 14)
        benefitsLabel.textAlignment = .left
        benefitsLabel.numberOfLines = 0
        benefitsLabel.lineBreakMode = .byWordWrapping
        benefitsContainer.addSubview(benefitsLabel)
        benefitsLabel.snp.makeConstraints { make in
            make.top.equalTo(15)
            make.left.equalTo(15)
            make.right.equalTo(-15)
            make.bottom.equalTo(-15)
        }


        // 3天免费试用开关容器
        freeTrialContainer.backgroundColor = UIColor(valueRGB: 0xFFF8C5)
        freeTrialContainer.layer.cornerRadius = 10
        freeTrialContainer.layer.borderWidth = 2
        freeTrialContainer.layer.borderColor = UIColor(valueRGB: 0xFFEC53).cgColor
        self.addSubview(freeTrialContainer)
        freeTrialContainer.snp.makeConstraints { make in
            make.top.equalTo(benefitsContainer.snp.bottom).offset(20)
            make.left.equalTo(25)
            make.right.equalTo(-25)
            make.height.equalTo(60)
        }

        // 3天免费试用文字
        freeTrialLabel.text = local("启用3天免费试用")
        freeTrialLabel.textColor = UIColor(valueRGB: 0x333333)
        freeTrialLabel.font = UIFont.systemFont(ofSize: 16)
        freeTrialContainer.addSubview(freeTrialLabel)
        freeTrialLabel.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.centerY.equalToSuperview()
        }

        // 3天免费试用开关
        freeTrialSwitch.isOn = false // 默认不打开
        freeTrialSwitch.onTintColor = UIColor(valueRGB: 0xFFEC53)
        freeTrialSwitch.addTarget(self, action: #selector(freeTrialSwitchChanged), for: .valueChanged)
        freeTrialContainer.addSubview(freeTrialSwitch)
        freeTrialSwitch.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.centerY.equalToSuperview()
        }

        // 底部链接栏 - 移至详细说明下方
        let bottomStackView = UIStackView()
        bottomStackView.axis = .horizontal
        bottomStackView.distribution = .equalSpacing
        bottomStackView.alignment = .center
        bottomStackView.spacing = 20
        self.addSubview(bottomStackView)
        bottomStackView.snp.makeConstraints { make in
            make.top.equalTo(freeTrialContainer.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.lessThanOrEqualTo(UIScreen.main.bounds.width - 40)
            make.height.equalTo(30)
        }
        
        // 使用条款
        termsButton.setAttributedTitle(buildUnderLineAttributedText(inputString: local("使用条款")), for: .normal)
        termsButton.addTarget(self, action: #selector(termOfUseClcikAction), for: .touchUpInside)
        bottomStackView.addArrangedSubview(termsButton)
        
        // 隐私策略
        privacyButton.setAttributedTitle(buildUnderLineAttributedText(inputString: local("隐私策略")), for: .normal)
        privacyButton.addTarget(self, action: #selector(privacyAgreementClickAction), for: .touchUpInside)
        bottomStackView.addArrangedSubview(privacyButton)
        
        // 恢复购买
        restoreButton.setAttributedTitle(buildUnderLineAttributedText(inputString: local("恢复购买")), for: .normal)
        restoreButton.addTarget(self, action: #selector(rePurchaseClickAction), for: .touchUpInside)
        bottomStackView.addArrangedSubview(restoreButton)
        
        // 立即订阅按钮(与欢迎页的继续按钮保持一致)
        // 初始状态下显示"立即订阅"，因为开关默认是关闭的
        subscribeButton.setTitle(local("继续"), for: .normal)
        subscribeButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        subscribeButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        subscribeButton.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        subscribeButton.layer.cornerRadius = 26
        subscribeButton.addTarget(self, action: #selector(subscribeButtonClick), for: .touchUpInside)
        self.addSubview(subscribeButton)
        subscribeButton.snp.makeConstraints { make in
            make.left.equalTo(39)
            make.right.equalTo(-39)
            make.height.equalTo(52)
            make.bottom.equalToSuperview().offset(-87)
        }
        
        // 价格标签 - 移至立即订阅按钮下方
        priceLabel.textColor = UIColor(valueRGB: 0x666666)
        priceLabel.font = UIFont.systemFont(ofSize: 14)
        priceLabel.textAlignment = .center
        self.addSubview(priceLabel)
        priceLabel.snp.makeConstraints { make in
            make.top.equalTo(subscribeButton.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
            make.left.equalTo(39)
            make.right.equalTo(-39)
            make.height.equalTo(20)
        }
        
        // 初始更新价格信息
        updatePriceInfo()
    }
    
    private func buildUnderLineAttributedText(inputString: String,_ lineVaue: Int = 1, _ textColor: UIColor = UIColor(valueRGB: 0x333333), _ textFont: UIFont = UIFont.systemFont(ofSize: 14)) -> NSMutableAttributedString {
        let resultAttributedString = NSMutableAttributedString(string: inputString)
        let range: NSRange = NSRange(location: 0, length: inputString.count)
        resultAttributedString.addAttributes([
            .underlineStyle: lineVaue, 
            .foregroundColor: textColor,
            .font: textFont], 
            range: range)
        return resultAttributedString
    }
    
    // 从IAP管理器获取价格
    private func setupPriceInfo() {
        // 延迟调用以确保Switch已经初始化
        DispatchQueue.main.async {
            self.updatePriceInfo()
        }
    }
    
    @objc func updatePriceInfo() {
        if freeTrialSwitch.isOn {
            // 开启3天免费试用
            let priceString = UserDefaults.standard.string(forKey: "featureBId") ?? "¥X.XX"
            subscribeButton.setTitle(local("开始免费试用"), for: .normal)
            priceLabel.text = local("免费试用3天，到期后") + priceString + local("/月")
        } else {
            // 没有开启3天免费试用
            let priceString = UserDefaults.standard.string(forKey: "featureBNId") ?? "¥X.XX"
            subscribeButton.setTitle(local("继续"), for: .normal)
            priceLabel.text = local("立即订阅") + priceString + local("/月")
        }
    }

    // 3天免费试用开关状态改变
    @objc func freeTrialSwitchChanged() {
        MobClick.event("Subscribe", attributes: ["Triple_screen_connection": "点击开启免费3天试用"])
        updatePriceInfo()
    }
    
    // 立即订阅按钮点击 - 根据Switch状态调用不同的订阅
    @objc func subscribeButtonClick() {
        // 显示加载指示器
        SVProgressHUD.show()

        if freeTrialSwitch.isOn {
            // 启用3天免费试用 - 调用featureBId (前3天免费)
            APPMakeStoreIAPManager.sharedManager.buyFeatureB()
        } else {
            // 不启用3天免费试用 - 调用featureBNId (前3天不免费)
            APPMakeStoreIAPManager.sharedManager.buyFeatureBN()
        }

        // 设置订阅结果回调
        APPMakeStoreIAPManager.sharedManager.delegate = self
    }
    
    @objc func termOfUseClcikAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.termOfUseClcikAction)) != nil) {
            self.delegate?.termOfUseClcikAction()
        }
    }
    
    @objc func privacyAgreementClickAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.privacyAgreementClickAction)) != nil) {
            self.delegate?.privacyAgreementClickAction()
        }
    }
    
    @objc func rePurchaseClickAction() {
        // 显示加载指示器
        SVProgressHUD.show()
        
        // 恢复购买
        APPMakeStoreIAPManager.sharedManager.restoreButClick {
            // 恢复完成后，不直接进入主页，等待回调
            // 如果没有恢复项目，IAP管理器会显示提示并隐藏加载器
        }
        
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.rePurchaseClickAction)) != nil) {
            self.delegate?.rePurchaseClickAction()
        }
    }
    
    // 点击关闭按钮直接进入首页
    @objc func closeBtnClick() {
        // 显示加载指示器
        SVProgressHUD.show()
        
        // 隐藏加载指示器后进入主页
        DispatchQueue.main.async {
            SVProgressHUD.dismiss()
            MobClick.event("Subscribe", attributes: ["close": "三联屏&关闭订阅 (进入到订阅页, 没拉起支付直, 接关闭订阅页)"])
            self.goToMainPage()
            
        }
    }
    
    // 进入应用首页
    private func goToMainPage() {
        // 记录首次使用
        UserDefaultsTool.saveTemporaryString(key: UserDefaultsConst.firstUse, value: "true")
        
        // 进入首页
        AppLoad.resetTabbarVC()
    }
}

// MARK: - 实现MKStoreKitDelegate协议
extension SubscribeView: MKStoreKitDelegate {
    func productCPurchased(_ productIdentifier: String) {
        // 订阅成功
        DispatchQueue.main.async {
            SVProgressHUD.dismiss()
            MobClick.event("Subscribe", attributes: ["result": "三连屏订阅成功"])

            // 发送订阅状态更新通知
            NotificationCenter.default.post(name: .subscriptionStatusDidUpdate, object: nil)

            self.goToMainPage()
        }
    }
    
    func failed() {
        // 订阅失败
        DispatchQueue.main.async {
            MobClick.event("Subscribe", attributes: ["status": "三联屏&购买失败 (拉起订阅弹窗但没支付)"])
            SVProgressHUD.dismiss()
            if !self.freeTrialSwitch.isOn {
                self.freeTrialSwitch.setOn(true, animated: true)
                // 触发Switch状态改变的方法，更新UI和价格信息
                self.freeTrialSwitchChanged()

                // 记录用户行为
                MobClick.event("Subscribe", attributes: ["auto_enable_free_trial": "用户取消订阅后自动开启3天免费试用"])
            }
        }
    }
    
    func buySuccessBack() {
        // 订阅成功回调
        DispatchQueue.main.async {
            SVProgressHUD.dismiss()
            MobClick.event("Subscribe", attributes: ["result": "三连屏订阅成功"])

            // 发送订阅状态更新通知
            NotificationCenter.default.post(name: .subscriptionStatusDidUpdate, object: nil)

            self.goToMainPage()
        }
    }
    
    func buycancelBack() {
        // 取消订阅回调
        DispatchQueue.main.async {
            SVProgressHUD.dismiss()

            // 如果用户没有开启三天免费的Switch，并且点击订阅并且取消，就自动开启三天免费的Switch开关
            if !self.freeTrialSwitch.isOn {
                self.freeTrialSwitch.setOn(true, animated: true)
                // 触发Switch状态改变的方法，更新UI和价格信息
                self.freeTrialSwitchChanged()

                // 记录用户行为
                MobClick.event("Subscribe", attributes: ["auto_enable_free_trial": "用户取消订阅后自动开启3天免费试用"])
            }
        }
    }
}

// MARK: - 订阅下划线跳转代理
protocol SubscribeChooseViewDelegate: NSObjectProtocol {
    func termOfUseClcikAction()
    func privacyAgreementClickAction()
    func rePurchaseClickAction()
}

// MARK: - 订阅说明视图
class SubscribeChooseView: UIView, SubscribePlanViewProtocol {
//    weak public var mdelegate: SubscribeChooseViewDelegate?
    let title = UILabel()
    let titleImageView = UIImageView()
    
    let planViewWidth = (UIScreen.main.bounds.width - 76) / 3
    let monthPlanView: SubscribePlanView
    let yearPlanView: SubscribePlanView
    let foreverPlanView: SubscribePlanView
    
    let scrollView = UIScrollView()
    let tipsLabel = UILabel()
    
    let termOfUseButton = UIButton()
    let privacyAgreementButton = UIButton()
    let rePurchaseButton = UIButton()
    
    var currentPlan = SubscribePlan.month
    
    weak public var delegate: SubscribeChooseViewDelegate?
    init() {
        self.monthPlanView = SubscribePlanView(subscribePlan: .month, isSelected: true)
        self.yearPlanView = SubscribePlanView(subscribePlan: .year, isSelected: false)
        self.foreverPlanView = SubscribePlanView(subscribePlan: .forever, isSelected: false)
        super.init(frame: .zero)
        self.monthPlanView.delegate = self
        self.yearPlanView.delegate = self
        self.foreverPlanView.delegate = self
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.title.text = "subscriotion_plan".localized
        self.title.adjustsFontSizeToFitWidth = true
        self.title.textColor = UIColor(valueRGB: 0x333333)
        self.title.font = UIFont.systemFont(ofSize: 20)
        self.title.textAlignment = .center
        self.addSubview(self.title)
        self.title.snp.makeConstraints { make in
            make.top.equalTo(20)
            make.centerX.equalToSuperview()
            make.height.equalTo(28)
            make.width.greaterThanOrEqualTo(80)
        }
        
        self.titleImageView.image = UIImage(named: "subscribe_vip")
        self.titleImageView.contentMode = .scaleAspectFit
        self.addSubview(self.titleImageView)
        self.titleImageView.snp.makeConstraints { make in
            make.width.equalTo(38)
            make.height.equalTo(20)
            make.right.equalTo(self.title.snp.left).offset(-4)
            make.centerY.equalTo(self.title)
        }
        
        self.monthPlanView.isUserInteractionEnabled = true
        self.addSubview(self.monthPlanView)
        self.monthPlanView.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(self.title.snp.bottom).offset(20)
            make.width.equalTo(planViewWidth)
            make.height.greaterThanOrEqualTo(132)
        }
        self.addSubview(self.yearPlanView)
        self.yearPlanView.snp.makeConstraints { make in
            make.left.equalTo(self.monthPlanView.snp.right).offset(18)
            make.top.equalTo(self.monthPlanView)
            make.width.equalTo(planViewWidth)
            make.height.greaterThanOrEqualTo(132)
        }
        self.addSubview(self.foreverPlanView)
        self.foreverPlanView.snp.makeConstraints { make in
            make.left.equalTo(self.yearPlanView.snp.right).offset(18)
            make.top.equalTo(self.title.snp.bottom).offset(20)
            make.width.equalTo(planViewWidth)
            make.height.greaterThanOrEqualTo(132)
        }
        
        self.tipsLabel.text = "long_subscription".localized
        self.tipsLabel.adjustsFontSizeToFitWidth = true
        self.tipsLabel.textColor = UIColor(valueRGB: 0x333333)
        self.tipsLabel.font = UIFont.systemFont(ofSize: 14)
        self.tipsLabel.numberOfLines = 0
        self.tipsLabel.textAlignment = .left
        self.addSubview(self.tipsLabel)
        self.tipsLabel.snp.makeConstraints { make in
            make.top.equalTo(self.monthPlanView.snp.bottom).offset(20)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.height.greaterThanOrEqualTo(50)
        }
        
        self.termOfUseButton.setAttributedTitle(self.buildUnderLineAttributedText(inputString: "terms_of_use".localized), for: .normal)
        self.termOfUseButton.titleLabel?.textAlignment = .center
        self.termOfUseButton.addTarget(self, action: #selector(termOfUseClcikAction), for: .touchUpInside)
        self.addSubview(self.termOfUseButton)
        self.termOfUseButton.snp.makeConstraints { make in
            make.top.equalTo(self.tipsLabel.snp.bottom).offset(14)
            make.left.equalTo(35)
            make.width.greaterThanOrEqualTo(48)
            make.height.equalTo(17)
        }
        self.privacyAgreementButton.setAttributedTitle(self.buildUnderLineAttributedText(inputString: "privacy_policy".localized), for: .normal)
        self.privacyAgreementButton.titleLabel?.textAlignment = .center
        self.privacyAgreementButton.addTarget(self, action: #selector(privacyAgreementClickAction), for: .touchUpInside)
        self.addSubview(self.privacyAgreementButton)
        self.privacyAgreementButton.snp.makeConstraints { make in
            make.top.equalTo(self.termOfUseButton)
            make.centerX.equalToSuperview()
            make.width.greaterThanOrEqualTo(48)
            make.height.equalTo(17)
        }
        self.rePurchaseButton.setAttributedTitle(self.buildUnderLineAttributedText(inputString: "restore_purchase".localized), for: .normal)
        self.rePurchaseButton.titleLabel?.textAlignment = .center
        self.rePurchaseButton.addTarget(self, action: #selector(rePurchaseClickAction), for: .touchUpInside)
        self.addSubview(self.rePurchaseButton)
        self.rePurchaseButton.snp.makeConstraints { make in
            make.top.equalTo(self.termOfUseButton)
            make.right.equalTo(-35)
            make.width.greaterThanOrEqualTo(48)
            make.height.equalTo(17)
        }
    }
    
    @objc func termOfUseClcikAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.termOfUseClcikAction)) != nil) {
            self.delegate?.termOfUseClcikAction()
        }
    }
    
    @objc func privacyAgreementClickAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.privacyAgreementClickAction)) != nil) {
            self.delegate?.privacyAgreementClickAction()
        }
    }
    
    @objc func rePurchaseClickAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.rePurchaseClickAction)) != nil) {
            self.delegate?.rePurchaseClickAction()
        }
    }
    
    private func buildUnderLineAttributedText(inputString: String,_ lineVaue: Int = 1, _ textColor: UIColor = UIColor(valueRGB: 0x333333), _ textFont: UIFont = UIFont.systemFont(ofSize: 12)) -> NSMutableAttributedString {
        let resultAttributedString = NSMutableAttributedString(string: inputString)
        let range: NSRange = NSRange(location: 0, length: inputString.count)
        resultAttributedString.addAttributes([
            .underlineStyle: lineVaue, 
            .foregroundColor: textColor,
            .font: textFont], 
            range: range)
        return resultAttributedString
    }
    
    func SubscribePlanChange(subscribePlan: SubscribePlan) {
        switch subscribePlan {
        case .month:
            self.monthPlanView.isSelected = true
            self.yearPlanView.isSelected = false
            self.foreverPlanView.isSelected = false
        case .year:
            self.monthPlanView.isSelected = false
            self.yearPlanView.isSelected = true
            self.foreverPlanView.isSelected = false
        case .forever:
            self.monthPlanView.isSelected = false
            self.yearPlanView.isSelected = false
            self.foreverPlanView.isSelected = true
        }
    }
}

// MARK: - 选择订阅协议
protocol SubscribePlanViewProtocol: NSObjectProtocol {
    func SubscribePlanChange(subscribePlan: SubscribePlan)
}

// MARK: - 选择订阅按钮
class SubscribePlanView: UIView {
    let backgroundView = UIView()
    let recommendedLabel = UILabel()
    let typeLabel = UILabel()
    let priceLabel = UILabel()
    let descriptionLabel = UILabel()
    
    let subscribePlan: SubscribePlan
    weak public var delegate: SubscribePlanViewProtocol?
    public var isSelected: Bool {
        willSet {
            if Thread.current.isMainThread {
                self.refreshView(isSelected: newValue)
            } else {
                DispatchQueue.main.async {
                    self.refreshView(isSelected: newValue)
                }
            }
        }
    }
    
    @objc func tapAction() {
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("SubscribePlanChange:"))) != nil) {
            self.delegate?.SubscribePlanChange(subscribePlan: self.subscribePlan)
        }
    }
    
    init(subscribePlan: SubscribePlan, isSelected: Bool) {
        self.subscribePlan = subscribePlan
        self.isSelected = isSelected
        super.init(frame: .zero)
        self.backgroundView.backgroundColor = isSelected ? UIColor(valueRGB: 0xFFF8C5) : UIColor(valueRGB: 0xF7F7F7)
        self.backgroundView.layer.cornerRadius = 8
        self.backgroundView.isUserInteractionEnabled = true
        self.addSubview(self.backgroundView)
        self.backgroundView.snp.makeConstraints { make in
            make.left.right.top.bottom.equalTo(self)
        }
        let tapGesture = UITapGestureRecognizer()
        tapGesture.addTarget(self, action: #selector(tapAction))
        self.backgroundView.addGestureRecognizer(tapGesture)
        
        self.recommendedLabel.text = "recommendations".localized
        self.recommendedLabel.adjustsFontSizeToFitWidth = true
        self.recommendedLabel.textColor = UIColor(valueRGB: 0x000000)
        self.recommendedLabel.font = UIFont.systemFont(ofSize: 11)
        self.recommendedLabel.textAlignment = .center
        self.recommendedLabel.backgroundColor = self.subscribePlan != .month ? UIColor(valueRGB: 0xFFEC53) : UIColor.clear
        self.backgroundView.addSubview(self.recommendedLabel)
        self.recommendedLabel.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.centerX.equalTo(self.backgroundView)
            make.width.equalTo(52)
            make.height.greaterThanOrEqualTo(18)
        }
        self.recommendedLabel.isHidden = self.subscribePlan != .month
        
        self.typeLabel.textColor = UIColor(valueRGB: 0x0F172A)
        self.typeLabel.font = UIFont.systemFont(ofSize: 14)
        self.typeLabel.textAlignment = .center
        self.typeLabel.numberOfLines = 0
        switch self.subscribePlan {
        case .month:
            self.typeLabel.text = "continuous_monthly".localized
        case .year:
            self.typeLabel.text = "continuous_annual".localized
        case .forever:
            self.typeLabel.text = "permanent_membership".localized
        }
        self.typeLabel.adjustsFontSizeToFitWidth = true
        self.backgroundView.addSubview(self.typeLabel)
        self.typeLabel.snp.makeConstraints { make in
            make.top.equalTo(self.recommendedLabel.snp.bottom).offset(7)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.height.greaterThanOrEqualTo(20)
        }
        
        self.priceLabel.textAlignment = .center
        self.priceLabel.attributedText = self.buildtPriceAttributedString(subscribePlan: self.subscribePlan)
        self.priceLabel.numberOfLines = 0
        self.backgroundView.addSubview(self.priceLabel)
        self.priceLabel.snp.makeConstraints { make in
            make.top.equalTo(self.typeLabel.snp.bottom).offset(10)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.height.greaterThanOrEqualTo(28)
        }
        
        self.descriptionLabel.backgroundColor = isSelected ? UIColor(valueRGB: 0xFFEC53) : UIColor.clear
        self.descriptionLabel.textAlignment = .center
        self.descriptionLabel.attributedText = self.buildDiscriptionAttributedString(subscribePlan: self.subscribePlan, isSelect: self.isSelected)
        self.descriptionLabel.numberOfLines = 0
        self.backgroundView.addSubview(self.descriptionLabel)
        self.descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(self.priceLabel.snp.bottom).offset(10)
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.height.greaterThanOrEqualTo(25)
            make.bottom.equalToSuperview().offset(-15)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func buildtPriceAttributedString(subscribePlan: SubscribePlan, _ textColor: UIColor = UIColor(valueRGB: 0x0F172A),_ currencyFont: UIFont = UIFont.boldSystemFont(ofSize: 14), _ priceFont: UIFont = UIFont.boldSystemFont(ofSize: 20)) -> NSMutableAttributedString {
        var price = "¥"
        switch subscribePlan {
        case .month:
            price.append("X.XX")
        case .year:
            price.append("XX.XX")
        case .forever:
            price.append("XX.XX")
        }
        let resultAttributedString = NSMutableAttributedString(string: price)
        let currencyRange = NSRange(location: 0, length: 1)
        resultAttributedString.addAttributes([
            .foregroundColor: textColor,
            .font: currencyFont],
            range: currencyRange)
        let range: NSRange = NSRange(location: 1, length: price.count - 1)
        resultAttributedString.addAttributes([
            .foregroundColor: textColor,
            .font: priceFont],
            range: range)
        return resultAttributedString
    }
    
    func buildDiscriptionAttributedString(subscribePlan: SubscribePlan, _ textColor: UIColor = UIColor(valueRGB: 0x0F172A),_ font: UIFont = UIFont.boldSystemFont(ofSize: 12),_ lineValue: Int = 1, isSelect: Bool) -> NSMutableAttributedString {
        var discription = ""
        switch subscribePlan {
        case .month:
            discription = "limited_time_offer".localized
        case .year:
            discription = "original_price_year".localized
        case .forever:
            discription = "original_price".localized
        }
        
        let resultAttributedString = NSMutableAttributedString(string: discription)
        let range: NSRange = NSRange(location: 0, length: discription.count)
        var attributesArr:[NSAttributedString.Key: Any] = [
            .foregroundColor: textColor,
            .font: isSelect ? font : UIFont.systemFont(ofSize: 12)]
        if subscribePlan != .month {
            attributesArr[.strikethroughStyle] = lineValue
        }
        resultAttributedString.addAttributes(attributesArr,
            range: range)
        return resultAttributedString
    }
    
    func refreshView(isSelected: Bool) {
        if self.subscribePlan == .month {
            self.recommendedLabel.backgroundColor = isSelected ? UIColor(valueRGB: 0xFFEC53) : UIColor.clear
        }
        self.backgroundView.backgroundColor = isSelected ? UIColor(valueRGB: 0xFFF8C5) : UIColor(valueRGB: 0xF7F7F7)
        self.recommendedLabel.isHidden = self.subscribePlan != .month ? true : false
        self.recommendedLabel.backgroundColor = isSelected ? UIColor(valueRGB: 0xFFEC53) : UIColor.clear
        self.descriptionLabel.backgroundColor = isSelected ? UIColor(valueRGB: 0xFFEC53) : UIColor.clear
        self.descriptionLabel.attributedText = self.buildDiscriptionAttributedString(subscribePlan: self.subscribePlan, isSelect: isSelected)
    }
}


