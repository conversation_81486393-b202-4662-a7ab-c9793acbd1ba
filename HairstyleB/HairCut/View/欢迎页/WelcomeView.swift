//
//  WelcomeView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/7/30.
//

import Foundation
import UIKit
import SnapKit

protocol WelcomeViewDelegate: NSObjectProtocol {
    func paymentGestureClick()
    func termOfUseClcikAction()
    func privacyAgreementClickAction()
    func rePurchaseClickAction()
    func homeVCAction()
}

class WelcomeView: UIView, SubscribeViewDelegate {
    //视图
    private var imageView = UIImageView()
    private var continueButton = UIButton()
    private let progressBackGroundView = UIView()
    private let progressView = UIView()
    let subscribeView = SubscribeView()
    //数据
    public var pageStep: Int {
        willSet {
            self.pageStep = newValue
            if pageStep > 1 && pageStep <= 3 {
                self.refreshView()
            }
        }
    }
    private let isChineseSlimpalLanguage = LanguageTool.currentLanguage() == .chineseSimplified
    weak public var delegate: (WelcomeViewDelegate)?
    
    init() {
        self.pageStep = 1
        super.init(frame: .zero)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.setGradientBackground(colors: [UIColor(valueRGB: 0xFFF396), UIColor(valueRGB: 0xFFFFFF)], resizeBound: UIScreen.main.bounds)
        
        self.continueButton.setTitle("continue".localized, for: .normal)
        self.continueButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        self.continueButton.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        self.continueButton.layer.cornerRadius = 26
        self.continueButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        self.continueButton.addTarget(self, action: #selector(continueButtonClick), for: .touchUpInside)
        self.addSubview(self.continueButton)
        self.continueButton.snp.makeConstraints { make in
            make.left.equalTo(39)
            make.right.equalTo(-39)
            make.height.equalTo(52)
            make.bottom.equalToSuperview().offset(-87)
        }
        
        self.addSubview(self.imageView)
        self.imageView.image = UIImage(named: self.isChineseSlimpalLanguage == false ? "welcome_picture_1_en" : "welcome_picture_1")
        self.imageView.contentMode = .scaleAspectFit
        self.imageView.snp.makeConstraints { make in
            make.left.equalTo(18)
            make.right.equalTo(-18)
            make.top.equalTo(152)
            make.bottom.equalTo(self.continueButton.snp.top).offset(-93)
        }
        
        self.progressBackGroundView.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
        self.progressBackGroundView.layer.cornerRadius = 3
        self.addSubview(self.progressBackGroundView)
        self.progressBackGroundView.snp.makeConstraints { make in
            make.top.equalTo(continueButton.snp.top).offset(-41)
            make.centerX.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(6)
        }
        
        self.progressView.backgroundColor = UIColor(valueRGB: 0x333333)
        self.progressView.layer.cornerRadius = 3
        self.progressBackGroundView.addSubview(self.progressView)
        self.progressView.snp.makeConstraints { make in
            make.top.equalTo(self.progressBackGroundView)
            make.width.equalTo(20)
            make.height.equalTo(6)
            make.left.equalTo(0)
        }
        
    }
    
    @objc func continueButtonClick() {
        if self.pageStep == 3 {
            //有订阅去首页tabbar
            if APPMakeStoreIAPManager.featureVip() {
                if self.delegate != nil && (self.delegate?.responds(to: #selector(self.continueButtonClick)) != nil) {
                    self.subscribeView.removeFromSuperview()
                    self.delegate?.homeVCAction()
                }
            } else {
                //没有订阅则展示订阅页面
                self.subscribeView.delegate = self
                self.addSubview(self.subscribeView)
                subscribeView.snp.makeConstraints { make in
                    make.left.right.top.bottom.equalToSuperview()
                }
            }
        } else {
            self.pageStep += 1
        }
    }
    
    private func refreshView() {
        if self.pageStep == 2 {
            self.imageView.image = UIImage(named: self.isChineseSlimpalLanguage == false ? "welcome_picture_2_en" : "welcome_picture_2")
            self.progressView.removeFromSuperview()
            self.progressBackGroundView.addSubview(self.progressView)
            self.progressView.snp.makeConstraints { make in
                make.top.equalTo(self.progressBackGroundView)
                make.width.equalTo(20)
                make.height.equalTo(6)
                make.left.equalTo(20)
            }
        } else if self.pageStep >= 3 {
            self.imageView.image = UIImage(named: self.isChineseSlimpalLanguage == false ? "welcome_picture_3_en" : "welcome_picture_3")
            self.progressView.removeFromSuperview()
            self.progressBackGroundView.addSubview(self.progressView)
            self.progressView.snp.makeConstraints { make in
                make.top.equalTo(self.progressBackGroundView)
                make.width.equalTo(20)
                make.height.equalTo(6)
                make.left.equalTo(40)
            }
        } else {
            printLog(message: "Welcom-pageStep:\(pageStep)")
        }
    }
    
    //协议传递
    @objc func paymentGestureClick() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.paymentGestureClick)) != nil) {
            self.delegate?.paymentGestureClick()
        }
    }
    
    @objc func termOfUseClcikAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.termOfUseClcikAction)) != nil) {
            self.delegate?.termOfUseClcikAction()
        }
    }
    
    @objc func privacyAgreementClickAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.privacyAgreementClickAction)) != nil) {
            self.delegate?.privacyAgreementClickAction()
        }
    }
    
    @objc func rePurchaseClickAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.rePurchaseClickAction)) != nil) {
            self.delegate?.rePurchaseClickAction()
        }
    }
}
