//
//  LaunchScreenView.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/7/31.
//

import Foundation
import UIKit
import SnapKit

class LaunchScreenView: UIView {
    private let iconImage = UIImageView()
    private let appNameLabel = UILabel()
    private let appMessageLabel = UILabel()
    private let loadingLabel = UILabel()
    private let loadingView1 = UIView()
    private let loadingView2 = UIView()
    private let loadingView3 = UIView()
    private let loadingView4 = UIView()
    public var count: Int {
        willSet {
            if newValue == 1 {
                self.loadingView1.backgroundColor = UIColor(valueRGB: 0x333333)
                self.loadingView2.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
                self.loadingView3.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
                self.loadingView4.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
            } else if newValue == 2 {
                self.loadingView1.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
                self.loadingView2.backgroundColor = UIColor(valueRGB: 0x333333)
                self.loadingView3.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
                self.loadingView4.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
            } else if newValue == 3 {
                self.loadingView1.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
                self.loadingView2.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
                self.loadingView3.backgroundColor = UIColor(valueRGB: 0x333333)
                self.loadingView4.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
            } else if newValue == 4 {
                self.loadingView1.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
                self.loadingView2.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
                self.loadingView3.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
                self.loadingView4.backgroundColor = UIColor(valueRGB: 0x333333)
            } else {
                printLog(message: "error: \(newValue)")
            }
        }
    }
    
    init() {
        self.count = 0
        super.init(frame: .zero)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.setGradientBackground(colors: [UIColor(valueRGB: 0xFFF396), UIColor(valueRGB: 0xFFFFFF)], resizeBound: UIScreen.main.bounds)
        
        if let appIcon = UIApplication.shared.alternateIconName {
            self.iconImage.image = UIImage(named: appIcon)
        } else {
            self.iconImage.image = UIImage(named: AppSetting.appIconName)
        }
        self.addSubview(self.iconImage)
        self.iconImage.layer.cornerRadius = 24
        self.iconImage.layer.masksToBounds = true
        self.iconImage.snp.makeConstraints { make in
            make.width.equalTo(90)
            make.height.equalTo(90)
            make.centerX.equalToSuperview()
            make.top.equalTo(UIScreen.main.bounds.height * 269 / 812)
        }
        
        self.appNameLabel.text = "hairstyle_test".localized
        self.appNameLabel.adjustsFontSizeToFitWidth = true
        self.appNameLabel.textAlignment = .center
        self.appNameLabel.textColor = UIColor(valueRGB: 0x333333)
        self.appNameLabel.font = UIFont.systemFont(ofSize: 24)
        self.appNameLabel.numberOfLines = 0
        self.addSubview(self.appNameLabel)
        self.appNameLabel.snp.makeConstraints { make in
            make.top.equalTo(self.iconImage.snp.bottom).offset(28)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.height.greaterThanOrEqualTo(34)
        }
        
        self.appMessageLabel.text = "face_shape_test&hairstyle_change".localized
        self.appMessageLabel.adjustsFontSizeToFitWidth = true
        self.appMessageLabel.textAlignment = .center
        self.appMessageLabel.textColor = UIColor(valueRGB: 0x666666)
        self.appMessageLabel.font = UIFont.systemFont(ofSize: 13)
        self.appMessageLabel.numberOfLines = 0
        self.addSubview(self.appMessageLabel)
        self.appMessageLabel.snp.makeConstraints { make in
            make.top.equalTo(self.appNameLabel.snp.bottom).offset(28)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.height.greaterThanOrEqualTo(18)
        }
        
        self.loadingLabel.text = "loading".localized
        self.loadingLabel.textAlignment = .center
        self.loadingLabel.textColor = UIColor(valueRGB: 0x666666)
        self.loadingLabel.font = UIFont.systemFont(ofSize: 16)
        self.loadingLabel.numberOfLines = 0
        self.addSubview(self.loadingLabel)
        let bottom = UIScreen.main.bounds.height * 117 / 812
        self.loadingLabel.snp.makeConstraints { make in
            make.bottom.equalTo(self).offset(-bottom)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.height.greaterThanOrEqualTo(22)
        }
        
        self.loadingView2.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
        self.loadingView2.layer.cornerRadius = 3
        self.addSubview(self.loadingView2)
        self.loadingView2.snp.makeConstraints { make in
            make.top.equalTo(self.loadingLabel.snp.bottom).offset(16)
            make.width.equalTo(6)
            make.height.equalTo(6)
            make.centerX.equalToSuperview().offset(-8)
        }
        
        self.loadingView3.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
        self.loadingView3.layer.cornerRadius = 3
        self.addSubview(self.loadingView3)
        self.loadingView3.snp.makeConstraints { make in
            make.top.equalTo(self.loadingLabel.snp.bottom).offset(16)
            make.width.equalTo(6)
            make.height.equalTo(6)
            make.centerX.equalToSuperview().offset(8)
        }
        
        self.loadingView1.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
        self.loadingView1.layer.cornerRadius = 3
        self.addSubview(self.loadingView1)
        self.loadingView1.snp.makeConstraints { make in
            make.top.equalTo(self.loadingLabel.snp.bottom).offset(16)
            make.width.equalTo(6)
            make.height.equalTo(6)
            make.right.equalTo(self.loadingView2.snp.left).offset(-13)
        }
        
        self.loadingView4.backgroundColor = UIColor(valueRGB: 0xEEEEEE)
        self.loadingView4.layer.cornerRadius = 3
        self.addSubview(self.loadingView4)
        self.loadingView4.snp.makeConstraints { make in
            make.top.equalTo(self.loadingLabel.snp.bottom).offset(16)
            make.width.equalTo(6)
            make.height.equalTo(6)
            make.left.equalTo(self.loadingView3.snp.left).offset(13)
        }
    }
}
