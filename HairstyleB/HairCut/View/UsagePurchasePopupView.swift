//
//  UsagePurchasePopupView.swift
//  HairCut
//
//  Created by AI Assistant on 2025/01/21.
//

import UIKit
import SnapKit

/// 次数购买弹窗
class UsagePurchasePopupView: UIView {
    
    // MARK: - 回调
    var onPurchaseButtonTapped: (() -> Void)?
    var onSubscribeButtonTapped: (() -> Void)?
    var onDismiss: (() -> Void)?
    
    // MARK: - 属性
    var sourceFrom: String = "" // 来源标识，用于友盟埋点
    
    // MARK: - UI组件
    private let backgroundView = UIView()
    private let containerView = UIView()
    private let diamondImageView = UIImageView()
    private let purchaseButton = UIButton(type: .custom)
    private let subscribeButton = UIButton(type: .custom)
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    private func setupUI() {
        // 背景遮罩
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        addSubview(backgroundView)
        
        // 容器视图
        containerView.backgroundColor = UIColor.white
        containerView.layer.cornerRadius = 16
        containerView.layer.masksToBounds = true
        addSubview(containerView)
        
        // 钻石图片
        diamondImageView.image = UIImage(named: "次数购买")
        diamondImageView.contentMode = .scaleAspectFit
        containerView.addSubview(diamondImageView)
        
        // 购买次数按钮
        purchaseButton.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        purchaseButton.setTitle(local("¥10 购买10次使用次数"), for: .normal)
        purchaseButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        purchaseButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        purchaseButton.layer.cornerRadius = 25
        purchaseButton.layer.masksToBounds = true
        containerView.addSubview(purchaseButton)
        
        // 订阅会员按钮
        subscribeButton.backgroundColor = UIColor.white
        subscribeButton.setTitle(local("订阅会员"), for: .normal)
        subscribeButton.setTitleColor(UIColor(valueRGB: 0xFFEC53), for: .normal)
        subscribeButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subscribeButton.layer.cornerRadius = 25
        subscribeButton.layer.borderWidth = 2
        subscribeButton.layer.borderColor = UIColor(valueRGB: 0xFFEC53).cgColor
        subscribeButton.layer.masksToBounds = true
        containerView.addSubview(subscribeButton)
    }
    
    private func setupConstraints() {
        // 背景遮罩
        safeConstraints(backgroundView) { make in
            make.edges.equalToSuperview()
        }

        // 容器视图 - 307*336
        safeConstraints(containerView) { make in
            make.center.equalToSuperview()
            make.width.equalTo(307)
            make.height.equalTo(336)
        }

        // 钻石图片 - 307*230
        safeConstraints(diamondImageView) { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.equalTo(307)
            make.height.equalTo(230)
        }



        // 订阅会员按钮
        safeConstraints(subscribeButton) { make in
            make.bottom.equalTo(-20)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.height.equalTo(50)
            make.bottom.equalTo(-20)
        }

        // 购买次数按钮
        safeConstraints(purchaseButton) { make in
            make.bottom.equalTo(self.subscribeButton.snp.top).offset(-16)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.height.equalTo(50)
        }
    }
    
    private func setupActions() {
        // 背景点击隐藏
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(tapGesture)
        
        // 按钮点击事件
        purchaseButton.addTarget(self, action: #selector(purchaseButtonTapped), for: .touchUpInside)
        subscribeButton.addTarget(self, action: #selector(subscribeButtonTapped), for: .touchUpInside)
    }
    
    // MARK: - 公共方法

    /// 更新购买按钮价格
    func updatePurchasePrice(_ price: String) {
        purchaseButton.setTitle("\(price) " + local("购买10次使用次数"), for: .normal)
    }

    /// 更新UI布局（根据是否为VIP用户）
    func updateLayoutForVIPStatus(_ isVIP: Bool) {
        if isVIP {
            // VIP用户：隐藏订阅按钮，购买按钮移到订阅按钮位置
            subscribeButton.isHidden = true

            // 重新设置购买按钮约束
            safeRemakeConstraints(purchaseButton) { make in
                make.left.equalTo(20)
                make.right.equalTo(-20)
                make.height.equalTo(50)
                make.bottom.equalTo(-20)  // 直接到底部
            }
        } else {
            // 非VIP用户：显示订阅按钮，保持原有布局
            subscribeButton.isHidden = false

            // 订阅会员按钮
            safeConstraints(subscribeButton) { make in
                make.bottom.equalTo(-20)
                make.left.equalTo(20)
                make.right.equalTo(-20)
                make.height.equalTo(50)
                make.bottom.equalTo(-20)
            }

            // 购买次数按钮
            safeConstraints(purchaseButton) { make in
                make.bottom.equalTo(self.subscribeButton.snp.top).offset(-16)
                make.left.equalTo(20)
                make.right.equalTo(-20)
                make.height.equalTo(50)
            }
        }
    }
    
    /// 显示弹窗 - 使用PopView
    func show() {
        // 设置视图大小
        frame = UIScreen.main.bounds

        // 使用PopView弹出全屏视图，遮住tabbar
        PopView.popSideContentView(self, direct: PopViewDirection.slideInCenter)
    }

    /// 显示弹窗但不替换旧弹窗 - 用于多层弹窗场景
    func showWithoutReplacingOldView() {
        // 设置视图大小
        frame = UIScreen.main.bounds

        // 使用PopView的特殊方法弹出，保持旧弹窗存在
        let showAnimation = CABasicAnimation(keyPath: "transform.scale")
        showAnimation.fromValue = 0.3
        showAnimation.toValue = 1.0
        showAnimation.duration = 0.3
        showAnimation.timingFunction = CAMediaTimingFunction(name: .easeOut)
        
        let hidenAnimation = CABasicAnimation(keyPath: "transform.scale")
        hidenAnimation.fromValue = 1.0
        hidenAnimation.toValue = 0.3
        hidenAnimation.duration = 0.25
        hidenAnimation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        
        PopView.popContentView(self, show: showAnimation, hidenAnimation: hidenAnimation)
    }

    /// 隐藏弹窗
    func hide() {
        PopView.hidenPopView()
        onDismiss?()
    }
    
    // MARK: - 事件处理
    
    @objc private func backgroundTapped() {
        hide()
    }
    
    @objc private func purchaseButtonTapped() {
        print("🔥 购买按钮被点击了！")

        // 友盟埋点：点击10次次数包
        MobClick.event("Subscribe", attributes: ["Subscription_pop_up": "点击10次次数包"])

        print("🔥 准备调用onPurchaseButtonTapped回调")
        onPurchaseButtonTapped?()
        print("🔥 onPurchaseButtonTapped回调调用完成")
    }
    
    @objc private func subscribeButtonTapped() {
        // 友盟埋点：点击会员订阅
        MobClick.event("Subscribe", attributes: ["Subscription_pop_up": "点击会员订阅（点一次算一次）"])
        
        // 根据来源添加相应的友盟埋点
        if sourceFrom == "AI美甲" {
            MobClick.event("Subscribe", attributes: ["Source": "AI美甲"])
        }
        
        onSubscribeButtonTapped?()
    }
}
