//
//  NailCameraViewController.swift
//  HairCut
//
//  Created by AI Assistant on 2025/1/14.
//

import UIKit
import AVFoundation
import SnapKit

// MARK: - Delegate Protocol
protocol NailCameraViewControllerDelegate: AnyObject {
    func nailCameraDidSelectImage(_ image: UIImage)
    func nailCameraDidCancel()
}

class NailCameraViewController: UIViewController {
    
    // MARK: - Properties
    weak var delegate: NailCameraViewControllerDelegate?
    
    private var captureSession: AVCaptureSession?
    private var videoPreviewLayer: AVCaptureVideoPreviewLayer?
    private var photoOutput: AVCapturePhotoOutput?
    private var currentCameraPosition: AVCaptureDevice.Position = .back
    
    // MARK: - UI Elements
    private let topView = UIView()
    private let bottomView = UIView()
    private let cameraContainerView = UIView()

    private let handImageView = UIImageView()
    private let backButton = UIButton(type: .custom)
    private let captureButton = UIButton(type: .custom)
    private let flipButton = UIButton(type: .custom)
    private let galleryButton = UIButton(type: .custom)
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupCamera()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 隐藏导航栏
        navigationController?.setNavigationBarHidden(true, animated: animated)
        startCameraSession()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 恢复导航栏显示
        navigationController?.setNavigationBarHidden(false, animated: animated)
        stopCameraSession()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 确保相机预览层在布局变化时正确更新
        if let previewLayer = videoPreviewLayer {
            previewLayer.frame = cameraContainerView.bounds
        }
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = .black

        // 设置顶部视图
        topView.backgroundColor = .white
        view.addSubview(topView)

        // 设置相机容器视图
        cameraContainerView.backgroundColor = .black
        view.addSubview(cameraContainerView)

        // 设置底部视图
        bottomView.backgroundColor = .white
        view.addSubview(bottomView)

        // 设置手掌图片 - 添加到相机容器中
        handImageView.image = UIImage(named: "手掌")
        handImageView.contentMode = .scaleAspectFit
        handImageView.alpha = 0.6
        cameraContainerView.addSubview(handImageView)

        // 设置返回按钮 - 添加到顶部视图
        backButton.setImage(UIImage(named: "返回"), for: .normal)
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        topView.addSubview(backButton)

        // 设置拍照按钮 - 添加到底部视图
        captureButton.setImage(UIImage(named: "拍摄"), for: .normal)
        captureButton.addTarget(self, action: #selector(captureButtonTapped), for: .touchUpInside)
        bottomView.addSubview(captureButton)

        // 设置翻转按钮 - 添加到底部视图
        flipButton.setImage(UIImage(named: "翻转"), for: .normal)
        flipButton.setTitle(local("翻转"), for: .normal)
        flipButton.setTitleColor(.black, for: .normal)
        flipButton.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        flipButton.setImagePosition(with: .top, spacing: 4)
        flipButton.addTarget(self, action: #selector(flipButtonTapped), for: .touchUpInside)
        bottomView.addSubview(flipButton)

        // 设置图库按钮 - 添加到底部视图
        galleryButton.setImage(UIImage(named: "图库"), for: .normal)
        galleryButton.setTitle(local("图库"), for: .normal)
        galleryButton.setTitleColor(.black, for: .normal)
        galleryButton.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        galleryButton.setImagePosition(with: .top, spacing: 4)
        galleryButton.addTarget(self, action: #selector(galleryButtonTapped), for: .touchUpInside)
        bottomView.addSubview(galleryButton)

        setupConstraints()
    }
    
    private func setupConstraints() {
        
        // 返回按钮约束 - 在顶部视图左侧
        
        
        // 顶部视图约束
        safeConstraints(topView) { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(100)
        }

        safeConstraints(backButton) { make in
            make.top.equalTo(self.view.safeAreaLayoutGuide.snp.top).offset(10)
            make.left.equalToSuperview().offset(16)
            make.width.height.equalTo(30)
        }

        // 底部视图约束
        safeConstraints(bottomView) { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(150)
        }

        // 相机容器视图约束 - 在顶部和底部视图之间
        safeConstraints(cameraContainerView) { make in
            make.top.equalTo(self.topView.snp.bottom)
            make.bottom.equalTo(self.bottomView.snp.top)
            make.left.right.equalToSuperview()
        }

        // 手掌图片约束 - 在相机容器中居中显示
        safeConstraints(handImageView) { make in
            make.center.equalTo(self.cameraContainerView)
            make.width.equalTo(SCREEN_WIDTH * 0.8)
            make.height.equalTo(SCREEN_WIDTH * 0.8 * 1.2) // 手掌比例约为1:1.2
        }

        

        // 拍照按钮约束 - 在底部视图中央
        safeConstraints(captureButton) { make in
            make.center.equalTo(self.bottomView)
            make.width.height.equalTo(80)
        }

        // 图库按钮约束 - 在底部视图，拍照按钮左侧
        safeConstraints(galleryButton) { make in
            make.centerY.equalTo(self.captureButton)
            make.right.equalTo(self.captureButton.snp.left).offset(-60)
            make.width.equalTo(60)
            make.height.equalTo(80)
        }

        // 翻转按钮约束 - 在底部视图，拍照按钮右侧
        safeConstraints(flipButton) { make in
            make.centerY.equalTo(self.captureButton)
            make.left.equalTo(self.captureButton.snp.right).offset(60)
            make.width.equalTo(60)
            make.height.equalTo(80)
        }
    }
    
    // MARK: - Camera Setup
    private func setupCamera() {
        // 检查相机权限
        checkCameraPermission { [weak self] granted in
            if granted {
                self?.initializeCamera()
            } else {
                self?.showPermissionAlert()
            }
        }
    }

    private func checkCameraPermission(completion: @escaping (Bool) -> Void) {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            completion(true)
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    completion(granted)
                }
            }
        case .denied, .restricted:
            completion(false)
        @unknown default:
            completion(false)
        }
    }

    private func initializeCamera() {
        captureSession = AVCaptureSession()
        guard let session = captureSession else { return }
        session.sessionPreset = .photo

        guard let backCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
            print("Unable to access back camera!")
            return
        }

        do {
            let input = try AVCaptureDeviceInput(device: backCamera)
            photoOutput = AVCapturePhotoOutput()

            guard let output = photoOutput else { return }

            if session.canAddInput(input) && session.canAddOutput(output) {
                session.addInput(input)
                session.addOutput(output)

                setupLivePreview()
            }
        } catch let error {
            print("Error Unable to initialize back camera: \(error.localizedDescription)")
        }
    }

    private func showPermissionAlert() {
        let alert = UIAlertController(title: local("相机权限"), message: local("需要相机权限来拍摄照片"), preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: local("设置"), style: .default) { _ in
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        })
        alert.addAction(UIAlertAction(title: local("取消"), style: .cancel) { [weak self] _ in
            self?.delegate?.nailCameraDidCancel()
            self?.dismiss(animated: true)
        })
        present(alert, animated: true)
    }
    
    private func setupLivePreview() {
        guard let session = captureSession else { return }
        videoPreviewLayer = AVCaptureVideoPreviewLayer(session: session)
        guard let previewLayer = videoPreviewLayer else { return }

        previewLayer.videoGravity = .resizeAspectFill
        previewLayer.connection?.videoOrientation = .portrait
        cameraContainerView.layer.insertSublayer(previewLayer, at: 0)

        DispatchQueue.main.async {
            previewLayer.frame = self.cameraContainerView.bounds
        }
    }
    
    private func startCameraSession() {
        DispatchQueue.global(qos: .userInitiated).async {
            guard let session = self.captureSession, !session.isRunning else { return }
            session.startRunning()
        }
    }

    private func stopCameraSession() {
        DispatchQueue.global(qos: .userInitiated).async {
            guard let session = self.captureSession, session.isRunning else { return }
            session.stopRunning()
        }
    }
    
    // MARK: - Button Actions
    @objc private func backButtonTapped() {
        delegate?.nailCameraDidCancel()
        dismiss(animated: true, completion: nil)
    }
    
    @objc private func captureButtonTapped() {
        guard let output = photoOutput else { return }
        let settings = AVCapturePhotoSettings(format: [AVVideoCodecKey: AVVideoCodecType.jpeg])
        output.capturePhoto(with: settings, delegate: self)
    }
    
    @objc private func flipButtonTapped() {
        switchCamera()
    }
    
    @objc private func galleryButtonTapped() {
        PTMPhotoPickerHelper.presentPhotoPicker(from: self) { [weak self] image in
            if let image = image {
                self?.delegate?.nailCameraDidSelectImage(image)
                self?.dismiss(animated: true, completion: nil)
            }
        }
    }
    
    // MARK: - Camera Functions
    private func switchCamera() {
        guard let session = captureSession else { return }
        session.beginConfiguration()

        // Remove current input
        if let currentInput = session.inputs.first as? AVCaptureDeviceInput {
            session.removeInput(currentInput)
        }

        // Switch camera position
        currentCameraPosition = currentCameraPosition == .back ? .front : .back

        // Add new input
        guard let newCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: currentCameraPosition) else {
            print("Unable to access camera!")
            session.commitConfiguration()
            return
        }

        do {
            let newInput = try AVCaptureDeviceInput(device: newCamera)
            if session.canAddInput(newInput) {
                session.addInput(newInput)
            }
        } catch let error {
            print("Error switching camera: \(error.localizedDescription)")
        }

        session.commitConfiguration()
    }
}

// MARK: - AVCapturePhotoCaptureDelegate
extension NailCameraViewController: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        guard let imageData = photo.fileDataRepresentation() else { return }
        guard let image = UIImage(data: imageData) else { return }

        // 直接返回图片给delegate
        delegate?.nailCameraDidSelectImage(image)
        dismiss(animated: true, completion: nil)
    }
}
