//
//  HairStyleMainListHairCell.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/11/15.
//

import Foundation
import UIKit
import SDWebImage

class HairStyleMainListHairCell: UICollectionViewCell {
    static let identifier = "HairStyleMainListHairCell"
    
    private var imageView = UIImageView()
    public var imageUrl : String? {
        willSet {
            self.imageView.sd_setImage(with: URL(string: newValue ?? "")) { [weak self]image, error, cacgeType, url in
                if error != nil {
                    printLog(message: "\(String(describing: error))")
                    return
                }
                self?.imageView.image = image
            }
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.layer.cornerRadius = 10
        self.layer.masksToBounds = true
        self.backgroundColor = UIColor.white
        self.addSubview(self.imageView)
        self.imageView.contentMode = .scaleAspectFit
        self.imageView.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
    }
}
