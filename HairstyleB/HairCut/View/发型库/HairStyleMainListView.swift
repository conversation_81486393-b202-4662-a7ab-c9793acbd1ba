//
//  HairStyleMainListView.swift
//  HairCut
//
//  Created by Bigger on 2024/11/14.
//

import Foundation
import UIKit

@objc protocol HairStyleMainListViewDelegate: NSObjectProtocol {
    func selectHair(hairUrlString: String, selectIndex: Int)
}

class HairStyleMainListView: UIView {
    private let topChooseView = UIView()
    private let maleLabel = UILabel()
    private let maleLabelColorView = UIView()
    private let femaleLabel = UILabel()
    private let femaleLabelColorView = UIView ()
    private var hairCollectionView: UICollectionView? = nil
    
    weak public var delegate: HairStyleMainListViewDelegate?
    public var imageArr = [HaitStyleHairData]()
    private var showImageArr = [HaitStyleHairData]()
    public var isMale = true;
    
    init() {
        super.init(frame: .zero)
        self.backgroundColor = UIColor(valueRGB: 0xF7F7F7)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.addSubview(self.topChooseView);
        self.topChooseView.backgroundColor = UIColor(valueRGB: 0xFFFFFF)
        self.topChooseView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(88)
        }
        self.topChooseView.layoutIfNeeded()
        self.topChooseView.addSubview(self.maleLabelColorView)
        self.maleLabelColorView.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        self.maleLabelColorView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-15);
            make.centerX.equalToSuperview().multipliedBy(2.0 / 3.0)
            make.width.greaterThanOrEqualTo(72)
            make.height.equalTo(8)
        }
        
        let isChinese = LanguageTool.currentLanguage() == .chineseSimplified || LanguageTool.currentLanguage() == .chineseTranditional
        self.maleLabel.text = "hairstyle_top_male".localized
        self.maleLabel.textColor = UIColor(valueRGB: 0x000000)
        self.maleLabel.isUserInteractionEnabled = true
        self.maleLabel.textAlignment = .center
        self.maleLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        self.topChooseView.addSubview(self.maleLabel)
        self.maleLabel.snp.makeConstraints { make in
            make.left.right.bottom.equalTo(self.maleLabelColorView)
            make.height.equalTo(isChinese ? 16 : 20)
        }
        let maleLabelTap = UITapGestureRecognizer()
        maleLabelTap.addTarget(self, action: #selector(tapAction(_:)))
        self.maleLabel.addGestureRecognizer(maleLabelTap)
        
        self.topChooseView.addSubview(self.femaleLabelColorView)
        self.femaleLabelColorView.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        self.femaleLabelColorView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-15);
            make.centerX.equalToSuperview().multipliedBy(4.0 / 3.0)
            make.width.greaterThanOrEqualTo(72)
            make.height.equalTo(8)
        }
        self.femaleLabelColorView.isHidden = true
        
        self.femaleLabel.text = "hairstyle_top_female".localized
        self.femaleLabel.textColor = UIColor(valueRGB: 0x000000)
        self.femaleLabel.isUserInteractionEnabled = true
        self.femaleLabel.textAlignment = .center
        self.femaleLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        self.topChooseView.addSubview(self.femaleLabel)
        self.femaleLabel.snp.makeConstraints { make in
            make.left.right.bottom.equalTo(self.femaleLabelColorView)
            make.height.equalTo(isChinese ? 16 : 20)
        }
        let femaleLabelTap = UITapGestureRecognizer()
        femaleLabelTap.addTarget(self, action: #selector(tapAction(_:)))
        self.femaleLabel.addGestureRecognizer(femaleLabelTap)
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.itemSize = CGSize(width: 105, height: 118)
        layout.minimumLineSpacing = 5
        layout.minimumInteritemSpacing = 5
        self.hairCollectionView = UICollectionView(frame: CGRect(x: 0, y: 16, width: UIScreen.main.bounds.width - 53, height: UIScreen.main.bounds.height), collectionViewLayout: layout)
        self.hairCollectionView?.showsVerticalScrollIndicator = false
        self.addSubview(self.hairCollectionView ?? UICollectionView())
        self.hairCollectionView?.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview()
            make.top.equalTo(self.topChooseView.snp.bottom).offset(26)
        }
        self.hairCollectionView?.collectionViewLayout = layout
        self.hairCollectionView?.delegate = self
        self.hairCollectionView?.dataSource = self
        self.hairCollectionView?.backgroundColor = UIColor.clear
        self.hairCollectionView?.bounces = false
        self.hairCollectionView?.register(HairStyleMainListHairCell.self, forCellWithReuseIdentifier: HairStyleMainListHairCell.identifier)
    }
    
    @objc func tapAction(_ gesture: UIGestureRecognizer) {
        if gesture.view == self.maleLabel {
            if self.isMale == true {
                return
            }
            self.femaleLabelColorView.isHidden = true
            self.maleLabelColorView.isHidden = false
            self.isMale = true
            self.refreshData()
        } else if gesture.view == self.femaleLabel {
            if self.isMale == false {
                return
            }
            self.femaleLabelColorView.isHidden = false
            self.maleLabelColorView.isHidden = true
            self.isMale = false
            self.refreshData()
        }
    }
    
    public func refreshData() {
        var images = [HaitStyleHairData]()
        self.imageArr.forEach { data in
            if data.isMale == self.isMale {
                images.append(data)
            }
        }
        
        self.showImageArr = images
        self.hairCollectionView?.reloadData()
    }
}

typealias HairStyleMainListViewCollectionDelegate = HairStyleMainListView
extension HairStyleMainListViewCollectionDelegate: UICollectionViewDelegate, UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.showImageArr.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HairStyleMainListHairCell.identifier, for: indexPath) as? HairStyleMainListHairCell else {
            return UICollectionViewCell()
        }

        cell.imageUrl = self.showImageArr[indexPath.row].smallImage
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if let delegate = self.delegate,delegate.responds(to:#selector(delegate.selectHair(hairUrlString:selectIndex:))) {
            delegate.selectHair(hairUrlString: self.showImageArr[indexPath.row].bigImage ?? "", selectIndex: indexPath.row)
        }
    }
}
