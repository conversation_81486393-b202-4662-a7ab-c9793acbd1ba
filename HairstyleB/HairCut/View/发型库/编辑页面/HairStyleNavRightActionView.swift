//
//  HairStyleNavRightActionView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/11/15.
//

import Foundation
import UIKit

protocol HairStyleNavRightActionViewDelegate: NSObjectProtocol {
    func saveImageAction()
    func changeImageAction()
}

class HairStyleNavRightActionView: UIView {
    private var saveButton = UIButton()
    private var changeButton = UIButton()
    
    public var isEnable: Bool = true {
        willSet {
            DispatchQueue.main.async {
                self.saveButton.isEnabled = newValue
                self.changeButton.isEnabled = newValue
            }
        }
    }
    weak public var delegate: HairStyleNavRightActionViewDelegate?
    
    init() {
        super.init(frame: .zero)
        self.backgroundColor = UIColor.clear
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.isUserInteractionEnabled = true
        self.saveButton = UIButton(type: .custom)
        self.saveButton.isUserInteractionEnabled = true
        self.saveButton.layer.cornerRadius = 13
        self.saveButton.setTitle("save".localized, for: .normal)
        self.saveButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        self.saveButton.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        self.saveButton.titleLabel?.font = UIFont.systemFont(ofSize: 13)
        self.addSubview(self.saveButton)
        self.saveButton.snp.makeConstraints { make in
            make.width.equalTo(52)
            make.height.equalTo(26)
            make.centerY.equalToSuperview()
            make.right.equalToSuperview()
        }
        self.saveButton.addTarget(self, action: #selector(saveImageAction), for: .touchUpInside)
        
        let isVi = LanguageTool.currentLanguage() == .vietnamese
        
        self.changeButton = UIButton(type: .custom)
        self.changeButton.isUserInteractionEnabled = true
        self.changeButton.layer.cornerRadius = 13
        self.changeButton.setTitle("change_picture".localized, for: .normal)
        self.changeButton.titleEdgeInsets = UIEdgeInsets(top: 0, left: 5, bottom: 0, right: 5)
        self.changeButton.titleLabel?.adjustsFontSizeToFitWidth = true
        self.changeButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        self.changeButton.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        self.changeButton.titleLabel?.font = UIFont.systemFont(ofSize: 13)
        self.addSubview(self.changeButton)
        self.changeButton.snp.makeConstraints { make in
            if isVi {
                make.width.equalTo(88)
            } else {
                make.width.equalTo(78)
            }
            
            make.height.equalTo(26)
            make.centerY.equalToSuperview()
            make.right.equalTo(self.saveButton.snp.left).offset(-10)
        }
        
        self.changeButton.addTarget(self, action: #selector(changeImageAction), for: .touchUpInside)
        
    }
    
    @objc func saveImageAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.saveImageAction)) != nil) {
            self.delegate?.saveImageAction()
        }
    }
    
    @objc func changeImageAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.changeImageAction)) != nil) {
            self.delegate?.changeImageAction()
        }
    }
    
    override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        if self.saveButton.frame.contains(point) {
            return self.saveButton
        }
        
        if self.changeButton.frame.contains(point) {
            return self.changeButton
        }
        
        return super.hitTest(point, with: event)
    }
}
