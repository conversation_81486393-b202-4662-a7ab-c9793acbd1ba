//
//  HairStyleEditHairChooseView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/11/15.
//

import Foundation
import UIKit
import SwiftyJ<PERSON>N

@objc protocol HairStyleEditHairChooseViewDelegate: NSObjectProtocol {
    func selectHair(hairUrlString: String, selectIndex: Int)
    func changeGender()
    func scrollAction()
}

class HairStyleEditHairChooseView: UIView {
    public var maleImageArr = [HaitStyleHairData]()
    public var femalImageArr = [HaitStyleHairData]()
    public var maleSelectedIndex: Int? = nil
    public var femaleSelcetedIndex: Int? = nil
    public var isMale: Bool = false {
        willSet {
            self.genderLabel.text = newValue ? "hairstyle_top_male".localized : "hairstyle_top_female".localized
            
            MobClick.event("Hairstyle_Library", attributes: ["source": self.genderLabel.text])
        }
    }
    
    private var hairCollectionView : UICollectionView? = nil
    private var scrollTouchView = UIView()
    private var scrollActionView = UIView()
    
    private var genderItem = UIView()
    private var genderLabel = UILabel()
    private var genderImage = UIImageView()
    
    weak public var delegate: HairStyleEditHairChooseViewDelegate?
    
    init() {
        super.init(frame: .zero)
        self.backgroundColor = UIColor(valueRGB: 0xFFFFFF)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.layoutIfNeeded()
        self.backgroundColor = UIColor(valueRGB: 0xFFFFFF)
        self.addSubview(self.scrollTouchView)
        self.scrollTouchView.isUserInteractionEnabled = true
        self.scrollTouchView.backgroundColor = UIColor.clear
        self.scrollTouchView.snp.makeConstraints { make in
            make.width.equalTo(50)
            make.height.equalTo(30)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
        }
        let scrollGesture = UITapGestureRecognizer()
        scrollGesture.addTarget(self, action: #selector(scrollAction))
        self.scrollTouchView.addGestureRecognizer(scrollGesture)
        
        self.scrollTouchView.addSubview(self.scrollActionView)
        self.scrollActionView.layer.cornerRadius = 1.5
        self.scrollActionView.layer.masksToBounds = true
        self.scrollActionView.backgroundColor = UIColor(valueRGB: 0x333333)
        self.scrollActionView.snp.makeConstraints { make in
            make.width.equalTo(30)
            make.height.equalTo(3)
            make.centerX.equalToSuperview()
            make.top.equalTo(10)
        }
        
        let isVi = LanguageTool.currentLanguage() == .vietnamese
        
        let plus = UIDevice.current.userInterfaceIdiom == .pad ? 1.5 : 1
        self.genderItem.layer.cornerRadius = 13
        self.genderItem.layer.masksToBounds = true
        self.genderItem.layer.borderColor = UIColor(valueRGB: 0xDBDBDB).cgColor
        self.genderItem.layer.borderWidth = 1
        self.addSubview(self.genderItem)
        self.genderItem.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            if isVi {
                make.width.equalTo(88 * plus)
            } else {
                make.width.equalTo(76 * plus)
            }
            
            make.height.equalTo(26 * plus)
        }
        
        self.genderItem.addSubview(self.genderImage)
        self.genderImage.image = UIImage(named: "hairstyle_gender_change")
        self.genderImage.snp.makeConstraints { make in
            make.height.width.equalTo(18 * plus)
            make.right.equalToSuperview().offset(-4 * plus)
            make.centerY.equalToSuperview()
        }
        self.genderItem.isUserInteractionEnabled = true
        let genderGesture = UITapGestureRecognizer()
        genderGesture.addTarget(self, action: #selector(genderChangeAction))
        self.genderItem.addGestureRecognizer(genderGesture)
        
        self.genderItem.addSubview(self.genderLabel)
        self.genderLabel.text = self.isMale ? "hairstyle_top_male".localized : "hairstyle_top_female".localized
        self.genderLabel.textColor = UIColor(valueRGB: 0x000000)
        self.genderLabel.textAlignment = .center
        self.genderLabel.font = UIFont.systemFont(ofSize: 10 * plus, weight: .medium)
        self.genderLabel.adjustsFontSizeToFitWidth = true
        self.genderLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12 * plus)
            make.top.equalTo(5 * plus)
            make.bottom.equalTo(-5 * plus)
            make.right.equalTo(self.genderImage.snp.left).offset(-1)
        }
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.itemSize = CGSize(width: 105, height: 118)
        layout.minimumLineSpacing = 5
        layout.minimumInteritemSpacing = 5
        self.hairCollectionView = UICollectionView(frame: CGRect(x: 0, y: 16, width: UIScreen.main.bounds.width - 53, height: UIScreen.main.bounds.height), collectionViewLayout: layout)
        self.hairCollectionView?.showsVerticalScrollIndicator = false
        self.addSubview(self.hairCollectionView ?? UICollectionView())
        self.hairCollectionView?.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview()
            make.top.equalTo(self.genderItem.snp.bottom).offset(16)
        }
        self.hairCollectionView?.collectionViewLayout = layout
        self.hairCollectionView?.delegate = self
        self.hairCollectionView?.dataSource = self
        self.hairCollectionView?.backgroundColor = UIColor.clear
        self.hairCollectionView?.bounces = false
        self.hairCollectionView?.register(HairStyleEditHairChooseCell.self, forCellWithReuseIdentifier: HairStyleEditHairChooseCell.identifier)
    }
    
    @objc func scrollAction() {
        if let delegate = self.delegate,delegate.responds(to:#selector(delegate.scrollAction)) {
            delegate.scrollAction()
        }
    }
    
    @objc func genderChangeAction() {
        self.isMale = !self.isMale
        self.refreshData()
        if let delegate = self.delegate,delegate.responds(to:#selector(delegate.changeGender)) {
            delegate.changeGender()
        }
    }
    
    public func refreshData() {
        self.hairCollectionView?.reloadData()
    }
    
    public func scrollToRow() {
        self.hairCollectionView?.layoutIfNeeded()
        guard let index = self.isMale ? self.maleSelectedIndex : self.femaleSelcetedIndex,
              let numberItem = self.hairCollectionView?.numberOfItems(inSection: 0),
                index < numberItem else {
            self.hairCollectionView?.scrollToItem(at: IndexPath(item: 0, section: 0), at: .top, animated: true)
            return
        }
        let indexPath = IndexPath(item: index, section: 0)
        self.hairCollectionView?.scrollToItem(at: indexPath, at: .top, animated: true)
    }
}

typealias HairStyleEditHairChooseViewCollectionDelegate = HairStyleEditHairChooseView
extension HairStyleEditHairChooseViewCollectionDelegate: UICollectionViewDelegate, UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return isMale == true ? self.maleImageArr.count : self.femalImageArr.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HairStyleEditHairChooseCell.identifier, for: indexPath) as? HairStyleEditHairChooseCell else {
            return UICollectionViewCell()
        }
        
        if isMale == true {
            cell.imageUrl = self.maleImageArr[indexPath.row].smallImage
            cell.isSelectedItem = indexPath.row == self.maleSelectedIndex
        } else {
            cell.imageUrl = self.femalImageArr[indexPath.row].smallImage
            cell.isSelectedItem = indexPath.row == self.femaleSelcetedIndex
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if isMale == true {
            self.maleSelectedIndex = indexPath.row
        } else {
            self.femaleSelcetedIndex = indexPath.row
        }
        
        self.hairCollectionView?.reloadData()
        if let delegate = self.delegate,delegate.responds(to:#selector(delegate.selectHair(hairUrlString:selectIndex:))) {
            var hairUrlString = ""
            if isMale == true {
                hairUrlString = self.maleImageArr[indexPath.row].bigImage ?? ""
            } else {
                hairUrlString = self.femalImageArr[indexPath.row].bigImage ?? ""
            }
            delegate.selectHair(hairUrlString: hairUrlString, selectIndex: indexPath.row)
        }
    }
}
