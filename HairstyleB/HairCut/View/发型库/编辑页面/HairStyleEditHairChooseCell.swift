//
//  HairStyleEditHairChooseCell.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/11/15.
//

import Foundation
import UIKit
import SDWebImage

class HairStyleEditHairChooseCell: UICollectionViewCell {
    static let identifier = "HairStyleEditHairChooseCell"
    
    private var imageView = UIImageView()
    public var isSelectedItem: Bool = false {
        willSet {
            self.layer.borderColor = newValue == true ? UIColor.hex(string: "#FFEC53").cgColor : UIColor.hex(string: "#F7F7F7").cgColor
        }
    }
    
    public var imageUrl : String? {
        willSet {
            self.imageView.sd_setImage(with: URL(string: newValue ?? "")) { [weak self]image, error, cacgeType, url in
                if error != nil {
                    printLog(message: "\(String(describing: error))")
                    return
                }
                self?.imageView.image = image
            }
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.layer.cornerRadius = 10
        self.layer.masksToBounds = true
        self.layer.borderColor = self.isSelectedItem == true ? UIColor(valueRGB: 0xFFEC53).cgColor : UIColor(valueRGB: 0xF7F7F7).cgColor
        self.layer.borderWidth = 3
        self.backgroundColor = UIColor(valueRGB: 0xF7F7F7)
        self.addSubview(self.imageView)
        self.imageView.contentMode = .scaleAspectFit
        self.imageView.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
    }
}
