//
//  HairStyleEditColorChooseView.swift
//  HairCut
//
//  Created by Bigger on 2024/11/15.
//

import Foundation
import UIKit

@objc protocol HairStyleEditColorChooseViewDelegate: NSObjectProtocol {
    func addHairColorAction(color: UIColor)
    func updateHairColorAction(color: UIColor)
}

class HairStyleEditColorChooseView: UIView {
    private let colorPickerView = HDColorPickerView()
    private let barView = HDColorPickerBarView()
    private let addBtn = UIButton()
    public var colorCollectionView: UICollectionView? = nil
    private var colorArray = [String]()
    private var moveColor: UIColor? = nil
    private let plus = UIDevice.current.userInterfaceIdiom == .pad ? 1.5 : 1
    
    weak public var delegate: HairStyleEditColorChooseViewDelegate?
    
    init() {
        super.init(frame: .zero)
        self.backgroundColor = UIColor(valueRGB: 0xFFFFFF)
        self.moveColor = UIColor.red
        self.colorArray = HDEditManager.shared.colorHexArray
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.isUserInteractionEnabled = true
        self.layoutIfNeeded()
        self.layer.cornerRadius = 12
        self.layer.masksToBounds = true
        
        self.addSubview(self.colorPickerView)
        self.colorPickerView.snp.makeConstraints { make in
            make.top.equalTo(16 * plus)
            make.left.equalTo(11 * plus)
            make.right.equalTo(-11 * plus)
            make.height.equalTo(HDColorPickerConst.colorPickerGradientHeight * plus)
        }
        self.colorPickerView.layoutIfNeeded()
        
        let pickerPointView = HDPointView(frame: CGRect(x: 0, y: 0, width: HDColorPickerConst.colorPickerGradientBtnWidth * plus, height: HDColorPickerConst.colorPickerGradientBtnWidth * plus))
        pickerPointView.backgroundColor = UIColor.clear
        pickerPointView.layer.cornerRadius = 10.0
        pickerPointView.layer.borderWidth = 2.0
        pickerPointView.layer.borderColor = UIColor.white.cgColor
        self.colorPickerView.pointView = pickerPointView
        self.colorPickerView.pickerLayer.cornerRadius = 8
        self.colorPickerView.pickerLayer.masksToBounds = true
        
        self.addSubview(self.barView)
        self.barView.snp.makeConstraints { make in
            make.left.right.equalTo(self.colorPickerView)
            make.top.equalTo(self.colorPickerView.snp.bottom).offset(15 * plus)
            make.height.equalTo(8 * plus)
        }
        self.barView.layoutIfNeeded()
        let barPointView = HDPointView(frame: CGRect(x: 0, y: 0, width: HDColorPickerConst.colorPickerSliderBtnWidth * plus, height: HDColorPickerConst.colorPickerSliderBtnWidth * plus))
        barPointView.backgroundColor = UIColor.white
        barPointView.layer.cornerRadius = 10
        barPointView.layer.shadowOpacity = 1
        barPointView.layer.shadowOffset = CGSize(width: 0, height: 2)
        barPointView.layer.shadowColor = UIColor.black.withAlphaComponent(0.08).cgColor
        barPointView.fixY = true
        self.barView.pointView = barPointView
        
        self.barView.barLayer.cornerRadius = 4.0
        self.barView.barLayer.masksToBounds = true
        self.barView.pickerView = self.colorPickerView
        
        //回调改变颜色
        self.colorPickerView.changeHandler = { [weak self](view: HDColorPickerView, color: UIColor) in
            self?.moveHairColorAction(color: color)
        }
        self.barView.setHue(0.67)
        
        self.addSubview(self.addBtn)
        self.addBtn.snp.makeConstraints { make in
            make.left.equalTo(self.barView)
            make.height.equalTo(24 * plus)
            make.width.equalTo(49 * plus)
            make.bottom.equalToSuperview().offset(-16)
        }
        self.addBtn.layer.cornerRadius = 12
        self.addBtn.layer.masksToBounds = true
        self.addBtn.setTitle("append".localized, for: .normal)
        self.addBtn.setTitleColor(UIColor(valueRGB: 0x000000), for: .normal)
        self.addBtn.setImage(UIImage(named: "hairstyle_add"), for: .normal)
        self.addBtn.titleLabel?.font = UIFont.systemFont(ofSize: 10 * plus, weight: .medium)
        self.addBtn.titleLabel?.adjustsFontSizeToFitWidth = true
        self.addBtn.contentEdgeInsets = UIEdgeInsets(top: 5, left: 5, bottom: 5, right: 5)
        self.addBtn.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        
        self.addBtn.addTarget(self, action: #selector(addBtnAction), for: .touchUpInside)
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 18 * plus, height: 18 * plus)
        layout.minimumLineSpacing = 14 * plus
        layout.minimumInteritemSpacing = 0
        self.colorCollectionView = UICollectionView(frame: CGRect(x: 0, y: 16, width: UIScreen.main.bounds.width - 53, height: UIScreen.main.bounds.height), collectionViewLayout: layout)
        self.colorCollectionView?.showsHorizontalScrollIndicator = false
        self.colorCollectionView?.dataSource = self
        self.colorCollectionView?.delegate = self
        self.addSubview(self.colorCollectionView ?? UICollectionView())
        self.colorCollectionView?.snp.makeConstraints { make in
            make.left.equalTo(self.addBtn.snp.right).offset(18 * plus)
            make.right.equalToSuperview().offset(-11 * plus)
            make.height.equalTo(18 * plus)
            make.centerY.equalTo(self.addBtn)
        }
        self.colorCollectionView?.register(HairStyleEditColorCell.self, forCellWithReuseIdentifier: HairStyleEditColorCell.identifier)
    }
    
    // MARK: - 操作相关函数
    @objc func addBtnAction() {
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("addHairColorAction:"))) != nil) {
            guard let color = self.moveColor else {
                return
            }
            self.delegate?.addHairColorAction(color: color)
        }
    }
    
    public func refreshCollectionView() {
        self.colorArray = HDEditManager.shared.colorHexArray
        self.colorCollectionView?.reloadData()
    }
    
    func moveHairColorAction(color: UIColor) {
        self.moveColor = color
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("updateHairColorAction:"))) != nil) {
            self.delegate?.updateHairColorAction(color: color)
        }
    }
    
    //由于初始化的时候，默认颜色已经返回，需要在出现的时候重新回调一次刷新头发颜色
    public func refreshColor() {
        let color = self.colorPickerView.color
        self.colorPickerView.color = color
    }
}

typealias HairStyleEditColorChooseViewCollectionView = HairStyleEditColorChooseView
extension HairStyleEditColorChooseViewCollectionView: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.colorArray.count
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HairStyleEditColorCell.identifier, for: indexPath) as? HairStyleEditColorCell else {
            return UICollectionViewCell()
        }

        cell.colorHexString = self.colorArray[indexPath.row]
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let color = UIColor.hex(string: self.colorArray[indexPath.row])
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("updateHairColorAction:"))) != nil) {
            self.delegate?.updateHairColorAction(color: color)
        }
    }
}
