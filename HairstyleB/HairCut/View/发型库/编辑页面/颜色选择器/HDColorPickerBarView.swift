//
//  HDColorPickerBarView.swift
//  HairCut
//
//  Created by Bigger on 2024/11/18.
//

import UIKit
import QuartzCore

class HDColorPickerBarView: UIView {
    
    weak var pickerView: HDColorPickerView?
    
    var barLayer = HDColorPickerBarLayer()
    
    var changeHandler: ((HDColorPickerBarView, UIColor) -> Void)?
    
    var inputChangeHandler: ((HDColorPickerBarView, Bool) -> Void)?
    
    var pointView: (UIView & HDColorPickerPointView)? {
        didSet {
            oldValue?.removeFromSuperview()
            if let pointView = pointView {
                addSubview(pointView)
                pointView.moveToPoint(self.selectedPoint)
            }
        }
    }
    
    private var selectedPoint: CGPoint = .zero
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    private func setupView() {
        self.isUserInteractionEnabled = true
        self.layoutIfNeeded()
        barLayer.anchorPoint = .zero
        let plus = UIDevice.current.userInterfaceIdiom == .pad ? 1.5 : 1
        barLayer.bounds = CGRectMake(0, 0, HDColorPickerConst.colorPickerSliderWidth * plus, HDColorPickerConst.colorPickerSliderHeight * plus)
//        barLayer.bounds = bounds
        barLayer.cornerRadius = 8
        layer.addSublayer(barLayer)
        barLayer.setNeedsDisplay()
        
        selectedPoint = .zero
        
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
        longPress.minimumPressDuration = 0.01
        addGestureRecognizer(longPress)
    }
    
    // MARK: - Public Methods
    
    func setHue(_ hue: CGFloat) {
        selectedPoint = CGPoint(x: bounds.width * hue, y: selectedPoint.y)
        self.updateSelectedPoint()
    }
    
    // MARK: - Layout
    
    override func layoutSubviews() {
        super.layoutSubviews()
        pointView?.moveToPoint(self.selectedPoint)
    }
    
    // MARK: - Private Methods
    
    private func updateSelectedPoint() {
        let normalizedPoint = normalizeSelectedPoint(selectedPoint)
        selectedPoint = normalizedPoint
        
        pointView?.moveToPoint(self.selectedPoint)
        
        let cgColor = createColor(fromHSV: barLayer.hsv(for: selectedPoint))
        let color = UIColor(cgColor: cgColor)
        pickerView?.color = color
        changeHandler?(self, color)
    }
    
    private func normalizeSelectedPoint(_ point: CGPoint) -> CGPoint {
        var newPoint = point
        newPoint.x = min(bounds.width, max(0.0, point.x))
        newPoint.y = min(bounds.height, max(0.0, point.y))
        return newPoint
    }
    
    // MARK: - Gesture
    
    @objc private func handleLongPress(_ longPress: UILongPressGestureRecognizer) {
        if let inputChangeHandler = inputChangeHandler {
            let isInteracting = longPress.state == .began
            inputChangeHandler(self, isInteracting)
        }
        
        if longPress.state == .ended || longPress.state == .cancelled {
            inputChangeHandler?(self, false)
        }
        
        selectedPoint = longPress.location(in: self)
        self.updateSelectedPoint()
    }
}
