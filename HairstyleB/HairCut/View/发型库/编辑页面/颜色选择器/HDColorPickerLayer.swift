//
//  HDColorPickerLayer.swift
//  HairCut
//
//  Created by Bigger on 2024/11/18.
//

import QuartzCore
import CoreGraphics

class HDColorPickerLayer: CALayer {
    
    private(set) var hsv: HDHSV = HDHSV(h: 0.0, s: 1.0, v: 1.0)
    public var _color: CGColor?
    
    var color: CGColor? {
        get {
            return _color
        }
        set {
            willChangeValue(forKey: "color")
            releaseColor()
            
            _color = newValue
            if let color = newValue {
                let hsvValue = createHSV(from: color)
                self.hsv = HDHSV(h: hsvValue.h, s: 1.0, v: 1.0) // Adjust saturation and brightness to 1.0
            }
            
            didChangeValue(forKey: "color")
            setNeedsDisplay()
        }
    }
    
    override func draw(in ctx: CGContext) {
        guard let image = createHSLMapImage(hue: hsv.h) else {
            return
        }
        
        let drawRect = ctx.boundingBoxOfClipPath
        
        ctx.saveGState()
        defer { ctx.restoreGState() }
        
        let flipVertical = CGAffineTransform(a: 1, b: 0, c: 0, d: -1, tx: 0, ty: drawRect.height)
        ctx.concatenate(flipVertical)
        ctx.draw(image, in: drawRect)
    }
    
    deinit {
        releaseColor()
    }
    
    // MARK: - Helpers
    
    public func hsv(for point: CGPoint) -> HDHSV {
        let sPer: CGFloat = {
            guard bounds.width > 0 else { return 0 }
            return point.x / bounds.width
        }()
        
        let vPer: CGFloat = 1.0 - {
            guard bounds.height > 0 else { return 0 }
            return point.y / bounds.height
        }()
        
        return HDHSV(h: hsv.h, s: sPer, v: vPer)
    }
    
    public func point(for hsv: HDHSV) -> CGPoint {
        let x = bounds.width * hsv.s
        let y = bounds.height * (1.0 - hsv.v)
        return CGPoint(x: floor(x), y: floor(y))
    }
    
    private func releaseColor() {
        _color = nil
    }
}
