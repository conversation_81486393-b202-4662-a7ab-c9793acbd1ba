//
//  HDColorPickerHelpers.swift
//  HairCut
//
//  Created by Bigger on 2024/11/18.
//

import Foundation
import QuartzCore

struct HDHSV {
    var h: CGFloat
    var s: CGFloat
    var v: CGFloat
}

struct HDRGB {
    var r: CGFloat
    var g: CGFloat
    var b: CGFloat
}

enum HDHSVIDX: UInt32 {
    case hue = 0
    case saturation = 1
    case value = 2
}

//转换方法声明

func hueComponentFactors(hue: CGFloat) -> (red: CGFloat, green: CGFloat, blue: CGFloat) {
    precondition(hue >= 0.0 && hue <= 1.0, "Hue should be in the range [0, 1]")
    let hueDeg = hue * 360.0
    let hueValue = hueDeg / 60.0
    let val = 1.0 - abs(hueValue.truncatingRemainder(dividingBy: 2.0) - 1.0)
    
    var red: CGFloat = 0.0
    var green: CGFloat = 0.0
    var blue: CGFloat = 0.0
    
    if hueValue < 1.0 {
        red = 1.0
        green = val
        blue = 0.0
    } else if hueValue < 2.0 {
        red = val
        green = 1.0
        blue = 0.0
    } else if hueValue < 3.0 {
        red = 0.0
        green = 1.0
        blue = val
    } else if hueValue < 4.0 {
        red = 0.0
        green = val
        blue = 1.0
    } else if hueValue < 5.0 {
        red = val
        green = 0.0
        blue = 1.0
    } else {
        red = 1.0
        green = 0.0
        blue = val
    }
    
    return (red, green, blue)
}

func rgbFromHSV(_ hsv: HDHSV) -> HDRGB {
    let (r, g, b) = hueComponentFactors(hue: hsv.h)
        
        let valSat = hsv.v * hsv.s
        let valOff = hsv.v - valSat
        
        return HDRGB(
            r: r * valSat + valOff,
            g: g * valSat + valOff,
            b: b * valSat + valOff
        )
}

func hsvFromRGB(_ rgb: HDRGB) -> HDHSV {
    let maximum = max(rgb.r, max(rgb.g, rgb.b))
    let minimum = min(rgb.r, min(rgb.g, rgb.b))
    
    var hsv = HDHSV(h: 0.0, s: 0.0, v: maximum)
    let delta = maximum - minimum
    
    if maximum != 0.0 {
        hsv.s = delta / maximum
    } else {
        hsv.s = 0.0
    }
    
    if hsv.s == 0.0 {
        hsv.h = 0.0 // Hue is undefined, setting to 0 for grayscale
    } else {
        var hue: CGFloat = 0.0
        if rgb.r == maximum {
            hue = (rgb.g - rgb.b) / delta
        } else if rgb.g == maximum {
            hue = 2.0 + (rgb.b - rgb.r) / delta
        } else if rgb.b == maximum {
            hue = 4.0 + (rgb.r - rgb.g) / delta
        }
        
        hue = hue / 6.0
        if hue < 0.0 {
            hue += 1.0
        }
        
        hsv.h = hue
    }
    
    return hsv
}

func blendValue(_ value: UInt8, percent: UInt8) -> UInt8 {
    return UInt8((UInt32(value) * UInt32(percent)) / 255)
}

func createColor(fromHSV hsv: HDHSV) -> CGColor {
    return createColor(fromRGB: rgbFromHSV(hsv))
}

func createColor(fromRGB rgb: HDRGB) -> CGColor {
    let colorSpace = CGColorSpaceCreateDeviceRGB()
    let components: [CGFloat] = [rgb.r, rgb.g, rgb.b, 1.0]
    return CGColor(colorSpace: colorSpace, components: components)!
}

func createRGB(from color: CGColor) -> HDRGB {
    let components = color.components!
    return HDRGB(r: components[0], g: components[1], b: components[2])
}

func createHSV(from color: CGColor) -> HDHSV {
    return hsvFromRGB(createRGB(from: color))
}

// createHSLMapImage 实现
public func createHSLMapImage(hue: CGFloat) -> CGImage? {
//    let HD_RGB_MAX_I: Int = 255
    let HD_RGB_MAX_F: CGFloat = 255.0
    let HD_BYTE_MULTI: Int = 4
    let HD_BYTE_COMP: Int = 8
    
    let size = CGSize(width: HD_RGB_MAX_F, height: HD_RGB_MAX_F)
    
    // 分配内存
    guard let data = malloc(Int(size.width * size.height * CGFloat(HD_BYTE_MULTI))) else {
        return nil
    }
    
    // 创建颜色空间
    guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB) else {
        free(data)
        return nil
    }
    
    // 创建上下文
    let bitmapInfo: CGBitmapInfo = CGBitmapInfo(rawValue: CGBitmapInfo.byteOrder32Little.rawValue | CGImageAlphaInfo.noneSkipFirst.rawValue)
    guard let context = CGContext(
        data: data,
        width: Int(size.width),
        height: Int(size.height),
        bitsPerComponent: HD_BYTE_COMP,
        bytesPerRow: Int(size.width) * HD_BYTE_MULTI,
        space: colorSpace,
        bitmapInfo: bitmapInfo.rawValue
    ) else {
        free(data)
        return nil
    }
    
    defer {
        free(data)
    }
    
    // 获取颜色因子
    let (r, g, b) = hueComponentFactors(hue: hue)
    
    let redPercent = UInt8((1.0 - r) * HD_RGB_MAX_F)
    let greenPercent = UInt8((1.0 - g) * HD_RGB_MAX_F)
    let bluePercent = UInt8((1.0 - b) * HD_RGB_MAX_F)
    
    let width = Int(size.width)
    let height = Int(size.height)
    let bytesPerRow = context.bytesPerRow
    let dataPointer = data.assumingMemoryBound(to: UInt8.self)
    
    for idx in 1...width {
        var ptr = dataPointer.advanced(by: (idx - 1) * HD_BYTE_MULTI)
        
        let red = width - Int(HDBlendValue(UInt8(idx), redPercent))
        let green = width - Int(HDBlendValue(UInt8(idx), greenPercent))
        let blue = width - Int(HDBlendValue(UInt8(idx), bluePercent))
        
        for val in stride(from: height, through: 0, by: -1) {
            ptr[0] = UInt8((val * blue) >> HD_BYTE_COMP)
            ptr[1] = UInt8((val * green) >> HD_BYTE_COMP)
            ptr[2] = UInt8((val * red) >> HD_BYTE_COMP)
            
            ptr = ptr.advanced(by: bytesPerRow)
        }
    }
    
    // 创建图像
    guard let image = context.makeImage() else {
        return nil
    }
    
    return image
}

func HDBlendValue(_ value: UInt8, _ percent: UInt8) -> UInt8 {
    return UInt8(UInt32(value) * UInt32(percent) / UInt32(255.0))
}

//createBarImage 实现
func createBarImage(hsvIndex: HDHSVIDX, hsv: HDHSV) -> CGImage? {
    let size = 256
    var data = [UInt8](repeating: 0, count: size * 4)
    
    let colorSpace = CGColorSpaceCreateDeviceRGB()
    let bitmapInfo: CGBitmapInfo = CGBitmapInfo(rawValue: CGBitmapInfo.byteOrder32Little.rawValue | CGImageAlphaInfo.noneSkipFirst.rawValue)
    
    guard let context = CGContext(data: &data,
                                   width: size,
                                   height: 1,
                                   bitsPerComponent: 8,
                                   bytesPerRow: size * 4,
                                   space: colorSpace,
                                   bitmapInfo: bitmapInfo.rawValue) else { return nil }
    
    for x in 0..<size {
        let val = CGFloat(x) / 255.0
        var adjustedHSV = hsv
        switch hsvIndex {
        case .hue:
            adjustedHSV.h = val
        case .saturation:
            adjustedHSV.s = val
        case .value:
            adjustedHSV.v = val
        }
        let rgb = rgbFromHSV(adjustedHSV)
        data[x * 4 + 0] = UInt8(rgb.b * 255.0)
        data[x * 4 + 1] = UInt8(rgb.g * 255.0)
        data[x * 4 + 2] = UInt8(rgb.r * 255.0)
    }
    
    return context.makeImage()
}
