//
//  HDImageTool.m
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/11/20.
//

#import "HDImageTool.h"
#import "HairCutMalloc.h"

NSUInteger kAlphaOffset(NSUInteger x, NSUInteger y, NSUInteger w) { return y * w * 4 + x * 4 + 0; }
NSUInteger kRedOffset(NSUInteger x, NSUInteger y, NSUInteger w) { return y * w * 4 + x * 4 + 1; }
NSUInteger kGreenOffset(NSUInteger x, NSUInteger y, NSUInteger w) { return y * w * 4 + x * 4 + 2; }
NSUInteger kBlueOffset(NSUInteger x, NSUInteger y, NSUInteger w) { return y * w * 4 + x * 4 + 3; }

@implementation HDImageTool

+ (void *)getBitmapFromImage:(UIImage *)image {

    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    
    if (colorSpace == NULL) {
        return NULL;
    }
    
    void *bitmapData = HairCutMalloc(image.size.width * image.size.height * 4);
    
    if (bitmapData == NULL) {
        CGColorSpaceRelease(colorSpace);
        return NULL;
    }
    
    CGContextRef context = CGBitmapContextCreate (bitmapData, image.size.width, image.size.height, 8, image.size.width * 4, colorSpace, kCGImageAlphaPremultipliedFirst);
    
    CGColorSpaceRelease(colorSpace);
    
    if (context == NULL) {
        free(bitmapData);
        return NULL;
    }
        
    CGRect rect = CGRectMake(0.0f, 0.0f, image.size.width, image.size.height);
    CGContextDrawImage(context, rect, image.CGImage);
    
    void *data = CGBitmapContextGetData(context);
    CGContextRelease(context);
    
    return data;
}

+ (UIImage *)imageWithBitmap:(unsigned char *)bits withSize:(CGSize)size {
    
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    
    if (colorSpace == NULL) {
        free(bits);
        return nil;
    }
    
    CGContextRef context = CGBitmapContextCreate(bits, size.width, size.height, 8, size.width * 4, colorSpace, kCGImageAlphaPremultipliedFirst);
    if (context == NULL) {
        free (bits);
        CGColorSpaceRelease(colorSpace );
        return nil;
    }
    
    CGColorSpaceRelease(colorSpace);
    CGImageRef imageRef = CGBitmapContextCreateImage(context);
    free(CGBitmapContextGetData(context));
    CGContextRelease(context);
    
    UIImage *rstImage = [UIImage imageWithCGImage:imageRef];
    CFRelease(imageRef);
    
    return rstImage;
}



+ (long long)getTotalValue:(unsigned char *)bitmap
                imageWidth:(NSInteger)imageWidth
               imageHeight:(NSInteger)imageHeight {
    
    NSInteger red;
    NSInteger green;
    NSInteger blue;
    
    long count = 0;
    long long totalValue = 0;
    
    for (NSInteger x = 0; x < imageWidth; x++) {
        for (NSInteger y = 0; y < imageHeight; y++) {
            
            if (bitmap[kAlphaOffset(x, y, imageWidth)] > 0) {
                
                red = bitmap[kRedOffset(x, y, imageWidth)];
                green = bitmap[kGreenOffset(x, y, imageWidth)];
                blue = bitmap[kBlueOffset(x, y, imageWidth)];
                
                totalValue += (red+green+blue);
                
                count++;
            }
            
        }
    }
    
    totalValue = totalValue / count;
    return totalValue;
}

+ (CGRect)imageValidRect:(UIImage *)image {
    
    NSInteger width = image.size.width;
    NSInteger height = image.size.height;
    
    unsigned char *inbits = [self getBitmapFromImage:image];
    
    CGFloat minX = width;
    CGFloat minY = height;
    CGFloat maxX = 0;
    CGFloat maxY = 0;
    
    for (NSInteger x = 0; x < width; x++) {
        for (NSInteger y = 0; y < height; y++) {
            
            CGFloat alpha =  inbits[kAlphaOffset(x, y, width)] / 255.0;
            
            if (alpha > 0.1) {
                
                if (x < minX) {
                    minX = x;
                }
                
                if (x > maxX) {
                    maxX = x;
                }
                
                if (y < minY) {
                    minY = y;
                }
                
                if (y > maxY) {
                    maxY = y;
                }
                
            }
            
        }
        
    }
    
    free(inbits);
    
    return CGRectMake(minX, minY, maxX - minX, maxY - minY);
    
}


+ (UIImage *)hairColoredWithHairImage:(UIImage *)hairImage color:(UIColor *)color {
    
    NSInteger width = hairImage.size.width;
    NSInteger height = hairImage.size.height;
    
    unsigned char *inbits = [self getBitmapFromImage:hairImage];
    unsigned char *outbits = (unsigned char *)HairCutMalloc((width) * (height) * 4);
    
    long long totalValue = [self getTotalValue:inbits imageWidth:width imageHeight:height];
    
    CGColorRef colorr = [color CGColor];
    size_t numComponents = CGColorGetNumberOfComponents(colorr);
    
    NSInteger red = 0;
    NSInteger green = 0;
    NSInteger blue = 0;
    
    if (numComponents == 4) {
        const CGFloat *components = CGColorGetComponents(colorr);
        red = components[0]*255;
        green = components[1]*255;
        blue = components[2]*255;
    }
    
    NSInteger tmpRed;
    NSInteger tmpGreen;
    NSInteger tmpBlue;
    
    NSInteger rstRed;
    NSInteger rstGreen;
    NSInteger rstBlue;
    
    for (NSInteger y = 0; y < height; y++) {
        for (NSInteger x = 0; x < width; x++) {
            
            if (inbits[kAlphaOffset(x, y, width)] > 0) {
                
                tmpRed = inbits[kRedOffset(x, y, width)];
                tmpGreen = inbits[kGreenOffset(x, y, width)];
                tmpBlue = inbits[kBlueOffset(x, y, width)];
                
                rstRed = (red + (tmpRed + tmpGreen + tmpBlue - totalValue) / 3) * 0.5 + 0.5 * tmpRed;
                rstGreen = (green + (tmpRed + tmpGreen + tmpBlue - totalValue) / 3) * 0.5 + 0.5 * tmpGreen;
                rstBlue = (blue + (tmpRed + tmpGreen + tmpBlue - totalValue) / 3) * 0.5 + 0.5 * tmpBlue;
                
                rstGreen= rstGreen > 255 ? 255 : rstGreen;
                rstBlue= rstBlue > 255 ? 255 : rstBlue;
                rstRed= rstRed > 255 ? 255 : rstRed;
                
                rstRed = rstRed >0 ? rstRed : 0;
                rstGreen = rstGreen > 0 ? rstGreen : 0;
                rstBlue = rstBlue > 0 ? rstBlue : 0;
                
                CGFloat rate =inbits[kAlphaOffset(x, y, width)] / 255.0;
                
                outbits[kRedOffset(x, y, width)] = rstRed * rate;
                outbits[kGreenOffset(x, y, width)] = rstGreen * rate;
                outbits[kBlueOffset(x, y, width)] = rstBlue * rate;
                outbits[kAlphaOffset(x, y, width)] = 255 * rate;
                
            }else {
                outbits[kRedOffset(x, y, width)] = 0;
                outbits[kGreenOffset(x, y, width)] = 0;
                outbits[kBlueOffset(x, y, width)] = 0;
                outbits[kAlphaOffset(x, y, width)] = 0;
            }
        }
    }
    
    free(inbits);
    return [self imageWithBitmap:outbits withSize:CGSizeMake(width, height)];
}

@end
