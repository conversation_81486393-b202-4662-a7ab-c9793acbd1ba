//
//  HDEditManager.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/11/18.
//

import UIKit
class HDEditManager {
    
    // MARK: - Properties
    static let shared = HDEditManager()
    
    var hairColor: UIColor?
    var originImage: UIImage?
    var hairImage: UIImage?
    var colorHexArray: [String] = []
    
    // MARK: - Initializer
    private init() {
        initConfig()
    }
    
    // MARK: - Methods
    private func initConfig() {
        loadColorHexArray()
        if let firstHex = colorHexArray.first {
            hairColor = UIColor.hex(string: firstHex)
        }
        originImage = nil
        hairImage = nil
    }
    
    private func loadColorHexArray() {
        if let savedColors = UserDefaults.standard.array(forKey: HDColorPickerConst.colopPickerColorHexData) as? [String], !savedColors.isEmpty {
            colorHexArray = savedColors
        } else {
            colorHexArray = [
                "#610B0B","#532A03","#8F4827","#242A40","#FEDBFF"
            ]
        }
    }
    
    func updateColorHexArray() {
        UserDefaults.standard.set(colorHexArray, forKey: HDColorPickerConst.colopPickerColorHexData)
        UserDefaults.standard.synchronize()
    }
    
    public func addColorHexString(_ hexString: String?) {
        guard let hex = hexString else {
            return
        }
        // 避免重复添加
        if !self.colorHexArray.contains(hex) {
            // 确保总数量不超过 10
            if self.colorHexArray.count >= 10 {
                // 移除最早添加的颜色
                self.colorHexArray.removeLast()
            }
            self.colorHexArray.insert(hex, at: 0)
            self.updateColorHexArray()
        }
    }
    
    //复原操作
    public func recoverHairImageAction() {
        self.hairImage = self.originImage
        self.hairColor = nil
    }
    
    //改变发型
    public func changeHairAction(newOriginHair: UIImage?) {
        guard let originImage = newOriginHair else {
            return
        }
        
        self.originImage = originImage
        guard let hariColor = self.hairColor else {
            self.hairImage = newOriginHair
            return
        }
        let newImage = HDImageTool.hairColored(withHairImage:originImage, color: hariColor)
        self.hairImage = newImage
    }
    
    //改变发色
    public func changeHairColorAction(newColor: UIColor) {
        guard let originImage = self.originImage else {
            return
        }
        let newImage = HDImageTool.hairColored(withHairImage:originImage, color: newColor)
        self.hairImage = newImage
        self.hairColor = newColor
    }
}
