//
//  HDColorPickerView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/11/18.
//

import UIKit

class HDColorPickerView: UIView {
    
    @objc var color: UIColor {
        get {
            return UIColor(cgColor: pickerLayer.color!)
        }
        set {
            pickerLayer.color = newValue.cgColor
        }
    }
    private var HDColorPickerViewColorChangeContext: UInt8 = 0
    private(set) var selectedColor: UIColor? = nil
    private var selectedPoint: CGPoint = .zero
    
    public var pickerLayer = HDColorPickerLayer()
    private var settingUp = false
    private let plus = UIDevice.current.userInterfaceIdiom == .pad ? 1.5 : 1
    
    var pointView: (UIView & HDColorPickerPointView)? {
        didSet {
            oldValue?.removeFromSuperview()
            if let pointView = pointView {
                addSubview(pointView)
                pointView.moveToPoint(self.selectedPoint)
            }
        }
    }
    
    public var changeHandler: ((HDColorPickerView, UIColor) -> Void)?
    var inputChangeHandler: ((HDColorPickerView, Bool) -> Void)?
    
    private static let selectedPointDefault: CGFloat = -CGFloat.greatestFiniteMagnitude
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    private func setupView() {
        pickerLayer.anchorPoint = .zero
        self.layoutIfNeeded()
        pickerLayer.bounds = CGRectMake(0, 0, HDColorPickerConst.colorPickerGradientWidth  * plus, HDColorPickerConst.colorPickerGradientHeight * plus)
        layer.addSublayer(pickerLayer)
        
        performSetup { [weak self] view in
            guard let self = self else { return }
            view.pickerLayer.addObserver(view, forKeyPath:NSStringFromSelector(#selector(getter: color)), options: [], context: &HDColorPickerViewColorChangeContext)
            view.color = .red
            let longPress = UILongPressGestureRecognizer(target: self, action: #selector(self.gestureRecognizerDidRecognize(_:)))
            longPress.minimumPressDuration = 0.01
            self.addGestureRecognizer(longPress)
            self.selectedPoint = CGPoint(x: HDColorPickerView.selectedPointDefault * plus, y: HDColorPickerView.selectedPointDefault * plus)
            _ = self.curSelectedPoint()
        }
    }
    
    deinit {
        pickerLayer.removeObserver(self, forKeyPath: NSStringFromSelector(#selector(getter: color)), context: &HDColorPickerViewColorChangeContext)
    }
    
    // MARK: - Setup
    
    private func performSetup(_ setup: @escaping (HDColorPickerView) -> Void) {
        settingUp = true
        setup(self)
        settingUp = false
    }
    
    // MARK: - Color
    func curSelectedPoint() -> CGPoint {
        var point = self.selectedPoint
        if point.x == HDColorPickerView.selectedPointDefault * plus {
            point.x = HDColorPickerConst.colorPickerGradientWidth * plus - HDColorPickerConst.colorPickerGradientBtnWidth * plus
        }
        if point.y == HDColorPickerView.selectedPointDefault * plus {
            point.y = 0.0
        }
        self.selectedPoint = point
        return point
    }
    
    private func updateColors(notifyChangeHandler: Bool) {
        let cgColor = createColor(fromHSV: pickerLayer.hsv(for: curSelectedPoint()))
        self.setSelectedColor(UIColor(cgColor: cgColor), callChangeHandler: notifyChangeHandler)
    }
                              
    private func setSelectedColor(_ color: UIColor, callChangeHandler: Bool) {
        selectedColor = color
        if callChangeHandler {
            changeHandler?(self, color)
        }
    }
    
    // MARK: - Setters
    
    func setSelectedPoint(_ point: CGPoint) {
        selectedPoint = normalizeSelectedPoint(point)
        pointView?.moveToPoint(self.selectedPoint)
        updateColors(notifyChangeHandler: !settingUp)
    }
    
//    func setSelectedColor(_ selectedColor: UIColor) {
//        setSelectedColor(selectedColor, callChangeHandler: !settingUp)
//    }
    
    private func normalizeSelectedPoint(_ point: CGPoint) -> CGPoint {
        var point = point
        if point.x != HDColorPickerView.selectedPointDefault * plus {
            point.x = min(bounds.width, max(0.0, point.x))
        }
        if point.y != HDColorPickerView.selectedPointDefault * plus {
            point.y = min(bounds.height, max(0.0, point.y))
        }
        return point
    }
    
    func selectSaturation(_ saturation: CGFloat, value: CGFloat) {
        layoutIfNeeded()
        selectedPoint = pickerLayer.point(for: HDHSV(h: 1.0, s: saturation, v: value))
    }
    
    // MARK: - Layout
    
    override func layoutSubviews() {
        super.layoutSubviews()
        pointView?.moveToPoint(self.selectedPoint)
    }
    
    // MARK: - Gesture Handling
    
    @objc private func gestureRecognizerDidRecognize(_ longPress: UILongPressGestureRecognizer) {
        if let inputChangeHandler = inputChangeHandler {
            let isInteracting = longPress.state == .began
            inputChangeHandler(self, isInteracting)
        }
        
        if longPress.state == .ended || longPress.state == .cancelled {
            inputChangeHandler?(self, false)
        }
        
        self.setSelectedPoint(longPress.location(in: self))
    }
    
    // MARK: - KVO Handling
    
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if context == &HDColorPickerViewColorChangeContext {
            updateColors(notifyChangeHandler: !settingUp)
        } else {
            super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
        }
    }
}
