//
//  HDColorCell.swift
//  HairCut
//
//  Created by Bigger on 2024/11/18.
//

import UIKit
import SnapKit

class HDColorCCell: UICollectionViewCell {
    
    private let colorView: UIView = UIView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        contentView.backgroundColor = .clear
        contentView.layer.cornerRadius = 12.0
        
        contentView.addSubview(colorView)
        
        colorView.snp.makeConstraints { make in
            make.center.equalTo(contentView)
            make.width.height.equalTo(24)
        }
    }
    
    func renderCell(with color: UIColor, isSelected: Bool) {
        colorView.backgroundColor = color
        
        if isSelected {
            contentView.layer.borderWidth = 1.0
            contentView.layer.borderColor = UIColor.white.cgColor
            
            colorView.layer.cornerRadius = 9.0
            colorView.snp.updateConstraints { make in
                make.width.height.equalTo(18)
            }
        } else {
            contentView.layer.borderWidth = 0.5
            contentView.layer.borderColor = UIColor.white.withAlphaComponent(0.5).cgColor
            
            colorView.layer.cornerRadius = 12.0
            colorView.snp.updateConstraints { make in
                make.width.height.equalTo(24)
            }
        }
    }
}
