//
//  HDPointView.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/11/18.
//

import UIKit

protocol HDColorPickerPointView: AnyObject {
    func moveToPoint(_ point: CGPoint)
}

class HDPointView: UIView, HDColorPickerPointView {
    
    public var fixY: Bool = false
    
    func moveToPoint(_ point: CGPoint) {
        var adjustedPoint = point
        let myheight = superview?.bounds.height ?? 0
        if fixY {
            adjustedPoint.y = (myheight - bounds.height) / 2.0
        } else {
            if adjustedPoint.y >= (myheight - bounds.height) {
                adjustedPoint.y = (myheight - bounds.height)
            }
        }
        let mywidth = superview?.bounds.width ?? 0
        if adjustedPoint.x >= (mywidth - bounds.width) {
            adjustedPoint.x = (mywidth - bounds.width)
        }
        
        var frame = self.frame
        frame.origin = adjustedPoint
        self.frame = frame
    }
}
