//
//  HDColorPickerBarLayer.swift
//  HairCut
//
//  Created by Bigger on 2024/11/18.
//

import QuartzCore
import CoreGraphics

class HDColorPickerBarLayer: CALayer {
    
    override func draw(in ctx: CGContext) {
        guard let image = createBarImage(hsvIndex: .hue, hsv: HDHSV(h: 0.0, s: 1.0, v: 1.0)) else { return }
        
        let drawRect = ctx.boundingBoxOfClipPath
        
        ctx.saveGState()
        defer { ctx.restoreGState() }
        
        let flipVertical = CGAffineTransform(a: 1, b: 0, c: 0, d: -1, tx: 0, ty: drawRect.height)
        ctx.concatenate(flipVertical)
        ctx.draw(image, in: drawRect)
    }
    
    func hsv(for point: CGPoint) -> HDHSV {
        let width = bounds.width
        guard width > 0 else {
            return HDHSV(h: 0.0, s: 1.0, v: 1.0)
        }
        let percent = point.x / width
        let rawHue = 360.0 * percent
        let hue = rawHue / 360.0
        
        return HDHSV(h: hue, s: 1.0, v: 1.0)
    }
}
