//
//  HDColorPalette.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/11/18.
//

import UIKit
import SnapKit

protocol HDColorPaletteDelegate: AnyObject {
    func colorPalette(_ view: HDColorPalette, didSelectType type: Int)
}

class HDColorPalette: UIView, UICollectionViewDataSource, UICollectionViewDelegate {
    
    weak var delegate: HDColorPaletteDelegate?
    private var collectionView: UICollectionView!
    private var selectedIndex: Int = -1
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        selectedIndex = -1
        
        let imageView = UIImageView()
        imageView.image = UIImage(named: "color_picker")
        addSubview(imageView)
        
        imageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        let pickerButton = UIButton()
        pickerButton.addTarget(self, action: #selector(pickerAction), for: .touchUpInside)
        addSubview(pickerButton)
        
        pickerButton.snp.makeConstraints { make in
            make.edges.equalTo(imageView)
        }
        
        let flowLayout = UICollectionViewFlowLayout()
        flowLayout.scrollDirection = .horizontal
        flowLayout.minimumLineSpacing = 16
        flowLayout.minimumInteritemSpacing = 6
        flowLayout.itemSize = CGSize(width: 24, height: 24)
        
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: flowLayout)
        collectionView.register(HDColorCCell.self, forCellWithReuseIdentifier: "HDColorCCell")
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.dataSource = self
        collectionView.delegate = self
        addSubview(collectionView)
        
        collectionView.snp.makeConstraints { make in
            make.left.equalTo(imageView.snp.right).offset(16)
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.height.equalTo(24)
        }
    }
    
    @objc private func pickerAction() {
        delegate?.colorPalette(self, didSelectType: 2)
    }
    
    // MARK: - UICollectionViewDataSource
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return HDEditManager.shared.colorHexArray.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "HDColorCCell", for: indexPath) as! HDColorCCell
        
        let colorString = HDEditManager.shared.colorHexArray[indexPath.item]
        let color = UIColor.hex(string: colorString)
        let isSelected = selectedIndex == indexPath.item
        
        cell.renderCell(with: color, isSelected: isSelected)
        
        return cell
    }
    
    // MARK: - UICollectionViewDelegate
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedIndex = indexPath.item
        
        let colorString = HDEditManager.shared.colorHexArray[indexPath.item]
        HDEditManager.shared.hairColor = UIColor.hex(string: colorString)
        
        collectionView.reloadData()
        
        delegate?.colorPalette(self, didSelectType: 1)
    }
}
