//
//  HairStyleEditColorCell.swift
//  HairCut
//
//  Created by Bigger on 2024/11/18.
//

import Foundation
import UIKit
class HairStyleEditColorCell: UICollectionViewCell {
    static let identifier = "HairStyleEditColorCell"
    
    private var colorView = UIView()
    public var colorHexString: String = "" {
        willSet {
            self.colorView.backgroundColor = UIColor.hex(string: newValue)
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.addSubview(colorView)
        self.colorView.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
    }
    
}
