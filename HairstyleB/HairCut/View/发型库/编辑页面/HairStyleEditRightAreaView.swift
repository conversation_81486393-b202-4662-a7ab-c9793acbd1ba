//
//  HairStyleEditRightAreaView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/11/15.
//

import Foundation
import UIKit

protocol HairStyleEditRightAreaViewDelegate: NSObjectProtocol {
    func hideHairAction(isHide: Bool)
    func colorAction(isColor: Bool)
    func recoverAction()
}

class HairStyleEditRightAreaView: UIView {
    private var colorItem = UIView()
    private var colorImage = UIImageView()
    private var colorLabel = UILabel()
    private var recoverItem = UIView()
    private var recoverImage = UIImageView()
    private var recoverLabel = UILabel()
    private var hideItem = UIView()
    private var hideImage = UIImageView()
    private var hideLabel = UILabel()
    
    public var isHideImage: Bool = false {
        willSet {
            self.hideImage.image = UIImage(named: newValue == true ? "hairstye_hide_on" : "hairstye_hide_off")
        }
    }
    public var isRecover: Bool = false {
        willSet {
            self.recoverImage.image = UIImage(named: newValue == true ? "hairstye_recover_on" : "hairstye_recover_off")
        }
    }
    public var isColor: Bool = false {
        willSet {
            self.colorImage.image = UIImage(named: newValue == true ? "hairstye_color_on" : "hairstye_color_off")
        }
    }
    
    weak public var delegate: HairStyleEditRightAreaViewDelegate?
    
    init() {
        super.init(frame: .zero)
        self.backgroundColor = UIColor(valueRGB: 0xFFFFFF)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.isUserInteractionEnabled = true
        self.layer.shadowOffset = CGSizeMake(0, 1)
        self.layer.shadowOpacity = 1
        self.layer.shadowRadius = 4
        self.layer.cornerRadius = 18
        self.layer.masksToBounds = true
        
        let isVi = LanguageTool.currentLanguage() == .vietnamese
        
        let plus = UIDevice.current.userInterfaceIdiom == .pad ? 1.5 : 1
        self.addSubview(self.colorItem)
        self.colorItem.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(22 * plus)
            if isVi {
                make.height.equalTo(45 * plus)
            } else {
                make.height.equalTo(30 * plus)
            }
        }
        self.colorImage.image = UIImage(named: "hairstye_color_off")
        self.colorItem.addSubview(self.colorImage)
        self.colorImage.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(22 * plus)
        }
        let colorGesture = UITapGestureRecognizer()
        colorGesture.addTarget(self, action: #selector(colorAction))
        self.colorItem.addGestureRecognizer(colorGesture)
        
        self.colorLabel.text = "hairstyle_color_change".localized
        self.colorLabel.textColor = UIColor(valueRGB: 0x333333)
        self.colorLabel.textAlignment = .center
        self.colorLabel.font = UIFont.systemFont(ofSize:isVi ? 10 * plus :  8 * plus, weight: .medium)
        self.colorLabel.numberOfLines = isVi ? 2 : 1
        self.colorLabel.adjustsFontSizeToFitWidth = true
        self.colorItem.addSubview(self.colorLabel)
        self.colorLabel.snp.makeConstraints { make in
            make.top.equalTo(self.colorImage.snp.bottom)
            make.left.right.equalToSuperview()
            if isVi {
                make.height.equalTo(13 * plus)
            } else {
                make.height.equalTo(8 * plus)
            }
        }
        
        self.addSubview(self.hideItem)
        self.hideItem.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(1.0 / 3.0)
            make.width.equalTo(22 * plus)
            if isVi {
                make.height.equalTo(45 * plus)
            } else {
                make.height.equalTo(30 * plus)
            }
        }
        let hideGesture = UITapGestureRecognizer()
        hideGesture.addTarget(self, action: #selector(hideAction))
        self.hideItem.addGestureRecognizer(hideGesture)
        
        self.hideImage.image = UIImage(named: "hairstye_hide_off")
        self.hideItem.addSubview(self.hideImage)
        self.hideImage.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(22 * plus)
        }
        self.hideLabel.text = "hairstyle_hide_hair".localized
        self.hideLabel.textColor = UIColor(valueRGB: 0x333333)
        self.hideLabel.textAlignment = .center
        self.hideLabel.font = UIFont.systemFont(ofSize: isVi ? 10 * plus : 8 * plus, weight: .medium)
        self.hideLabel.numberOfLines = isVi ? 2 : 1
        self.hideLabel.adjustsFontSizeToFitWidth = true
        self.hideItem.addSubview(self.hideLabel)
        self.hideLabel.snp.makeConstraints { make in
            make.top.equalTo(self.hideImage.snp.bottom)
            make.left.right.equalToSuperview()
            if isVi {
                make.height.equalTo(13 * plus)
            } else {
                make.height.equalTo(8 * plus)
            }
        }
        
        self.addSubview(self.recoverItem)
        self.recoverItem.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(5.0 / 3.0)
            make.width.equalTo(22 * plus)
            if isVi {
                make.height.equalTo(45 * plus)
            } else {
                make.height.equalTo(30 * plus)
            }
        }
        let recoverGesture = UITapGestureRecognizer()
        recoverGesture.addTarget(self, action: #selector(recoverAction))
        self.recoverItem.addGestureRecognizer(recoverGesture)
        
        self.recoverImage.image = UIImage(named: "hairstye_recover_off")
        self.recoverItem.addSubview(self.recoverImage)
        self.recoverImage.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(22 * plus)
        }
        self.recoverLabel.text = "recover".localized
        self.recoverLabel.textColor = UIColor(valueRGB: 0x333333)
        self.recoverLabel.textAlignment = .center
        self.recoverLabel.font = UIFont.systemFont(ofSize:isVi ? 10 * plus : 8 * plus, weight: .medium)
        self.recoverLabel.numberOfLines = isVi ? 2 : 1
        self.recoverLabel.adjustsFontSizeToFitWidth = true
        self.recoverItem.addSubview(self.recoverLabel)
        self.recoverLabel.snp.makeConstraints { make in
            make.top.equalTo(self.recoverImage.snp.bottom)
            make.left.right.equalToSuperview()
            if isVi {
                make.height.equalTo(13 * plus)
            } else {
                make.height.equalTo(8 * plus)
            }
        }
    }
    
    @objc func hideAction() {
        MobClick.event("Hairstyle_Library", attributes: ["source": "隐藏发型"])
        self.isHideImage = !self.isHideImage
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("hideHairAction:"))) != nil) {
            self.delegate?.hideHairAction(isHide: self.isHideImage)
        }
    }
    
    @objc func colorAction() {
        MobClick.event("Hairstyle_Library", attributes: ["source": "换发色"])
        self.isColor = !self.isColor
        if self.delegate != nil && (self.delegate?.responds(to: Selector.init(("colorAction:"))) != nil) {
            self.delegate?.colorAction(isColor: self.isColor)
        }
    }
    
    @objc func recoverAction() {
        MobClick.event("Hairstyle_Library", attributes: ["source": "复原"])
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.recoverAction)) != nil) {
            self.delegate?.recoverAction()
        }
    }
}
