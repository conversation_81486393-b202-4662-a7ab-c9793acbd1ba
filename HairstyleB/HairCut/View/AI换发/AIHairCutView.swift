//
//  AIHairCutView.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/1.
//

import Foundation
import UIKit
import SnapKit

protocol AIHairCutViewDelegate: NSObjectProtocol,UIViewController {
    func uploadImageAction()
    func generateResultImageAction(data: AIHairModel,requestjson:[String: Any])
}

class AIHairCutView: UIView, HairCutChooseViewDelegate {
    public let generateButton = UIButton()
    private let backgroundSrollView = UIView()
    public let uploadImageButtonView = AIHairCutUploadButtonView()
    public let hairCutChooseView = HairCutChooseView()
    
//    public let hairCutAreaView = HairCutAreaView()
    private var clothesStyleSelectedIndex = -1
    private var hairStyleSelectedIndex = -1
    
    public var popularModeCollectionView: UICollectionView? = nil
    public var hairCutAreaView: UICollectionView? = nil
    
    weak public var delegate: AIHairCutViewDelegate?
    public var hairStyleArr = [AIHairModel]()
    public var clothesStyleArr = [AIHairModel]()
    public var hairModel : AIHairModel!
    public var adManager = MyAdManager()
    var adView : UIView!
    //false：自定义参数 true：推荐模版
    public var isShowHairPage: Bool {
        willSet {
            // true: 显示发型模版页面 (popularModeCollectionView)
            // false: 显示造型模版页面 (hairCutAreaView)
            self.hairCutAreaView?.isHidden = newValue
            self.popularModeCollectionView?.isHidden = !newValue
        }
    }
    
    //生成按钮enable
    public var isEnableGenerate: Bool = false {
        willSet {
            self.generateButton.isEnabled = newValue
            self.generateButton.backgroundColor = newValue ? UIColor(valueRGB: 0xFFEC53) : UIColor(valueRGB: 0xFFEC53).alpha(0.5)
        }
    }
    
    // 注释：手动选区功能已废弃，使用火山引擎后不再需要
    /*
    // 新增属性
    private var maskImage: UIImage?
    private let selectMaskButton = UIButton(type: .system)
    private var areaSelectVC: AreaSelectViewController?

    // 添加选择区域开关按钮
    private let areaSelectionCheckbox = UIButton(type: .custom)
    // 选项卡容器视图
    private var checkboxContainer = UIView()
    // 是否开启选择区域功能的状态
    public var isAreaSelectionEnabled: Bool = false {
        didSet {
            updateAreaSelectionCheckbox()
            // 保存状态到UserDefaults
            UserDefaults.standard.set(isAreaSelectionEnabled, forKey: "isAreaSelectionEnabled")
        }
    }
    */
    
    init(isShowPopularPage: Bool) {
        self.isShowHairPage = true  // 默认显示发型模版
        // 从UserDefaults加载之前保存的状态
//        self.isAreaSelectionEnabled = UserDefaults.standard.bool(forKey: "isAreaSelectionEnabled")
        super.init(frame: .zero)
        self.initView()
        self.buildCollectionView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.fetchHairData()
        self.backgroundColor = UIColor(valueRGB: 0xF9F9F9)
        
        // 注释：手动选区功能已废弃，使用火山引擎后不再需要
        /*
        // 设置选择区域复选框
        if #available(iOS 13.0, *) {
            self.areaSelectionCheckbox.setImage(UIImage(systemName: "square"), for: .normal)
            self.areaSelectionCheckbox.setImage(UIImage(systemName: "checkmark.square.fill"), for: .selected)
        } else {
            // 使用文本符号作为 iOS 13 以下的备选方案
            self.areaSelectionCheckbox.setTitle("□ 手动选择修改区域", for: .normal)
            self.areaSelectionCheckbox.setTitle("✓ 手动选择修改区域", for: .selected)
        }
        self.areaSelectionCheckbox.setTitle(local(" 手动选择修改区域"), for: .normal)
        self.areaSelectionCheckbox.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        self.areaSelectionCheckbox.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        self.areaSelectionCheckbox.contentHorizontalAlignment = .left
        self.areaSelectionCheckbox.titleEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: 0)
        self.areaSelectionCheckbox.tintColor = UIColor(valueRGB: 0xFFEC53)
        self.areaSelectionCheckbox.addTarget(self, action: #selector(toggleAreaSelection), for: .touchUpInside)
        self.areaSelectionCheckbox.isSelected = self.isAreaSelectionEnabled

        // 选择修改区域按钮 - 移到这里，与复选框同一层级
        selectMaskButton.setTitle(local("选择修改区域"), for: .normal)
        selectMaskButton.setTitleColor(UIColor.hex(string: "333333"), for: .normal)
        selectMaskButton.backgroundColor = themColor
        selectMaskButton.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .medium)
        selectMaskButton.layer.cornerRadius = 12
        selectMaskButton.isHidden = true
        selectMaskButton.addTarget(self, action: #selector(selectMaskAction), for: .touchUpInside)

        */

        // 注释：手动选区功能已废弃，使用火山引擎后不再需要
        /*
        // 配置复选框约束
        self.areaSelectionCheckbox.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.height.equalTo(20)
            make.width.greaterThanOrEqualTo(140)
        }

        // 配置选择区域按钮约束
        selectMaskButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.greaterThanOrEqualTo(100)
            make.height.equalTo(24)
        }
        */

        self.generateButton.layer.cornerRadius = 25
        self.generateButton.backgroundColor = self.isEnableGenerate ? UIColor(valueRGB: 0xFFEC53) : UIColor(valueRGB: 0xFFEC53).alpha(0.5)
        self.generateButton.setTitle("generate".localized, for: .normal)
        self.generateButton.titleLabel?.textAlignment = .center
        self.generateButton.titleLabel?.font = UIFont.systemFont(ofSize: 15)
        self.generateButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        self.generateButton.isEnabled = self.isEnableGenerate
        self.generateButton.addTarget(self, action: #selector(generateResultImageAction), for: .touchUpInside)
        self.addSubview(self.generateButton)
        safeConstraints(self.generateButton) { make in
            make.bottom.equalTo(-20)
            make.left.equalTo(58)
            make.right.equalTo(-58)
            make.height.equalTo(50)
        }
        
        self.uploadImageButtonView.layer.cornerRadius = 10
        self.uploadImageButtonView.isUserInteractionEnabled = true
        let uploadImageTap = UITapGestureRecognizer()
        uploadImageTap.addTarget(self, action: #selector(upload))
        self.uploadImageButtonView.addGestureRecognizer(uploadImageTap)
        
        self.addSubview(self.uploadImageButtonView)
        safeConstraints(self.uploadImageButtonView) { make in
            make.left.equalTo(16)
            make.top.equalTo(40) // 向下移动，为次数显示留出空间
            make.right.equalTo(-16)
            make.height.equalTo(60)
        }

        self.backgroundSrollView.backgroundColor = UIColor.white
        self.backgroundSrollView.isUserInteractionEnabled = true
        self.backgroundSrollView.layer.cornerRadius = 16

        self.addSubview(self.backgroundSrollView)
        safeConstraints(self.backgroundSrollView) { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(self.uploadImageButtonView.snp.bottom).offset(10)
            make.bottom.equalTo(self.generateButton.snp.top).offset(-10)
        }

        self.hairCutChooseView.isShowPopularPage = self.isShowHairPage
        self.hairCutChooseView.isUserInteractionEnabled = true
        self.hairCutChooseView.delegate = self
        self.backgroundSrollView.addSubview(self.hairCutChooseView)
        safeConstraints(self.hairCutChooseView) { make in
            make.left.equalTo(16)
            make.top.equalTo(14)
            make.width.equalTo(self.uploadImageButtonView)
            make.height.equalTo(21)
        }
        
        // UI初始化状态
        self.hairCutAreaView?.isHidden = self.isShowHairPage  // true: 隐藏hairCutAreaView
        self.popularModeCollectionView?.isHidden = !self.isShowHairPage  // true: 显示popularModeCollectionView
    }
    
    @objc func upload()
    {
        let i = persistentGetObject(forKey: "uplaodTip")
        if i==nil
        {
            let view = AIHairCutTipView(frame: CGRect())
            view.layoutIfNeeded()
            let pop =  PopView.popSideContentView(view, direct: .slideInCenter)
            pop?.backgroundColor = .black.alpha(0.5)
            view.onCompletion  = { [weak self] isConfirmed in
                if isConfirmed {
                    self?.uploadImageAction()
                    persistentSetObject(1, forKey: "uplaodTip")
                }
            }
            
        }
        else
        {
            self.uploadImageAction()
        }
        
    }
    
    
    @objc func uploadImageAction() {
        if self.uploadImageButtonView.image != nil {
            self.checkGenerateEnable()
            return
        }
        
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.uploadImageAction)) != nil) {
            self.delegate?.uploadImageAction()
        }
    }
    
    
    
    
    @objc func generateResultImageAction() {
        MobClick.event("Intelligent_hair_replacement", attributes: ["source": "点击生成"])
        
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.generateResultImageAction)) != nil) {
            guard let data = self.getPopularModeAIHairCutData(),
                let image = self.uploadImageButtonView.image else {
                AppLoad.showActionAlert(message: "not_all_options_selected".localized)
                return
            }
            
            // 移除原有VIP检查，统一由VolcanoEngineAPI管理次数控制
            
            var newImage = image
            let fixSize = ImageTool().getFixSize(image.size, maxSize: 800 * 800)
            if image.size.width > fixSize.width || image.size.height > fixSize.height {
                newImage = image.createImage(fixSize)!
            }
            
            data.origin_image = newImage
            
            // 注释：手动选区功能已废弃，使用火山引擎后不再需要
            /*
            // 如果启用了区域选择并且有mask图片，将mask添加到data中
            if self.isAreaSelectionEnabled && self.maskImage != nil {
                data.mask_image = self.maskImage
                print("使用选择的区域进行生成")
            } else if self.isAreaSelectionEnabled && self.maskImage == nil {
                // 如果启用了区域选择但没有mask，提示用户选择区域
                SVProgressHUD.showInfo(withStatus: local("请先选择修改区域"))
                return
            }
            */
            
            SVProgressHUD.show()
            AIHairModel.fectchQuestJson(type: data.type) { result in
                
                switch result {
                    
                case .success(let questdata):
                    SVProgressHUD.dismiss()
                    guard let json = try? JSONSerialization.jsonObject(with: questdata) as? [String: Any] else {
                        
                        return
                    }
                    var requestjson: [String: Any] = [:]
                    requestjson = json
                    
                    // 注释：手动选区功能已废弃，使用火山引擎后不再需要
                    /*
                    // 如果有遮罩图片，添加到请求参数中
                    if self.isAreaSelectionEnabled && self.maskImage != nil {
                        requestjson["use_mask"] = true
                    }
                    */
                    
                    self.delegate?.generateResultImageAction(data: data, requestjson: requestjson)
                case .failure:
                    SVProgressHUD.showError(withStatus: local("获取数据失败"))
                    return
                }
            }
        }
    }
    
    // 保留协议方法，但简化实现（火山引擎方案不需要复杂的参数设置）
    func customParameterTapAction() {
        // 点击发型模版按钮
        if self.isShowHairPage != true {
            // 如果当前不是显示发型模版，则切换到发型模版
            self.isShowHairPage = true
            self.checkGenerateEnable()
        }
    }

    func recommendedTemplateAction() {
        // 点击造型模版按钮
        if self.isShowHairPage != false {
            // 如果当前不是显示造型模版，则切换到造型模版
            self.isShowHairPage = false
            self.checkGenerateEnable()
        }
    }
    
    public func addImageAction(image: UIImage) {
        self.uploadImageButtonView.image = image
        
        //UI处理
        DispatchQueue.main.async {
            let height = UIScreen.main.bounds.height * 187 / 812
            
            // 使用类属性访问容器视图
            // 移除旧的约束
            safeRemakeConstraints(self.uploadImageButtonView) { make in
                make.left.equalTo(16)
                make.top.equalTo(40) // 向下移动，为次数显示留出空间
                make.right.equalTo(-16)
                make.height.equalTo(height)
            }

            // 重新设置背景滚动视图的约束
            safeRemakeConstraints(self.backgroundSrollView) { make in
                make.left.right.equalToSuperview()
                make.top.equalTo(self.uploadImageButtonView.snp.bottom).offset(10)
                make.bottom.equalTo(self.generateButton.snp.top).offset(-10)
            }
            
            self.layoutIfNeeded()
        }

        // 注释：手动选区功能已废弃，使用火山引擎后不再需要
        /*
        // 显示选择修改区域按钮，但要考虑isAreaSelectionEnabled状态
        self.selectMaskButton.isHidden = (image == nil || !self.isAreaSelectionEnabled)

        // 释放旧的areaSelectVC
        self.areaSelectVC = nil
        */

        // 注释：手动选区功能已废弃，使用火山引擎后不再需要
        /*
        // 清空maskImage
        self.maskImage = nil
        */
    }
    
    // 注释：设置参数功能已废弃，使用火山引擎后不再需要
    /*
    func setCustomParametersEnable() {
        self.checkGenerateEnable()
    }
    */
    
    //检查生成按钮是否能使用
    public func checkGenerateEnable() {
        if self.isShowHairPage {
            let pIsEnableGenerate = self.hairStyleSelectedIndex != -1 && self.uploadImageButtonView.image != nil
            if self.isEnableGenerate != pIsEnableGenerate {
                self.isEnableGenerate = pIsEnableGenerate
            }
        } else {
            let pIsEnableGenerate = self.clothesStyleSelectedIndex != -1 && self.uploadImageButtonView.image != nil
            if self.isEnableGenerate != pIsEnableGenerate {
                self.isEnableGenerate = pIsEnableGenerate
            }
        }
    }

    // 注释：手动选区功能已废弃，使用火山引擎后不再需要
    /*
    @objc private func selectMaskAction() {
        // 检查是否启用了区域选择
        if !self.isAreaSelectionEnabled {
            return
        }

        guard let image = self.uploadImageButtonView.image else { return }
        // 如果areaSelectVC已存在且图片一致，直接present，否则新建
        if areaSelectVC == nil || areaSelectVC?.image !== image {
            areaSelectVC = AreaSelectViewController(image: image)
            areaSelectVC?.delegate = self
        }
        // 获取当前vc
        if let vc = self.delegate as? UIViewController {
            areaSelectVC?.modalPresentationStyle = .fullScreen
            vc.present(areaSelectVC!, animated: true)
            MobClick.event("Intelligent_hair_replacement", attributes: ["source": "选择修改区域"])
        }
    }

    // AreaSelectViewControllerDelegate
    func areaSelectViewController(_ vc: AreaSelectViewController, didFinishWithMask mask: UIImage) {
        self.maskImage = mask
        // 保存到相册用于debug
//        UIImageWriteToSavedPhotosAlbum(mask, nil, nil, nil)
//        print("mask saved to album")
        // 自动关闭涂抹VC
        if let presented = (self.delegate as? UIViewController)?.presentedViewController, presented == vc {
            presented.dismiss(animated: true)
        }
    }

    // 添加切换区域选择状态的方法
    @objc private func toggleAreaSelection() {
        self.isAreaSelectionEnabled = !self.isAreaSelectionEnabled
        MobClick.event("Intelligent_hair_replacement", attributes: ["source": "点击手动选择修改区域"])

        // 根据区域选择状态控制设置按钮的显示/隐藏
        if let settingButton = self.subviews.first(where: { $0 is UIButton && $0.tag == 1001 }) {
            settingButton.isHidden = self.isAreaSelectionEnabled
        }
    }

    // 更新复选框UI
    private func updateAreaSelectionCheckbox() {
        self.areaSelectionCheckbox.isSelected = self.isAreaSelectionEnabled
        // 如果当前有图片，根据状态显示或隐藏选择区域按钮
        if self.uploadImageButtonView.image != nil {
            self.selectMaskButton.isHidden = !self.isAreaSelectionEnabled
        }
    }
    */
}

typealias AIHairCutViewAdMode = AIHairCutView
extension AIHairCutViewAdMode {
    func uploadAdView() {
        // 此方法不再使用广告UI，仅作为检查模型是否需要VIP的方法
        let _ = self.getPopularModeAIHairCutData()
        
        // 隐藏旧的广告视图（如果有）
        if let view = adView {
            view.isHidden = true
        }
    }
}

typealias AIHairCutViewHotMode = AIHairCutView
extension AIHairCutViewHotMode: UICollectionViewDelegate, UICollectionViewDataSource {
    func buildCollectionView() {

        let width = (UIScreen.main.bounds.width - 28 - 16) * 1.0 / 2
        let height = 218 * (UIScreen.main.bounds.width / 375.0)  // 按照375宽度的比例缩放

        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.itemSize = CGSize(width: width, height: height)
        layout.minimumLineSpacing = 7
        layout.minimumInteritemSpacing = 6

        let layout2 = UICollectionViewFlowLayout()
        layout2.scrollDirection = .vertical
        layout2.itemSize = CGSize(width: width, height: height)
        layout2.minimumLineSpacing = 7
        layout2.minimumInteritemSpacing = 6
        
        self.popularModeCollectionView = UICollectionView(frame: CGRect(x: 0, y: 16, width: self.frame.width, height: 326), collectionViewLayout: layout)
        self.popularModeCollectionView?.showsVerticalScrollIndicator = false
        self.popularModeCollectionView?.bounces = false
        
        self.hairCutAreaView = UICollectionView(frame: CGRect(x: 0, y: 16, width: self.frame.width, height: 326), collectionViewLayout: layout2)
        self.hairCutAreaView?.showsVerticalScrollIndicator = false
        self.hairCutAreaView?.bounces = false
        
        self.backgroundSrollView.addSubview(self.popularModeCollectionView ?? UICollectionView())
        self.backgroundSrollView.addSubview(self.hairCutAreaView ?? UICollectionView())
        if let popularModeCollectionView = self.popularModeCollectionView {
            safeConstraints(popularModeCollectionView) { make in
                make.top.equalTo(self.hairCutChooseView.snp.bottom).offset(14)
                make.left.equalTo(16)
                make.width.equalTo(UIScreen.main.bounds.width - 32)
                make.bottom.equalTo(-16)
            }
        }
        if let hairCutAreaView = self.hairCutAreaView {
            safeConstraints(hairCutAreaView) { make in
                make.top.equalTo(self.hairCutChooseView.snp.bottom).offset(14)
                make.left.equalTo(16)
                make.width.equalTo(UIScreen.main.bounds.width - 32)
                make.bottom.equalTo(-16)
            }
        }
        self.hairCutAreaView?.collectionViewLayout = layout2
        self.hairCutAreaView?.delegate = self
        self.hairCutAreaView?.dataSource = self
        self.hairCutAreaView?.backgroundColor = UIColor.white
        self.hairCutAreaView?.register(HotHairCutCollectionViewCell.self, forCellWithReuseIdentifier: HotHairCutCollectionViewCell.identifier)
        
        self.popularModeCollectionView?.collectionViewLayout = layout
        self.popularModeCollectionView?.delegate = self
        self.popularModeCollectionView?.dataSource = self
        self.popularModeCollectionView?.backgroundColor = UIColor.white
        self.popularModeCollectionView?.register(HotHairCutCollectionViewCell.self, forCellWithReuseIdentifier: HotHairCutCollectionViewCell.identifier)
        
        // 注释：设置按钮功能已废弃，使用火山引擎后不再需要参数设置
        /*
        let Setingbutton = UIButton.createButton(withBGImageName: "shezhi")
        Setingbutton.tag = 1001  // 添加tag以便后续查找
        self.addSubview(Setingbutton)
        Setingbutton.snp.makeConstraints { make in
            make.right.equalTo(-26)
            make.top.equalTo(backgroundSrollView.snp.top).offset(12)
            make.width.height.equalTo(24)
        }
        Setingbutton.addTapAction {
            MobClick.event("Intelligent_hair_replacement", attributes: ["source": "设置"])
            let setting =  AIHairCutSettingView(frame: CGRect())
            let pop = PopView.popSideContentView(setting, direct: .slideFromBottom)
            pop?.clickOutHidden = true
        }
        */
        
        // 注释：手动选区功能已废弃，使用火山引擎后不再需要
        /*
        // 初始化时根据区域选择状态设置按钮的显示/隐藏
        Setingbutton.isHidden = self.isAreaSelectionEnabled
        */
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return collectionView == self.popularModeCollectionView ? self.hairStyleArr.count : self.clothesStyleArr.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HotHairCutCollectionViewCell.identifier, for: indexPath) as? HotHairCutCollectionViewCell else {
            return UICollectionViewCell()
        }
        if collectionView == self.popularModeCollectionView
        {
            let model = self.hairStyleArr[indexPath.row]
            if isChinese {
                cell.titleString = model.name
                
            }else
            {
                cell.titleString = model.name_en
            }
            cell.imageString = model.image
            cell.isSelectedItem = indexPath.row == self.hairStyleSelectedIndex
            
        }
        else
        {
            let model = self.clothesStyleArr[indexPath.row]
            cell.imageString = model.image
            cell.isSelectedItem = indexPath.row == self.clothesStyleSelectedIndex
            if isChinese {
                cell.titleString = model.name
                
            }else
            {
                cell.titleString = model.name_en
            }
        }
        
            
        return cell
    }
    
    
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let index = indexPath.row
        
        if collectionView == self.popularModeCollectionView
        {
            MobClick.event("Intelligent_hair_replacement", attributes: ["source": "发型模板"])
            if self.hairStyleSelectedIndex == index {
                return
            }
            var arr = [IndexPath]()
            if self.hairStyleSelectedIndex != -1 {
                arr.append(IndexPath(row: self.hairStyleSelectedIndex, section: 0))
            }
            self.hairStyleSelectedIndex = index
            arr.append(IndexPath(row: index, section: 0))
            //一次刷新两个item，不能单个刷2遍，因为是异步操作
            self.popularModeCollectionView?.reloadItems(at: arr)
            //检查enable
            
            // 添加选择埋点
            let hairModel = self.hairStyleArr[index]
            self.hairModel = hairModel;
            MobClick.event("Intelligent_hair_replacement", attributes: ["choose": hairModel.name])
            
            self.checkGenerateEnable()
            uploadAdView()
        }
        else
        {
            MobClick.event("Intelligent_hair_replacement", attributes: ["source": "造型模板"])
            if self.clothesStyleSelectedIndex == index {
                return
            }
            var arr = [IndexPath]()
            if self.clothesStyleSelectedIndex != -1 {
                arr.append(IndexPath(row: self.clothesStyleSelectedIndex, section: 0))
            }
            self.clothesStyleSelectedIndex = index
            arr.append(IndexPath(row: index, section: 0))
            //一次刷新两个item，不能单个刷2遍，因为是异步操作
            self.hairCutAreaView?.reloadItems(at: arr)
            //检查enable
            
            // 添加选择埋点
            let clothesModel = self.clothesStyleArr[index]
            MobClick.event("Intelligent_hair_replacement", attributes: ["choose": clothesModel.name])
            self.hairModel = clothesModel
            self.checkGenerateEnable()
            uploadAdView()
        }
    }
    
    /// 推荐模版生成数据
    ///
    private func getPopularModeAIHairCutData() -> AIHairModel? {
        if (self.isShowHairPage)
        {
            // 显示发型模版时，返回hairStyleArr中选中的数据
            if self.hairStyleArr.count > 0 && (hairStyleSelectedIndex != -1) {
                return self.hairStyleArr[self.hairStyleSelectedIndex]
            }
        }
        else
        {
            // 显示造型模版时，返回clothesStyleArr中选中的数据 
            if self.clothesStyleArr.count > 0 && (clothesStyleSelectedIndex != -1) {
                return self.clothesStyleArr[self.clothesStyleSelectedIndex]
            }
        }
        return nil
    }
    
    
    public func fetchHairData() {
//        SVProgressHUD.show()
        let url = "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/hairmodel.json" // 发型模版数据
        
        AIHairModel.fetchHairData(from: url, parameters: nil) { result in
            SVProgressHUD.dismiss()
            switch result {
            case .success(let data):
                self.hairStyleArr = data
                print("发型数据加载成功，共 \(data.count) 条")
                DispatchQueue.main.async {
                    
                    if let model = self.hairModel
                    {
                        print("🔍 AIHairCutView: 发型数据加载完成，检查预选 - model.name: \(model.name), model.type: \(model.type), model.ID: \(model.ID)")
                        if model.type == 1
                        {
                            print("🔍 AIHairCutView: 模型type=1，在发型数组中查找匹配ID")
                            for (index, hairModel) in self.hairStyleArr.enumerated() {
                                if model.ID == hairModel.ID
                                {
                                    print("🔍 AIHairCutView: 找到匹配的发型，index: \(index), name: \(hairModel.name)")
                                    self.hairStyleSelectedIndex = index
                                    self.hairModel = hairModel  // 确保hairModel被正确设置
                                    self.uploadAdView()
                                    self.checkGenerateEnable()  // 检查生成按钮状态
                                    break
                                }
                            }
                        } else {
                            print("🔍 AIHairCutView: 模型type=\(model.type)，不是发型类型")
                        }
                    }
                    self.popularModeCollectionView?.reloadData()
                    // 确保布局计算完成
                    self.popularModeCollectionView?.layoutIfNeeded()
                     
                    // 滑动到目标位置
                    if self.hairStyleSelectedIndex != -1,
                       self.hairStyleSelectedIndex < self.hairStyleArr.count {
                        
                        let indexPath = IndexPath(item: self.hairStyleSelectedIndex, section: 0)
                        self.popularModeCollectionView?.scrollToItem(
                            at: indexPath,
                            at: .centeredVertically,
                            animated: true
                        )
                    }
                    
                    // 检查生成按钮的状态
                    self.checkGenerateEnable()
                }
            case .failure(let error):
                print("请求失败: \(error.localizedDescription)")
                SVProgressHUD.showError(withStatus: "获取失败".localized)
            }
        }
        
        let url2 = "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/clothesmodel.json" // 造型模版数据
        AIHairModel.fetchHairData(from: url2, parameters: nil) { result in
            SVProgressHUD.dismiss()
            switch result {
            case .success(let data):
                self.clothesStyleArr = data
                print("造型数据加载成功，共 \(data.count) 条")
                DispatchQueue.main.async {
                    if let model = self.hairModel
                    {
                        print("🔍 AIHairCutView: 造型数据加载完成，检查预选 - model.name: \(model.name), model.type: \(model.type), model.ID: \(model.ID)")
                        if model.type == 2  // 造型的type是2
                        {
                            print("🔍 AIHairCutView: 模型type=2，在造型数组中查找匹配ID")
                            for (index, clothesModel) in self.clothesStyleArr.enumerated() {
                                if model.ID == clothesModel.ID
                                {
                                    print("🔍 AIHairCutView: 找到匹配的造型，index: \(index), name: \(clothesModel.name)")
                                    self.clothesStyleSelectedIndex = index
                                    self.hairModel = clothesModel  // 确保hairModel被正确设置
                                    self.uploadAdView()
                                    self.checkGenerateEnable()  // 检查生成按钮状态
                                    break
                                }
                            }
                        } else {
                            print("🔍 AIHairCutView: 模型type=\(model.type)，不是造型类型")
                        }
                    }
                    
                    self.hairCutAreaView?.reloadData()
                    // 确保布局计算完成
                    self.hairCutAreaView?.layoutIfNeeded()
                     
                    // 滑动到目标位置
                    if self.clothesStyleSelectedIndex != -1,
                       self.clothesStyleSelectedIndex < self.clothesStyleArr.count {
                        
                        let indexPath = IndexPath(item: self.clothesStyleSelectedIndex, section: 0)
                        self.hairCutAreaView?.scrollToItem(
                            at: indexPath,
                            at: .centeredVertically,
                            animated: true
                        )
                    }
                    
                    // 检查生成按钮的状态
                    self.checkGenerateEnable()
                }
            case .failure(let error):
                print("请求失败: \(error.localizedDescription)")
                SVProgressHUD.showError(withStatus: "获取失败".localized)
            }
        }
    }
}



