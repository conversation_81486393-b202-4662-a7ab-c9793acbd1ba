import UIKit
import SVProgressHUD

protocol AreaSelectViewControllerDelegate: AnyObject {
    func areaSelectViewController(_ vc: AreaSelectViewController, didFinishWithMask mask: UIImage)
}

class AreaSelectViewController: UIViewController {
    let image: UIImage
    weak var delegate: AreaSelectViewControllerDelegate?

    private let paintView = PTMPaintView(frame: .zero)
    private let slider = UISlider()
    private let brushButton = UIButton(type: .custom)
    private let eraserButton = UIButton(type: .custom)
    private let confirmButton = UIButton(type: .system)
    private let backButton = UIButton(type: .custom)
    private let titleLabel = UILabel()
    private let toolBar = UIView()
    private let previewView = BrushPreviewView(frame: .zero)
    private let imageContainerView = UIView()
    
    // 提示视图相关
    private let guideOverlay = UIView()
    private let guidePopup = UIView()

    private var brushWidth: CGFloat = 20
    private var eraserWidth: CGFloat = 20
    private var previewHideWorkItem: DispatchWorkItem?
    private var toolType: DrawView.ToolType = .brush {
        didSet {
            paintView.toolType = toolType
            updateToolUI()
        }
    }
    
    // UserDefaults键
    private let hasShownAreaSelectGuideKey = "hasShownAreaSelectGuide"

    init(image: UIImage) {
        self.image = image
        super.init(nibName: nil, bundle: nil)
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor.hex(string: "#F9F9F9")
        setupUI()
        
        // 检查是否需要显示引导
        checkAndShowGuide()
    }
    
    private func checkAndShowGuide() {
        // 检查是否已经显示过引导
        if !UserDefaults.standard.bool(forKey: hasShownAreaSelectGuideKey) {
            // 第一次使用，显示引导
            setupGuideView()
        }
    }
    
    private func setupGuideView() {
        // 半透明背景覆盖层
        guideOverlay.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        guideOverlay.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(guideOverlay)
        NSLayoutConstraint.activate([
            guideOverlay.topAnchor.constraint(equalTo: view.topAnchor),
            guideOverlay.leftAnchor.constraint(equalTo: view.leftAnchor),
            guideOverlay.rightAnchor.constraint(equalTo: view.rightAnchor),
            guideOverlay.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        // 弹窗视图
        guidePopup.backgroundColor = .white
        guidePopup.layer.cornerRadius = 12
        guidePopup.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(guidePopup)
        NSLayoutConstraint.activate([
            guidePopup.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            guidePopup.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            guidePopup.widthAnchor.constraint(equalTo: view.widthAnchor, multiplier: 0.8),
            guidePopup.heightAnchor.constraint(lessThanOrEqualTo: view.heightAnchor, multiplier: 0.6)
        ])
        
        // 提示标题
        let titleLabel = UILabel()
        titleLabel.text = "请涂抹想要修改的区域（衣服/发型）".localized
        titleLabel.font = UIFont.systemFont(ofSize: 17, weight: .medium)
        titleLabel.textColor = UIColor.hex(string: "#333333")
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 0
        guidePopup.addSubview(titleLabel)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: guidePopup.topAnchor, constant: 20),
            titleLabel.leftAnchor.constraint(equalTo: guidePopup.leftAnchor, constant: 15),
            titleLabel.rightAnchor.constraint(equalTo: guidePopup.rightAnchor, constant: -15)
        ])
        
        // 示例图片
        let imageView = UIImageView()
        imageView.image = UIImage(named: "area_select_guide") // 假设有这个示例图片
        imageView.contentMode = .scaleAspectFit
        imageView.translatesAutoresizingMaskIntoConstraints = false
        guidePopup.addSubview(imageView)
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 15),
            imageView.leftAnchor.constraint(equalTo: guidePopup.leftAnchor, constant: 15),
            imageView.rightAnchor.constraint(equalTo: guidePopup.rightAnchor, constant: -15),
            imageView.heightAnchor.constraint(lessThanOrEqualTo: guidePopup.heightAnchor, multiplier: 0.5)
        ])
        
        // 完成按钮
        let doneButton = UIButton(type: .system)
        doneButton.setTitle("完成".localized, for: .normal)
        doneButton.setTitleColor(.black, for: .normal)
        doneButton.backgroundColor = UIColor.hex(string: "#FFE14D")
        doneButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 17)
        doneButton.layer.cornerRadius = 20
        doneButton.addTarget(self, action: #selector(dismissGuide), for: .touchUpInside)
        guidePopup.addSubview(doneButton)
        doneButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            doneButton.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 20),
            doneButton.centerXAnchor.constraint(equalTo: guidePopup.centerXAnchor),
            doneButton.widthAnchor.constraint(equalToConstant: 180),
            doneButton.heightAnchor.constraint(equalToConstant: 40),
            doneButton.bottomAnchor.constraint(equalTo: guidePopup.bottomAnchor, constant: -20)
        ])
    }
    
    @objc private func dismissGuide() {
        // 标记已显示过引导
        UserDefaults.standard.set(true, forKey: hasShownAreaSelectGuideKey)
        UserDefaults.standard.synchronize()
        
        // 动画移除引导视图
        UIView.animate(withDuration: 0.3, animations: {
            self.guideOverlay.alpha = 0
            self.guidePopup.alpha = 0
        }) { _ in
            self.guideOverlay.removeFromSuperview()
            self.guidePopup.removeFromSuperview()
        }
    }

    private func setupUI() {
        // 顶部栏
        let topBar = UIView()
        topBar.backgroundColor = .clear
        view.addSubview(topBar)
        topBar.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            topBar.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            topBar.leftAnchor.constraint(equalTo: view.leftAnchor),
            topBar.rightAnchor.constraint(equalTo: view.rightAnchor),
            topBar.heightAnchor.constraint(equalToConstant: 56)
        ])
        // 返回按钮
        backButton.setImage(UIImage(named: "left_arrow"), for: .normal)
        backButton.addTarget(self, action: #selector(backTapped), for: .touchUpInside)
        topBar.addSubview(backButton)
        backButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            backButton.leftAnchor.constraint(equalTo: topBar.leftAnchor, constant: 12),
            backButton.centerYAnchor.constraint(equalTo: topBar.centerYAnchor),
            backButton.widthAnchor.constraint(equalToConstant: 32),
            backButton.heightAnchor.constraint(equalToConstant: 32)
        ])
        // 标题
        titleLabel.text = local("选择区域")
        titleLabel.font = UIFont.boldSystemFont(ofSize: 20)
        titleLabel.textColor = UIColor.hex(string: "#333333")
        titleLabel.textAlignment = .center
        topBar.addSubview(titleLabel)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: topBar.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: topBar.centerYAnchor)
        ])
        // 确认按钮
        confirmButton.setTitle("确认".localized, for: .normal)
        confirmButton.setTitleColor(.black, for: .normal)
        confirmButton.backgroundColor = UIColor.hex(string: "#FFE14D")
        confirmButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 17)
        confirmButton.layer.cornerRadius = 20
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        topBar.addSubview(confirmButton)
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            confirmButton.rightAnchor.constraint(equalTo: topBar.rightAnchor, constant: -16),
            confirmButton.centerYAnchor.constraint(equalTo: topBar.centerYAnchor),
            confirmButton.widthAnchor.constraint(equalToConstant: 72),
            confirmButton.heightAnchor.constraint(equalToConstant: 40)
        ])
        // 图片区域容器
        imageContainerView.translatesAutoresizingMaskIntoConstraints = false
        imageContainerView.clipsToBounds = true
        view.addSubview(imageContainerView)
        NSLayoutConstraint.activate([
            imageContainerView.topAnchor.constraint(equalTo: topBar.bottomAnchor, constant: 10),
            imageContainerView.leftAnchor.constraint(equalTo: view.leftAnchor),
            imageContainerView.rightAnchor.constraint(equalTo: view.rightAnchor),
            imageContainerView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -200)
        ])
        // paintView
        paintView.image = image
        paintView.contentMode = .scaleAspectFit
        paintView.translatesAutoresizingMaskIntoConstraints = false
        imageContainerView.addSubview(paintView)
        NSLayoutConstraint.activate([
            paintView.topAnchor.constraint(equalTo: imageContainerView.topAnchor),
            paintView.leftAnchor.constraint(equalTo: imageContainerView.leftAnchor),
            paintView.rightAnchor.constraint(equalTo: imageContainerView.rightAnchor),
            paintView.bottomAnchor.constraint(equalTo: imageContainerView.bottomAnchor)
        ])
        paintView.toolType = toolType
        // 预览圈
        previewView.isHidden = true
        previewView.translatesAutoresizingMaskIntoConstraints = false
        imageContainerView.addSubview(previewView)
        NSLayoutConstraint.activate([
            previewView.centerXAnchor.constraint(equalTo: imageContainerView.centerXAnchor),
            previewView.centerYAnchor.constraint(equalTo: imageContainerView.centerYAnchor),
            previewView.widthAnchor.constraint(equalToConstant: 120),
            previewView.heightAnchor.constraint(equalToConstant: 120)
        ])
        // 底部工具栏
        toolBar.backgroundColor = .white
        toolBar.layer.cornerRadius = 20
        view.addSubview(toolBar)
        toolBar.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            toolBar.leftAnchor.constraint(equalTo: view.leftAnchor, constant: 16),
            toolBar.rightAnchor.constraint(equalTo: view.rightAnchor, constant: -16),
            toolBar.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -16),
            toolBar.heightAnchor.constraint(equalToConstant: 141)
        ])
        // slider
        slider.minimumValue = 5
        slider.maximumValue = 60
        slider.value = Float(brushWidth)
        slider.minimumTrackTintColor = UIColor.hex(string: "#FFE14D")
        slider.maximumTrackTintColor = UIColor.hex(string: "#F7F7F7")
        slider.setThumbImage(UIImage(named: "slider_thumb"), for: .normal)
        slider.addTarget(self, action: #selector(sliderChanged), for: .valueChanged)
        slider.addTarget(self, action: #selector(sliderTouchUp), for: [.touchUpInside, .touchUpOutside, .touchCancel])
        toolBar.addSubview(slider)
        slider.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            slider.leftAnchor.constraint(equalTo: toolBar.leftAnchor, constant: 24),
            slider.rightAnchor.constraint(equalTo: toolBar.rightAnchor, constant: -24),
            slider.topAnchor.constraint(equalTo: toolBar.topAnchor, constant: 24),
            slider.heightAnchor.constraint(equalToConstant: 24)
        ])
        // 画笔按钮
        brushButton.setImage(UIImage(named: "icon_brush"), for: .normal)
        brushButton.setImage(UIImage(named: "icon_brush_selet"), for: .selected)
        brushButton.setTitle(local("画笔"), for: .normal)
        brushButton.setTitleColor(UIColor.hex(string: "#333333"), for: .normal)
        brushButton.titleLabel?.font = UIFont.systemFont(ofSize: 15)
        brushButton.addTarget(self, action: #selector(brushTapped), for: .touchUpInside)
        
        // 使用ZYEButtonPositionAndSpace设置图片在上，文字在下
        brushButton.contentHorizontalAlignment = .center
        brushButton.setImagePosition(with: .top, spacing: 8)
        
        toolBar.addSubview(brushButton)
        brushButton.translatesAutoresizingMaskIntoConstraints = false
        
        // 橡皮擦按钮
        eraserButton.setImage(UIImage(named: "icon_eraser"), for: .normal)
        eraserButton.setImage(UIImage(named: "icon_eraser_selet"), for: .selected)
        eraserButton.setTitle(local("橡皮擦"), for: .normal)
        eraserButton.setTitleColor(UIColor.hex(string: "#333333"), for: .normal)
        eraserButton.titleLabel?.font = UIFont.systemFont(ofSize: 15)
        eraserButton.addTarget(self, action: #selector(eraserTapped), for: .touchUpInside)
        
        // 使用ZYEButtonPositionAndSpace设置图片在上，文字在下
        eraserButton.contentHorizontalAlignment = .center
        eraserButton.setImagePosition(with: .top, spacing: 8)
        
        toolBar.addSubview(eraserButton)
        eraserButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            brushButton.centerXAnchor.constraint(equalTo: toolBar.centerXAnchor, constant: -60),
            brushButton.topAnchor.constraint(equalTo: slider.bottomAnchor, constant: 16),
            brushButton.widthAnchor.constraint(equalToConstant: 70),
            brushButton.heightAnchor.constraint(equalToConstant: 64),
            eraserButton.centerXAnchor.constraint(equalTo: toolBar.centerXAnchor, constant: 60),
            eraserButton.topAnchor.constraint(equalTo: slider.bottomAnchor, constant: 16),
            eraserButton.widthAnchor.constraint(equalToConstant: 80),
            eraserButton.heightAnchor.constraint(equalToConstant: 64)
        ])
        updateToolUI()
    }

    @objc private func backTapped() {
        self.dismiss(animated: true)
    }
    @objc private func brushTapped() {
        toolType = .brush
        showPreview()
        schedulePreviewHide()
    }
    @objc private func eraserTapped() {
        toolType = .eraser
        showPreview()
        schedulePreviewHide()
    }
    @objc private func sliderChanged() {
        let value = CGFloat(slider.value)
        if toolType == .brush {
            brushWidth = value
            paintView.lineWidth = brushWidth
        } else {
            eraserWidth = value
            paintView.lineWidth = eraserWidth
        }
        showPreview()
    }
    @objc private func sliderTouchUp() {
        schedulePreviewHide()
    }
    @objc private func confirmTapped() {
        // 添加确认埋点
        MobClick.event("Intelligent_hair_replacement", attributes: ["select_the_area": "确认"])
        
        // 显示加载指示器
        SVProgressHUD.show(withStatus: local("正在生成mask..."))
        
        // 在后台线程生成mask
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { 
                DispatchQueue.main.async {
                    SVProgressHUD.dismiss()
                }
                return 
            }
            
            // 在后台线程生成mask
            let mask = self.paintView.generateMask(size: self.image.size)
            
            // 返回主线程处理结果
            DispatchQueue.main.async {
                // 隐藏加载指示器
                SVProgressHUD.dismiss()
                
                // 调用代理方法并关闭视图
                self.delegate?.areaSelectViewController(self, didFinishWithMask: mask)
                self.dismiss(animated: true)
            }
        }
    }
    private func updateToolUI() {
        
        brushButton.setTitleColor(toolType == .brush ? UIColor.hex(string: "#333333") : UIColor.hex(string: "#999999"), for: .normal)
        eraserButton.setTitleColor(toolType == .eraser ? UIColor.hex(string: "#333333") : UIColor.hex(string: "#999999"), for: .normal)
        brushButton.isSelected = toolType == .brush
        eraserButton.isSelected = toolType == .eraser
    }
    private func showPreview() {
        previewHideWorkItem?.cancel()
        previewView.isHidden = false
        let width = toolType == .brush ? brushWidth : eraserWidth
        previewView.circleRadius = width
        previewView.isEraser = (toolType == .eraser)
        previewView.circleColor = paintView.drawView.themeColor
    }
    private func schedulePreviewHide() {
        previewHideWorkItem?.cancel()
        let workItem = DispatchWorkItem { [weak self] in
            self?.previewView.isHidden = true
        }
        previewHideWorkItem = workItem
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5, execute: workItem)
    }
} 
