//
//  AIHairCutTipView.swift
//  HairCut
//
//  Created by fs0011 on 2025/3/19.
//

import Foundation
import UIKit

class AIHairCutTipView :UIView{
    typealias CompletionHandler = (_ isConfirmed: Bool) -> Void
        var onCompletion: CompletionHandler?
    
    var Tipimg1: UIImageView!
    var Tipimg2: UIImageView!
    private var currentStep = 1
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.initView();
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func initView(){
        self.snp.makeConstraints { make in
            make.width.equalTo(307)
            make.height.equalTo(336)
        }
        self.backgroundColor = .white
        self.layer.cornerRadius = 20
        self.layer.masksToBounds = true
        
        let tishiLabel = UILabel.createLabel(title: "换造型小提示".localized, textColor: .black, textAlignment: .center, font: UIFont.systemFont(ofSize: 20))
        self.addSubview(tishiLabel)
        tishiLabel.snp.makeConstraints { make in
            make.top.equalTo(20)
            make.centerX.equalTo(307*0.5)
        }
        
        Tipimg1 = UIImageView.createSizeFitImageView(name: isChineseLanguage() ? "Tipimg1" : "Tipimg1_en")
        self.addSubview(Tipimg1)
        Tipimg1.snp.makeConstraints { make in
            make.centerY.equalTo(336*0.5)
            make.right.equalTo(-155)
            make.width.equalTo(133)
            make.height.equalTo(176)
        }
        Tipimg2 = UIImageView.createSizeFitImageView(name: isChineseLanguage() ? "Tipimg2" : "Tipimg2_en")
        self.addSubview(Tipimg2)
        Tipimg2.snp.makeConstraints { make in
            make.centerY.equalTo(336*0.5)
            make.left.equalTo(155)
            make.width.equalTo(133)
            make.height.equalTo(176)
        }
        
        let btn = UIButton.createButton(title: "下一步".localized, color: darkTextColor, font: bigMedlumFont)
        btn.backgroundColor = themColor
        self.addSubview(btn)
        btn.layer.cornerRadius = 22
        btn.layer.masksToBounds = true
        btn.snp.makeConstraints { make in
            make.centerX.equalTo(307*0.5)
            make.width.equalTo(227)
            make.height.equalTo(44)
            make.bottom.equalTo(-27)
        }
        btn.addTapAction { [self] in
            if currentStep == 1
            {
                self.Tipimg1.image = UIImage(named: isChineseLanguage() ? "Tipimg3" : "Tipimg3_en")
                self.Tipimg2.image = UIImage(named: isChineseLanguage() ? "Tipimg4" : "Tipimg4_en")
                currentStep = 2
                tishiLabel.text = "换发型小提示".localized
                btn.setTitle("确认".localized, for: .normal)
            }
            else
            {
                PopView.hidenPopView()
                self.onCompletion?(true)
            }
        }
        
    }
    
    // Helper function to check if current language is Chinese
    private func isChineseLanguage() -> Bool {
        let preferredLanguage = Locale.preferredLanguages.first ?? ""
        return preferredLanguage.starts(with: "zh")
    }
}
