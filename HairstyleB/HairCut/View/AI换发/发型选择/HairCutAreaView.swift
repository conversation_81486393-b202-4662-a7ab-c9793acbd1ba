//
//  HairCutAreaView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/8/2.
//

import Foundation
import UIKit
import SnapKit

protocol HairCutAreaViewDelegate: NSObjectProtocol {
    func setCustomParametersEnable()
}

class HairCutAreaView: UIView {
    private let hairCutTypeLabel = UILabel()
    private let hairCutLengthLabel = UILabel()
    private let hairCutColorLabel = UILabel()
    public let genderButton = UIButton()
    public var hairCutTypeCollectionView: UICollectionView? = nil
    public var hairCutLengthCollectionView: UICollectionView? = nil
    public var hairCutColorCollectionView: UICollectionView? = nil
    
    private var maleHairCutTypeArr = [MaleHairType]()
    private var femaleHairCutTypeArr = [FeMaleHairType]()
    private var maleHairCutLengthArr = [MaleHairLength]()
    private var femaleHairCutLengthArr = [FeMaleHairLength]()
    private var hairCutColorArr = [HairColor]()
    
    private var gender: Gender = .Male
    private var hairCutTypeIndex: Int = -1
    private var hairCutLengthIndex: Int = -1
    private var hairCutColorIndex: Int = -1
    
    weak public var delegate: HairCutAreaViewDelegate? = nil
    
    init() {
        super.init(frame: .zero)
        self.initView()
        self.addHairTypeCollectionView()
        self.addHairLengthCollectionView()
        self.addHairColorCollectionView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.hairCutTypeLabel.text = "hairstyle".localized
        self.hairCutTypeLabel.adjustsFontSizeToFitWidth = true
        self.hairCutTypeLabel.textColor = UIColor(valueRGB: 0x333333)
        self.hairCutTypeLabel.font = UIFont.boldSystemFont(ofSize: 15)
        self.hairCutTypeLabel.textAlignment = .left
        self.addSubview(self.hairCutTypeLabel)
        self.hairCutTypeLabel.snp.makeConstraints { make in
            make.left.top.equalTo(10)
            make.width.greaterThanOrEqualTo(30)
            make.height.equalTo(21)
        }
        
        self.genderButton.layer.cornerRadius = 13
        self.genderButton.backgroundColor = UIColor.white
        self.genderButton.layer.borderColor = UIColor(valueRGB: 0xDBDBDB).cgColor
        self.genderButton.layer.borderWidth = 1
        self.genderButton.setTitle("men_hairstyles".localized, for: .normal)
        self.genderButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        self.genderButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 10)
        self.genderButton.setImage(UIImage(named: "aiharicut_exchange_sex"), for: .normal)
        self.genderButton.semanticContentAttribute = .forceRightToLeft
        self.genderButton.addTarget(self, action: #selector(genderButtonAction), for: .touchUpInside)
        self.addSubview(self.genderButton)
        self.genderButton.snp.makeConstraints { make in
            make.right.equalTo(-10)
            make.height.equalTo(26)
            if LanguageTool.currentLanguage() == .chineseSimplified || LanguageTool.currentLanguage() == .chineseTranditional {
                make.width.greaterThanOrEqualTo(76)
            } else {
                make.width.greaterThanOrEqualTo(150)
            }
            
            make.top.equalTo(8)
        }
    }
    
    public func getCustomPageIsEnable() -> Bool {
        let isNoChoose = self.hairCutTypeIndex == -1 || self.hairCutLengthIndex == -1 || self.hairCutColorIndex == -1
        return !isNoChoose
    }
    
    @objc func genderButtonAction() {
        self.hairCutTypeIndex = -1
        self.hairCutLengthIndex = -1
        self.hairCutColorIndex = -1
        if self.gender == .Male {
            self.gender = .Female
            self.genderButton.setTitle("women_hairstyles".localized, for: .normal)
        } else {
            self.gender = .Male
            self.genderButton.setTitle("men_hairstyles".localized, for: .normal)
        }
        self.hairCutTypeCollectionView?.reloadData()
        self.hairCutLengthCollectionView?.reloadData()
        self.hairCutColorCollectionView?.reloadData()
        
        self.delegate?.setCustomParametersEnable()
    }
}

typealias HairCutAreaViewHairCutType = HairCutAreaView
extension HairCutAreaViewHairCutType {
    func addHairTypeCollectionView() {
        self.maleHairCutTypeArr = [MaleHairType.buzzCut, MaleHairType.combOver, MaleHairType.spiked, MaleHairType.dreadlocks, MaleHairType.ringlets, MaleHairType.longCurly, MaleHairType.afr, MaleHairType.manBun, MaleHairType.Dreadlocks, MaleHairType.quiffWithSidePart, MaleHairType.pompadour]
        self.femaleHairCutTypeArr = [FeMaleHairType.wavy, FeMaleHairType.ringlets, FeMaleHairType.dreadlocks, FeMaleHairType.twintails, FeMaleHairType.ponytail, FeMaleHairType.himeCut, FeMaleHairType.doubleBun, FeMaleHairType.hairBun, FeMaleHairType.crownBraid, FeMaleHairType.bobcut, FeMaleHairType.halfUpdo, FeMaleHairType.frenchBraid, FeMaleHairType.straightHair]
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.itemSize = CGSize(width: 74, height: 34)
        layout.minimumLineSpacing = 4.5
        layout.minimumInteritemSpacing = 4.5
        self.hairCutTypeCollectionView = UICollectionView(frame: CGRect(x: 0, y: 16, width: self.frame.width, height: 326), collectionViewLayout: layout)
        self.hairCutTypeCollectionView?.showsVerticalScrollIndicator = false
        self.addSubview(self.hairCutTypeCollectionView ?? UICollectionView())
        self.hairCutTypeCollectionView?.isScrollEnabled = false
        self.hairCutTypeCollectionView?.snp.makeConstraints { make in
            make.top.equalTo(self.hairCutTypeLabel.snp.bottom).offset(12)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.height.equalTo(110)
        }
        self.hairCutTypeCollectionView?.collectionViewLayout = layout
        self.hairCutTypeCollectionView?.delegate = self
        self.hairCutTypeCollectionView?.dataSource = self
        self.hairCutTypeCollectionView?.backgroundColor = UIColor.white
        self.hairCutTypeCollectionView?.register(HairCutTextCell.self, forCellWithReuseIdentifier: HairCutTextCell.identifier)
        self.hairCutTypeCollectionView?.backgroundColor = UIColor.clear
        self.hairCutTypeCollectionView?.tag = 0
    }
    
    func buildHairTypeText(_ type: MaleHairType) -> String {
        switch type {
        case .buzzCut: return "buzzCut".localized
        case .combOver: return "combOver".localized
        case .spiked: return "spiked".localized
        case .dreadlocks: return "dreadlocks".localized
        case .ringlets: return "ringlets".localized
        case .longCurly: return "longCurly".localized
        case .afr: return "afr".localized
        case .manBun: return "manBun".localized
        case .Dreadlocks: return "Dreadlocks".localized
        case .quiffWithSidePart: return "quiffWithSidePart".localized
        case .pompadour: return "pompadour".localized
        }
    }
    
    func buildHairTypeText(_ type: FeMaleHairType) -> String {
        switch type {
        case .wavy: return "wavy".localized
        case .ringlets: return "ringlets".localized
        case .dreadlocks: return "dreadlocks".localized
        case .twinBraids: return "twinBraids".localized
        case .twintails: return "twintails".localized
        case .ponytail: return "ponytail".localized
        case .himeCut: return "himeCut".localized
        case .doubleBun: return "doubleBun".localized
        case .hairBun: return "hairBun".localized
        case .crownBraid: return "crownBraid".localized
        case .bobcut: return "bobcut".localized
        case .halfUpdo: return "halfUpdo".localized
        case .frenchBraid: return "frenchBraid".localized
        case .straightHair: return "straightHair".localized
        }
    }
}

typealias HairCutAreaViewHairCutLength = HairCutAreaView
extension HairCutAreaViewHairCutLength {
    func addHairLengthCollectionView() {
        self.maleHairCutLengthArr = [MaleHairLength.long, MaleHairLength.short]
        self.femaleHairCutLengthArr = [FeMaleHairLength.long, FeMaleHairLength.short]
        
        self.hairCutLengthLabel.text = "length".localized
        self.hairCutLengthLabel.adjustsFontSizeToFitWidth = true
        self.hairCutLengthLabel.textColor = UIColor(valueRGB: 0x333333)
        self.hairCutLengthLabel.font = UIFont.boldSystemFont(ofSize: 15)
        self.hairCutLengthLabel.textAlignment = .left
        self.addSubview(self.hairCutLengthLabel)
        self.hairCutLengthLabel.snp.makeConstraints { make in
            make.left.equalTo(10)
            make.top.equalTo(self.hairCutTypeCollectionView?.snp.bottom ?? 0).offset(14)
            make.width.greaterThanOrEqualTo(30)
            make.height.equalTo(21)
        }
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.itemSize = CGSize(width: 74, height: 34)
        layout.minimumLineSpacing = 1.5
        layout.minimumLineSpacing = 1.5
        self.hairCutLengthCollectionView = UICollectionView(frame: CGRect(x: 0, y: 16, width: self.frame.width, height: 326), collectionViewLayout: layout)
        self.hairCutLengthCollectionView?.showsVerticalScrollIndicator = false
        self.addSubview(self.hairCutLengthCollectionView ?? UICollectionView())
        self.hairCutLengthCollectionView?.snp.makeConstraints { make in
            make.top.equalTo(self.hairCutLengthLabel.snp.bottom).offset(12)
            make.left.equalTo(10)
            make.width.equalTo(160)
            make.height.equalTo(50)
        }
        self.hairCutLengthCollectionView?.collectionViewLayout = layout
        self.hairCutLengthCollectionView?.delegate = self
        self.hairCutLengthCollectionView?.dataSource = self
        self.hairCutLengthCollectionView?.backgroundColor = UIColor.white
        self.hairCutLengthCollectionView?.register(HairCutTextCell.self, forCellWithReuseIdentifier: HairCutTextCell.identifier)
        self.hairCutLengthCollectionView?.backgroundColor = UIColor.clear
        self.hairCutLengthCollectionView?.tag = 1
    }
    
    func buildHairLengthText(_ type: MaleHairLength) -> String {
        switch type {
        case .long: return "long_hair".localized
        case .short: return "short_hair".localized
        }
    }
    
    func buildHairLengthText(_ type: FeMaleHairLength) -> String {
        switch type {
        case .long: return "long_hair".localized
        case .short: return "short_hair".localized
        }
    }
}

typealias HairCutAreaViewCollectionColor = HairCutAreaView
extension HairCutAreaViewCollectionColor {
    func addHairColorCollectionView() {
        self.hairCutColorArr = [HairColor.black, HairColor.brown, HairColor.blue, HairColor.green, HairColor.purple, HairColor.red, HairColor.white, HairColor.silver, HairColor.grey, HairColor.blonde]
        
        self.hairCutColorLabel.text = "hair_color".localized
        self.hairCutColorLabel.textColor = UIColor(valueRGB: 0x333333)
        self.hairCutColorLabel.font = UIFont.boldSystemFont(ofSize: 15)
        self.hairCutColorLabel.textAlignment = .left
        self.addSubview(self.hairCutColorLabel)
        self.hairCutColorLabel.snp.makeConstraints { make in
            make.left.equalTo(10)
            make.top.equalTo(self.hairCutLengthCollectionView?.snp.bottom ?? 0).offset(14)
            make.width.greaterThanOrEqualTo(30)
            make.height.equalTo(21)
        }
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.itemSize = CGSize(width: 34, height: 34)
        layout.minimumLineSpacing = 7
        layout.minimumInteritemSpacing = 5
        self.hairCutColorCollectionView = UICollectionView(frame: CGRect(x: 0, y: 16, width: self.frame.width, height: 326), collectionViewLayout: layout)
        self.hairCutColorCollectionView!.showsVerticalScrollIndicator = false
        self.addSubview(self.hairCutColorCollectionView ?? UICollectionView())
        self.hairCutColorCollectionView?.snp.makeConstraints { make in
            make.top.equalTo(self.hairCutColorLabel.snp.bottom).offset(12)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.height.equalTo(200)
        }
        self.hairCutColorCollectionView?.collectionViewLayout = layout
        self.hairCutColorCollectionView?.delegate = self
        self.hairCutColorCollectionView?.dataSource = self
        self.hairCutColorCollectionView?.backgroundColor = UIColor.white
        self.hairCutColorCollectionView?.register(HairCutImageCell.self, forCellWithReuseIdentifier: HairCutImageCell.identifier)
        self.hairCutColorCollectionView?.backgroundColor = UIColor.clear
        self.hairCutColorCollectionView?.tag = 2
    }
    
    func buildHairColorImageText(_ type: HairColor) -> String {
        switch type {
        case .black: return "faceshape_color_black"
        case .brown: return "faceshape_color_brown"
        case .blue: return "faceshape_color_blue"
        case .green: return "faceshape_color_green"
        case .purple: return "faceshape_color_purple"
        case .red: return "faceshape_color_red"
        case .white: return "faceshape_color_white"
        case .silver: return "faceshape_color_silver"
        case .grey: return "faceshape_color_grey"
        case .blonde: return "faceshape_color_blonde"
        }
    }
}

/// tag0:发型选择
/// tag1:长度
/// tag2:发色

typealias HairCutAreaViewCollectionDelegate = HairCutAreaView
extension HairCutAreaViewCollectionDelegate: UICollectionViewDelegate, UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        let tag = collectionView.tag
        if tag == 2 {
            return hairCutColorArr.count
        }
        
        if gender == .Male {
            if tag == 0 {
                return maleHairCutTypeArr.count
            } else {
                return maleHairCutLengthArr.count
            }
        }

        if tag == 0 {
            return femaleHairCutTypeArr.count
        } else {
            return femaleHairCutLengthArr.count
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let tag = collectionView.tag
        
        if tag == 2 {
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HairCutImageCell.identifier, for: indexPath) as? HairCutImageCell else {
                return UICollectionViewCell()
            }
            cell.imageUrlString = self.buildHairColorImageText(hairCutColorArr[indexPath.row])
            cell.isSelectedItem = self.hairCutColorIndex == indexPath.row
            return cell
        }
        
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HairCutTextCell.identifier, for: indexPath) as? HairCutTextCell else {
            return UICollectionViewCell()
        }
        
        if gender == .Male {
            if tag == 0 {
                cell.titleString = self.buildHairTypeText(maleHairCutTypeArr[indexPath.row])
                cell.isSelectedItem = indexPath.row == self.hairCutTypeIndex
            }  else {
                cell.titleString = self.buildHairLengthText(maleHairCutLengthArr[indexPath.row])
                cell.isSelectedItem = indexPath.row == self.hairCutLengthIndex
            }
        } else {
            if tag == 0 {
                cell.titleString = self.buildHairTypeText(femaleHairCutTypeArr[indexPath.row])
                cell.isSelectedItem = indexPath.row == self.hairCutTypeIndex
            } else {
                cell.titleString = self.buildHairLengthText(femaleHairCutLengthArr[indexPath.row])
                cell.isSelectedItem = indexPath.row == self.hairCutLengthIndex
            }
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let tag = collectionView.tag
        let index = indexPath.row
        self.handleHairCutChooseItemClickAction(tag: tag, index: index)
    }
    
    private func handleHairCutChooseItemClickAction(tag: Int, index: Int) {
        if tag == 0 {
            if self.hairCutTypeIndex == index {
                return
            }
            var arr = [IndexPath]()
            if self.hairCutTypeIndex != -1 {
                arr.append(IndexPath(row: self.hairCutTypeIndex, section: 0))
            }
            self.hairCutTypeIndex = index
            arr.append(IndexPath(row: index, section: 0))
            //一次刷新两个item，不能单个刷2遍，因为是异步操作
            self.hairCutTypeCollectionView?.reloadItems(at: arr)
        }  else if tag == 1 {
            if self.hairCutLengthIndex == index {
                return
            }
            var arr = [IndexPath]()
            if self.hairCutLengthIndex != -1 {
                arr.append(IndexPath(row: self.hairCutLengthIndex, section: 0))
            }
            self.hairCutLengthIndex = index
            arr.append(IndexPath(row: index, section: 0))
            //一次刷新两个item，不能单个刷2遍，因为是异步操作
            self.hairCutLengthCollectionView?.reloadItems(at: arr)
        } else if tag == 2 {
            if self.hairCutColorIndex == index {
                return
            }
            var arr = [IndexPath]()
            if self.hairCutColorIndex != -1 {
                arr.append(IndexPath(row: self.hairCutColorIndex, section: 0))
            }
            self.hairCutColorIndex = index
            arr.append(IndexPath(row: index, section: 0))
            //一次刷新两个item，不能单个刷2遍，因为是异步操作
            self.hairCutColorCollectionView?.reloadItems(at: arr)
        }
        self.delegate?.setCustomParametersEnable()
    }
    
    public func getAIHairCutData() -> AIHairCutData? {
        if self.hairCutTypeIndex == -1 || self.hairCutLengthIndex == -1 || self.hairCutColorIndex == -1 {
            return nil
        }
        
        let data = AIHairCutData()
        let hairCutType = self.gender == .Male ? self.maleHairCutTypeArr[self.hairCutTypeIndex].rawValue : self.femaleHairCutTypeArr[self.hairCutTypeIndex].rawValue
        let hairCutLength = self.gender == .Male ? self.maleHairCutLengthArr[self.hairCutLengthIndex].rawValue : self.femaleHairCutLengthArr[self.hairCutLengthIndex].rawValue
        let hairCutColor = self.hairCutColorArr[self.hairCutColorIndex].rawValue
        data.prompt = "\(hairCutType),\(hairCutLength),\(hairCutColor)"
        return data
    }
}
