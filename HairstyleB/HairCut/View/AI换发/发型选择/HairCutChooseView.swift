//
//  HairCutChooseView.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/2.
//

import Foundation
import UIKit
import SnapKit

protocol HairCutChooseViewDelegate: NSObjectProtocol {
    func customParameterTapAction()
    func recommendedTemplateAction()
}

class HairCutChooseView: UIView {
    public let customParameterItem = UIView()
    private let customParameterLabel = UILabel()
    private let customParameterView = UIView()
    
    public let recommendedTemplateItem = UIView()
    private let recommendedTemplateLabel = UILabel()
    private let recommendedTemplateView = UIView()
    private let line = UIView()
    
    public var isShowPopularPage: Bool {
        willSet {
            // true: 选中发型模版标签，显示发型模版内容
            // false: 选中造型模版标签，显示造型模版内容
            
            // 发型模版UI状态 (customParameter对应发型模版)
            self.customParameterView.isHidden = !newValue // newValue为true时显示发型模版状态条
            self.customParameterLabel.textColor = newValue ? UIColor(valueRGB: 0x333333) : UIColor(valueRGB: 0x999999)
            
            // 造型模版UI状态 (recommendedTemplate对应造型模版)
            self.recommendedTemplateView.isHidden = newValue // newValue为true时隐藏造型模版状态条
            self.recommendedTemplateLabel.textColor = newValue ? UIColor(valueRGB: 0x999999) : UIColor(valueRGB: 0x333333)
        }
    }
    
    weak public var delegate: HairCutChooseViewDelegate?
    
    init() {
        self.isShowPopularPage = true  // 默认选择发型模版
        super.init(frame: .zero)
        
        self.customParameterItem.isUserInteractionEnabled = true
        self.addSubview(self.customParameterItem)
        self.customParameterItem.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.width.greaterThanOrEqualTo(75)
            make.height.equalTo(21)
            make.top.equalTo(0)
        }
        let customParameterTap = UITapGestureRecognizer()
        customParameterTap.addTarget(self, action: #selector(customParameterTapAction))
        self.customParameterItem.addGestureRecognizer(customParameterTap)
        
        self.customParameterView.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        self.customParameterItem.addSubview(self.customParameterView)
        self.customParameterView.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.width.equalTo(self.customParameterItem.snp.width)
            make.height.equalTo(10.5)
            make.top.equalTo(10.5)
        }
        
        self.customParameterLabel.textAlignment = .left
        self.customParameterLabel.text = "发型模板".localized
        self.customParameterLabel.adjustsFontSizeToFitWidth = true
        self.customParameterLabel.textColor = UIColor(valueRGB: 0x333333)
        self.customParameterLabel.font = UIFont.systemFont(ofSize: 15)
        self.customParameterItem.addSubview(self.customParameterLabel)
        self.customParameterLabel.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
            make.width.equalTo(self.customParameterItem.snp.width)
        }
        
        self.recommendedTemplateItem.isUserInteractionEnabled = true
        self.addSubview(self.recommendedTemplateItem)
        self.recommendedTemplateItem.snp.makeConstraints { make in
            make.left.equalTo(self.customParameterLabel.snp.right).offset(32)
            make.width.greaterThanOrEqualTo(60)
            make.height.equalTo(21)
            make.top.equalTo(self.customParameterLabel)
        }
        let recommendedTemplateTap = UITapGestureRecognizer()
        recommendedTemplateTap.addTarget(self, action: #selector(recommendedTemplateAction))
        self.recommendedTemplateItem.addGestureRecognizer(recommendedTemplateTap)
        
        self.recommendedTemplateView.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        self.recommendedTemplateItem.addSubview(self.recommendedTemplateView)
        self.recommendedTemplateView.snp.makeConstraints { make in
            make.left.equalTo(self.customParameterLabel.snp.right).offset(32)
            make.width.equalTo(self.recommendedTemplateItem.snp.width)
            make.height.equalTo(10.5)
            make.top.equalTo(10.5)
        }
        self.recommendedTemplateView.isHidden = true
        
        self.recommendedTemplateLabel.textAlignment = .left
        self.recommendedTemplateLabel.text = "造型模板".localized
        self.recommendedTemplateLabel.adjustsFontSizeToFitWidth = true
        self.recommendedTemplateLabel.textColor = UIColor(valueRGB: 0x999999)
        self.recommendedTemplateLabel.font = UIFont.systemFont(ofSize: 15)
        self.recommendedTemplateItem.addSubview(self.recommendedTemplateLabel)
        self.recommendedTemplateLabel.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
            make.width.equalTo(self.recommendedTemplateItem.snp.width)
        }
        
        self.line.backgroundColor = UIColor(valueRGB: 0xB3B3B3)
        self.addSubview(self.line)
        self.line.snp.makeConstraints { make in
            make.left.equalTo(self.customParameterLabel.snp.right).offset(16)
            make.width.equalTo(1)
            make.height.equalTo(21)
            make.top.equalTo(self.customParameterLabel)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func customParameterTapAction() {
        if self.isShowPopularPage != true {
            self.isShowPopularPage = true
            if self.delegate != nil && (self.delegate?.responds(to: #selector(self.customParameterTapAction)) != nil) {
                self.delegate?.customParameterTapAction()
            }
        }  else {
            printLog(message: "\(self.isShowPopularPage)")
        }
    }
    
    @objc func recommendedTemplateAction () {
        if self.isShowPopularPage != false {
            self.isShowPopularPage = false
            if self.delegate != nil && (self.delegate?.responds(to: #selector(self.recommendedTemplateAction)) != nil) {
                self.delegate?.recommendedTemplateAction()
            }
        } else {
            printLog(message: "\(self.isShowPopularPage)")
        }
    }
}
