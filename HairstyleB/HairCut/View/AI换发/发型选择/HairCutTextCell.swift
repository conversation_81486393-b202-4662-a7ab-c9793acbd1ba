//
//  HairCutTextCell.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/8/1.
//

import Foundation
import UIKit
import SnapKit

class HairCutTextCell: UICollectionViewCell {
    static let identifier = "HairCutTextCell"
    
    private var title = UILabel()
    public var titleString: String? {
        willSet {
            self.title.text = newValue ?? ""
            self.titleString = newValue
        }
    }
    
    public var isSelectedItem: Bool {
        willSet {
            self.title.backgroundColor = newValue == true ?  UIColor.init(valueRGB: 0xFFEC53) : UIColor.white
        }
    }
    
    override init(frame: CGRect) {
        self.isSelectedItem = false
        super.init(frame: frame)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.contentView.layer.cornerRadius = 10
        self.contentView.layer.masksToBounds = true
        self.title.backgroundColor = UIColor.white
        self.title.textColor = UIColor(valueRGB: 0x333333)
        self.title.font = UIFont.systemFont(ofSize: 12)
        self.title.adjustsFontSizeToFitWidth = true
        self.title.textAlignment = .center
        self.contentView.addSubview(self.title)
        self.title.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.width.equalTo(74)
            make.height.greaterThanOrEqualTo(34)
        }
    }
}
