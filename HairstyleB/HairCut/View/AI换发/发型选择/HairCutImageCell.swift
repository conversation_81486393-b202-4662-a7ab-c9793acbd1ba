//
//  HairCutImageCell.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/1.
//

import Foundation
import UIKit
import SnapKit

class HairCutImageCell: UICollectionViewCell {
    static let identifier = "HairCutImageCell"
    
    private var imageView = UIImageView()
    private var selectedImageView = UIImageView()
    public var imageUrlString: String? {
        willSet {
            if newValue != nil && newValue?.count ?? 0 > 0 {
                self.imageView.image = UIImage(named: newValue ?? "")
            }
        }
    }
    
    public var isSelectedItem: Bool {
        willSet {
            self.selectedImageView.isHidden = !newValue
            self.imageView.alpha = newValue == true ? 0.9 : 1
        }
    }
    
    override init(frame: CGRect) {
        self.isSelectedItem = false
        super.init(frame: frame)
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initView() {
        self.imageView.layer.cornerRadius = 17
        self.imageView.contentMode = .scaleAspectFit
        self.addSubview(self.imageView)
        self.imageView.snp.makeConstraints { make in
            make.width.height.equalTo(34)
            make.left.right.top.equalToSuperview()
        }
        
        self.selectedImageView.contentMode = .scaleAspectFit
        self.selectedImageView.image = UIImage(named: "faceshape_color_selected")
        self.addSubview(self.selectedImageView)
        self.selectedImageView.snp.makeConstraints { make in
            make.width.height.equalTo(24)
            make.center.equalToSuperview()
        }
        self.selectedImageView.isHidden = true
    }
}

