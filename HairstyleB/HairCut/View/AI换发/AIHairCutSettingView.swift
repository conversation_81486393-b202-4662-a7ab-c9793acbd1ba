//
//  AIHairCutSettingView.swift
//  HairCut
//
//  Created by fs0011 on 2025/3/19.
//

import Foundation
import UIKit
class HairCutConfig: NSObject {
    // MARK: - 共享实例
    static let shared = HairCutConfig()
    private override init() {}
    
    // MARK: - 可观测属性
    @objc dynamic var denoise: Double = 1.0 {
        didSet { saveToLocal() }
    }
    
    @objc dynamic var expand: Int = 30 {
        didSet { saveToLocal() }
    }
    
    @objc dynamic var dilation: Int = 0 {
        didSet { saveToLocal() }
    }
    
    // MARK: - 本地存储
    private let userDefaultsKey = "HairCutConfigData"
    private let ioQueue = DispatchQueue(label: "config.io.queue",  qos: .utility)
    
    // 自动保存机制
    private func saveToLocal() {
        ioQueue.async  { [weak self] in
            guard let config = self else { return }
            UserDefaults.standard.set([
                "denoise": config.denoise,
                "expand": config.expand,
                "dilation": config.dilation
            ], forKey: config.userDefaultsKey)
        }
    }
    
    // 初始化加载
    func loadFromLocal() {
        ioQueue.async  { [weak self] in
            guard let data = UserDefaults.standard.dictionary(forKey:  self?.userDefaultsKey ?? "") as? [String: Double] else { return }
            
            
                self?.denoise = data["denoise"] ?? 1
                self?.expand = Int(data["expand"] ?? 30)
                self?.dilation = Int(data["dilation"] ?? 0)
            
        }
    }
}
class AIHairCutSettingView: UIView {
    
    private var config = HairCutConfig.shared
        private var observers = [NSKeyValueObservation]()
    var dilationSlider:UISlider!
    var expandSlider:UISlider!
    var dilationLabel:UILabel!
    var expandLabel:UILabel!
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        config.loadFromLocal()
        
        setupObservers()
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
            observers.forEach  { $0.invalidate()  }
        }
    // MARK: - 观察者配置
        private func setupObservers() {
            let options: NSKeyValueObservingOptions = [.new, .initial]
            
            // 降噪参数绑定
            observers.append(config.observe(\.denoise,  options: options) { [weak self] _, change in
                guard let value = change.newValue  else { return }
                
            })
            
            // 扩展参数绑定
            observers.append(config.observe(\.expand,  options: options) { [weak self] _, change in
                guard let value = change.newValue  else { return }
               
            })
        }
    
    
    func initView(){
        self.snp .makeConstraints { make in
            make.width.equalTo(SCREEN_WIDTH)
            make.height.equalTo(16+206+66+bottomHeight)
        }
        
        let view = UIView()
        view.backgroundColor = .white;
        self.addSubview(view)
        view.snp.makeConstraints { make in
            make.top.equalTo(16);
            make.left.equalTo(16);
            make.right.equalTo(-16);
        }
        
        let qiangduLabel = UILabel.createLabel(title: "绘画强度".localized, textColor: darkTextColor, textAlignment: .left, font: smallFont);
        view.addSubview(qiangduLabel);
        qiangduLabel.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(16)
        }
        var arr = Array<UIButton>()
        for (index,type) in ["低".localized,"中".localized,"高".localized].enumerated()
        {
            let btn = UIButton.createButton(title: type, color: darkTextColor, font: smallMedlumFont)
            view.addSubview(btn);
            btn.snp.makeConstraints { make in
                make.left.equalTo(16+(24+52)*index)
                make.width.equalTo(52)
                make.height.equalTo(26)
                make.top.equalTo(qiangduLabel.snp.bottom).offset(10)
            }
            btn.layer.cornerRadius = 13
            btn.layer.masksToBounds = true
            btn.setBackgroundColor(UIColor.colorWithHexString("#F9F9FC", alpha: 1), for: .normal)
            btn.setBackgroundColor(themColor, for: .selected)
            if  self.config.denoise == 0.75 && index == 0
            {
                btn.isSelected = true
            }
            if  self.config.denoise == 0.8 && index == 1
            {
                btn.isSelected = true
            }
            if  self.config.denoise == 1 && index == 2
            {
                btn.isSelected = true
            }
            arr.append(btn)
            btn.addTapAction {
                for allbtn in arr {
                    allbtn.isSelected = false
                }
                btn.isSelected = true
                switch index {
                case 0:
                    self.config.denoise = 0.75
                    MobClick.event("Intelligent_hair_replacement", attributes: ["settings": "低"])
                case 1:
                    self.config.denoise = 0.8
                    MobClick.event("Intelligent_hair_replacement", attributes: ["settings": "中"])
                case 2:
                    self.config.denoise = 1
                    MobClick.event("Intelligent_hair_replacement", attributes: ["settings": "高"])
                default: break
                }
            }
            
        }
        
        let mengban = UILabel.createLabel(title: "脸部蒙版大小".localized, textColor: darkTextColor, textAlignment: .left, font: smallFont)
        view.addSubview(mengban)
        mengban.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.top.equalTo(qiangduLabel.snp.bottom).offset(46)
        }
        
        
        dilationSlider = UISlider();
        view.addSubview(dilationSlider)
        dilationSlider.minimumValue = 0;
        dilationSlider.maximumValue = 50.0;
        dilationSlider.value = Float(self.config.dilation)
        let trackHeight: CGFloat = 4
        let cornerRadius = trackHeight / 2 // 圆角半径等于高度的一半

        // 绘制最小轨道图片
        let trackMiniImage = UIGraphicsImageRenderer(size: CGSize(width: 10, height: trackHeight)).image { context in
            let rect = CGRect(x: 0, y: 0, width: 10, height: trackHeight)
            let path = UIBezierPath(roundedRect: rect, cornerRadius: cornerRadius)
            themColor.setFill()
            path.fill()
        }

        // 绘制最大轨道图片
        let trackMaxImage = UIGraphicsImageRenderer(size: CGSize(width: 10, height: trackHeight)).image { context in
            let rect = CGRect(x: 0, y: 0, width: 10, height: trackHeight)
            let path = UIBezierPath(roundedRect: rect, cornerRadius: cornerRadius)
            unseletColor.setFill()
            path.fill()
        }

        // 应用到 UISlider
        dilationSlider.setMinimumTrackImage(trackMiniImage.resizableImage(withCapInsets: UIEdgeInsets(top: 0, left: 5, bottom: 0, right: 5)), for: .normal)
        dilationSlider.setMaximumTrackImage(trackMaxImage.resizableImage(withCapInsets: UIEdgeInsets(top: 0, left: 5, bottom: 0, right: 5)), for: .normal)

        
//        scaleSlider.transform = CGAffineTransformMakeScale(1.0, 6*scaleX);
//        scaleSlider.minimumTrackTintColor = UIColor.colorWithHexString("#FFD7FF" ,alpha:1);
//        scaleSlider.maximumTrackTintColor = UIColor.colorWithHexString("#F9F9F9" ,alpha:1);
        // 设置 UISlider 的选中状态背景颜色
        dilationSlider.tintColor = seletedColor;
        
        // 设置 UISlider 的 thumbImage
        dilationSlider.setThumbImage(UIImage(named: "thumbImage"), for: .normal)
        dilationSlider.layer.cornerRadius = 2;
        dilationSlider.snp.makeConstraints { make in
            make.left.equalTo(16*scaleX)
            make.right.equalTo(-87*scaleX)
            make.top.equalTo(mengban.snp.bottom).offset(10)
            make.height.equalTo(21*scaleX)
        }
        dilationSlider.addTarget(self, action:#selector(sliderValueChanged(_:)) , for: .valueChanged)
        
        dilationLabel = UILabel.createLabel(title: "\(self.config.dilation)", textColor: darkTextColor, textAlignment: .center, font: smallMedlumFont)
        view.addSubview(dilationLabel)
        dilationLabel.backgroundColor = UIColor.colorWithHexString("#FFF8C5", alpha: 1)
        dilationLabel.layer.cornerRadius = 13
        dilationLabel.layer.masksToBounds = true
        dilationLabel.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.width.equalTo(52)
            make.height.equalTo(26)
            make.centerY.equalTo(dilationSlider)
        }
        
        let quyu = UILabel.createLabel(title: "发型&造型区域大小".localized, textColor: darkTextColor, textAlignment: .left, font: smallFont)
        view.addSubview(quyu)
        quyu.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.top.equalTo(dilationSlider.snp.bottom).offset(16)
        }
        
        
        expandSlider = UISlider()
        view.addSubview(expandSlider)
        expandSlider.minimumValue = 0;
        expandSlider.maximumValue = 50;
        expandSlider.value = Float(self.config.expand)

        

        // 应用到 UISlider
        expandSlider.setMinimumTrackImage(trackMiniImage.resizableImage(withCapInsets: UIEdgeInsets(top: 0, left: 5, bottom: 0, right: 5)), for: .normal)
        expandSlider.setMaximumTrackImage(trackMaxImage.resizableImage(withCapInsets: UIEdgeInsets(top: 0, left: 5, bottom: 0, right: 5)), for: .normal)

        
//        scaleSlider.transform = CGAffineTransformMakeScale(1.0, 6*scaleX);
//        scaleSlider.minimumTrackTintColor = UIColor.colorWithHexString("#FFD7FF" ,alpha:1);
//        scaleSlider.maximumTrackTintColor = UIColor.colorWithHexString("#F9F9F9" ,alpha:1);
        // 设置 UISlider 的选中状态背景颜色
        expandSlider.tintColor = seletedColor;
        
        // 设置 UISlider 的 thumbImage
        expandSlider.setThumbImage(UIImage(named: "thumbImage"), for: .normal)
        expandSlider.layer.cornerRadius = 2;
        expandSlider.snp.makeConstraints { make in
            make.left.equalTo(16*scaleX)
            make.right.equalTo(-87*scaleX)
            make.top.equalTo(quyu.snp.bottom).offset(10)
            make.height.equalTo(21*scaleX)
        }
        expandSlider.addTarget(self, action:#selector(sliderValueChanged(_:)) , for: .valueChanged)
        
        expandLabel = UILabel.createLabel(title: "\(self.config.expand)", textColor: darkTextColor, textAlignment: .center, font: smallMedlumFont)
        expandLabel.backgroundColor = UIColor.colorWithHexString("#FFF8C5", alpha: 1)
        expandLabel.layer.cornerRadius = 13
        expandLabel.layer.masksToBounds = true
        view.addSubview(expandLabel)
        expandLabel.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.width.equalTo(52)
            make.height.equalTo(26)
            make.centerY.equalTo(expandSlider)
            make.bottom.equalTo(-16)
        }
        
        self.layoutIfNeeded()
        
        
        
        
     
        
        // shadowCode
        view.layer.shadowColor = UIColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 0.25).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: -1)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 10
        view.layer.cornerRadius = 20
        
        
        
    }
    
    @objc private func sliderValueChanged(_ sender: UISlider) {
        print("Slider value: \(sender.value)")
        if sender == dilationSlider
        {
            self.config.dilation = Int(CGFloat(sender.value))
            dilationLabel.text = "\(self.config.dilation)"
            // 添加脸部蒙版大小埋点
            MobClick.event("Intelligent_hair_replacement", attributes: ["settings": "脸部蒙版大小拖动滑块"])
        }
        else
        {
            self.config.expand = Int(CGFloat(sender.value))
            expandLabel.text = "\(self.config.expand)"
            // 添加发型&造型区域大小埋点
            MobClick.event("Intelligent_hair_replacement", attributes: ["settings": "发型&造型区域大小拖动滑块"])
        }
    }
}
