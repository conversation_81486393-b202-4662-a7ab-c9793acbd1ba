import UIKit

class BrushPreviewView: UIView {
    var circleRadius: CGFloat = 20 {
        didSet { setNeedsDisplay() }
    }
    var circleColor: UIColor = UIColor.red {
        didSet { setNeedsDisplay() }
    }
    var isEraser: Bool = false {
        didSet { setNeedsDisplay() }
    }
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .clear
        isUserInteractionEnabled = false
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    override func draw(_ rect: CGRect) {
        guard circleRadius > 0 else { return }
        let center = CGPoint(x: bounds.midX, y: bounds.midY)
        let path = UIBezierPath(arcCenter: center, radius: circleRadius/2, startAngle: 0, endAngle: CGFloat.pi*2, clockwise: true)
        if isEraser {
            // 橡皮擦：灰色边框，透明填充
            UIColor.white.withAlphaComponent(0.7).setFill()
            path.fill(with: .clear, alpha: 0)
            UIColor.gray.setStroke()
            path.lineWidth = 2
            path.stroke()
        } else {
            // 画笔：themeColor边框，透明填充
            UIColor.clear.setFill()
            path.fill()
            circleColor.setStroke()
            path.lineWidth = 2
            path.stroke()
        }
    }
} 