//
//  AIHairCutUploadButtonView.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/8/2.
//

import Foundation
import UIKit
import SnapKit

class AIHairCutUploadButtonView: UIView {
    private let inputImageView = UIImageView()
    private let imageView = UIImageView()
    private let titleLabel = UILabel()
    
    public var image: UIImage? {
        willSet {
            self.inputImageView.isHidden = newValue != nil ? false : true
            self.imageView.isHidden = newValue != nil ? true : false
            self.titleLabel.isHidden = newValue != nil ? true : false
            self.backgroundColor = newValue != nil ? UIColor(valueRGB: 0xF9F9F9) : UIColor(valueRGB: 0xFFF8C5)
            if newValue != nil {
                self.inputImageView.image = newValue
            } else {
                self.inputImageView.backgroundColor = UIColor.white
            }
        }
    }
    
    init() {
        super.init(frame: .zero)
        self.backgroundColor = UIColor(valueRGB: 0xFFF8C5)
        self.titleLabel.text = "upload_image".localized
        self.titleLabel.adjustsFontSizeToFitWidth = true
        self.titleLabel.textColor = UIColor(valueRGB: 0x333333)
        self.titleLabel.font = UIFont.boldSystemFont(ofSize: 16)
        self.titleLabel.textAlignment = .center
        self.addSubview(self.titleLabel)
        self.titleLabel.snp.makeConstraints { make in
            make.top.equalTo(19)
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview().offset(16)
            make.width.greaterThanOrEqualTo(64)
            make.height.equalTo(22)
        }
        
        self.imageView.image = UIImage(named: "aihaircut_add")
        self.imageView.contentMode = .scaleAspectFit
        self.addSubview(self.imageView)
        self.imageView.snp.makeConstraints { make in
            make.width.height.equalTo(24)
            make.right.equalTo(self.titleLabel.snp.left).offset(-8)
            make.centerY.equalTo(self.titleLabel)
        }
        
        self.inputImageView.contentMode = .scaleAspectFit
        self.addSubview(self.self.inputImageView)
        let width = UIScreen.main.bounds.width * 155 / 375
        let height = UIScreen.main.bounds.height * 187 / 812
        self.inputImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.width.equalTo(width)
            make.centerX.equalToSuperview()
            make.height.equalTo(height)
        }
        self.inputImageView.isHidden = true
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
