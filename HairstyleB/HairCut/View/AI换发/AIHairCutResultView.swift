//
//  AIHairCutResultView.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/2.
//

import Foundation
import UIKit
import SnapKit

protocol AIHairCutResultViewDelegate: NSObjectProtocol {
    func oneMorePictureAction()
    func rebuildImageAction()
}

class AIHairCutResultView: UIView {
    public var resultImageView : PTMImageSliderView!
    private let oneMoreButton = UIButton()
    private let reBuildButton = UIButton()
    var aiModel : AIHairModel!
    public var imageData: Data? {
        willSet {
            guard let resultImageView = self.resultImageView,  let data = newValue,let image = UIImage(data: data) else {
                return
            }
            self.resultImageView.afterImage = image
        }
    }
    
    weak public var delegate: AIHairCutResultViewDelegate? = nil
    
    override init(frame: CGRect) {
        super.init(frame: CGRect())
        self.initView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setup(){
        guard let data = self.imageData,let image = UIImage(data: data) else {
            return
        }
        
        let resultImageHeight = UIScreen.main.bounds.height * 456 / 812
        self.resultImageView = PTMImageSliderView(frame: CGRect(x: 0, y: 46, width: SCREEN_WIDTH, height: resultImageHeight), before:self.aiModel.origin_image , after: image )
        self.resultImageView.beforeImage = self.aiModel.origin_image
        self.resultImageView.afterImage = image
        self.resultImageView.backgroundColor = UIColor.white
        self.isUserInteractionEnabled = true
        self.addSubview(self.resultImageView)
    }
    
    
    private func initView() {
        self.backgroundColor = UIColor(valueRGB: 0xF9F9F9)
        
        
        
//        self.resultImageView.snp.makeConstraints { make in
//            make.top.equalTo(46)
//            make.left.right.equalToSuperview()
//            make.height.equalTo(resultImageHeight)
//        }
        
        
        
        
        
        self.oneMoreButton.backgroundColor = UIColor(valueRGB: 0xFFEC53)
        self.oneMoreButton.layer.cornerRadius = 25
        self.oneMoreButton.setTitle("one_more".localized, for: .normal)
        self.oneMoreButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
        self.oneMoreButton.titleLabel?.textAlignment = .center
        self.oneMoreButton.addTarget(self, action: #selector(rebuildImageAction), for: .touchUpInside)
        self.addSubview(self.oneMoreButton)
        self.oneMoreButton.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.right.equalTo(-16)
            make.height.equalTo(50)
            make.bottom.equalTo(-38)
        }
        
//        self.reBuildButton.backgroundColor = UIColor(valueRGB: 0xF9F9F9)
//        self.reBuildButton.layer.cornerRadius = 25
//        self.reBuildButton.layer.borderWidth = 1
//        self.reBuildButton.layer.borderColor = UIColor(valueRGB: 0xDBDBDB).cgColor
//        self.reBuildButton.setTitle("try_again".localized, for: .normal)
//        self.reBuildButton.setTitleColor(UIColor(valueRGB: 0x333333), for: .normal)
//        self.reBuildButton.titleLabel?.textAlignment = .center
//        self.reBuildButton.addTarget(self, action:#selector(rebuildImageAction), for: .touchUpInside)
//        self.addSubview(self.reBuildButton)
//        self.reBuildButton.snp.makeConstraints { make in
//            make.right.equalTo(-16)
//            make.width.equalTo(163)
//            make.height.equalTo(50)
//            make.bottom.equalTo(-38)
//        }
    }
    
    @objc func oneMorePictureAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.oneMorePictureAction)) != nil) {
            self.delegate?.oneMorePictureAction()
        }
    }
    
    @objc func rebuildImageAction() {
        if self.delegate != nil && (self.delegate?.responds(to: #selector(self.rebuildImageAction)) != nil) {
            self.delegate?.rebuildImageAction()
        }
    }
}
