import UIKit

class DrawView: UIView {
    enum ToolType { case brush, eraser }
    
    // MARK: - 公开属性
    var toolType: ToolType = .brush
    var lineWidth: CGFloat = 20
    var themeColor: UIColor = UIColor(red: 1, green: 0.51, blue: 0.6, alpha: 0.5) // #FF8399, alpha 0.5
    
    // MARK: - 私有属性
    private var path = UIBezierPath()
    private var lastPoint: CGPoint?
    private var drawingImage: UIImage?
    private var isDrawing = false
    private var imageRect: CGRect = .zero
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .clear
        isUserInteractionEnabled = true
        isMultipleTouchEnabled = false
    }
    
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    
    // 设置图像有效区域
    func setImageRect(_ rect: CGRect) {
        imageRect = rect
    }
    
    // MARK: - 触摸事件
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard touches.count == 1, let point = touches.first?.location(in: self) else { isDrawing = false; return }
        
        isDrawing = true
        path = UIBezierPath()
        path.lineWidth = lineWidth
        path.lineCapStyle = .round
        path.lineJoinStyle = .round
        path.move(to: point)
        lastPoint = point
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawing, touches.count == 1, let point = touches.first?.location(in: self) else { return }
        
        // 如果有上一个点，绘制线段
        if let last = lastPoint, hypot(point.x - last.x, point.y - last.y) > 0.5 {
            path.addLine(to: point)
            drawLine(from: last, to: point)
            lastPoint = point
            
            // 添加拖动埋点
            if toolType == .brush {
                MobClick.event("Intelligent_hair_replacement", attributes: ["select_the_area": "画笔拖动"])
            } else {
                MobClick.event("Intelligent_hair_replacement", attributes: ["select_the_area": "橡皮擦拖动"])
            }
        }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if isDrawing { 
            if let last = lastPoint, let point = touches.first?.location(in: self) {
                // 只在结束点与上一点有明显距离时才绘制最后一段
                if hypot(point.x - last.x, point.y - last.y) > 0.5 {
                    path.addLine(to: point)
                    drawLine(from: last, to: point)
                }
            }
        }
        path.removeAllPoints()
        lastPoint = nil
        isDrawing = false
    }
    
    // MARK: - 绘制逻辑
    private func drawLine(from fromPoint: CGPoint, to toPoint: CGPoint) {
        // 简化版的绘制方法
        UIGraphicsBeginImageContextWithOptions(bounds.size, false, 0)
        drawingImage?.draw(in: bounds)
        
        let ctx = UIGraphicsGetCurrentContext()
        ctx?.saveGState()
        
        // 设置剪切区域 - 只在有效imageRect内绘制
        if !imageRect.isEmpty {
            ctx?.clip(to: imageRect)
        }
        
        ctx?.setLineWidth(lineWidth)
        ctx?.setLineCap(.round)
        ctx?.setLineJoin(.round)
        
        if toolType == .brush {
            ctx?.setStrokeColor(themeColor.cgColor)
            ctx?.setBlendMode(.copy) // 保留.copy混合模式以防止半透明颜色互相覆盖
        } else {
            ctx?.setBlendMode(.clear)
        }
        
        ctx?.beginPath()
        ctx?.move(to: fromPoint)
        ctx?.addLine(to: toPoint)
        ctx?.strokePath()
        
        ctx?.restoreGState()
        
        drawingImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        setNeedsDisplay()
    }
    
    override func draw(_ rect: CGRect) {
        // 确保绘制内容精确定位在正确位置
        drawingImage?.draw(in: rect)
    }
    
    // MARK: - 生成Mask
    func generateMask(size: CGSize) -> UIImage {
        // 如果没有绘制内容，返回全黑mask
        guard let drawingImage = drawingImage else {
            UIGraphicsBeginImageContextWithOptions(size, false, 1)
            UIColor.black.setFill()
            UIRectFill(CGRect(origin: .zero, size: size))
            let mask = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            return mask ?? UIImage()
        }
        
        // 创建与原图相同大小的画布
        UIGraphicsBeginImageContextWithOptions(size, false, 1)
        
        // 填充黑色背景
        UIColor.black.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        
        // 绘制白色画笔区域
        if let ctx = UIGraphicsGetCurrentContext() {
            // 翻转Y轴解决上下颠倒问题
            ctx.translateBy(x: 0, y: size.height)
            ctx.scaleBy(x: 1, y: -1)
            
            // 计算缩放因子
            let scaleX = size.width / bounds.width
            let scaleY = size.height / bounds.height
            
            // 设置纯白色填充
            ctx.setFillColor(UIColor.white.cgColor)
            
            // 应用缩放
            ctx.scaleBy(x: scaleX, y: scaleY)
            
            // 使用drawingImage中的非透明区域作为mask
            if let cgImage = drawingImage.cgImage {
                ctx.clip(to: bounds, mask: cgImage)
                ctx.fill(bounds)
            }
        }
        
        let finalMask = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return finalMask ?? UIImage()
    }
    
    // MARK: - 公开方法
    func getDrawingImage() -> UIImage? {
        return drawingImage
    }
}

private extension UIImage {
    func resized(to size: CGSize) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(size, false, self.scale)
        self.draw(in: CGRect(origin: .zero, size: size))
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return newImage ?? self
    }
} 