import UIKit

class PTMPaintView: UIImageView, UIGestureRecognizerDelegate {
    let drawView = DrawView(frame: .zero)
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        isUserInteractionEnabled = true
        contentMode = .scaleAspectFit
        setupGestures()
        setupDrawView()
    }
    
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    
    // MARK: - 子视图和手势
    private func setupGestures() {
        let pinch = UIPinchGestureRecognizer(target: self, action: #selector(handlePinch(_:)))
        addGestureRecognizer(pinch)
        let pan = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        pan.minimumNumberOfTouches = 2
        pan.maximumNumberOfTouches = 2
        pan.delegate = self
        addGestureRecognizer(pan)
    }
    
    private func setupDrawView() {
        drawView.isUserInteractionEnabled = true
        drawView.backgroundColor = .clear
        addSubview(drawView)
    }
    
    // MARK: - 布局更新
    override func layoutSubviews() {
        super.layoutSubviews()
        // 设置drawView.frame与PTMPaintView的bounds一致
        drawView.frame = bounds
        
        // 设置drawView的图像有效区域
        let imageRect = calculateImageRect()
        drawView.setImageRect(imageRect)
    }
    
    // 获取原始图片中的有效区域
    private func calculateImageRect() -> CGRect {
        guard let image = image else { return bounds }
        
        let imageSize = image.size
        let viewSize = bounds.size
        
        // 计算AspectFit下的图片实际显示区域
        let scale: CGFloat
        if imageSize.width / imageSize.height > viewSize.width / viewSize.height {
            // 图片比容器宽，以宽度为准
            scale = viewSize.width / imageSize.width
        } else {
            // 图片比容器高，以高度为准
            scale = viewSize.height / imageSize.height
        }
        
        let scaledWidth = imageSize.width * scale
        let scaledHeight = imageSize.height * scale
        let x = (viewSize.width - scaledWidth) / 2.0
        let y = (viewSize.height - scaledHeight) / 2.0
        
        return CGRect(x: x, y: y, width: scaledWidth, height: scaledHeight)
    }
    
    // 将视图坐标转换为图片坐标
    func viewToImageCoordinates(_ point: CGPoint) -> CGPoint? {
        guard let image = image else { return nil }
        
        let imageRect = calculateImageRect()
        
        // 检查点是否在图片区域内
        if !imageRect.contains(point) {
            return nil
        }
        
        // 计算相对位置
        let relativeX = (point.x - imageRect.origin.x) / imageRect.width
        let relativeY = (point.y - imageRect.origin.y) / imageRect.height
        
        // 转换为图片坐标
        return CGPoint(x: relativeX * image.size.width, 
                      y: relativeY * image.size.height)
    }
    
    // 将图片坐标转换为视图坐标
    func imageToViewCoordinates(_ point: CGPoint) -> CGPoint {
        guard let image = image else { return .zero }
        
        let imageRect = calculateImageRect()
        
        // 计算相对位置
        let relativeX = point.x / image.size.width
        let relativeY = point.y / image.size.height
        
        // 转换为视图坐标
        return CGPoint(x: imageRect.origin.x + relativeX * imageRect.width,
                      y: imageRect.origin.y + relativeY * imageRect.height)
    }
    
    // MARK: - 手势处理
    @objc private func handlePinch(_ gesture: UIPinchGestureRecognizer) {
        if gesture.state == .began || gesture.state == .changed {
            adjustAnchorPoint(for: gesture)
            let scale = gesture.scale
            self.transform = self.transform.scaledBy(x: scale, y: scale)
            gesture.scale = 1.0
        }
    }
    
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gesture.state == .began || gesture.state == .changed {
            let translation = gesture.translation(in: superview)
            center = CGPoint(x: center.x + translation.x, y: center.y + translation.y)
            gesture.setTranslation(.zero, in: superview)
        }
    }
    
    private func adjustAnchorPoint(for gesture: UIGestureRecognizer) {
        if gesture.state == .began {
            let locationInView = gesture.location(in: self)
            let locationInSuperview = gesture.location(in: superview)
            layer.anchorPoint = CGPoint(x: locationInView.x / bounds.size.width, y: locationInView.y / bounds.size.height)
            center = locationInSuperview
        }
    }
    
    // 允许两个手势同时识别
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
    
    // MARK: - 画笔相关
    var toolType: DrawView.ToolType {
        get { drawView.toolType }
        set { drawView.toolType = newValue }
    }
    
    var lineWidth: CGFloat {
        get { drawView.lineWidth }
        set { drawView.lineWidth = newValue }
    }
    
    // 生成mask
    func generateMask(size: CGSize) -> UIImage {
        // 如果没有图像或绘制内容，返回全黑mask
        guard let image = self.image, let drawingImage = drawView.getDrawingImage() else {
            UIGraphicsBeginImageContextWithOptions(size, true, 1)
            UIColor.black.setFill()
            UIRectFill(CGRect(origin: .zero, size: size))
            let mask = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            return mask ?? UIImage()
        }
        
        // 打印图像的关键属性以便分析
        print("-----图像属性分析开始-----")
        print("原始图片尺寸: \(image.size)")
        print("图片方向: \(image.imageOrientation.rawValue)")
        print("图片scale: \(image.scale)")
        print("图片renderingMode: \(image.renderingMode.rawValue)")
        print("CGImage属性:")
        if let cgImage = image.cgImage {
            print("  宽度: \(cgImage.width)")
            print("  高度: \(cgImage.height)")
            print("  位深: \(cgImage.bitsPerComponent)")
            print("  字节对齐: \(cgImage.bytesPerRow)")
            print("  Alpha信息: \(cgImage.alphaInfo.rawValue)")
            print("  像素格式: \(cgImage.bitmapInfo.rawValue)")
            if let colorSpace = cgImage.colorSpace {
                print("  颜色空间名称: \(CFCopyDescription(colorSpace))")
            }
        }
        print("容器大小: \(bounds.size)")
        print("渲染区域: \(calculateImageRect())")
        print("-----图像属性分析结束-----")
        
        // 创建一个新的黑白mask图像 - 使用不透明上下文
        UIGraphicsBeginImageContextWithOptions(size, true, 1)
        
        // 填充黑色背景
        UIColor.black.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        
        // 获取图像实际显示区域
        let imageRect = calculateImageRect()
        
        // 获取上下文
        guard let ctx = UIGraphicsGetCurrentContext() else {
            UIGraphicsEndImageContext()
            return UIImage()
        }
        
        // 应用Y轴翻转 - 所有情况都需要翻转
        ctx.translateBy(x: 0, y: size.height)
        ctx.scaleBy(x: 1, y: -1)
        print("Debug: 统一应用Y轴翻转")
        
        // 将绘制内容转换为CGImage
        guard let drawCGImage = drawingImage.cgImage else {
            UIGraphicsEndImageContext()
            return UIImage()
        }
        
        // 计算缩放和偏移
        let scaleX = size.width / imageRect.width
        let scaleY = size.height / imageRect.height
        
        // 计算绘制区域在原图坐标系中的位置
        let destRect = CGRect(
            x: -imageRect.origin.x * scaleX,
            y: -imageRect.origin.y * scaleY,
            width: bounds.width * scaleX,
            height: bounds.height * scaleY
        )
        
        // 使用drawingImage作为mask - 简化处理过程
        ctx.saveGState()
        
        // 使用灰色填充被mask区域 - 方便后续识别处理
        UIColor.gray.setFill()  // 使用灰色，确保与黑色背景区分
        ctx.setBlendMode(.normal)
        ctx.clip(to: destRect, mask: drawCGImage)
        ctx.fill(destRect)
        
        ctx.restoreGState()
        
        let maskImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        // 二次处理：确保是纯黑白
        guard let maskCGImage = maskImage?.cgImage else {
            return UIImage()
        }
        
        // 创建输出图像的数据缓冲区
        let width = Int(size.width)
        let height = Int(size.height)
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)
        
        // 创建位图上下文
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)
        
        guard let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: bitmapInfo.rawValue
        ) else { return UIImage(cgImage: maskCGImage) }
        
        // 绘制mask到上下文中
        let rect = CGRect(x: 0, y: 0, width: width, height: height)
        context.draw(maskCGImage, in: rect)
        
        // 处理每个像素：将所有非纯黑像素转换为纯白色
        for y in 0..<height {
            for x in 0..<width {
                let offset = (y * width + x) * bytesPerPixel
                let r = pixelData[offset]
                let g = pixelData[offset + 1]
                let b = pixelData[offset + 2]
                
                // 如果像素不是纯黑色(0,0,0)，则设置为纯白色(255,255,255)
                if r > 0 || g > 0 || b > 0 {
                    pixelData[offset] = 255     // R
                    pixelData[offset + 1] = 255 // G
                    pixelData[offset + 2] = 255 // B
                    pixelData[offset + 3] = 255 // A (不透明)
                }
            }
        }
        
        // 创建处理后的图像
        guard let outputCGImage = context.makeImage() else {
            return UIImage(cgImage: maskCGImage)
        }
        
        let finalMask = UIImage(cgImage: outputCGImage)
        
        // 保存到相册便于调试
//        UIImageWriteToSavedPhotosAlbum(finalMask, nil, nil, nil)
        print("Debug: New mask saved to photo album - 二次处理黑白图")
        
        return finalMask
    }
} 
