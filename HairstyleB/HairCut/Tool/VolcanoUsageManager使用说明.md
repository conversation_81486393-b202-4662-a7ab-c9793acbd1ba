# VolcanoUsageManager 使用说明

## 概述
`VolcanoUsageManager` 是一个全局统计管理火山引擎异步图生图流程次数的控制类，支持通过JSON配置文件动态调整参数。

## 功能特性

### 1. 次数控制策略
- **未订阅用户**: 默认1次/月
- **3天免费试用**: 10次/试用期
- **订阅用户**: 20次/月

### 2. 自动状态检测
- 自动检测用户订阅状态变化
- 自动识别免费试用期
- 自动处理订阅续费和过期

### 3. VIP用户版本更新兼容
- 自动检测已订阅用户是否首次使用次数系统
- 为版本更新后的VIP用户自动分配基础次数
- 避免老用户更新后没有可用次数的问题

### 3. 在线JSON配置支持
通过在线JSON文件可以动态调整所有参数，配置URL：
`https://controls.oss-cn-hangzhou.aliyuncs.com/ios-hair-cut-count.json`

```json
{
  "defaultFreeCount": 1,      // 未订阅用户默认次数
  "freeTrialCount": 10,       // 3天免费试用期次数
  "subscribedCount": 20,      // 订阅用户每月次数
  "resetDay": 1,              // 每月重置日期(1-28)
  "enableUsageLimit": true,   // 是否启用次数限制
  "warningThreshold": 3       // 剩余次数警告阈值
}
```

## 使用方法

### 1. 基本使用

```swift
// 获取剩余次数
let remaining = VolcanoUsageManager.shared.getRemainingCount()
print("剩余次数: \(remaining)")

// 检查是否可以使用(不消耗次数)
if VolcanoUsageManager.shared.canUse() {
    // 可以使用
    print("可以使用火山引擎API")
} else {
    // 次数不足
    let message = VolcanoUsageManager.shared.getUsageLimitMessage()
    print("使用受限: \(message)")
}

// 消耗一次使用次数
if VolcanoUsageManager.shared.consumeUsage() {
    // 成功消耗，可以调用API
    print("次数消耗成功")
} else {
    // 次数不足
    print("次数不足，无法使用")
}
```

### 2. 订阅状态变化处理

```swift
// 当用户订阅状态发生变化时，调用此方法刷新
VolcanoUsageManager.shared.refreshSubscriptionStatus()

// 获取当前订阅状态描述
let status = VolcanoUsageManager.shared.getSubscriptionStatusDescription()
print("当前状态: \(status)")
```

### 3. 调试和管理

```swift
// 获取详细配置信息(用于调试)
let configInfo = VolcanoUsageManager.shared.getConfigInfo()
print(configInfo)

// 管理员功能：重置当月使用次数
VolcanoUsageManager.shared.resetMonthlyUsage()

// 管理员功能：手动设置使用次数
VolcanoUsageManager.shared.setUsageCount(5)

// 手动刷新在线配置
VolcanoUsageManager.shared.refreshOnlineConfig { success in
    if success {
        print("在线配置刷新成功")
    } else {
        print("在线配置刷新失败")
    }
}
```

## 集成到现有代码

### 1. 在VolcanoEngineAPI中的集成

已经在 `VolcanoEngineAPI.swift` 中集成了使用次数检查：

```swift
// 在API调用前检查次数
guard VolcanoUsageManager.shared.canUse() else {
    let message = VolcanoUsageManager.shared.getUsageLimitMessage()
    completion(.failure(NSError(domain: "VolcanoUsageLimit", code: 1001, userInfo: [NSLocalizedDescriptionKey: message])))
    return
}

// API成功处理后消耗次数
_ = VolcanoUsageManager.shared.consumeUsage()
```

### 2. 在UI中的使用示例

```swift
// 在发型编辑页面
@IBAction func startHairEdit(_ sender: UIButton) {
    // 检查使用次数
    guard VolcanoUsageManager.shared.canUse() else {
        let message = VolcanoUsageManager.shared.getUsageLimitMessage()
        showAlert(message: message)
        return
    }
    
    // 显示剩余次数
    let remaining = VolcanoUsageManager.shared.getRemainingCount()
    if remaining <= 3 {
        showWarning(message: "剩余使用次数: \(remaining)")
    }
    
    // 继续执行发型编辑
    performHairEdit()
}
```

## 状态管理逻辑

### 1. 订阅状态检测
- 通过 `APPMakeStoreIAPManager.featureVip()` 检测VIP状态
- 通过 `UserDefaults.standard.object(forKey: "VipExpiresDate")` 检测过期时间
- 自动区分免费试用期和正式订阅

### 2. 次数重置机制
- 每月指定日期自动重置使用次数
- 订阅状态变化时重置次数
- 支持手动重置(管理员功能)

### 3. 数据持久化
- 用户记录保存在 `UserDefaults` 中
- 配置优先级：在线JSON > 本地JSON > 默认配置
- 支持在线配置实时更新

## 注意事项

1. **线程安全**: 所有方法都是线程安全的
2. **性能优化**: 状态检查有缓存机制，避免频繁计算
3. **错误处理**: 所有异常情况都有相应的错误处理
4. **调试支持**: 提供详细的日志输出和调试信息

## 配置文件位置

- **在线配置**: `https://controls.oss-cn-hangzhou.aliyuncs.com/ios-hair-cut-count.json` (优先级最高)
- **本地备用配置**: `HairstyleB/HairCut/Resource/Json/volcano_usage_config.json`
- **用户记录**: `UserDefaults` 中的 `VolcanoUserSubscriptionRecord` 键

## 在线配置JSON格式示例

```json
{
  "defaultFreeCount": 1,
  "freeTrialCount": 10,
  "subscribedCount": 20,
  "resetDay": 1,
  "enableUsageLimit": true,
  "warningThreshold": 3
}
```

修改在线JSON文件后，应用会在下次启动时自动获取最新配置，也可以通过 `refreshOnlineConfig` 方法手动刷新。

## 扩展功能

如需添加新的使用次数策略或配置参数，只需：

1. 在 `VolcanoUsageConfig` 结构体中添加新字段
2. 在JSON配置文件中添加对应配置
3. 在相关方法中添加处理逻辑

这样可以保持代码的可扩展性和配置的灵活性。

## VIP用户版本更新兼容

### 自动初始化机制
当已订阅的VIP用户更新到包含次数系统的版本时，系统会自动：

1. **检测VIP状态**: 通过 `APPMakeStoreIAPManager.featureVip()` 检测用户是否为VIP
2. **检查系统版本**: 通过版本标记判断是否首次使用次数系统
3. **自动初始化**: 为VIP用户分配完整的基础次数（20次/月）
4. **状态设置**: 自动设置为订阅状态，避免次数不足问题

### 测试和调试
```swift
// 重置次数系统版本标记（用于测试VIP用户初始化）
VolcanoUsageManager.shared.resetUsageSystemVersion()

// 重置后重启应用，VIP用户将重新进行初始化流程
```

这确保了老用户在版本更新后能够正常使用新的次数系统功能。
