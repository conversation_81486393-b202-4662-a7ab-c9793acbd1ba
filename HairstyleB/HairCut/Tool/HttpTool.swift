//
//  HttpTool.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/7/29.
//

import Alamofire
import Reachability
import Promises
import SwiftyJSON




// MARK: - 百度API配置
struct BaiduAPIConfig {
    // 百度API Key
    static var apiKey = ""
    // 百度Secret Key
    static var secretKey = ""
    
    // 获取百度访问令牌URL
    static let tokenUrl = "https://aip.baidubce.com/oauth/2.0/token"
    
    // 图像内容安全检测URL
    static let imgCensorUrl = "https://aip.baidubce.com/rest/2.0/solution/v1/img_censor/v2/user_defined"
    
    /// 设置百度API参数
    /// - Parameters:
    ///   - apiKey: 百度API Key
    ///   - secretKey: 百度Secret Key
    static func configure(apiKey: String, secretKey: String) {
        self.apiKey = apiKey
        self.secretKey = secretKey
        printLog(message: "百度API配置完成 - API Key: \(apiKey.prefix(5))..., Secret Key: \(secretKey.prefix(5))...")
    }
}

// MARK: - 使用示例
/*
// 在AppDelegate的didFinishLaunchingWithOptions方法中配置
func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    // 配置百度API参数
    BaiduAPIConfig.configure(
        apiKey: "您的百度API Key", 
        secretKey: "您的百度Secret Key"
    )
    
    // ... 其他初始化代码
    return true
}
*/

typealias HttpData = Promise<HttpRes>
typealias HttpHandler = (Bool) -> Void

enum HttpStatus {
    case unknow
    /// 服务器异常，即 code != 20000
    case serverExcep
    /// 网络异常
    case netExcep
    /// 未连接网络
    case unconnectNet
    /// 数据异常
    case dataExcep
}

/// 网络请求类型
enum HttpMethod {
    case get
    case post
    case put
}

// MARK: -
struct HttpError: Error {
    var statusCode: Int = 0
    var statusMesg: String?
    var state: HttpStatus = .unknow
}

struct HttpRes {
    var json: JSON
}

struct HttpConfig {
    /// 请求类型
    var method: HttpMethod = .post
    /// 请求url
    var url = ""
    /// 请求参数
    var params = [String: Any]()
    /// 是否显示网络错误提示
    var isShowErrInfo: Bool = false
    /// 请求头
    var headers: [String: String] = [:]
}

// MARK: -
class HttpTool {
    public static let shared = HttpTool()
    private let session: Session
    
    private init() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 60
        configuration.timeoutIntervalForResource = 60
        self.session = Session(configuration: configuration)
    }
    
    /// 是否连接wifi
    public var isAvailableWifi: Bool {
        let reachability = try? Reachability()
        guard reachability?.connection == .wifi else {
            return false
        }
        return true
    }
    
    /// 是否联网
    public var isAvailable: Bool {
        let reachability = try? Reachability()
        guard reachability?.connection == .wifi || reachability?.connection == .cellular else {
            return false
        }
        return true
    }
    
    /// 常规http/https请求
    /// 发起网络请求，只有code=200才会执行then，否则执行catch，无论then或catch都返回HttpRes
    /// - Parameter config: 配置项
    /// - Returns: Promise
    public func request(config: HttpConfig) -> Promise<HttpRes> {
        return Promise<HttpRes> { [weak self] (resolve, reject) in
            
            //网络判断
            guard self?.isAvailable == true else {
                let httpError = HttpError(statusCode: 1303, statusMesg: "手机无网络", state: .unconnectNet)
                if config.isShowErrInfo {
                    AppLoad.showActionAlert(message: "internet_fail".localized)
                }
                reject(httpError)
                return
            }
            
            //参数组合
            let url = config.url
            var params = [String: Any]()
            if let commonParams = self?.getCommonParams() {
                params.merge(commonParams)
            }
            params.merge(config.params)

            var method = HTTPMethod.post
            switch config.method {
            case .post: method = HTTPMethod.post
            case .get: method = HTTPMethod.get
            case .put: method = HTTPMethod.put
            }
            
            
            // 使用 config.headers 作为请求头
            let header = HTTPHeaders(config.headers)
            //发起请求
            var coder: ParameterEncoding = URLEncoding.default
            switch config.method {
            case .post: coder = JSONEncoding.default
            default: coder = URLEncoding.default
            }
            
            self?.session.request(url, method: method, parameters: params, encoding: coder, headers: header).response { response in
                var httpError = HttpError()
                guard let result = response.data else {
                    httpError.state = .netExcep
                    httpError.statusMesg = "网络异常"
                    httpError.statusCode = 1302
                    reject(httpError)
                    return
                }
                
                let json = JSON(result)
                
                let httpRes = HttpRes(json: json)
                //百度token没有code，特殊处理
                if url.contains(HttpConst.badiduToken) || url.contains(HttpConst.faceTestV3) {
                    resolve(httpRes)
                    return
                }
                guard json["prompt_id"] == nil else {
                    resolve(httpRes)
                    return
                }
                httpError.statusCode = json["code"].intValue
                httpError.statusMesg = json["message"].stringValue
                
                guard httpError.statusCode != 200 else {
                    resolve(httpRes)
                    return
                }
                
                httpError.state = .serverExcep
                reject(httpError)
            }
        }
    }
    
    /// 常规http/https请求
    /// 发起网络请求，只有code=200才会执行then，否则执行catch，无论then或catch都返回HttpRes
    /// - Parameter config: 配置项
    /// - Returns: Promise

    public func requestWithOuth(config: HttpConfig) -> Promise<HttpRes> {
        return Promise<HttpRes> { [weak self] (resolve, reject) in
            // 1. 创建授权请求
            var authConfig = HttpConfig()
            authConfig.url = HttpConst.authLink
            authConfig.method = .post
            
            // 2. 准备授权请求参数
            let currentDate = Date()
            let timestampInMilliseconds = Int(currentDate.timeIntervalSince1970 * 1000)
            
            let paras: [String: Any] = [
                "appId": "com.fullstack.api",
                "appKeys": ["biggerlens_comfyui_en"],
                "timestamp": timestampInMilliseconds
            ]
            
            // 3. 将参数转换为 JSON 字符串
            guard let jsonData = try? JSONSerialization.data(withJSONObject: paras, options: []),
                  let datastr = String(data: jsonData, encoding: .utf8) else {
                reject(HttpError(statusCode: 1301, statusMesg: "参数转换失败", state: .serverExcep))
                return
            }
            
            // 4. RSA 加密
            let pub2 = "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCblVmnev6bID5A9PgvPpgaH7aY\nCoroVm8ejy8oClnUprPtLLbfQ/LJV7jjFIqac2yxYgOOX10J+cDeU3GMMggoXs1w\nABBAX07E7vM4TMmMPLurckrmXn/H02Hi0loU5S6UQYyFm1TCHIlWrNBgpRHh4AO8\nxJl5pn03BZ9xbqJZRwIDAQAB\n-----END PUBLIC KEY-----"
            
            guard let paraEncryptString = RSA.encryptString(datastr, publicKey: pub2) else {
                reject(HttpError(statusCode: 1301, statusMesg: "加密失败", state: .serverExcep))
                return
            }
            
            let parameters: [String: Any] = ["para": paraEncryptString]
            
            // 5. 发起授权请求
            let headers: HTTPHeaders = [
                "Content-Type": "application/json"
            ]
            
            let sessionManager = Session.default
            sessionManager.session.configuration.timeoutIntervalForRequest = 30 // 设置超时时间
            
            // 6. 将参数转换为 Data
            guard let parameterData = try? JSONSerialization.data(withJSONObject: parameters, options: .fragmentsAllowed) else {
                reject(HttpError(statusCode: 1301, statusMesg: "参数转换为 Data 失败", state: .serverExcep))
                return
            }
            
            // 7. 发起 upload 请求
            sessionManager.upload(multipartFormData: { multipartFormData in
                // 将 para 参数作为表单字段添加
                if let paraData = paraEncryptString.data(using: .utf8) {
                    multipartFormData.append(paraData, withName: "para")
                }
            }, to: authConfig.url, method: .post, headers: headers)
            .responseJSON { response in
                switch response.result {
                case .success(let value):
                    let json = JSON(value)
                    
                    // 8. 解析授权响应
                    guard let appSecret = json["data"]["appKeys"][0]["appSecret"].string else {
                        reject(HttpError(statusCode: 1301, statusMesg: "授权响应解析失败", state: .serverExcep))
                        return
                    }
                    
                    // 9. 生成 token
                    let timestampInSeconds = Int(Date().timeIntervalSince1970)
                    let dict: [String: Any] = [
                        "appKey": appSecret,
                        "timestamp": timestampInSeconds
                    ]
                    
                    guard let keyData = try? JSONSerialization.data(withJSONObject: dict, options: []),
                          let key = String(data: keyData, encoding: .utf8) else {
                        reject(HttpError(statusCode: 1301, statusMesg: "token 生成失败", state: .serverExcep))
                        return
                    }
                    
                    let pubkey = "-----BEGIN PUBLIC KEY-----\nMIGeMA0GCSqGSIb3DQEBAQUAA4GMADCBiAKBgHsYFtK91vMa+KtqgPKJHQw2M7x9\nJk+xSkOsFbpPNBDNn/XVl+MTBj9LhR109+z10v2EmdB6lUR0yvPxUqDjBfiqkxc3\nTnR0+3Yq0gDTL+rwp/iKSVL/ViALEfwIgwrzg521QyAHp9jHenwAitqiOZTrez4A\nP+nX9I24LMBnugO1AgMBAAE=\n-----END PUBLIC KEY-----"
                    
                    guard let cryString = RSA.encryptString(key, publicKey: pubkey) else {
                        reject(HttpError(statusCode: 1301, statusMesg: "token 加密失败", state: .serverExcep))
                        return
                    }
                    
                    // 10. 网络判断
                    guard self?.isAvailable == true else {
                        let httpError = HttpError(statusCode: 1303, statusMesg: "手机无网络", state: .unconnectNet)
                        if config.isShowErrInfo {
                            AppLoad.showActionAlert(message: "internet_fail".localized)
                        }
                        reject(httpError)
                        return
                    }
                    
                    // 11. 更新请求头
                    var headers = self?.getHttpHeader() ?? HTTPHeaders()
                    headers["token"] = cryString
                    var newConfig = config
                    newConfig.headers = headers.dictionary
                    
                    // 12. 调用现有的 request 方法
                    HttpTool.shared.request(config: newConfig).then { httpRes in
                        resolve(httpRes)
                    }.catch { error in
                        reject(error)
                    }
                    
                case .failure(let error):
                    reject(HttpError(statusCode: 1301, statusMesg: "授权请求失败: \(error.localizedDescription)", state: .serverExcep))
                }
            }
        }
    }
    
    /// 检查图片内容安全
    /// - Parameter image: 需要检查的图片
    /// - Returns: Promise<Bool> - true表示图片安全，false表示图片不安全
    public func checkImageSafety(_ image: UIImage) -> Promise<Bool> {
        return Promise<Bool> { (resolve, reject) in
            // 网络判断
            guard self.isAvailable else {
                let httpError = HttpError(statusCode: 1303, statusMesg: "手机无网络", state: .unconnectNet)
                AppLoad.showActionAlert(message: "internet_fail".localized)
                printLog(message: "图像安全检查失败: 网络不可用")
                reject(httpError)
                return
            }
            
            // 检查百度API参数是否配置
            guard !BaiduAPIConfig.apiKey.isEmpty, !BaiduAPIConfig.secretKey.isEmpty else {
                printLog(message: "警告: 百度API参数未配置，跳过图像安全检查")
                // 如果未配置，默认图片安全
                resolve(true)
                return
            }
            
            printLog(message: "开始图像安全检查 - 图像尺寸: \(image.size.width)x\(image.size.height)")
            
            // 1. 获取百度访问token - 使用自定义URL请求而不是HttpTool方法
            let tokenUrl = BaiduAPIConfig.tokenUrl
            printLog(message: "正在获取百度API令牌，URL: \(tokenUrl)")
            
            // 创建URL请求
            guard let url = URL(string: tokenUrl) else {
                printLog(message: "百度API令牌URL无效")
                reject(HttpError(statusCode: 1305, statusMesg: "百度API令牌URL无效", state: .dataExcep))
                return
            }
            
            // 准备请求参数
            let params = [
                "grant_type": "client_credentials",
                "client_id": BaiduAPIConfig.apiKey,
                "client_secret": BaiduAPIConfig.secretKey
            ]
            
            // 将参数转换为查询字符串格式
            let bodyString = params.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
            let bodyData = bodyString.data(using: .utf8)
            
            // 创建请求
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.httpBody = bodyData
            request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
            
            printLog(message: "百度API令牌请求参数: \(bodyString)")
            
            // 执行请求
            let task = URLSession.shared.dataTask(with: request) { [weak self] (data, response, error) in
                guard let strongSelf = self else {
                    reject(HttpError(statusCode: 1305, statusMesg: "内部错误", state: .dataExcep))
                    return
                }
                
                // 处理响应
                if let error = error {
                    printLog(message: "百度API令牌请求网络错误: \(error.localizedDescription)")
                    reject(HttpError(statusCode: 1305, statusMesg: "网络请求失败: \(error.localizedDescription)", state: .netExcep))
                    return
                }
                
                guard let data = data else {
                    printLog(message: "百度API令牌请求无数据返回")
                    reject(HttpError(statusCode: 1305, statusMesg: "无数据返回", state: .dataExcep))
                    return
                }
                
                // 解析响应JSON
                do {
                    let tokenJson = try JSON(data: data)
                    printLog(message: "获取百度API令牌结果: \(tokenJson)")
                    
                    // 检查是否有错误
                    if let error = tokenJson["error"].string {
                        let errorDesc = tokenJson["error_description"].string ?? "未知错误"
                        printLog(message: "百度API令牌获取失败: \(error) - \(errorDesc)")
                        reject(HttpError(statusCode: 1305, statusMesg: "获取百度Token失败: \(errorDesc)", state: .dataExcep))
                        return
                    }
                    
                    guard let accessToken = tokenJson["access_token"].string else {
                        printLog(message: "百度API令牌获取失败，返回数据: \(tokenJson)")
                        reject(HttpError(statusCode: 1305, statusMesg: "获取百度Token失败", state: .dataExcep))
                        return
                    }
                    
                    printLog(message: "百度API令牌获取成功: \(accessToken.prefix(8))...")
                    
                    // 2. 调用百度图像审核API
                    strongSelf.checkImageWithBaiduToken(image: image, accessToken: accessToken, resolve: resolve, reject: reject)
                } catch {
                    printLog(message: "百度API令牌响应解析失败: \(error.localizedDescription)")
                    reject(HttpError(statusCode: 1305, statusMesg: "响应解析失败", state: .dataExcep))
                }
            }
            
            // 开始请求
            task.resume()
        }
    }
    
    /// 使用百度令牌检查图片内容
    /// - Parameters:
    ///   - image: 需要检查的图片
    ///   - accessToken: 百度API访问令牌
    ///   - resolve: 成功回调
    ///   - reject: 失败回调
    private func checkImageWithBaiduToken(image: UIImage, accessToken: String, resolve: @escaping (Bool) -> Void, reject: @escaping (Error) -> Void) {
        // 调用百度图像审核API
        let checkUrl = "\(BaiduAPIConfig.imgCensorUrl)?access_token=\(accessToken)"
        
        // 确保图片尺寸符合要求：最短边大于等于15像素，最长边小于等于4096像素
        var processedImage = image
        let shortestSide = min(image.size.width, image.size.height)
        let longestSide = max(image.size.width, image.size.height)
        
        printLog(message: "原始图片尺寸: \(image.size.width)x\(image.size.height), 最短边: \(shortestSide), 最长边: \(longestSide)")
        
        // 百度API要求最短边至少15px
        if shortestSide < 15 {
            // 图片太小，需要放大
            let scale = 15 / shortestSide
            let newWidth = image.size.width * scale
            let newHeight = image.size.height * scale
            let newSize = CGSize(width: newWidth, height: newHeight)
            
            UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
            image.draw(in: CGRect(origin: .zero, size: newSize))
            if let resizedImage = UIGraphicsGetImageFromCurrentImageContext() {
                processedImage = resizedImage
            }
            UIGraphicsEndImageContext()
            printLog(message: "图片已调整大小: \(processedImage.size.width)x\(processedImage.size.height) (放大)")
        } else if longestSide > 4096 {
            // 图片太大，需要缩小
            let scale = 4096 / longestSide
            let newWidth = image.size.width * scale
            let newHeight = image.size.height * scale
            let newSize = CGSize(width: newWidth, height: newHeight)
            
            UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
            image.draw(in: CGRect(origin: .zero, size: newSize))
            if let resizedImage = UIGraphicsGetImageFromCurrentImageContext() {
                processedImage = resizedImage
            }
            UIGraphicsEndImageContext()
            printLog(message: "图片已调整大小: \(processedImage.size.width)x\(processedImage.size.height) (缩小)")
        }
        
        // 尝试先使用PNG格式，如果太大再使用JPEG格式
        guard let pngData = processedImage.pngData() else {
            printLog(message: "图片转换为PNG格式失败")
            reject(HttpError(statusCode: 1306, statusMesg: "图片转换失败", state: .dataExcep))
            return
        }
        
        // 检查PNG数据大小并进行base64编码
        let pngBase64 = UIImage.base64String(from: processedImage)
        printLog(message: "pngBase64:\(pngBase64)")
        
        // 创建请求参数 - 确保使用标准表单格式
        let params = "image=\(pngBase64)"
        printLog(message: "params:\(params)")
        
        // 直接使用Alamofire发送请求，避免String转Data的问题
        let url = "https://aip.baidubce.com/rest/2.0/solution/v1/img_censor/v2/user_defined?access_token=\(accessToken)"
        printLog(message: "使用Alamofire发送请求到: \(url)")

        var headers = HTTPHeaders()
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        AF.request(url, 
                   method: .post, 
                   parameters: ["image": pngBase64], 
                   encoding: URLEncoding.default, 
                   headers: headers)
          .responseData { response in
            // 处理响应
            if let error = response.error {
                printLog(message: "百度图像审核API网络错误: \(error.localizedDescription)")
                reject(HttpError(statusCode: 1306, statusMesg: "网络请求失败: \(error.localizedDescription)", state: .netExcep))
                return
            }
            
            // 打印HTTP响应状态
            if let httpResponse = response.response {
                printLog(message: "HTTP响应状态码: \(httpResponse.statusCode)")
                printLog(message: "HTTP响应头: \(httpResponse.allHeaderFields)")
            }
            
            // 检查数据
            guard let data = response.data else {
                printLog(message: "百度图像审核API无数据返回")
                reject(HttpError(statusCode: 1306, statusMesg: "无数据返回", state: .dataExcep))
                return
            }
            
            // 打印原始响应内容用于调试
            if let responseString = String(data: data, encoding: .utf8) {
                printLog(message: "原始响应内容: \(responseString)")
            }
            
            // 解析响应JSON
            do {
                let resultJson = try JSON(data: data)
                printLog(message: "百度图像审核完成，返回结果: \(resultJson)")
                
                // 检查是否有错误
                if let errorCode = resultJson["error_code"].int {
                    let errorMsg = resultJson["error_msg"].string ?? "未知错误"
                    printLog(message: "百度图像审核失败: 错误码\(errorCode) - \(errorMsg)")
                    
                    // 根据错误码返回更具体的错误信息
                    var detailedErrorMsg = errorMsg
                    if errorCode == 216201 { // 图像格式错误
                        detailedErrorMsg = "图片格式错误，请确保: 1.图片格式为JPG/PNG/BMP 2.Base64编码正确 3.大小和尺寸符合要求"
                        printLog(message: "图片格式错误可能原因: 1.格式不支持 2.编码错误 3.大小或尺寸不符合")
                        
                    } else if errorCode == 18 { // QPS限制
                        detailedErrorMsg = "API请求频率限制，请稍后再试"
                    }
                    
                    // 遇到任何错误，直接返回失败
                    reject(HttpError(statusCode: 1306, statusMesg: "图像审核失败: \(detailedErrorMsg)", state: .dataExcep))
                    return
                }
                
                // 处理结果
                let conclusion = resultJson["conclusion"].stringValue
                printLog(message: "图像审核结论: \(conclusion)")
                
                // 判断图片是否安全 - 根据百度API，"合规"表示图片安全
                let isSafe = conclusion == "合规"
                
                if !isSafe {
                    // 记录不安全原因
                    let data = resultJson["data"].arrayValue
                    
                    let reasons = data.map { item in
                        return "\(item["msg"].stringValue)(置信度:\(item["probability"].floatValue))"
                    }.joined(separator: ", ")
                    
                    printLog(message: "图片安全检查不通过: \(reasons)")
                } else {
                    printLog(message: "图片安全检查通过")
                }
                
                resolve(isSafe)
            } catch {
                printLog(message: "百度图像审核响应解析失败: \(error.localizedDescription)")
                reject(HttpError(statusCode: 1306, statusMesg: "响应解析失败", state: .dataExcep))
            }
        }
    }
    
    /// 使用JPEG格式重试
    private func retryWithJpeg(processedImage: UIImage, accessToken: String, resolve: @escaping (Bool) -> Void, reject: @escaping (Error) -> Void) {
        guard let jpegData = processedImage.jpegData(compressionQuality: 0.8) else {
            printLog(message: "JPEG转换失败")
            reject(HttpError(statusCode: 1306, statusMesg: "图片转换为JPEG失败", state: .dataExcep))
            return
        }
        
        let jpegBase64 = jpegData.base64EncodedString()
        let jpegBase64Size = Double(jpegBase64.count) / (1024 * 1024)
        
        printLog(message: "JPEG重试: 格式图片大小: \(Double(jpegData.count) / (1024 * 1024))MB, Base64后大小: \(jpegBase64Size)MB")
        
        if jpegBase64Size > 4 {
            printLog(message: "JPEG重试: 图片太大")
            reject(HttpError(statusCode: 1306, statusMesg: "图片太大，无法进行内容安全检查", state: .dataExcep))
            return
        }
        
        // 调用百度图像审核API
        let checkUrl = "\(BaiduAPIConfig.imgCensorUrl)?access_token=\(accessToken)"
        
        // URL编码处理
        guard let encodedBase64 = jpegBase64.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) else {
            printLog(message: "JPEG重试: Base64编码URL转义失败")
            reject(HttpError(statusCode: 1306, statusMesg: "Base64编码URL转义失败", state: .dataExcep))
            return
        }
        
        // 创建URL请求
        guard let url = URL(string: checkUrl) else {
            printLog(message: "JPEG重试: URL无效")
            reject(HttpError(statusCode: 1306, statusMesg: "URL无效", state: .dataExcep))
            return
        }
        
        // 创建请求参数
        let params = "image=\(encodedBase64)"
        guard let bodyData = params.data(using: .utf8) else {
            printLog(message: "JPEG重试: 创建请求体失败")
            reject(HttpError(statusCode: 1306, statusMesg: "创建请求体失败", state: .dataExcep))
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.httpBody = bodyData
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        printLog(message: "JPEG重试: 正在调用百度图像审核API...")
        
        // 执行请求
        let task = URLSession.shared.dataTask(with: request) { (data, response, error) in
            if let error = error {
                printLog(message: "JPEG重试: 网络错误: \(error.localizedDescription)")
                reject(HttpError(statusCode: 1306, statusMesg: "网络请求失败", state: .netExcep))
                return
            }
            
            guard let data = data else {
                printLog(message: "JPEG重试: 无数据返回")
                reject(HttpError(statusCode: 1306, statusMesg: "无数据返回", state: .dataExcep))
                return
            }
            
            if let responseString = String(data: data, encoding: .utf8) {
                printLog(message: "JPEG重试: 原始响应内容: \(responseString)")
            }
            
            do {
                let resultJson = try JSON(data: data)
                printLog(message: "JPEG重试: 审核完成，返回结果: \(resultJson)")
                
                if let errorCode = resultJson["error_code"].int {
                    let errorMsg = resultJson["error_msg"].string ?? "未知错误"
                    printLog(message: "JPEG重试: 失败: 错误码\(errorCode) - \(errorMsg)")
                    
                    // 所有重试都失败了
                    reject(HttpError(statusCode: 1306, statusMesg: "图像审核失败: \(errorMsg)", state: .dataExcep))
                    return
                }
                
                let conclusion = resultJson["conclusion"].stringValue
                printLog(message: "JPEG重试: 图像审核结论: \(conclusion)")
                
                let isSafe = conclusion == "合规"
                
                if !isSafe {
                    let data = resultJson["data"].arrayValue
                    let reasons = data.map { item in
                        return "\(item["msg"].stringValue)(置信度:\(item["probability"].floatValue))"
                    }.joined(separator: ", ")
                    
                    printLog(message: "JPEG重试: 图片安全检查不通过: \(reasons)")
                } else {
                    printLog(message: "JPEG重试: 图片安全检查通过")
                }
                
                resolve(isSafe)
            } catch {
                printLog(message: "JPEG重试: 响应解析失败: \(error.localizedDescription)")
                reject(HttpError(statusCode: 1306, statusMesg: "响应解析失败", state: .dataExcep))
            }
        }
        
        task.resume()
    }
    
    /// 上传图片
    public func uploadImage(config: HttpConfig) -> Promise<HttpRes> {
        return Promise<HttpRes> { [weak self] (resolve, reject) in
            
            //网络判断
            guard self?.isAvailable == true else {
                let httpError = HttpError(statusCode: 1303, statusMesg: "手机无网络", state: .unconnectNet)
                if config.isShowErrInfo {
                    AppLoad.showActionAlert(message: "internet_fail".localized)
                }
                reject(httpError)
                return
            }
            
            //参数组合，抓出图片资源
            let url = config.url
            var params = [String: Any]()
            if let commonParams = self?.getCommonParams() {
                params.merge(commonParams)
            }
            params.merge(config.params)
            guard var img = params["img"] as? UIImage else{
                let error = HttpError(statusCode: 1304, statusMesg: "找不到图片", state: .dataExcep)
                if config.isShowErrInfo {
                    AppLoad.showActionAlert(message: "image_upload_fail".localized)
                }
                reject(error)
                return
            }
            
            printLog(message: "准备上传图片 - 原始尺寸: \(img.size.width)x\(img.size.height)")
            
            let fixSize = ImageTool().getFixSize(img.size, maxSize: 800 * 800)
            if img.size.width > fixSize.width || img.size.height > fixSize.height {
                guard let newImage = img.createImage(fixSize) else {
                    let error = HttpError(statusCode: 1304, statusMesg: "图片转尺寸失败", state: .dataExcep)
                    if config.isShowErrInfo {
                        AppLoad.showActionAlert(message: "image_upload_fail".localized)
                    }
                    reject(error)
                    return
                }
                img = newImage
                printLog(message: "图片已缩放至: \(img.size.width)x\(img.size.height)")
            }
            params.removeValue(forKey: "img")
            
            // 在上传图片前检查图片安全性
            guard let strongSelf = self else {
                let error = HttpError(statusCode: 1305, statusMesg: "内部错误", state: .dataExcep)
                reject(error)
                return
            }
            
            printLog(message: "开始图片安全检查")
            strongSelf.checkImageSafety(img).then { isSafe in
                printLog(message: "图片安全检查结果: \(isSafe ? "安全" : "不安全")")
                
                guard isSafe else {
                    // 图片不安全，返回失败
                    let error = HttpError(statusCode: 1307, statusMesg: "图片内容不安全", state: .dataExcep)
                    if config.isShowErrInfo {
                        AppLoad.showActionAlert(message: "图片内容不合规，请更换图片")
                    }
                    reject(error)
                    return
                }
                
                // 图片安全，继续上传流程
                var method = HTTPMethod.post
                switch config.method {
                case .post: method = HTTPMethod.post
                case .get: method = HTTPMethod.get
                case .put: method = HTTPMethod.put
                }
                
                let header = strongSelf.getHttpHeader()
                
                printLog(message: "开始上传图片到: \(url)")
                
                //发起请求
                strongSelf.session.upload(multipartFormData: { multipartFormData in
                    if let imageData = img.pngData() {
                        let timestamp = Int(Date().timeIntervalSince1970) // 获取当前时间戳
                            let fileName = "\(timestamp)_upload.png" // 在文件名前添加时间戳
                        multipartFormData.append(imageData, withName: "image", fileName: fileName)
                        printLog(message: "图片已添加到请求, 文件名: \(fileName), 数据大小: \(imageData.count) 字节")
                    }
                }, to: url, method: method, headers: header) { result in
                    printLog(message: "上传图片结果：\(result)")
                }.response { response in
                    var httpError = HttpError()
                    guard let result = response.data else {
                        httpError.state = .netExcep
                        httpError.statusMesg = "网络异常"
                        httpError.statusCode = 1302
                        printLog(message: "上传失败: 网络异常")
                        reject(httpError)
                        return
                    }
                    
                    let json = JSON(result)
                    printLog(message: "上传图片服务器响应: \(json)")
                    
                    let httpRes = HttpRes(json: json)
                    httpError.statusCode = json["code"].intValue
                    httpError.statusMesg = json["message"].stringValue
                    let name = json["name"]
                    guard json["name"] == nil else {
                        printLog(message: "上传成功: \(json)")
                        resolve(httpRes)
                        return
                    }
                    
                    httpError.statusMesg = json["msg"].stringValue
                    httpError.state = .serverExcep
                    printLog(message: "上传失败: 服务器异常, 错误: \(httpError.statusMesg ?? "未知错误"), 状态码: \(httpError.statusCode)")
                    reject(httpError)
                }
            }.catch { error in
                printLog(message: "图片检查或上传过程中出错: \(error)")
                reject(error)
            }
        }
    }
}

private typealias CommonParams = HttpTool
private extension CommonParams {
    /// 获取公共参数
    /// - Returns: [String: Any]
    func getCommonParams() -> [String: Any] {
        var params = [String: Any]()
        params["zone"] = NSTimeZone.system.identifier
        return params
    }
    
    func getHttpHeader() -> HTTPHeaders {
        
        var headers = HTTPHeaders()
        headers["Content-Type"] = "application/json"
        headers["Accept"] = "application/json"
        return headers
    }
}

