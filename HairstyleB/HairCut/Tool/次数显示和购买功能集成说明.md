# 次数显示和购买功能集成说明

## 📋 修改概述

按照用户要求，在AI发型页面和美甲功能页面的左上角添加了剩余次数显示和添加次数按钮，并实现了智能购买弹窗逻辑。

## 🎯 主要功能

### 1. 次数显示
- **位置**: 页面左上角
- **格式**: "剩余次数: X"
- **样式**: 14号字体，灰色文字 (#999999)
- **实时更新**: 生成成功后自动更新显示

### 2. 添加次数按钮
- **图片**: 使用asset中的"添加次数按钮"
- **位置**: 次数显示右侧，间距8px
- **尺寸**: 24x24px
- **功能**: 点击弹出购买弹窗

### 3. 智能购买弹窗
- **VIP用户**: 只显示购买次数按钮
- **非VIP用户**: 显示购买次数 + 订阅会员按钮
- **购买成功**: 自动更新次数显示

## 📱 修改的页面

### 1. AIHairCutVC.swift (AI发型选择页)
```swift
// 新增属性
private var usageCountLabel: UILabel!
private var addUsageButton: UIButton!

// 在viewDidLoad中添加
setupUsageCountUI()
updateUsageCountDisplay()

// 生成成功后更新
case .success(let processedImage):
    self?.updateUsageCountDisplay()  // 新增
    self?.navigateToResult(with: processedImage)
```

### 2. AIHairResultVC.swift (AI发型结果页)
```swift
// 新增属性
private var usageCountLabel: UILabel!
private var addUsageButton: UIButton!

// 在viewDidLoad中添加
setupUsageCountUI()
updateUsageCountDisplay()

// 再次生成成功后更新
case .success(let processedImage):
    self?.updateUsageCountDisplay()  // 新增
    // 更新结果页面...
```

### 3. NailProcessViewController.swift (美甲处理页)
```swift
// 新增属性
private var usageCountLabel: UILabel!
private var addUsageButton: UIButton!

// 在viewDidLoad中添加
setupUsageCountUI()
updateUsageCountDisplay()

// 处理成功后更新
case .success(let processedImage):
    self?.updateUsageCountDisplay()  // 新增
    self?.showProcessedResult(processedImage)
```

## 🔧 核心实现

### 1. 次数显示UI设置
```swift
private func setupUsageCountUI() {
    // 创建次数显示标签
    usageCountLabel = UILabel()
    usageCountLabel.font = UIFont.systemFont(ofSize: 14)
    usageCountLabel.textColor = UIColor(valueRGB: 0x999999)
    usageCountLabel.text = "剩余次数: 0"
    
    // 创建添加次数按钮
    addUsageButton = UIButton(type: .custom)
    addUsageButton.setImage(UIImage(named: "添加次数按钮"), for: .normal)
    addUsageButton.addTarget(self, action: #selector(addUsageButtonTapped), for: .touchUpInside)
    
    // 添加到视图并设置约束
    view.addSubview(usageCountLabel)
    view.addSubview(addUsageButton)
    
    usageCountLabel.snp.makeConstraints { make in
        make.left.equalTo(16)
        make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(16)
    }
    
    addUsageButton.snp.makeConstraints { make in
        make.left.equalTo(usageCountLabel.snp.right).offset(8)
        make.centerY.equalTo(usageCountLabel)
        make.width.height.equalTo(24)
    }
}
```

### 2. 次数更新方法
```swift
private func updateUsageCountDisplay() {
    let remaining = VolcanoUsageManager.shared.getRemainingCount()
    usageCountLabel.text = "剩余次数: \(remaining)"
}
```

### 3. 购买按钮点击处理
```swift
@objc private func addUsageButtonTapped() {
    VolcanoEngineAPI.showUsagePurchasePopup {
        // 购买成功后更新显示
        DispatchQueue.main.async {
            self.updateUsageCountDisplay()
        }
    }
}
```

## 🎨 弹窗逻辑优化

### 1. UsagePurchasePopupView.swift 新增方法
```swift
/// 更新UI布局（根据是否为VIP用户）
func updateLayoutForVIPStatus(_ isVIP: Bool) {
    if isVIP {
        // VIP用户：隐藏订阅按钮，购买按钮移到订阅按钮位置
        subscribeButton.isHidden = true
        
        // 重新设置购买按钮约束
        purchaseButton.snp.remakeConstraints { make in
            make.top.equalTo(diamondImageView.snp.bottom).offset(16)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.height.equalTo(50)
            make.bottom.equalTo(-20)  // 直接到底部
        }
    } else {
        // 非VIP用户：显示订阅按钮，保持原有布局
        subscribeButton.isHidden = false
        // 恢复原有约束...
    }
}
```

### 2. VolcanoEngineAPI.swift 弹窗调用优化
```swift
static func showUsagePurchasePopup(completion: @escaping () -> Void) {
    let popupView = UsagePurchasePopupView()
    
    // 获取次数购买产品价格
    let purchasePrice = UserDefaults.standard.string(forKey: "com.Fengyin.Camera.Frequency.package") ?? "¥10"
    popupView.updatePurchasePrice(purchasePrice)
    
    // 检查是否为VIP用户，更新布局
    let isVIP = APPMakeStoreIAPManager.featureVip()
    popupView.updateLayoutForVIPStatus(isVIP)
    
    // 设置回调...
    popupView.show(in: topViewController.view)
}
```

## 🔄 原有逻辑移除

### 1. 移除VIP检查
- **AIHairCutView.swift**: 移除原有的VIP权限检查逻辑
- **AIHairResultVC.swift**: 移除"再次生成"的VIP检查
- **统一管理**: 所有次数控制统一由VolcanoEngineAPI管理

### 2. 统一API调用
- **发型AI**: 使用 `VolcanoEngineAPI.processAsyncImageGeneration`
- **造型AI**: 使用 `VolcanoEngineAPI.processAsyncImageGeneration`
- **美甲功能**: 使用 `VolcanoEngineAPI.processNailWithModel`

## 📊 次数管理策略

### 1. 发型编辑（同步）
- **管理器**: `HairEditUsageManager`
- **限制**: 未订阅每日2次，订阅无限制
- **超限**: 直接弹出订阅页面

### 2. 异步图生图（发型AI、造型AI、美甲）
- **管理器**: `VolcanoUsageManager`
- **限制**: 基于在线JSON配置 + 购买次数包
- **超限**: 弹出购买弹窗（VIP/非VIP差异化显示）

## 🎯 用户体验优化

1. **无缝集成**: 次数显示不影响原有UI布局
2. **实时更新**: 生成成功后立即更新次数显示
3. **智能弹窗**: 根据VIP状态显示不同的购买选项
4. **一键购买**: 购买成功后自动更新显示，无需刷新页面
5. **统一体验**: 所有异步图生图功能使用相同的次数管理策略

## 🔍 测试要点

1. **次数显示**: 确认在所有相关页面正确显示剩余次数
2. **按钮功能**: 测试添加次数按钮点击弹出购买弹窗
3. **VIP差异**: 测试VIP和非VIP用户看到的弹窗差异
4. **购买流程**: 测试购买成功后次数增加和显示更新
5. **生成更新**: 测试生成成功后次数减少和显示更新
6. **页面切换**: 测试在不同页面间切换时次数显示的一致性

## 📋 所需资源

- **图片资源**: `添加次数按钮.png` (24x24px)
- **产品ID**: `com.Fengyin.Camera.Frequency.package`
- **在线配置**: `https://controls.oss-cn-hangzhou.aliyuncs.com/ios-hair-cut-count.json`

## ✅ 完成状态

- ✅ AI发型选择页次数显示
- ✅ AI发型结果页次数显示  
- ✅ 美甲处理页次数显示
- ✅ 智能购买弹窗逻辑
- ✅ VIP用户差异化显示
- ✅ 生成成功后次数更新
- ✅ 购买成功后次数更新
- ✅ 原有VIP检查逻辑移除
- ✅ 统一异步图生图API调用

所有功能已完成集成，可以进行测试和上线！🎉
