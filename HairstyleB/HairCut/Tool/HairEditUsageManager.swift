//
//  HairEditUsageManager.swift
//  HairCut
//
//  Created by AI Assistant on 2025/01/21.
//

import Foundation

/// 发型编辑使用次数管理器
class HairEditUsageManager {
    
    // MARK: - 单例
    static let shared = HairEditUsageManager()
    private init() {}
    
    // MARK: - 常量
    private let freeUsageCount = 2  // 未订阅用户免费次数
    private let usageKey = "HairEditUsageCount"
    private let lastResetDateKey = "HairEditLastResetDate"
    
    // MARK: - 公共方法
    
    /// 检查是否可以使用发型编辑功能
    func canUseHairEdit() -> Bool {
        // 如果是VIP用户，无限制使用
        if APPMakeStoreIAPManager.featureVip() {
            return true
        }
        
        // 非VIP用户检查免费次数
        resetDailyUsageIfNeeded()
        let usedCount = getCurrentUsageCount()
        return usedCount < freeUsageCount
    }
    
    /// 消耗一次发型编辑使用次数
    /// - Returns: 是否成功消耗(true: 成功, false: 次数不足)
    func consumeHairEditUsage() -> Bool {
        // 如果是VIP用户，直接返回成功
        if APPMakeStoreIAPManager.featureVip() {
            print("✅ VIP用户使用发型编辑，无次数限制")
            return true
        }
        
        // 非VIP用户检查并消耗次数
        resetDailyUsageIfNeeded()
        let usedCount = getCurrentUsageCount()
        
        guard usedCount < freeUsageCount else {
            print("❌ 发型编辑免费次数已用完: \(usedCount)/\(freeUsageCount)")
            return false
        }
        
        // 消耗次数
        UserDefaults.standard.set(usedCount + 1, forKey: usageKey)
        let newUsedCount = usedCount + 1
        print("✅ 发型编辑次数消耗成功: \(newUsedCount)/\(freeUsageCount)")
        
        return true
    }
    
    /// 获取剩余发型编辑次数
    func getRemainingHairEditCount() -> Int {
        // 如果是VIP用户，返回无限制标识
        if APPMakeStoreIAPManager.featureVip() {
            return Int.max
        }
        
        // 非VIP用户返回剩余次数
        resetDailyUsageIfNeeded()
        let usedCount = getCurrentUsageCount()
        return max(0, freeUsageCount - usedCount)
    }
    
    /// 获取发型编辑使用状态描述
    func getHairEditStatusDescription() -> String {
        if APPMakeStoreIAPManager.featureVip() {
            return "VIP用户，无限制使用"
        } else {
            let remaining = getRemainingHairEditCount()
            return "免费用户，今日剩余: \(remaining)/\(freeUsageCount)"
        }
    }
    
    /// 重置每日使用次数(管理员功能)
    func resetDailyUsage() {
        UserDefaults.standard.set(0, forKey: usageKey)
        UserDefaults.standard.set(Date(), forKey: lastResetDateKey)
        print("🔄 发型编辑每日使用次数已重置")
    }
    
    // MARK: - 私有方法
    
    /// 获取当前已使用次数
    private func getCurrentUsageCount() -> Int {
        return UserDefaults.standard.integer(forKey: usageKey)
    }
    
    /// 检查并重置每日使用次数
    private func resetDailyUsageIfNeeded() {
        let currentDate = Date()
        let calendar = Calendar.current
        
        // 获取上次重置日期
        if let lastResetDate = UserDefaults.standard.object(forKey: lastResetDateKey) as? Date {
            // 检查是否跨天了
            if !calendar.isDate(lastResetDate, inSameDayAs: currentDate) {
                // 跨天了，重置使用次数
                UserDefaults.standard.set(0, forKey: usageKey)
                UserDefaults.standard.set(currentDate, forKey: lastResetDateKey)
                print("🌅 发型编辑次数已自动重置(新的一天)")
            }
        } else {
            // 首次使用，设置重置日期
            UserDefaults.standard.set(currentDate, forKey: lastResetDateKey)
        }
    }
}
