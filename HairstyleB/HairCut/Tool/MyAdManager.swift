//
//  MyAdManager.swift
//  phonetransSW
//
//  Created by fs0011 on 2024/8/12.
//

import Foundation
import Foundation
import BUAdSDK
import Alamofire

protocol MyAdManagerDelegate: AnyObject {
    func myAdManagerInterstitialAdCloseRefresh()
    func myAdManagerInterstitialAdRenderSuccess()
    func myAdManagerLoadInterstitialAdFailed()
    
    func myAdManagerFullscreenVideoAdCloseRefresh()
    func myAdManagerFullscreenVideoAdDidDownLoadVideo()
    func myAdManagerLoadFullscreenVideoAdFailed()
    
    func myAdManagerBannerViewCloseRefresh()
    func myAdManagerBannerAdViewRenderSuccess()
    func myAdManagerLoadBannerViewFailed()
    
    func myAdManagerRewardedVideoAdCloseIsVerify(_ verify: Bool)
    func myAdManagerRewardedVideoAdDidDownLoadVideo()
    func myAdManagerLoadRewardedVideoAdFailed()
}
extension MyAdManagerDelegate {
    func myAdManagerInterstitialAdCloseRefresh() {}
    func myAdManagerInterstitialAdRenderSuccess() {}
    func myAdManagerLoadInterstitialAdFailed() {}
    
    func myAdManagerFullscreenVideoAdCloseRefresh() {}
    func myAdManagerFullscreenVideoAdDidDownLoadVideo() {}
    func myAdManagerLoadFullscreenVideoAdFailed() {}
    
    func myAdManagerBannerViewCloseRefresh() {}
    func myAdManagerBannerAdViewRenderSuccess() {}
    func myAdManagerLoadBannerViewFailed() {}
    
    func myAdManagerRewardedVideoAdCloseIsVerify(_ verify: Bool) {}
    func myAdManagerRewardedVideoAdDidDownLoadVideo() {}
    func myAdManagerLoadRewardedVideoAdFailed() {}
}
class MyAdManager: NSObject, BUNativeExpressFullscreenVideoAdDelegate, BUNativeExpressBannerViewDelegate, BUNativeExpressRewardedVideoAdDelegate {
    
    weak var delegate: MyAdManagerDelegate?
    private var  viewController : UIViewController! = UIViewController();
    var isShowRewardedVideoAd = false
    
    private var fullscreenVideoAd: BUNativeExpressFullscreenVideoAd?
    private var bannerView: BUNativeExpressBannerView?
    private var rewardedAd: BUNativeExpressRewardedVideoAd?
    
    private var isVerify = false
    private var fullscreenVideoAdCount = 0
    private var bannerViewCount = 0
    private var rewardedAdCount = 0
    
    func showFullscreenVideoAd(with viewController: UIViewController) {
        self.fullscreenVideoAdCount = 0
        self.viewController = viewController
        if fullscreenVideoAd == nil {
            fullscreenVideoAd = BUNativeExpressFullscreenVideoAd(slotID: AD_Fullscreen)
            fullscreenVideoAd?.delegate = self
            fullscreenVideoAd?.loadData()
        }
    }
    
    func nativeExpressFullscreenVideoAdDidLoad(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        print("nativeExpressFullscreenVideoAdDidLoad")
    }
    
    func nativeExpressFullscreenVideoAdViewRenderSuccess(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        print("nativeExpressFullscreenVideoAdViewRenderSuccess")
    }
    
    func nativeExpressFullscreenVideoAd(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd, didFailWithError error: Error?) {
        print("nativeExpressFullscreenVideoAd failed with error: \(String(describing: error))")
        if let _ = self.fullscreenVideoAd {
            delegate?.myAdManagerLoadFullscreenVideoAdFailed()
        }
    }
    
    func nativeExpressFullscreenVideoAdViewRenderFail(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd, error: Error?) {
        print("nativeExpressFullscreenVideoAdViewRenderFail")
        if let _ = self.fullscreenVideoAd {
            delegate?.myAdManagerLoadFullscreenVideoAdFailed()
        }
    }
    
    func nativeExpressFullscreenVideoAdDidDownLoadVideo(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        print("nativeExpressFullscreenVideoAdDidDownLoadVideo")
        self.fullscreenVideoAdCount = 100
        
        DispatchQueue.main.async {
            self.fullscreenVideoAd?.show(fromRootViewController: self.viewController)
            self.delegate?.myAdManagerFullscreenVideoAdDidDownLoadVideo()
        }
    }
    
    func nativeExpressFullscreenVideoAdDidClose(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        self.fullscreenVideoAd = nil
    }
    
    func showBannerAd(with viewController: UIViewController, frame: CGRect, size: CGSize) {
        self.bannerViewCount = 0
        self.viewController = viewController
        if bannerView == nil {
            bannerView = BUNativeExpressBannerView(slotID: AD_Banner, rootViewController: viewController, adSize: size)
            bannerView?.frame = frame
            bannerView?.delegate = self
            bannerView?.loadAdData()
        }
    }
    
    func nativeExpressBannerAdViewRenderSuccess(_ bannerAdView: BUNativeExpressBannerView) {
        print("nativeExpressBannerAdViewRenderSuccess")
        self.bannerViewCount = 100
        self.viewController?.view.addSubview(self.bannerView!)
        delegate?.myAdManagerBannerAdViewRenderSuccess()
    }
    
    func nativeExpressBannerAdViewDidRemoved(_ bannerAdView: BUNativeExpressBannerView) {
        print("nativeExpressBannerAdViewDidRemoved")
        UIView.animate(withDuration: 0.3) {
            self.bannerView?.alpha = 0
        } completion: { finished in
            self.bannerView?.removeFromSuperview()
            self.bannerView = nil
            self.delegate?.myAdManagerBannerViewCloseRefresh()
        }
    }
    
    func nativeExpressBannerAdView(_ bannerAdView: BUNativeExpressBannerView, didLoadFailWithError error: Error?) {
        print("nativeExpressBannerAdView failed with error: \(String(describing: error))")
        if bannerViewCount < 5 {
            reloadBannerAdView()
        } else {
            delegate?.myAdManagerLoadBannerViewFailed()
        }
    }
    
    private func reloadBannerAdView() {
        bannerView?.loadAdData()
        bannerViewCount += 1
    }
    
    func showRewardedVideoAd(with viewController: UIViewController) {
        self.rewardedAdCount = 0
        
        let model = BURewardedVideoModel()
        model.userId = ""
        
        if rewardedAd == nil {
            rewardedAd = BUNativeExpressRewardedVideoAd(slotID: AdsCNSetting.rewardId, rewardedVideoModel: model)
            rewardedAd?.delegate = self
            self.viewController = viewController
            rewardedAd?.loadData()
        }
    }
    
    func nativeExpressRewardedVideoAdDidDownLoadVideo(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) {
        print("nativeExpressRewardedVideoAdDidDownLoadVideo")
        self.rewardedAdCount = 100
        
        DispatchQueue.main.async {
            self.rewardedAd?.show(fromRootViewController: self.viewController!)
            self.delegate?.myAdManagerRewardedVideoAdDidDownLoadVideo()
        }
    }
    
    func nativeExpressRewardedVideoAd(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd, didFailWithError error: Error?) {
        print("nativeExpressRewardedVideoAd failed with error: \(String(describing: error))")
        if rewardedAdCount < 8 {
            reloadRewardedVideoAd()
        } else {
            delegate?.myAdManagerLoadRewardedVideoAdFailed()
        }
    }
    
    private func reloadRewardedVideoAd() {
        rewardedAd?.loadData()
        rewardedAdCount += 1
    }
    
    func nativeExpressRewardedVideoAdDidClose(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) {
        print("nativeExpressRewardedVideoAdDidClose")
        self.rewardedAd = nil
        self.isShowRewardedVideoAd = false
        delegate?.myAdManagerRewardedVideoAdCloseIsVerify(isVerify)
    }
    
    func nativeExpressRewardedVideoAdServerRewardDidSucceed(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd, verify: Bool) {
        print("nativeExpressRewardedVideoAdServerRewardDidSucceed.verify = \(verify)")
        self.isVerify = verify
    }
}
