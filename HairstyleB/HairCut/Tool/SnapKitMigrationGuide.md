# SnapKit线程安全迁移指南

## 问题背景

项目中出现了多次AutoLayout线程安全崩溃，错误信息为：
```
CoreAutoLayout: _AssertAutoLayoutOnAllowedThreadsOnly + 316
```

这是因为在后台线程中调用了SnapKit的约束操作，违反了iOS的线程安全规则。

## 解决方案

### 1. 新增线程安全工具

已添加两个新文件：
- `ThreadSafeSnapKit.swift` - 全局线程安全函数
- `UIDefault.swift` - UIView扩展方法

### 2. 迁移方式

#### 方式一：使用全局函数（推荐）

**原代码：**
```swift
view.snp.makeConstraints { make in
    make.top.equalToSuperview()
    make.left.right.equalToSuperview().inset(20)
}
```

**新代码：**
```swift
safeConstraints(view) { make in
    make.top.equalToSuperview()
    make.left.right.equalToSuperview().inset(20)
}
```

#### 方式二：使用扩展方法

**原代码：**
```swift
view.snp.makeConstraints { make in
    make.center.equalToSuperview()
}
```

**新代码：**
```swift
view.safeSnpMakeConstraints { make in
    make.center.equalToSuperview()
}
```

### 3. 完整替换对照表

| 原方法 | 全局函数 | 扩展方法 |
|--------|----------|----------|
| `view.snp.makeConstraints` | `safeConstraints(view)` | `view.safeSnpMakeConstraints` |
| `view.snp.updateConstraints` | `safeUpdateConstraints(view)` | `view.safeSnpUpdateConstraints` |
| `view.snp.remakeConstraints` | `safeRemakeConstraints(view)` | `view.safeSnpRemakeConstraints` |
| `view.snp.removeConstraints()` | `safeRemoveConstraints(view)` | `view.safeSnpRemoveConstraints()` |

### 4. 其他UI操作的线程安全

除了约束，还提供了其他UI操作的线程安全方法：

```swift
// 文本设置
safeSetText(label, "新文本")
// 或
label.safeSetText("新文本")

// 图片设置
safeSetImage(imageView, newImage)
// 或
imageView.safeSetImage(newImage)

// 背景色设置
safeSetBackgroundColor(view, UIColor.red)
// 或
view.safeSetBackgroundColor(UIColor.red)

// 隐藏/显示
safeSetHidden(view, true)
// 或
view.safeSetHidden(true)

// 布局更新
safeLayoutIfNeeded(view)
// 或
view.safeLayoutIfNeeded()
```

### 5. 批量操作

对于需要同时进行多个UI操作的情况：

```swift
safeBatchUIOperations {
    safeConstraints(view1) { make in
        make.top.equalToSuperview()
    }
    safeSetText(label, "新文本")
    safeSetHidden(button, false)
}
```

### 6. 调试工具

启用调试模式来追踪AutoLayout操作：

```swift
// 在AppDelegate中启用
setAutoLayoutDebugging(enabled: true)
```

### 7. 需要替换的文件列表

根据代码检索，以下文件包含SnapKit调用，需要逐一检查和替换：

1. `HairCut/Demo/NailProcessDemoViewController.swift`
2. `HairCut/View/UsagePurchasePopupView.swift`
3. `HairCut/View/拍照/NailCameraViewController.swift`
4. `HairCut/VC/HairEditVC.swift`
5. `HairCut/View/首页/HomeView.swift`
6. `HairCut/VC/HomeVC.swift`
7. `HairCut/View/AI换发/AIHairCutView.swift`
8. `HairCut/VC/FaceShapeTestVC.swift`
9. `HairCut/VC/HairStyleVC.swift`
10. `HairCut/Tool/header/UIDefault.swift`

### 8. 自动替换脚本

可以使用以下正则表达式进行批量替换：

**查找：** `(\w+)\.snp\.makeConstraints`
**替换：** `safeConstraints($1)`

**查找：** `(\w+)\.snp\.updateConstraints`
**替换：** `safeUpdateConstraints($1)`

**查找：** `(\w+)\.snp\.remakeConstraints`
**替换：** `safeRemakeConstraints($1)`

### 9. 验证方法

替换完成后，可以通过以下方式验证：

1. 启用调试模式：`setAutoLayoutDebugging(enabled: true)`
2. 在控制台查看是否有线程安全警告
3. 运行应用，检查是否还有AutoLayout崩溃

### 10. 注意事项

1. **不要混用**：选择一种方式（全局函数或扩展方法）并保持一致
2. **测试充分**：替换后要充分测试所有UI功能
3. **逐步替换**：建议分批次替换，每次替换后进行测试
4. **保留备份**：替换前建议提交代码或创建备份

## 实施计划

### 第一阶段：核心文件
- `HomeView.swift`
- `BeautyEditVC.swift`
- `AIHairCutView.swift`

### 第二阶段：其他UI文件
- 所有包含SnapKit的ViewController
- 自定义View文件

### 第三阶段：验证和优化
- 全面测试
- 性能优化
- 清理旧代码

通过这个迁移，可以彻底解决AutoLayout线程安全问题，防止应用崩溃。
