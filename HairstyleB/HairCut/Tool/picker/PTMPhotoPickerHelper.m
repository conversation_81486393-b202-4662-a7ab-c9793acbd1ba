//
//  PTMPhotoPickerHelper.m
//  clearMosaic
//
//  Created by fs0011 on 2024/7/16.
//

#import "PTMPhotoPickerHelper.h"
#import "HXPhotoPicker.h"
//#import "HairCut-Swift.h"

@implementation PTMPhotoPickerHelper

+ (void)presentPhotoPickerFromViewController:(UIViewController *)viewController
                                  completion:(void (^)(UIImage * _Nullable image))completion {
    // 创建HXPhotoManager配置
    HXPhotoManager *manager = [[HXPhotoManager alloc] init];
    manager.type = HXPhotoManagerSelectedTypePhoto;
    manager.configuration.photoMaxNum = 1;
    manager.configuration.singleSelected = YES;
    manager.configuration.singleJumpEdit = NO;
    manager.configuration.showOriginalBytes = NO;
    
    // 使用HXPhotoPicker选择照片
    [viewController hx_presentSelectPhotoControllerWithManager:manager
                                                      didDone:^(NSArray<HXPhotoModel *> * _Nullable allList,
                                                                NSArray<HXPhotoModel *> * _Nullable photoList,
                                                                NSArray<HXPhotoModel *> * _Nullable videoList,
                                                                BOOL isOriginal,
                                                                UIViewController * _Nullable vc,
                                                                HXPhotoManager * _Nullable manager) {
        // 获取选中的照片
        if (photoList.count > 0) {
            HXPhotoModel *photoModel = photoList.firstObject;
            [photoModel requestPreviewImageWithSize:PHImageManagerMaximumSize startRequestICloud:nil progressHandler:nil success:^(UIImage * _Nullable image, HXPhotoModel * _Nullable model, NSDictionary * _Nullable info) {
                if (image && completion) {
                    completion(image);
                }
            } failed:^(NSDictionary * _Nullable info, HXPhotoModel * _Nullable model) {

            }];
            
        } else {
            if (completion) {
                completion(nil);
            }
        }
    } cancel:^(UIViewController * _Nullable vc, HXPhotoManager * _Nullable manager) {
        if (completion) {
            completion(nil);
        }
    }];
}

@end 
