//
//  PTMImageSliderView.m
//  photoTimeMachine
//
//  Created by fs0011 on 2023/6/13.
//

#import "PTMImageSliderView.h"

@interface PTMImageSliderView ()

@property (nonatomic, strong) UIImageView *beforeImageView;
@property (nonatomic, strong) UIImageView *afterImageView;
@property (nonatomic, strong) UIImageView *beforeImageViewOr;
@property (nonatomic, strong) UIImageView *afterImageViewOr;
@property (nonatomic, strong) UIView *lineView;
@property (nonatomic, strong) UIButton *sliderButton;

@end
@implementation PTMImageSliderView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        // 设置背景颜色为白色
        self.backgroundColor = [UIColor whiteColor];
        
        // 创建前后两个ImageView
        
        self.beforeImageViewOr = [[UIImageView alloc] initWithFrame:self.bounds];
        self.beforeImageViewOr.contentMode = UIViewContentModeScaleAspectFit;
        self.afterImageViewOr = [[UIImageView alloc] initWithFrame:self.bounds];
        self.afterImageViewOr.contentMode = UIViewContentModeScaleAspectFit;
        
        self.beforeImageView = [[UIImageView alloc] initWithFrame:self.bounds];
        self.beforeImageView.contentMode = UIViewContentModeScaleAspectFit;
        self.afterImageView = [[UIImageView alloc] initWithFrame:self.bounds];
        self.afterImageView.contentMode = UIViewContentModeScaleAspectFit;
        
        // 创建滑动按钮
        CGFloat sliderButtonWidth = 40.0;
        CGFloat sliderButtonHeight = 40.0;
        self.sliderButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, sliderButtonWidth, sliderButtonHeight)];
        [self.sliderButton setBackgroundImage:[UIImage imageNamed:@"拖动对比"] forState:0];
        //        self.sliderButton.backgroundColor = [UIColor lightGrayColor];
        self.sliderButton.center = CGPointMake(CGRectGetWidth(self.bounds) / 2, CGRectGetHeight(self.bounds)- 40-7);
        UIPanGestureRecognizer *panGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGesture:)];
        [self.sliderButton addGestureRecognizer:panGesture];
        
        // 创建白色线
        CGFloat lineWidth = 2.0;
        CGFloat lineHeight = self.bounds.size.height;
        self.lineView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, lineWidth, lineHeight)];
        self.lineView.backgroundColor = [UIColor whiteColor];
        self.lineView.center = CGPointMake(CGRectGetWidth(self.bounds) / 2, CGRectGetHeight(self.bounds)/2);
        
        // 添加视图到ImageSliderView
        [self addSubview:self.afterImageView];
        [self addSubview:self.beforeImageView];
        
        [self addSubview:self.lineView];
        [self addSubview:self.sliderButton];
        
        
        UILabel* la = [UILabel createLabelWithTitle:NSLocalizedString(@"制作前", nil) textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentCenter font:[UIFont systemFontOfSize:12]];
        la.backgroundColor = [[UIColor blackColor]colorWithAlphaComponent:0.5];
        la.layer.cornerRadius = 11;
        la.layer.masksToBounds = YES;
        [self addSubview:la];
        [la mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(46);
            make.height.mas_equalTo(22);
            make.top.mas_equalTo(12);
            make.left.mas_equalTo(16);
        }];
        
        UILabel* la2 = [UILabel createLabelWithTitle:NSLocalizedString(@"制作后", nil) textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentCenter font:[UIFont systemFontOfSize:12]];
        la2.backgroundColor = [[UIColor blackColor]colorWithAlphaComponent:0.5];
        la2.layer.cornerRadius = 11;
        la2.layer.masksToBounds = YES;
        [self addSubview:la2];
        [la2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(46);
            make.height.mas_equalTo(22);
            make.top.mas_equalTo(12);
            make.right.mas_equalTo(-16);
        }];
    
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame beforeImage:(UIImage *)beforeImage afterImage:(UIImage *)afterImage {
    self = [self initWithFrame:frame];
    if (self) {
        
        //        self.beforeImageViewOr.image = beforeImage;
        //        self.afterImageViewOr.image = afterImage;
        //        self.beforeImage = beforeImage;
        //        self.afterImage = afterImage;
    }
    return self;
}

- (void)setBeforeImage:(UIImage *)beforeImage {
    self.beforeImageViewOr.image = beforeImage;
    
    
    _beforeImage = beforeImage;
    self.beforeImageView.image = beforeImage;
    [self updateImageFrames];
}

- (void)setAfterImage:(UIImage *)afterImage {
    
    self.afterImageViewOr.image = afterImage;
    _afterImage = afterImage;
    self.afterImageView.image = afterImage;
    [self updateImageFrames];
}

- (void)updateImageFrames {
    CGFloat sliderX = self.sliderButton.center.x;
    CGFloat beforeImageWidth = sliderX;
    CGFloat afterImageWidth = CGRectGetWidth(self.bounds) - beforeImageWidth;
    
    CGRect beforeFrame = CGRectMake(0, 0, beforeImageWidth, CGRectGetHeight(self.bounds));
    CGRect afterFrame = CGRectMake(beforeImageWidth, 0, afterImageWidth, CGRectGetHeight(self.bounds));
    
    self.beforeImageView.frame = beforeFrame;
    
    self.beforeImageView.image = [self cropImageFromImageView:self.beforeImageViewOr withRect:beforeFrame];
    NSLog(@"---%@",@(self.beforeImageView.frame));
//    self.afterImageView.frame = afterFrame;
//    self.afterImageView.image = [self cropImageFromImageView:self.afterImageViewOr withRect:afterFrame];
}

- (void)handlePanGesture:(UIPanGestureRecognizer *)gesture {
    NSLog(@"%@",@(self.beforeImageView.frame));
    CGPoint translation = [gesture translationInView:self];
    CGPoint newCenter = CGPointMake(self.lineView.center.x + translation.x, self.lineView.center.y);
    
    // 限制滑动范围在控件内
    CGFloat minX = [UIScreen mainScreen].bounds.size.width-[self contentWidthForImageView:self.afterImageViewOr];
    CGFloat maxX = [self contentWidthForImageView:self.afterImageViewOr];
    newCenter.x = fmax(minX, fmin(newCenter.x, maxX));
    NSLog(@"%@",@(newCenter));
    self.lineView.center = newCenter;
    [gesture setTranslation:CGPointZero inView:self];
    newCenter = CGPointMake(self.lineView.center.x , self.sliderButton.center.y);
    self.sliderButton.center = newCenter;
    
    // 更新图片显示
//    CGFloat progress = (newCenter.x - minX) / (maxX - minX);
    CGFloat newWidth = MAX(0, CGRectGetMinX(self.lineView.frame)); // 确保宽度不为负数
        CGRect beforeFrame = self.beforeImageView.frame;
        beforeFrame.size.width = newWidth;
        self.beforeImageView.frame = beforeFrame;
    
    self.beforeImageView.image = [self cropImageFromImageView:self.beforeImageViewOr withRect:beforeFrame];
    
//    CGRect afterFrame = self.afterImageView.frame;
//    afterFrame.origin.x = CGRectGetMaxX(self.lineView.frame);
//    afterFrame.size.width = CGRectGetWidth(self.bounds) - CGRectGetMaxX(self.lineView.frame);
    
    
//    self.afterImageView.frame = afterFrame;
//    self.afterImageView.image = [self cropImageFromImageView:self.afterImageViewOr withRect:afterFrame];
}
/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */
- (UIImage *)cropImageFromImageView:(UIImageView *)imageView withRect:(CGRect)cropRect {
    CGSize imageSize = imageView.image.size;
        CGSize viewSize = imageView.frame.size;
        CGFloat imageAspectRatio = imageSize.width / imageSize.height;
        CGFloat viewAspectRatio = viewSize.width / viewSize.height;

        CGFloat scaleFactor;
        CGPoint offset = CGPointZero;

        // Determine the scale factor and offset to center the image in the view.
        if (imageAspectRatio > viewAspectRatio) {
            // Image is wider than the view aspect ratio
            scaleFactor = viewSize.width / imageSize.width;
            CGFloat scaledImageHeight = imageSize.height * scaleFactor;
            offset.y = (viewSize.height - scaledImageHeight) / 2.0;
        } else {
            // Image is taller than the view aspect ratio
            scaleFactor = viewSize.height / imageSize.height;
            CGFloat scaledImageWidth = imageSize.width * scaleFactor;
            offset.x = (viewSize.width - scaledImageWidth) / 2.0;
        }

        // Convert the crop rectangle from the view's coordinate space to the image's coordinate space
        CGRect scaledCropRect = CGRectMake((cropRect.origin.x - offset.x) / scaleFactor,
                                           (cropRect.origin.y - offset.y) / scaleFactor,
                                           cropRect.size.width / scaleFactor,
                                           cropRect.size.height / scaleFactor);

        // Crop the image
        CGImageRef croppedCGImage = CGImageCreateWithImageInRect(imageView.image.CGImage, scaledCropRect);
        UIImage *croppedImage = [UIImage imageWithCGImage:croppedCGImage];
        CGImageRelease(croppedCGImage);

        // Recreate the image with additional space on the left
        if (imageAspectRatio < viewAspectRatio) {
            CGSize finalSize = CGSizeMake(croppedImage.size.width + offset.x / scaleFactor, croppedImage.size.height);
            if(finalSize.width == 0)
            {
                finalSize.width = 0.5;
            }
            if(finalSize.height == 0)
            {
                finalSize.height = 0.5;
            }
            //ios 17不能输入0
            UIGraphicsBeginImageContextWithOptions(finalSize, NO, croppedImage.scale);
            [croppedImage drawInRect:CGRectMake(offset.x / scaleFactor, 0, croppedImage.size.width, croppedImage.size.height)];
            croppedImage = UIGraphicsGetImageFromCurrentImageContext();
            UIGraphicsEndImageContext();
        }

        return croppedImage;
}


- (CGFloat)contentWidthForImageView:(UIImageView *)imageView {
    CGSize imageSize = imageView.image.size;
        CGSize viewSize = imageView.frame.size;
        CGFloat imageAspectRatio = imageSize.width / imageSize.height;
        CGFloat viewAspectRatio = viewSize.width / viewSize.height;

        CGFloat contentWidth;
        CGFloat offsetX = 0;

        // Determine the content width and offsetX based on the image's aspect ratio relative to the view's aspect ratio
        if (imageAspectRatio > viewAspectRatio) {
            // Image is wider than the view aspect ratio
            contentWidth = viewSize.width;
        } else {
            // Image is taller than the view aspect ratio
            CGFloat scaleFactor = viewSize.height / imageSize.height;
            contentWidth = imageSize.width * scaleFactor;
            offsetX = (viewSize.width - contentWidth) / 2.0;
        }

        // Calculate the x value of the rightmost edge of the content
        CGFloat contentRightX = offsetX + contentWidth;
        
        return contentRightX;
}







@end
