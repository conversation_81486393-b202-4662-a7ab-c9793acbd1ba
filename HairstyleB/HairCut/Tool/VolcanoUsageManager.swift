//
//  VolcanoUsageManager.swift
//  HairCut
//
//  Created by AI Assistant on 2025/01/21.
//

import Foundation

// MARK: - 通知名称定义
extension Notification.Name {
    /// 剩余次数更新通知
    static let usageCountDidUpdate = Notification.Name("usageCountDidUpdate")
    /// 订阅状态更新通知
    static let subscriptionStatusDidUpdate = Notification.Name("subscriptionStatusDidUpdate")
}

// MARK: - 火山引擎使用次数配置模型
struct VolcanoUsageConfig: Codable {
    var defaultFreeCount: Int = 1           // 未订阅用户默认次数
    var freeTrialCount: Int = 10            // 3天免费试用期次数
    var subscribedCount: Int = 20           // 订阅用户每月次数
    var resetDay: Int = 1                   // 每月重置日期(1-28)
    
    // 可通过JSON修改的其他参数
    var enableUsageLimit: Bool = true       // 是否启用次数限制
    var warningThreshold: Int = 3           // 剩余次数警告阈值
}

// MARK: - 用户状态记录模型
struct UserSubscriptionRecord: Codable {
    var lastSubscriptionDate: Date?         // 上次订阅记录时间
    var lastFreeTrialDate: Date?           // 上次免费试用记录时间
    var currentMonthUsage: Int = 0         // 当月已使用次数
    var lastResetDate: Date?               // 上次重置时间
    var subscriptionStatus: SubscriptionStatus = .none
    
    enum SubscriptionStatus: String, Codable {
        case none = "none"                  // 未订阅
        case freeTrial = "freeTrial"        // 免费试用中
        case subscribed = "subscribed"      // 已订阅
    }
}

// MARK: - 火山引擎使用次数管理器
class VolcanoUsageManager {
    
    // MARK: - 单例
    static let shared = VolcanoUsageManager()
    private init() {
        // 先加载用户记录和默认配置
        loadUserRecord()
        config = VolcanoUsageConfig() // 使用默认配置

        // 检查是否是首次使用次数系统的VIP用户
        checkAndInitializeVipUserUsage()

        checkAndUpdateSubscriptionStatus()

        // 异步加载在线配置
        loadConfig()
    }
    
    // MARK: - 私有属性
    private var config: VolcanoUsageConfig = VolcanoUsageConfig()
    private var userRecord: UserSubscriptionRecord = UserSubscriptionRecord()
    
    private let configFileName = "volcano_usage_config"
    private let userRecordKey = "VolcanoUserSubscriptionRecord"
    private let onlineConfigURL = "https://controls.oss-cn-hangzhou.aliyuncs.com/ios-hair-cut-count.json"
    private let purchasedCountKey = "VolcanoPurchasedCount"  // 购买的次数
    private let usageSystemVersionKey = "VolcanoUsageSystemVersion"  // 次数系统版本标记
    
    // MARK: - 公共方法
    
    /// 获取当前剩余次数
    func getRemainingCount() -> Int {
        checkAndUpdateSubscriptionStatus()
        checkAndResetMonthlyUsage()

        let totalCount = getCurrentMonthTotalCount()
        let purchasedCount = getPurchasedCount()
        let remaining = max(0, totalCount + purchasedCount - userRecord.currentMonthUsage)

        print("🔥 火山引擎剩余次数: \(remaining), 基础: \(totalCount), 购买: \(purchasedCount), 已用: \(userRecord.currentMonthUsage)")
        return remaining
    }
    
    /// 消耗一次使用次数
    /// - Returns: 是否成功消耗(true: 成功, false: 次数不足)
    func consumeUsage() -> Bool {
        guard config.enableUsageLimit else {
            print("🔥 火山引擎次数限制已禁用，允许使用")
            return true
        }
        
        let remaining = getRemainingCount()
        guard remaining > 0 else {
            print("❌ 火山引擎次数不足，无法使用")
            return false
        }
        
        userRecord.currentMonthUsage += 1
        saveUserRecord()
        
        let newRemaining = remaining - 1
        print("✅ 火山引擎使用次数消耗成功，剩余: \(newRemaining)")
        
        // 检查是否需要警告
        if newRemaining <= config.warningThreshold && newRemaining > 0 {
            print("⚠️ 火山引擎剩余次数不足，仅剩: \(newRemaining)")
        }
        
        return true
    }
    
    /// 获取当前订阅状态描述
    func getSubscriptionStatusDescription() -> String {
        checkAndUpdateSubscriptionStatus()
        
        switch userRecord.subscriptionStatus {
        case .none:
            return "未订阅用户"
        case .freeTrial:
            return "3天免费试用中"
        case .subscribed:
            return "已订阅用户"
        }
    }
    
    /// 手动刷新订阅状态(用于订阅状态变化后调用)
    func refreshSubscriptionStatus() {
        checkAndUpdateSubscriptionStatus()
        checkAndResetMonthlyUsage()
        saveUserRecord()
        
        print("🔄 火山引擎订阅状态已刷新: \(getSubscriptionStatusDescription())")
    }
    
    /// 获取配置信息(用于调试)
    func getConfigInfo() -> String {
        return """
        火山引擎使用次数配置:
        - 未订阅默认次数: \(config.defaultFreeCount)
        - 免费试用次数: \(config.freeTrialCount)
        - 订阅用户次数: \(config.subscribedCount)
        - 每月重置日: \(config.resetDay)号
        - 启用次数限制: \(config.enableUsageLimit)
        - 当前状态: \(getSubscriptionStatusDescription())
        - 当月已用: \(userRecord.currentMonthUsage)
        - 剩余次数: \(getRemainingCount())
        """
    }

    /// 检查是否可以使用(不消耗次数)
    func canUse() -> Bool {
        return !config.enableUsageLimit || getRemainingCount() > 0
    }

    /// 获取使用次数不足的提示信息
    func getUsageLimitMessage() -> String {
        let remaining = getRemainingCount()

        if remaining <= 0 {
            switch userRecord.subscriptionStatus {
            case .none:
                return "今日免费次数已用完，订阅会员可获得更多使用次数"
            case .freeTrial:
                return "免费试用次数已用完，续费会员可获得更多使用次数"
            case .subscribed:
                return "本月使用次数已用完，下月自动重置"
            }
        } else if remaining <= config.warningThreshold {
            return "剩余使用次数不多，仅剩\(remaining)次"
        }

        return ""
    }

    /// 重置当月使用次数(管理员功能)
    func resetMonthlyUsage() {
        userRecord.currentMonthUsage = 0
        userRecord.lastResetDate = Date()
        saveUserRecord()
        print("🔄 管理员重置月度使用次数")
    }

    /// 手动设置使用次数(管理员功能)
    func setUsageCount(_ count: Int) {
        userRecord.currentMonthUsage = max(0, count)
        saveUserRecord()
        print("⚙️ 管理员设置使用次数为: \(count)")
    }

    /// 手动刷新在线配置
    func refreshOnlineConfig(completion: @escaping (Bool) -> Void) {
        loadOnlineConfig { [weak self] onlineConfig in
            if let config = onlineConfig {
                self?.config = config
                print("🔄 在线配置刷新成功")
                completion(true)
            } else {
                print("❌ 在线配置刷新失败")
                completion(false)
            }
        }
    }

    /// 购买次数包(固定10次)
    func purchaseUsagePackage() {
        let currentPurchased = getPurchasedCount()
        let newPurchased = currentPurchased + 10
        UserDefaults.standard.set(newPurchased, forKey: purchasedCountKey)
        print("💎 次数购买成功，新增10次，总购买次数: \(newPurchased)")

        // 发送次数更新通知
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: .usageCountDidUpdate, object: nil)
        }
    }

    /// 获取已购买的次数
    func getPurchasedCount() -> Int {
        return UserDefaults.standard.integer(forKey: purchasedCountKey)
    }

    /// 重置购买次数(管理员功能)
    func resetPurchasedCount() {
        UserDefaults.standard.set(0, forKey: purchasedCountKey)
        print("🔄 购买次数已重置")
    }

    /// 重置次数系统版本标记(管理员功能，用于测试VIP用户初始化)
    func resetUsageSystemVersion() {
        UserDefaults.standard.removeObject(forKey: usageSystemVersionKey)
        UserDefaults.standard.removeObject(forKey: userRecordKey)
        print("🔄 次数系统版本标记已重置，下次启动将重新初始化")
    }
    
    // MARK: - 私有方法
    
    /// 加载配置文件
    private func loadConfig() {
        // 首先尝试从在线JSON获取配置
        loadOnlineConfig { [weak self] onlineConfig in
            if let config = onlineConfig {
                self?.config = config
                print("✅ 成功加载在线火山引擎配置")
                return
            }

            // 在线配置失败，尝试加载本地JSON配置
            if let localConfig = self?.loadLocalConfig() {
                self?.config = localConfig
                print("✅ 成功加载本地火山引擎配置")
                return
            }

            // 使用默认配置
            self?.config = VolcanoUsageConfig()
            print("⚠️ 使用默认火山引擎配置")
        }
    }
    
    /// 从本地JSON文件加载配置
    private func loadLocalConfig() -> VolcanoUsageConfig? {
        guard let path = Bundle.main.path(forResource: configFileName, ofType: "json"),
              let data = NSData(contentsOfFile: path) as Data?,
              let config = try? JSONDecoder().decode(VolcanoUsageConfig.self, from: data) else {
            return nil
        }
        return config
    }
    
    /// 从在线JSON加载配置
    private func loadOnlineConfig(completion: @escaping (VolcanoUsageConfig?) -> Void) {
        guard let url = URL(string: onlineConfigURL) else {
            print("❌ 在线配置URL无效")
            completion(nil)
            return
        }

        print("🌐 开始获取在线火山引擎配置: \(onlineConfigURL)")

        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ 在线配置获取失败: \(error.localizedDescription)")
                    completion(nil)
                    return
                }

                guard let data = data else {
                    print("❌ 在线配置数据为空")
                    completion(nil)
                    return
                }

                do {
                    let config = try JSONDecoder().decode(VolcanoUsageConfig.self, from: data)
                    print("✅ 在线配置解析成功")
                    print("   - 未订阅默认次数: \(config.defaultFreeCount)")
                    print("   - 免费试用次数: \(config.freeTrialCount)")
                    print("   - 订阅用户次数: \(config.subscribedCount)")
                    print("   - 启用次数限制: \(config.enableUsageLimit)")
                    completion(config)
                } catch {
                    print("❌ 在线配置解析失败: \(error.localizedDescription)")
                    if let jsonString = String(data: data, encoding: .utf8) {
                        print("📄 原始JSON数据: \(jsonString)")
                    }
                    completion(nil)
                }
            }
        }

        task.resume()
    }
    
    /// 加载用户记录
    private func loadUserRecord() {
        guard let data = UserDefaults.standard.data(forKey: userRecordKey),
              let record = try? JSONDecoder().decode(UserSubscriptionRecord.self, from: data) else {
            userRecord = UserSubscriptionRecord()
            return
        }
        userRecord = record
    }
    
    /// 保存用户记录
    private func saveUserRecord() {
        guard let data = try? JSONEncoder().encode(userRecord) else { return }
        UserDefaults.standard.set(data, forKey: userRecordKey)
    }
    
    /// 检查并初始化VIP用户的使用次数（用于版本更新后的已订阅用户）
    private func checkAndInitializeVipUserUsage() {
        let isVip = APPMakeStoreIAPManager.featureVip()
        let currentSystemVersion = "1.0"  // 次数系统版本号
        let savedSystemVersion = UserDefaults.standard.string(forKey: usageSystemVersionKey)

        // 检查是否是首次使用次数系统
        let isFirstTimeUsingUsageSystem = (savedSystemVersion == nil)

        if isVip && isFirstTimeUsingUsageSystem {
            print("🎉 检测到VIP用户首次使用次数系统，初始化基础次数")

            // 初始化为订阅用户状态
            userRecord.subscriptionStatus = .subscribed
            userRecord.lastSubscriptionDate = Date()
            userRecord.currentMonthUsage = 0  // 给予完整的基础次数
            userRecord.lastResetDate = Date()

            // 标记次数系统版本
            UserDefaults.standard.set(currentSystemVersion, forKey: usageSystemVersionKey)

            // 保存记录
            saveUserRecord()

            print("✅ VIP用户次数初始化完成：订阅状态，当月剩余次数: \(getCurrentMonthTotalCount())")
        } else if !isVip && isFirstTimeUsingUsageSystem {
            print("👤 非VIP用户首次使用次数系统，标记版本")
            // 非VIP用户也需要标记版本，避免后续误判
            UserDefaults.standard.set(currentSystemVersion, forKey: usageSystemVersionKey)
        } else {
            print("📋 用户已使用过次数系统，跳过初始化")
        }
    }

    /// 检查并更新订阅状态
    private func checkAndUpdateSubscriptionStatus() {
        let isVip = APPMakeStoreIAPManager.featureVip()
        let currentDate = Date()

        if isVip {
            // 用户当前有VIP状态

            // 检查VIP过期时间，判断是否为长期订阅
            if let vipExpiresDate = UserDefaults.standard.object(forKey: "VipExpiresDate") as? Date {
                let currentDate = Date()
                let daysDifference = Calendar.current.dateComponents([.day], from: currentDate, to: vipExpiresDate).day ?? 0

                // 如果VIP有效期超过7天，认为是正式订阅（年费或月费），不再按免费试用处理
                if daysDifference > 7 {
                    // 正式订阅用户
                    if userRecord.subscriptionStatus != .subscribed {
                        // 从其他状态转为正式订阅
                        let previousStatus = userRecord.subscriptionStatus
                        userRecord.subscriptionStatus = .subscribed
                        userRecord.lastSubscriptionDate = currentDate

                        // 从任何状态转为正式订阅都重置次数，给予完整的20次基础次数
                        userRecord.currentMonthUsage = 0
                        print("🎉 用户从\(previousStatus)转为正式订阅，重置次数给予20次基础次数")
                    } else if let lastDate = userRecord.lastSubscriptionDate,
                              Calendar.current.dateComponents([.month], from: lastDate, to: currentDate).month ?? 0 >= 1 {
                        // 订阅状态超过一个月，重置次数
                        userRecord.lastSubscriptionDate = currentDate
                        userRecord.currentMonthUsage = 0
                        print("📅 订阅用户月度重置")
                    }
                } else if isInFreeTrialPeriod() {
                    // 在免费试用期内
                    if userRecord.subscriptionStatus != .freeTrial {
                        userRecord.subscriptionStatus = .freeTrial
                        userRecord.currentMonthUsage = 0
                        print("🆓 用户在3天免费试用期内")
                    }
                } else {
                    // VIP有效期较短但不在试用期，按正式订阅处理
                    if userRecord.subscriptionStatus != .subscribed {
                        let previousStatus = userRecord.subscriptionStatus
                        userRecord.subscriptionStatus = .subscribed
                        userRecord.lastSubscriptionDate = currentDate

                        // 从任何状态转为正式订阅都重置次数，给予完整的20次基础次数
                        userRecord.currentMonthUsage = 0
                        print("🎉 用户从\(previousStatus)转为正式订阅，重置次数给予20次基础次数")
                    }
                }
            }
        } else {
            // 用户当前没有VIP状态
            if userRecord.subscriptionStatus == .subscribed || userRecord.subscriptionStatus == .freeTrial {
                // 从订阅状态变为未订阅
                userRecord.subscriptionStatus = .none
                userRecord.currentMonthUsage = 0
                print("📉 用户VIP已过期，转为未订阅状态")
            }
        }
    }
    
    /// 检查是否可以给予免费试用
    private func canGrantFreeTrial() -> Bool {
        // 检查是否有VIP过期时间，如果有说明可能在免费试用期
        guard let vipExpiresDate = UserDefaults.standard.object(forKey: "VipExpiresDate") as? Date else {
            return false
        }

        let currentDate = Date()

        // 如果VIP还没过期，可能是在免费试用期
        if vipExpiresDate > currentDate {
            // 检查是否是首次免费试用(避免重复给予)
            if userRecord.lastFreeTrialDate == nil {
                return true
            }

            // 检查距离上次免费试用是否超过一定时间(比如30天)
            if let lastTrialDate = userRecord.lastFreeTrialDate {
                let daysSinceLastTrial = Calendar.current.dateComponents([.day], from: lastTrialDate, to: currentDate).day ?? 0
                return daysSinceLastTrial > 30
            }
        }

        return false
    }

    /// 检查用户是否在免费试用期内
    private func isInFreeTrialPeriod() -> Bool {
        guard let vipExpiresDate = UserDefaults.standard.object(forKey: "VipExpiresDate") as? Date,
              let lastTrialDate = userRecord.lastFreeTrialDate else {
            return false
        }

        let currentDate = Date()
        let trialEndDate = Calendar.current.date(byAdding: .day, value: 3, to: lastTrialDate) ?? lastTrialDate

        // 在试用期内且VIP还有效
        return currentDate <= trialEndDate && vipExpiresDate > currentDate
    }
    
    /// 检查并重置月度使用次数
    private func checkAndResetMonthlyUsage() {
        let currentDate = Date()
        let calendar = Calendar.current
        
        // 检查是否需要重置月度使用次数
        if let lastResetDate = userRecord.lastResetDate {
            let components = calendar.dateComponents([.month, .day], from: lastResetDate, to: currentDate)
            
            // 如果跨月了，或者到了重置日期
            if components.month ?? 0 > 0 || 
               (components.month == 0 && calendar.component(.day, from: currentDate) >= config.resetDay &&
                calendar.component(.day, from: lastResetDate) < config.resetDay) {
                userRecord.currentMonthUsage = 0
                userRecord.lastResetDate = currentDate
                print("🔄 月度使用次数已重置")
            }
        } else {
            userRecord.lastResetDate = currentDate
        }
    }
    
    /// 获取当月总次数
    private func getCurrentMonthTotalCount() -> Int {
        switch userRecord.subscriptionStatus {
        case .none:
            return config.defaultFreeCount
        case .freeTrial:
            return config.freeTrialCount
        case .subscribed:
            return config.subscribedCount
        }
    }
}
