//
//  GoodRepulationTool.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/10/8.
//

import Foundation
import StoreKit

class GoodRepulationTool {
    public static func showGoodRepulation(isNeedResult: Bool = false) {
        //非结果页面且第二次进入app时，10秒后弹出评分弹窗
        if isNeedResult == false && UserDefaultsTool.readTemporaryString(key: UserDefaultsConst.firstUse) != nil {
            DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 10) {
                printLog(message: "首页评分弹窗")
                showRatingView()
            }
            return
        }
        
        printLog(message: "结果页面评分弹窗")
        showRatingView()
    }
    
    private static func showRatingView() {
        if #available(iOS 14.0, *) {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                SKStoreReviewController.requestReview(in: windowScene)
            }
        } else {
            SKStoreReviewController.requestReview()
        }
    }
}
