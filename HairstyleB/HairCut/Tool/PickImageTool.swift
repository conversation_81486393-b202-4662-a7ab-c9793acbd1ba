//
//  PickImageTool.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/4.
//

import Foundation
import Promises
import AssetsLibrary
import Photos
import PhotosUI
import BSImagePicker

class PickImageTool: NSObject {
    private typealias PickImageCallback = (PickImageTool.Result) -> Void
    private typealias AlbumCallback = (Bool) -> Void

    private var callback: PickImageCallback?
    private var albumCallback: AlbumCallback?
    
    public enum AuthorizeStatus {
        case notDetermined
        case restricted
        case denied
        case authorized
    }
    
    public struct Result {
        let authorizeStatus: AuthorizeStatus
        let images: [UIImage]
    }
    
    /// 获取相机权限
    /// - Returns: 返回权限状态
    public static func cameraStatus() -> PickImageTool.AuthorizeStatus {
        let status = AVCaptureDevice.authorizationStatus(for: AVMediaType.video)
        switch status {
        case .authorized:    return .authorized
        case .restricted:    return .restricted
        case .denied:        return .denied
        case .notDetermined: return .notDetermined
        default: return .notDetermined
        }
    }
    
    /// 获取相册权限
    /// - Returns: 返回权限状态
    public static func albumStatus() -> PickImageTool.AuthorizeStatus {
        let phAuthStatus = PHPhotoLibrary.authorizationStatus()
        switch phAuthStatus {
        case .denied: return .denied
        case .restricted: return .restricted
        case .authorized: return .authorized
        default: return .notDetermined
        }
    }
    
    /// 保存图片到相册
    /// - Parameter image: 图片
    public func saveImageToAlbum(_ image: UIImage, callback: @escaping (Bool) -> Void) {
        self.albumCallback = callback
        UIImageWriteToSavedPhotosAlbum(image, self, #selector(PickImageTool.image(image:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    @objc private func image(image: UIImage, didFinishSavingWithError error: NSError?, contextInfo: UnsafeRawPointer) {
        if error == nil {
            self.albumCallback?(true)
        } else {
            self.albumCallback?(false)
        }
    }
    
    public func getImageWithCamera() -> Promise<PickImageTool.Result> {
        requestCameraAuthorization().then { authorizeStatus in
            return Promise { resolve, _ in
                if authorizeStatus != .authorized {
                    resolve(Result(authorizeStatus: authorizeStatus, images: [UIImage]()))
                    return
                }
                self.callback = resolve
                let cameraViewController = UIImagePickerController()
                cameraViewController.sourceType = .camera
                cameraViewController.allowsEditing = false
                cameraViewController.delegate = self
                cameraViewController.modalPresentationStyle = .overFullScreen
                AppLoad.getCurrentUivewController()?.present(cameraViewController, animated: true)
            }
        }
    }
    
    /// 从相册中获取图片
    /// - Parameter maxNum: 同时支持获取多少张
    /// - Returns: Promise<PickImage.Result>
    public func getImageWithAlbum(maxNum: Int) -> Promise<PickImageTool.Result> {
        
        if #available(iOS 14, *) {
            return Promise { resolve, _ in
                self.callback = resolve
                var config = PHPickerConfiguration()
                config.filter = PHPickerFilter.images
                config.selectionLimit = maxNum
                let picker = PHPickerViewController(configuration: config)
                picker.delegate = self
                picker.modalPresentationStyle = .fullScreen
                UIApplication.shared.keyWindow?.rootViewController?.present(picker, animated: true, completion: nil)
            }
        }
        
        return requestAlbumAuthorization(maxNum: maxNum).then { authorizeStatus in
            return Promise { resolve, _ in
                if authorizeStatus != .authorized {
                    resolve(Result(authorizeStatus: authorizeStatus, images: [UIImage]()))
                    return
                }
                let imagePicker = ImagePickerController()
                imagePicker.cancelButton.title = "return".localized
                imagePicker.doneButtonTitle = "complete".localized
                imagePicker.modalPresentationStyle = .fullScreen
                imagePicker.settings.selection.max = maxNum
                imagePicker.settings.theme.selectionStyle = .checked
                imagePicker.settings.fetch.assets.supportedMediaTypes = [.image]
                UIApplication.shared.keyWindow?.rootViewController?.presentImagePicker(imagePicker, select: nil, deselect: nil) { _ in
                    resolve(Result(authorizeStatus: .authorized, images: [UIImage]()))
                } finish: { assetList in
                    let imageManager = PHImageManager.default()
                    let imageRequestOption = PHImageRequestOptions()
                    imageRequestOption.isSynchronous = true
                    imageRequestOption.resizeMode = .none
                    imageRequestOption.deliveryMode = .opportunistic
                    var imageList = [UIImage]()
                    for asset in assetList {
                        imageManager.requestImage(for: asset, targetSize: PHImageManagerMaximumSize, contentMode: .aspectFill, options: imageRequestOption, resultHandler: {(result, _) -> Void in
                            guard let image = result else { return }
                            imageList.append(image)
                        })
                    }
                    resolve(Result(authorizeStatus: .authorized, images: imageList))
                }
            }
        }
    }
    
    /// 获取相机权限及授权状态
    /// - Returns: Promise<ImageAuthorization>
    public func requestCameraAuthorization() -> Promise<PickImageTool.AuthorizeStatus> {
        return Promise { resolve, _ in
            let avAuthStatus = AVCaptureDevice.authorizationStatus(for: .video)
            var authStatus: PickImageTool.AuthorizeStatus = .notDetermined
            guard avAuthStatus != .notDetermined else {
                AVCaptureDevice.requestAccess(for: .video) { (success) in
                    if success {
                        authStatus = .authorized
                    } else {
                        authStatus = .denied
                    }
                    resolve(authStatus)
                }
                return
            }
            switch avAuthStatus {
            case .denied: authStatus = .denied
            case .restricted: authStatus = .restricted
            case .authorized: authStatus = .authorized
            default: break
            }
            resolve(authStatus)
        }
    }
    
    
    /// 获取相册权限及授权状态
    /// - Returns: Promise<ImageAuthorizationStatus>
    public func requestAlbumAuthorization(maxNum: Int) -> Promise<PickImageTool.AuthorizeStatus> {
        return Promise { resolve, _ in
            let phAuthStatus = PHPhotoLibrary.authorizationStatus()
            var authStatus: PickImageTool.AuthorizeStatus = .notDetermined
            guard authStatus != .notDetermined else {
                PHPhotoLibrary.requestAuthorization { (status) in
                    switch status {
                    case .notDetermined: authStatus = .notDetermined
                    case .restricted: authStatus = .restricted
                    case .denied: authStatus = .denied
                    case .authorized: authStatus = .authorized
                    default: break
                    }
                    resolve(authStatus)
                }
                return
            }
            switch phAuthStatus {
            case .denied: authStatus = .denied
            case .restricted: authStatus = .restricted
            case .authorized: authStatus = .authorized
            default: break
            }
            resolve(authStatus)
        }
    }
}

private typealias PickerDelegate = PickImageTool
extension PickerDelegate: UIImagePickerControllerDelegate, UINavigationControllerDelegate, PHPickerViewControllerDelegate {
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true) { }
        self.callback?(Result(authorizeStatus: .authorized, images: [UIImage]()))
    }
    
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
        picker.dismiss(animated: true) {
            guard var image = info[UIImagePickerController.InfoKey.originalImage] as? UIImage else {
                self.callback?(Result(authorizeStatus: .authorized, images: [UIImage]()))
                return
            }
            
            UIGraphicsBeginImageContext(image.size)
            image.draw(in: CGRect(x: 0, y: 0, width: image.size.width, height: image.size.height))
            image = UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
            UIGraphicsEndImageContext()
            var images = [UIImage]()
            images.append(image)
            self.callback?(Result(authorizeStatus: .authorized, images: images))
        }
    }
    
    @available(iOS 14, *)
    func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
        
        guard results.count > 0 else {
            picker.dismiss(animated: true, completion: nil)
            self.callback?(Result(authorizeStatus: .authorized, images: [UIImage]()))
            return
        }
        
        picker.dismiss(animated: true) {
            var imageList = [UIImage]()
            for item in results {
                let provider = item.itemProvider
                
                guard provider.canLoadObject(ofClass: UIImage.self) else {
                    return
                }
                provider.loadObject(ofClass: UIImage.self) { [weak self](object, _) in
                    guard let image = object as? UIImage else { return }
                    imageList.append(image)
                    if imageList.count == results.count {
                        self?.callback?(Result(authorizeStatus: .authorized, images: imageList))
                    }
                }
            }
        }
    }
}
