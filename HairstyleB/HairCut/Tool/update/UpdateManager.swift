//
//  UpdateManager.swift
//  phonetransSW
//
//  Created by fs0011 on 2024/8/12.
//

import Foundation
import UIKit

class ZYEUpdateView: UIView {
    
    private var actionBlock: ((String) -> Void)?
    
    init(detail: String, actionCallback: @escaping (String) -> Void, isForce: Bool) {
        self.actionBlock = actionCallback
        super.init(frame: .zero)
        self.layer.cornerRadius = 16
        self.layer.masksToBounds = true
        self.backgroundColor = .white
        self.layoutWith(title: "更新", detail: detail, isForce: isForce)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func layoutWith(title: String, detail: String, isForce: Bool) {
        let titleLabel = UILabel()
        titleLabel.text = local("更新")
        titleLabel.textColor = UIColor.hex(string: "333333")
        titleLabel.textAlignment = .center
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        self.addSubview(titleLabel)
        safeSetTranslatesAutoresizingMaskIntoConstraints(titleLabel, false)
        safeBatchUIOperations {
            titleLabel.topAnchor.constraint(equalTo: self.topAnchor, constant: 24).isActive = true
            titleLabel.centerXAnchor.constraint(equalTo: self.centerXAnchor).isActive = true
        }

        let detailLabel = UILabel()
        detailLabel.text = detail
        detailLabel.textColor = UIColor.hex(string: "#434343")
        detailLabel.textAlignment = .center
        detailLabel.font = UIFont.systemFont(ofSize: 16)
        detailLabel.numberOfLines = 0
        self.addSubview(detailLabel)
        safeSetTranslatesAutoresizingMaskIntoConstraints(detailLabel, false)
        safeBatchUIOperations {
            detailLabel.centerXAnchor.constraint(equalTo: self.centerXAnchor).isActive = true
            detailLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 34).isActive = true
            detailLabel.widthAnchor.constraint(equalToConstant: 240).isActive = true
        }
        
        var bottomAnchor = detailLabel.bottomAnchor
        
        if !isForce {
            let ignoreButton = UIButton(type: .system)
            ignoreButton.setTitle(local("忽略此版本"), for: .normal)
            ignoreButton.setTitleColor(UIColor.hex(string: "#333333"), for: .normal)
            ignoreButton.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
            self.addSubview(ignoreButton)
            safeSetTranslatesAutoresizingMaskIntoConstraints(ignoreButton, false)
            safeBatchUIOperations {
                ignoreButton.centerXAnchor.constraint(equalTo: self.centerXAnchor).isActive = true
                ignoreButton.topAnchor.constraint(equalTo: detailLabel.bottomAnchor, constant: 24).isActive = true
            }
            ignoreButton.addTarget(self, action: #selector(cancelAction), for: .touchUpInside)
            bottomAnchor = ignoreButton.bottomAnchor
        }

        let updateButton = UIButton(type: .system)
        updateButton.setTitle(local("立即更新"), for: .normal)
        updateButton.setTitleColor(.white, for: .normal)
        updateButton.backgroundColor = .red
        updateButton.layer.cornerRadius = 22
        updateButton.layer.masksToBounds = true
        self.addSubview(updateButton)
        safeSetTranslatesAutoresizingMaskIntoConstraints(updateButton, false)
        safeBatchUIOperations {
            updateButton.topAnchor.constraint(equalTo: bottomAnchor, constant: 24).isActive = true
            updateButton.centerXAnchor.constraint(equalTo: self.centerXAnchor).isActive = true
            updateButton.widthAnchor.constraint(equalToConstant: 227).isActive = true
            updateButton.heightAnchor.constraint(equalToConstant: 44).isActive = true
            updateButton.bottomAnchor.constraint(equalTo: self.bottomAnchor, constant: -24).isActive = true
        }
        updateButton.addTarget(self, action: #selector(updateAction), for: .touchUpInside)
    }
    
    @objc private func cancelAction() {
        PopView.hidenPopView()
    }
    
    @objc private func updateAction(sender: UIButton) {
        actionBlock?(sender.titleLabel?.text ?? "")
    }
}

import Foundation
import Alamofire

class UpdateManager {
    static let shared = UpdateManager(updateUrl: updateJsonUrl)
    
    private var sessionManager: Session
    var AD_count: Int = 3
    var showGuide: Bool = false
    var baseDict: [String: Any]?
    var complete: ((Int) -> Void)?
    
    private init(updateUrl: String) {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        self.sessionManager = Session(configuration: configuration)
        
    }
    
    func postUrl(url: String, completion: @escaping () -> Void) {
            let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""
            let storedVersion = UserDefaults.standard.string(forKey: "version")

            if let storedVersion = storedVersion, self.compareVersion(currentVersion: version, newVersion: storedVersion) == .orderedSame {
                completion() // 即使版本相同，也调用完成处理闭包
                return
            }

            sessionManager.request(url, method: .get, headers: ["Accept": "application/json"]).responseJSON { response in
                switch response.result {
                case .success(let value):
                    if let dic = value as? [String: Any] {
                        self.handleResponse(dic: dic, currentVersion: version)
                    }
                case .failure(let error):
                    print("Request failed with error: \(error)")
                    self.AD_count = 3
                }
                completion() // 请求完成后调用完成处理闭包
            }
        }

    
    private func handleResponse(dic: [String: Any], currentVersion: String) {
        let displayUpdateContent = dic["displayUpdateContent"] as? Bool ?? false
        let forceUpdate = dic["forceUpdate"] as? Bool ?? false
        let show = displayUpdateContent
        let guide = dic["showGuide"] as? Bool ?? false
        let lastVersion = dic["version"] as? String ?? ""
        
        UserDefaults.standard.set(lastVersion, forKey: "version")
        
        var shouldShowUpdate = show
        if displayUpdateContent, self.compareVersion(currentVersion: currentVersion, newVersion: lastVersion) != .orderedAscending {
            shouldShowUpdate = false
        }
        if forceUpdate {
            shouldShowUpdate = true
        }
        if shouldShowUpdate {
            self.showUpdateView(isForce: forceUpdate, detail: dic["updateContent"] as? [String: String] ?? [:])
        }
        
        self.showGuide = guide
        self.AD_count = dic["AD_count"] as? Int ?? 3
        self.baseDict = dic["baseDict"] as? [String: Any]
        UserDefaults.standard.set(self.AD_count, forKey: "ad_count")
        self.complete?(self.AD_count)
    }
    
    private func showUpdateView(isForce: Bool, detail: [String: String]) {
        DispatchQueue.main.async {
            let updateView = ZYEUpdateView(detail: isForce ? local("当前版本低于最低支持版本") : (detail["zh"] ?? ""), actionCallback: { result in
                if let url = URL(string: "https://apps.apple.com/cn/app/id6450805550") {
                    UIApplication.shared.open(url)
                }
            }, isForce: isForce)
            
            PopView.popSideContentView(updateView, direct: .slideInCenter)
        }
    }
    
    func compareVersion(currentVersion: String, newVersion: String) -> ComparisonResult {
        let currentComponents = currentVersion.split(separator: ".").map { Int($0) ?? 0 }
        let newComponents = newVersion.split(separator: ".").map { Int($0) ?? 0 }
        
        for (current, new) in zip(currentComponents, newComponents) {
            if current < new {
                return .orderedAscending
            } else if current > new {
                return .orderedDescending
            }
        }
        
        return currentComponents.count < newComponents.count ? .orderedAscending : .orderedSame
    }
}
