//
//  APPStoreIAPObserver.swift
//  phonetransSW
//
//  Created by fs0011 on 2024/8/1.
//

import Foundation
import StoreKit
import Alamofire

typealias CheckReceiptCompleteResponseBlock = (String?, Error?) -> Void
let VipExpiresDate  = "VipExpiresDate"
let vipPerment  = "com.hairstyle.permanent"
class APPStoreIAPObserver: NSObject, SKPaymentTransactionObserver {
    
    var production = false
    var checkReceiptCompleteBlock: CheckReceiptCompleteResponseBlock?
    var receiptRequestData: Data?
    var subscribeArray: [String] = []
    static let sharedSecret = "a15ff9bf27604d658ac7fe23002d1c94"
    
    var requestURL: URL = URL(string: "https://buy.itunes.apple.com/verifyReceipt")!
    var iTunesSandboxVerifyReceiptURL : URL = URL(string: "https://sandbox.itunes.apple.com/verifyReceipt")!
    override init() {
        super.init()
    }
    
    func paymentQueue(_ queue: SKPaymentQueue, updatedTransactions transactions: [SKPaymentTransaction]) {
        for transaction in transactions {
            switch transaction.transactionState {
            case .purchased:
                completeTransaction(transaction)
            case .failed:
                failedTransaction(transaction)
            case .restored:
                restoreTransaction(transaction)
            default:
                break
            }
        }
    }
    
    func paymentQueue(_ queue: SKPaymentQueue, shouldAddStorePayment payment: SKPayment, for product: SKProduct) -> Bool {
        return true
    }
    
    func failedTransaction(_ transaction: SKPaymentTransaction) {
        if let error = transaction.error as NSError?, error.code != SKError.paymentCancelled.rawValue {
            // Optionally, display an error here.
        }
        APPMakeStoreIAPManager.sharedManager.paymentCanceled()
        SKPaymentQueue.default().finishTransaction(transaction)
    }
    
    func completeTransaction(_ transaction: SKPaymentTransaction) {
        let productId = transaction.payment.productIdentifier
        checkReceiptAndFinishTransaction(transaction: transaction, productId: productId)
    }
    
    func restoreTransaction(_ transaction: SKPaymentTransaction) {
        checkReceiptAndFinishTransaction(transaction: transaction, productId: transaction.original?.payment.productIdentifier)
    }
    
    func paymentQueueRestoreCompletedTransactionsFinished(_ queue: SKPaymentQueue) {
        if queue.transactions.isEmpty {
            APPMakeStoreIAPManager.sharedManager.alertNoItems()
            SVProgressHUD.dismiss()
        }
    }
    
    func checkVipDate() {
        guard let receiptURL = Bundle.main.appStoreReceiptURL,
              let receipt = try? Data(contentsOf: receiptURL) else { return }
        
        checkReceipt(receiptData: receipt, sharedSecret: APPStoreIAPObserver.sharedSecret, transaction: nil, productId: nil) { response, error in
            guard let response = response, error == nil else { return }
            let rec = self.toJSON(json: response)
            guard let array = rec?["latest_receipt_info"] as? [[String: Any]] else { return }
            
            let subscribe = array.filter { self.subscribeArray.contains($0["product_id"] as! String) }
            guard let dic = subscribe.first else { return }
            
            let expiresStr = dic["expires_date"] as! String
            let fmt = DateFormatter()
            fmt.dateFormat = "YYYY-MM-dd HH:mm:ss VV"
            fmt.locale = Locale(identifier: "en_US")
            let expiresDate = fmt.date(from: expiresStr)
            
            if expiresDate == nil {
                UserDefaults.standard.removeObject(forKey: VipExpiresDate)
            } else {
                let nowDate = self.getInternetDate() // 或者使用 Date()
                if expiresDate! < nowDate {
                    UserDefaults.standard.removeObject(forKey: VipExpiresDate)
                } else {
                    UserDefaults.standard.set(expiresDate, forKey: VipExpiresDate)
                }
            }
            
            NotificationCenter.default.post(name: NSNotification.Name("updateVip"), object: nil)
        }
    }
    
    func checkReceiptAndFinishTransaction(transaction: SKPaymentTransaction?, productId: String?) {
        print("🔥 APPStoreIAPObserver.checkReceiptAndFinishTransaction被调用")
        print("🔥 产品ID: \(productId ?? "nil")")
        print("🔥 subscribeArray: \(subscribeArray)")
        print("🔥 是否在subscribeArray中: \(subscribeArray.contains(productId!))")

        if subscribeArray.contains(productId!) {
            guard let receiptURL = Bundle.main.appStoreReceiptURL,
                  let receipt = try? Data(contentsOf: receiptURL) else { return }
            
            checkReceipt(receiptData: receipt, sharedSecret: APPStoreIAPObserver.sharedSecret, transaction: transaction, productId: productId) { response, error in
                guard let response = response, error == nil else { return }
                let rec = self.toJSON(json: response)
                guard let array = rec?["latest_receipt_info"] as? [[String: Any]] else { return }
                
                let subscribe = array.filter { self.subscribeArray.contains($0["product_id"] as! String) }
                guard let dic = subscribe.first else { return }
                
                let expiresStr = dic["expires_date"] as! String
                let fmt = DateFormatter()
                fmt.dateFormat = "YYYY-MM-dd HH:mm:ss VV"
                fmt.locale = Locale(identifier: "en_US")
                let expiresDate = fmt.date(from: expiresStr)
                
                if expiresDate == nil {
                    UserDefaults.standard.removeObject(forKey: VipExpiresDate)
                } else {
                    let nowDate = self.getInternetDate() // 或者使用 Date()
                    if expiresDate! < nowDate {
                        UserDefaults.standard.removeObject(forKey: VipExpiresDate)
                    } else {
                        UserDefaults.standard.set(expiresDate, forKey: VipExpiresDate)
                    }
                }
                
                if rec?["status"] as? Int == 0 {
                    APPMakeStoreIAPManager.sharedManager.provideContent(transaction?.payment.productIdentifier ?? "")
                    SKPaymentQueue.default().finishTransaction(transaction!)
                    if productId == vipPerment {
                        APPMakeStoreIAPManager.sharedManager.requestVIP(vip: productId!, exdate: "9999-01-01 00:00:00")
                    }
                } else {
                    APPMakeStoreIAPManager.sharedManager.paymentCanceled()
                }
            }
        } else {
            print("🔥 产品不在subscribeArray中，直接处理: \(productId!)")
            APPMakeStoreIAPManager.sharedManager.provideContent(productId!)
            SKPaymentQueue.default().finishTransaction(transaction!)
            if productId == vipPerment {
                print("🔥 检测到永久会员产品，调用requestVIP")
                APPMakeStoreIAPManager.sharedManager.requestVIP(vip: productId!, exdate: "9999-01-01 00:00:00")
            }
        }
    }
    
    func checkReceipt(receiptData: Data, sharedSecret: String, transaction: SKPaymentTransaction?, productId: String?, onCompletion: @escaping CheckReceiptCompleteResponseBlock) {
        self.checkReceiptCompleteBlock = onCompletion
        
        let receiptBase64 = receiptData.base64EncodedString(options: [])
        var jsonData: [String: Any] = ["receipt-data": receiptBase64]
        
        if !sharedSecret.isEmpty {
            jsonData["password"] = sharedSecret
        }
        
        let requestURL = self.requestURL
        
        AF.request(requestURL, method: .post, parameters: jsonData, encoding: JSONEncoding.default).responseJSON { response in
            switch response.result {
            case .success(let value):
                let responseJSON = value as! [String: Any]
                if let statusCode = responseJSON["status"] as? Int, statusCode == 21007 {
                    self.requestURL =  self.iTunesSandboxVerifyReceiptURL
                    if transaction == nil {
                        self.checkVipDate()
                    } else {
                        self.checkReceipt(receiptData: receiptData, sharedSecret: sharedSecret, transaction: transaction, productId: productId, onCompletion: onCompletion)
                    }
                } else {
                    self.checkReceiptCompleteBlock?(self.stringFromDictionary(dict: responseJSON), nil)
                }
            case .failure(let error):
                self.checkReceiptCompleteBlock?(nil, error)
            }
        }
    }
    
    func stringFromDictionary(dict: [String: Any]) -> String? {
        if let jsonData = try? JSONSerialization.data(withJSONObject: dict, options: .prettyPrinted) {
            return String(data: jsonData, encoding: .utf8)
        }
        return nil
    }
    
    func toJSON(json: String) -> [String: Any]? {
        if let data = json.data(using: .utf8) {
            return try? JSONSerialization.jsonObject(with: data, options: .mutableContainers) as? [String: Any]
        }
        return nil
    }
    
    func getInternetDate() -> Date {
        let url = URL(string: "http://m.baidu.com")!
        var request = URLRequest(url: url)
        request.cachePolicy = .reloadIgnoringCacheData
        request.timeoutInterval = 2
        request.httpShouldHandleCookies = false
        request.httpMethod = "GET"
        
        let semaphore = DispatchSemaphore(value: 0)
        var internetDate = Date()
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let httpResponse = response as? HTTPURLResponse, let date = httpResponse.allHeaderFields["Date"] as? String {
                let trimmedDate = String(date.dropFirst(5).dropLast(4))
                let dateFormatter = DateFormatter()
                dateFormatter.locale = Locale(identifier: "en_US")
                dateFormatter.dateFormat = "dd MMM yyyy HH:mm:ss"
                if let netDate = dateFormatter.date(from: trimmedDate) {
                    let zone = TimeZone.current
                    let interval = zone.secondsFromGMT(for: netDate)
                    internetDate = netDate.addingTimeInterval(TimeInterval(interval))
                }
            }
            semaphore.signal()
        }.resume()
        
        _ = semaphore.wait(timeout: .now() + 5)
        return internetDate
    }
}




extension Dictionary where Key == String {
    
    func valueFromDictionaryWithNSNullCheck(_ key: String) -> Any? {
        if let value = self[key] {
            if value is NSNull {
                return nil
            }
            return value
        }
        return nil
    }
    
    func isValidValueWithKey(_ key: String) -> Bool {
        if let value = self[key] {
            return !(value is NSNull)
        }
        return false
    }
}


import Foundation
import SVProgressHUD

extension String {
    
    static func base64String(from data: Data) -> String {
        return data.base64EncodedString()
    }
}
