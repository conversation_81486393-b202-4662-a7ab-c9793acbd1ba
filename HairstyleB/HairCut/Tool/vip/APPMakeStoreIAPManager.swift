//
//  APPMakeStoreIAPManager.swift
//  phonetransSW
//
//  Created by fs0011 on 2024/8/1.
//

import Foundation
import StoreKit

protocol MKStoreKitDelegate: AnyObject {
    func productCPurchased(_ productIdentifier: String)
    func failed()
    func buySuccessBack()
    func buycancelBack()
}

class APPMakeStoreIAPManager: NSObject, SKProductsRequestDelegate {
    
    static let sharedManager = APPMakeStoreIAPManager()
    
    var purchasableObjects: [SKProduct] = []
    var storeObserver: APPStoreIAPObserver?
    weak var delegate: MKStoreKitDelegate?
    
    static let featureAId = "com.hairstyle.week"
    static let featureBId = "com.FoshanFullstack.hairstyle.month"//前3天免费
    static let featureBNId = "com.FoshanFullstack.hairstyle.week.Nofree"//前3天不免费
    static let featureCId = "com.FoshanFullstack.hairstyle.year"
    static let featureDId = "com.hairstyle.permanent"
    static let featureDiscountId = "com.FoshanFullstack.hairstyle.month.discount"//挽留弹窗
    static let usagePackageId = "com.FoshanFullstack.hairstyle.Times"//次数包
    
    var featureCPurchased: Bool = false
    
    private override init() {
        super.init()
        storeObserver = APPStoreIAPObserver()
        storeObserver?.subscribeArray = [APPMakeStoreIAPManager.featureAId, APPMakeStoreIAPManager.featureBId, APPMakeStoreIAPManager.featureBNId,APPMakeStoreIAPManager.featureCId,APPMakeStoreIAPManager.featureDId, APPMakeStoreIAPManager.featureDiscountId]
        SKPaymentQueue.default().add(storeObserver!)

        // 直接在这里加载购买状态，避免静态方法调用问题
        let userDefaults = UserDefaults.standard
        let hasFeatureDInUserDefaults = userDefaults.bool(forKey: APPMakeStoreIAPManager.featureDId)
        if hasFeatureDInUserDefaults {
            print("🔍 检测到featureDId为true，加载永久会员状态")
            featureCPurchased = true
            requestVIP(vip: APPMakeStoreIAPManager.featureDId, exdate: "9999-01-01 00:00:00")
        } else {
            print("✅ 未检测到永久会员购买记录")
            featureCPurchased = false
        }

        storeObserver?.checkVipDate()
        requestProductData()
    }
    
    static func featureCPurchased() -> Bool {
        return sharedManager.featureCPurchased
    }
    
    static func feature7day() -> Bool {
        return featureVip()
    }
    
    static func featureVip() -> Bool {
//#if DEBUG
//
//        return true
//#endif

        print("🔍 featureVip()被调用")
        print("🔍 featureCPurchased: \(sharedManager.featureCPurchased)")

        if sharedManager.featureCPurchased {
            print("🔍 通过featureCPurchased返回true")
            return true
        }

        let userDefaults = UserDefaults.standard
        let vipExpiresDate = userDefaults.object(forKey: "VipExpiresDate") as? Date
        print("🔍 VipExpiresDate: \(vipExpiresDate?.description ?? "nil")")

        guard let expiresDate = vipExpiresDate else {
            print("🔍 没有VipExpiresDate，返回false")
            userDefaults.set(0, forKey: "Payment")
            return false
        }

        let nowDate = getInternetDate()
        print("🔍 当前时间: \(nowDate)")
        print("🔍 过期时间: \(expiresDate)")

        if expiresDate < nowDate {
            print("🔍 VIP已过期，返回false")
            userDefaults.set(0, forKey: "Payment")
            return false
        } else {
            print("🔍 VIP未过期，返回true")
            userDefaults.set(1, forKey: "Payment")
            return true
        }
    }
    
    static func getInternetDate() -> Date {
        
        return Date()
    }
    
    func requestProductData() {
        let productIdentifiers = Set([APPMakeStoreIAPManager.featureAId, APPMakeStoreIAPManager.featureBId, APPMakeStoreIAPManager.featureBNId, APPMakeStoreIAPManager.featureCId, APPMakeStoreIAPManager.featureDId, APPMakeStoreIAPManager.featureDiscountId, APPMakeStoreIAPManager.usagePackageId])
        let request = SKProductsRequest(productIdentifiers: productIdentifiers)
        request.delegate = self
        request.start()
    }
    
    func productsRequest(_ request: SKProductsRequest, didReceive response: SKProductsResponse) {
        print("Received products response...")
        purchasableObjects.append(contentsOf: response.products)
        for product in purchasableObjects {
            print("Feature: \(product.localizedTitle), Cost: \(product.price.doubleValue), ID: \(product.productIdentifier)")
            let price = getProductPrice(for: product)
            switch product.productIdentifier {
            case APPMakeStoreIAPManager.featureAId:
                UserDefaults.standard.set(price, forKey: "featureAId")
            case APPMakeStoreIAPManager.featureBId:
                UserDefaults.standard.set(price, forKey: "featureBId")
                NotificationCenter.default.post(name: NSNotification.Name("refreshPrice"), object: nil)
            case APPMakeStoreIAPManager.featureBNId:
                UserDefaults.standard.set(price, forKey: "featureBNId")
            case APPMakeStoreIAPManager.featureCId:
                UserDefaults.standard.set(price, forKey: "featureCId")
            case APPMakeStoreIAPManager.featureDId:
                UserDefaults.standard.set(price, forKey: "featureDId")
            case APPMakeStoreIAPManager.featureDiscountId:
                UserDefaults.standard.set(price, forKey: "featureDiscountId")
            case APPMakeStoreIAPManager.usagePackageId:
                UserDefaults.standard.set(price, forKey: "com.Fengyin.Camera.Frequency.package")
            default:
                break
            }
        }
    }
    
    func getProductPrice(for product: SKProduct) -> String {
        let numberFormatter = NumberFormatter()
        numberFormatter.formatterBehavior = .behavior10_4
        numberFormatter.numberStyle = .currency
        numberFormatter.locale = product.priceLocale
        return numberFormatter.string(from: product.price) ?? "$1.99"
    }
    
    func buyFeature(_ featureId: String) {
        print("🔥 buyFeature被调用，产品ID: \(featureId)")
        print("🔥 可用产品数量: \(purchasableObjects.count)")

        if SKPaymentQueue.canMakePayments() {
            print("🔥 设备支持内购")
            if let product = purchasableObjects.first(where: { $0.productIdentifier == featureId }) {
                print("🔥 找到产品，开始购买: \(product.productIdentifier)")
                let payment = SKPayment(product: product)
                SKPaymentQueue.default().add(payment)
            } else {
                // If products are not yet loaded, request product data and retry
                print("🔥 产品不可用，重新请求产品数据...")
                requestProductData()

                // Retry buying after 2 seconds
                Timer.scheduledTimer(withTimeInterval: 2.0, repeats: false) { _ in
                    print("🔥 2秒后重试购买...")
                    self.buyFeature(featureId)
                }
            }
        } else {
            print("🔥 设备不支持内购")
            let alert = UIAlertController(title: "提示", message: "你无权购买", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "好的", style: .default, handler: nil))
            UIApplication.shared.keyWindow?.rootViewController?.present(alert, animated: true, completion: nil)
        }
    }
    
    func paymentCanceled() {
        delegate?.failed()
    }
    
    func provideContent(_ productIdentifier: String) {
        print("🔥 provideContent被调用，产品ID: \(productIdentifier)")
        print("🔥 购买前featureCPurchased状态: \(featureCPurchased)")

        switch productIdentifier {
        case APPMakeStoreIAPManager.featureAId, APPMakeStoreIAPManager.featureBId, APPMakeStoreIAPManager.featureBNId, APPMakeStoreIAPManager.featureDiscountId:
            // 订阅产品购买成功，VIP状态基于过期时间判断，不设置featureCPurchased
            print("🔥 订阅产品购买成功: \(productIdentifier)，VIP状态基于过期时间判断")
            delegate?.productCPurchased(productIdentifier)
        case APPMakeStoreIAPManager.featureDId:
            // 永久会员购买成功，设置永久VIP状态
            print("🔥 永久会员购买成功: \(productIdentifier)")
            featureCPurchased = true
            let userDefaults = UserDefaults.standard
            userDefaults.set(true, forKey: APPMakeStoreIAPManager.featureDId)
            delegate?.productCPurchased(productIdentifier)
        case APPMakeStoreIAPManager.usagePackageId:
            // 次数包购买成功，不影响VIP状态，只通知代理
            print("🔥 次数包购买成功: \(productIdentifier)，不影响VIP状态")
            delegate?.productCPurchased(productIdentifier)
        default:
            print("🔥 未知产品ID: \(productIdentifier)")
            break
        }

        print("🔥 购买后featureCPurchased状态: \(featureCPurchased)")
        print("🔥 当前VIP状态: \(APPMakeStoreIAPManager.featureVip())")
    }
    

    
    static func updatePurchases() {
        print("updatePurchases")
        // 注意：这个方法不应该自动设置永久会员状态
        // 永久会员状态应该只在购买永久会员产品时设置
        // 移除错误的自动设置逻辑
    }
    
    func requestVIP(vip: String, exdate: String) {
        // Make network request to update VIP status
    }
    
    func restoreButClick(completionBlock: @escaping () -> Void) {
        SKPaymentQueue.default().restoreCompletedTransactions()
        completionBlock()
    }
    
    func alertNoItems() {
        let alert = UIAlertController(title: "提示".localized, message: "你还没有购买任何商品".localized, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "好的".localized, style: .default, handler: nil))
        UIApplication.shared.keyWindow?.rootViewController?.present(alert, animated: true, completion: nil)
    }
    
    static func onlyCheckVipDate() {
        DispatchQueue.global().async {
            sharedManager.storeObserver?.checkVipDate()
        }
    }
    
    func checkVipDate() {
        storeObserver?.checkVipDate()
    }
    
    static func changeOldToBuyA(setBool: Bool) {
        let userDefaults = UserDefaults.standard
        userDefaults.set(setBool, forKey: featureAId)
        sharedManager.featureCPurchased = setBool
    }
    
    static func updateAuyA() {
        let userDefaults = UserDefaults.standard
        userDefaults.set(false, forKey: featureDId)
        sharedManager.featureCPurchased = false
    }

    /// 清理错误的VIP状态（管理员功能）
    static func clearIncorrectVIPStatus() {
        let userDefaults = UserDefaults.standard
        print("🧹 清理错误的VIP状态...")

        // 清理永久会员状态
        userDefaults.set(false, forKey: featureDId)
        sharedManager.featureCPurchased = false

        // 清理其他可能的错误状态
        userDefaults.removeObject(forKey: "Payment")

        print("✅ VIP状态清理完成")
        print("🔍 当前VIP状态: \(featureVip())")
    }
    
    func failedTransaction(_ transaction: SKPaymentTransaction) {
        delegate?.failed()
        
        // 将 Error 转换为 NSError
        if let error = transaction.error as NSError? {
            let failureReason = error.localizedFailureReason ?? "Unknown reason"
            let recoverySuggestion = error.localizedRecoverySuggestion ?? "No suggestion"
            
            let message = "Reason: \(failureReason), You can try: \(recoverySuggestion)"
            let alert = UIAlertController(title: "无法完成购买", message: message, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "好的", style: .default, handler: nil))
            
            // 使用 keyWindow 的 rootViewController 来展示 alert
            if let rootViewController = UIApplication.shared.keyWindow?.rootViewController {
                rootViewController.present(alert, animated: true, completion: nil)
            }
        }
    }

    
    func buyFeatureA() { buyFeature(APPMakeStoreIAPManager.featureAId) }
    func buyFeatureB() { buyFeature(APPMakeStoreIAPManager.featureBId) }
    func buyFeatureBN() { buyFeature(APPMakeStoreIAPManager.featureBNId) }
    func buyFeatureC() { buyFeature(APPMakeStoreIAPManager.featureCId) }
    func buyFeatureD() { buyFeature(APPMakeStoreIAPManager.featureDId) }
    func buyFeatureDiscount() { buyFeature(APPMakeStoreIAPManager.featureDiscountId) }
}
