import UIKit
import SnapKit

class DiscountPopupView: UIView {
    
    private var closeAction: (() -> Void)?
    private var subscribeAction: (() -> Void)?
    private var originalPrice: String = ""
    private var discountPrice: String = ""
    
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 20
        view.clipsToBounds = true
        return view
    }()
    
    private lazy var gradientLayer: CAGradientLayer = {
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor.hex(string: "F95751").cgColor,
            UIColor.hex(string: "F9E1C0").cgColor
        ]
        gradient.startPoint = CGPoint(x: 0.5, y: 0)
        gradient.endPoint = CGPoint(x: 0.5, y: 1)
        return gradient
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var subtitleView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = local("成为会员可解锁全部功能")
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var priceBackgroundImageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "price_background"))
        imageView.contentMode = .scaleToFill
        return imageView
    }()
    
    private lazy var combinedPriceLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.numberOfLines = 1
        label.adjustsFontSizeToFitWidth = true
        label.minimumScaleFactor = 0.5
        return label
    }()
    
    private lazy var buttonStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        stackView.spacing = 10
        return stackView
    }()
    
    private lazy var termsButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(local("terms_of_use"), for: .normal)
        button.setTitleColor(UIColor.hex(string: "C46B5B"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 10, weight: .medium)
        button.titleLabel?.adjustsFontSizeToFitWidth = true
        button.titleLabel?.minimumScaleFactor = 0.5
        return button
    }()
    
    private lazy var privacyButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(local("privacy_policy"), for: .normal)
        button.setTitleColor(UIColor.hex(string: "C46B5B"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 10, weight: .medium)
        button.titleLabel?.adjustsFontSizeToFitWidth = true
        button.titleLabel?.minimumScaleFactor = 0.5
        return button
    }()
    
    private lazy var restoreButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(local("恢复购买"), for: .normal)
        button.setTitleColor(UIColor.hex(string: "C46B5B"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 10, weight: .medium)
        button.titleLabel?.adjustsFontSizeToFitWidth = true
        button.titleLabel?.minimumScaleFactor = 0.5
        return button
    }()
    
    private lazy var subscribeButtonBackground: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "subscribe_button"))
        imageView.contentMode = .scaleToFill
        imageView.isUserInteractionEnabled = true
        return imageView
    }()
    
    private lazy var subscribeButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle(local("免费试用并订阅"), for: .normal)
        button.setTitleColor(UIColor.hex(string: "FBF3EC"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        button.addTarget(self, action: #selector(subscribeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var priceTipLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.hex(string: "AD382F")
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var disclaimerLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.hex(string: "AD382F")
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "close_button"), for: .normal)
        button.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    init(frame: CGRect, originalPrice: String, discountPrice: String) {
        super.init(frame: frame)
        self.originalPrice = originalPrice
        self.discountPrice = discountPrice
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        gradientLayer.frame = backgroundView.bounds
        
        // Add underlines to buttons
        for button in [termsButton, privacyButton, restoreButton] {
            let attributeString = NSMutableAttributedString(string: button.title(for: .normal) ?? "")
            attributeString.addAttribute(.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: attributeString.length))
            button.setAttributedTitle(attributeString, for: .normal)
        }
    }
    
    private func setupView() {
        backgroundColor = UIColor.black.withAlphaComponent(0.5)
        
        // Add subviews
        addSubview(backgroundView)
        backgroundView.layer.insertSublayer(gradientLayer, at: 0)
        
        backgroundView.addSubview(titleLabel)
        backgroundView.addSubview(subtitleView)
        subtitleView.addSubview(subtitleLabel)
        
        // Add left and right arrow images
        let leftArrowImageView = UIImageView(image: UIImage(named: "left_arrow_decoration"))
        let rightArrowImageView = UIImageView(image: UIImage(named: "right_arrow_decoration"))
        subtitleView.addSubview(leftArrowImageView)
        subtitleView.addSubview(rightArrowImageView)
        
        backgroundView.addSubview(priceBackgroundImageView)
        priceBackgroundImageView.addSubview(combinedPriceLabel)
        
        backgroundView.addSubview(buttonStackView)
        buttonStackView.addArrangedSubview(termsButton)
        buttonStackView.addArrangedSubview(privacyButton)
        buttonStackView.addArrangedSubview(restoreButton)
        
        backgroundView.addSubview(subscribeButtonBackground)
        subscribeButtonBackground.addSubview(subscribeButton)
        
        // Add price tip label below subscribe button
        backgroundView.addSubview(priceTipLabel)
        
        backgroundView.addSubview(disclaimerLabel)
        addSubview(closeButton)
        
        // Setup constraints
        backgroundView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(300)
            make.height.equalTo(480)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(backgroundView).offset(30)
            make.centerX.equalToSuperview()
            make.width.equalTo(backgroundView).multipliedBy(0.8)
        }
        
        subtitleView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.height.equalTo(30)
            make.width.equalTo(backgroundView)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        leftArrowImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(subtitleLabel.snp.left).offset(-10)
            make.width.equalTo(22)
            make.height.equalTo(1)
        }
        
        rightArrowImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(subtitleLabel.snp.right).offset(10)
            make.width.equalTo(22)
            make.height.equalTo(1)
        }
        
        priceBackgroundImageView.snp.makeConstraints { make in
            make.top.equalTo(subtitleView.snp.bottom).offset(25)
            make.centerX.equalToSuperview()
            make.width.equalTo(240)
            make.height.equalTo(100)
        }
        
        combinedPriceLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(priceBackgroundImageView).multipliedBy(0.9)
            make.height.equalTo(50)
        }
        
        buttonStackView.snp.makeConstraints { make in
            make.top.equalTo(priceBackgroundImageView.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.equalTo(priceBackgroundImageView)
            make.height.equalTo(20)
        }
        
        termsButton.snp.makeConstraints { make in
            make.width.equalTo(buttonStackView).multipliedBy(0.3)
        }
        
        privacyButton.snp.makeConstraints { make in
            make.width.equalTo(buttonStackView).multipliedBy(0.3)
        }
        
        restoreButton.snp.makeConstraints { make in
            make.width.equalTo(buttonStackView).multipliedBy(0.3)
        }
        
        subscribeButtonBackground.snp.makeConstraints { make in
            make.top.equalTo(buttonStackView.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.equalTo(220)
            make.height.equalTo(50)
        }
        
        subscribeButton.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        priceTipLabel.snp.makeConstraints { make in
            make.top.equalTo(subscribeButtonBackground.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
            make.width.equalTo(backgroundView).multipliedBy(0.85)
            make.height.equalTo(16)
        }
        
        disclaimerLabel.snp.makeConstraints { make in
            make.top.equalTo(priceTipLabel.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
            make.width.equalTo(backgroundView).multipliedBy(0.85)
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(backgroundView.snp.bottom).offset(30)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(30)
        }
        
        // Setup title with attributed text
        updateTitle()
        
        // Setup price labels
        updatePrices()
        
        // Setup price tip label
        updatePriceTip()
        
        // Setup disclaimer
        updateDisclaimer()
        
        // Setup button actions
        termsButton.addTarget(self, action: #selector(termsButtonTapped), for: .touchUpInside)
        privacyButton.addTarget(self, action: #selector(privacyButtonTapped), for: .touchUpInside)
        restoreButton.addTarget(self, action: #selector(restoreButtonTapped), for: .touchUpInside)
    }
    
    private func updateTitle() {
        let titleText = local("恭喜获3折购月度会员")
        let attributedString = NSMutableAttributedString(string: titleText)
        
        // Apply base attributes for the entire string
        attributedString.addAttributes([
            .font: UIFont.systemFont(ofSize: 28, weight: .semibold),
            .foregroundColor: UIColor.white
        ], range: NSRange(location: 0, length: titleText.count))
        
        // Find the "3" and "折" characters and change their color
        if let threeRange = titleText.range(of: "3"),
           let dieRange = titleText.range(of: "折") {
            let threeNSRange = NSRange(threeRange, in: titleText)
            let dieNSRange = NSRange(dieRange, in: titleText)
            
            // Set special color for "3" and "折"
            attributedString.addAttributes([
                .foregroundColor: UIColor.hex(string: "FFF153")
            ], range: threeNSRange)
            
            attributedString.addAttributes([
                .foregroundColor: UIColor.hex(string: "FFF153")
            ], range: dieNSRange)
            
            // Special larger font for "3"
            attributedString.addAttributes([
                .font: UIFont.systemFont(ofSize: 48, weight: .semibold)
            ], range: threeNSRange)
        }
        
        titleLabel.attributedText = attributedString
    }
    
    private func updatePrices() {
        // 格式化价格，移除不必要的小数点和零
        let formattedOriginalPrice = formatPrice(originalPrice)
        let formattedDiscountPrice = formatPrice(discountPrice)
        
        // 创建富文本字符串
        let attributedString = NSMutableAttributedString()
        
        // 原价文本样式
        let originalPriceText = NSMutableAttributedString(string: local("原价"))
        originalPriceText.addAttributes([
            .font: UIFont.systemFont(ofSize: 20, weight: .medium),
            .foregroundColor: UIColor.hex(string: "B25400")
        ], range: NSRange(location: 0, length: originalPriceText.length))
        attributedString.append(originalPriceText)
        
        // 原价数值样式（带删除线）
        let originalPriceValueText = NSMutableAttributedString(string: " " + formattedOriginalPrice + " ")
        originalPriceValueText.addAttributes([
            .font: UIFont.systemFont(ofSize: 28, weight: .semibold),
            .foregroundColor: UIColor.hex(string: "FF4D35"),
            .strikethroughStyle: NSUnderlineStyle.single.rawValue
        ], range: NSRange(location: 0, length: originalPriceValueText.length))
        attributedString.append(originalPriceValueText)
        
        // 现价文本样式
        let currentPriceText = NSMutableAttributedString(string: local("现价"))
        currentPriceText.addAttributes([
            .font: UIFont.systemFont(ofSize: 20, weight: .medium),
            .foregroundColor: UIColor.hex(string: "B25400")
        ], range: NSRange(location: 0, length: currentPriceText.length))
        attributedString.append(currentPriceText)
        
        // 现价数值样式
        let currentPriceValueText = NSMutableAttributedString(string: " " + formattedDiscountPrice)
        currentPriceValueText.addAttributes([
            .font: UIFont.systemFont(ofSize: 32, weight: .semibold),
            .foregroundColor: UIColor.hex(string: "FF4D35")
        ], range: NSRange(location: 0, length: currentPriceValueText.length))
        attributedString.append(currentPriceValueText)
        
        // 设置富文本
        combinedPriceLabel.attributedText = attributedString
    }
    
    // New method: Format price, remove unnecessary decimal point and zeros
    private func formatPrice(_ price: String) -> String {
        // Separate currency symbol and digits
        let currencySymbol = String(price.prefix(1))
        let priceValue = String(price.dropFirst())
        
        // Check if there is a decimal point
        if priceValue.contains(".") {
            let components = priceValue.components(separatedBy: ".")
            if components.count == 2 {
                let wholePart = components[0]
                let decimalPart = components[1]
                
                // If decimal part is all zeros, remove decimal point and decimal part
                if decimalPart.allSatisfy({ $0 == "0" }) {
                    return currencySymbol + wholePart
                } else {
                    // If decimal point has non-zero digits, keep the decimal point
                    return price
                }
            }
        }
        
        // No decimal point or processing failed, return original price
        return price
    }
    
    private func updatePriceTip() {
        // 获取价格数字部分（不含货币符号）
        let priceDigits = discountPrice.replacingOccurrences(of: "[^0-9.]", with: "", options: .regularExpression)
        // 格式化价格，去掉无意义的小数点和零
        let formattedPriceDigits = formatPriceDigits(priceDigits)
        let currencySymbol = String(discountPrice.first ?? "$")
        priceTipLabel.text = local("3天免费之后") + currencySymbol + formattedPriceDigits + local("/月")
    }
    
    // Format price digits, remove unnecessary decimal point and zeros
    private func formatPriceDigits(_ priceDigits: String) -> String {
        // Check if there is a decimal point
        if priceDigits.contains(".") {
            let components = priceDigits.components(separatedBy: ".")
            if components.count == 2 {
                let wholePart = components[0]
                let decimalPart = components[1]
                
                // If decimal part is all zeros, remove decimal point and decimal part
                if decimalPart.allSatisfy({ $0 == "0" }) {
                    return wholePart
                }
            }
        }
        
        return priceDigits
    }
    
    private func updateDisclaimer() {
        disclaimerLabel.text = local("试用期间可随时取消")
    }
    
    func show(in viewController: UIViewController, closeAction: @escaping () -> Void, subscribeAction: @escaping () -> Void) {
        self.closeAction = closeAction
        self.subscribeAction = subscribeAction
        
        viewController.view.addSubview(self)
        self.frame = viewController.view.bounds
        
        // Animation
        self.alpha = 0
        self.backgroundView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        
        UIView.animate(withDuration: 0.3) {
            self.alpha = 1
            self.backgroundView.transform = .identity
        }
    }
    
    // 添加自动触发订阅的新方法
    func showAndAutoSubscribe(in viewController: UIViewController, closeAction: @escaping () -> Void, subscribeAction: @escaping () -> Void) {
        self.closeAction = closeAction
        self.subscribeAction = subscribeAction
        
        viewController.view.addSubview(self)
        self.frame = viewController.view.bounds
        
        // Animation
        self.alpha = 0
        self.backgroundView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        
        UIView.animate(withDuration: 0.3, animations: {
            self.alpha = 1
            self.backgroundView.transform = .identity
        }) { _ in
            // 显示弹窗后短暂延迟，然后自动触发订阅
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
//                self.subscribeAction?()
            }
        }
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.3, animations: {
            self.alpha = 0
            self.backgroundView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        }) { _ in
            self.removeFromSuperview()
        }
    }
    
    // MARK: - Actions
    
    @objc private func closeButtonTapped() {
        dismiss()
        closeAction?()
    }
    
    @objc private func subscribeButtonTapped() {
//        dismiss()
        subscribeAction?()
    }
    
    @objc private func termsButtonTapped() {
        if let url = URL(string: yyxyUrl) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
    }
    
    @objc private func privacyButtonTapped() {
        if let url = URL(string: yyzcUrl) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
    }
    
    @objc private func restoreButtonTapped() {
        dismiss()
        NotificationCenter.default.post(name: NSNotification.Name("RestorePurchases"), object: nil)
    }
} 
