//
//  VipViewController.swift
//  phonetransSW
//
//  Created by fs0011 on 2024/8/6.
//

import Foundation
import UIKit
import StoreKit
import SnapKit
import SVProgressHUD
import UMCommon

class VIPViewController: BaseVC, MKStoreKitDelegate {
    func buycancelBack() {
        
    }
    
    
    var weekPrice: UILabel!
    var monthPrice: UILabel!
    var yearPrice: UILabel!
    var svipPrice: UILabel!
    
    var OriweekPrice: UILabel!
    var OrimonthPrice: UILabel!
    var OriyearPrice: UILabel!
    var OrisvipPrice: UILabel!
    
    var chooseIndex: Int = 1
    var from : String! = ""
    var style: String? = nil // 添加style参数来存储发型名字或造型名字
    static var timeCount: Int = 0
    static var timer: Timer?
    
    // 订阅按钮和价格标签
    private var subscribeButton: UIButton!
    private var priceTipLabel: UILabel!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.chooseIndex = 1
        
        // Select the discounted option if opened from the discount shortcut
        if from == "限时特惠" {
            self.chooseIndex = 1 // Default to monthly subscription for the discount
        }
        
        self.view.backgroundColor = UIColor.colorWithHexString("fafafa", alpha: 1)
        self.navigationController?.navigationBar.isHidden = true
        // 设置导航栏返回按钮
//        self.navigationItem.leftBarButtonItem = UIBarButtonItem(image: UIImage(named: "left_arrow"), style: .plain, target: self, action: #selector(handleBackAction))
        
        DispatchQueue.main.async {
            self.layoutUI()
            APPMakeStoreIAPManager.sharedManager
            self.refreshPrice()
            
            let back = UIButton.createButton(withImageName: "left_arrow")
            self.view.addSubview(back)
            back.snp.makeConstraints { make in
                make.top.equalTo(24 * scaleX + KDNavH() + 20)
                make.left.equalTo(20 * scaleX)
                make.width.height.equalTo(24 * scaleX)
            }
            back.addTapAction {
                SVProgressHUD.dismiss()
                
                // 添加关闭埋点
                if self.from == "AI换发" && self.style != nil {
                    MobClick.event("Subscribe", attributes: ["close": "\(self.style!)&关闭订阅 (进入到订阅页, 没拉起支付直, 接关闭订阅页)"])
                } else if self.from == "三连屏" {
                    MobClick.event("Subscribe", attributes: ["close": "三连屏&关闭 (没拉起支付, 直接关闭)"])
                }
                
                // 根据不同的展示方式确定返回方式
                if let navigationController = self.navigationController {
                    // 使用导航控制器推出的情况
                    if navigationController.viewControllers.count > 1 {
                        navigationController.popViewController(animated: true)
                    } else {
                        // 导航控制器只有一个VC的情况，使用dismiss
                        self.dismiss(animated: true, completion: nil)
                    }
                } else {
                    // 直接模态展示的情况
                    self.dismiss(animated: true, completion: nil)
                }
            }
            
            // Show discount popup if coming from discount shortcut
            if self.from == "限时特惠" {
                self.showDiscountPopup()
            }
            
            // 添加来源埋点
            if self.from == "AI换发" {
                MobClick.event("Subscribe", attributes: ["source": "AI换发 (入口进入订阅页算一次)"])
            } else if self.from == "三连屏" {
                MobClick.event("Subscribe", attributes: ["source": "三连屏"])
            } else if self.from == "挽留弹窗" {
                MobClick.event("Subscribe", attributes: ["source": "挽留弹窗"])
            }
        }
        
        
        if UserDefaults.standard.string(forKey: "vip7day") == nil {
            APPMakeStoreIAPManager.sharedManager.requestProductData()
            VIPViewController.timeCount = 0
            VIPViewController.timer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(refreshPrice), userInfo: nil, repeats: true)
        }
        
        
    }
    
    func layoutUI() {
        let im = UIImageView.createSizeFitImageView(name: "viptop")
//        let currentLanguageRegion = (UserDefaults.standard.object(forKey: "AppleLanguages") as? [String])?.first
//        
//        if !(currentLanguageRegion?.contains("zh") ?? false) {
//            im.image = UIImage(named: "viptop_en")
//        }
        self.view.addSubview(im)
        im.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.left.right.equalToSuperview()
            make.height.equalTo(282 * scaleX)
        }
        
        let bac = UILabel()
        bac.backgroundColor = .white
        bac.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        bac.layer.shadowOffset = CGSize(width: 0, height: 2)
        bac.layer.shadowOpacity = 1
        bac.layer.shadowRadius = 20
        bac.layer.cornerRadius = 20 * scaleX
        bac.layer.masksToBounds = true
        self.view.addSubview(bac)
        bac.snp.makeConstraints { make in
            make.left.equalTo(15 * scaleX)
            make.right.equalTo(-15 * scaleX)
            make.height.equalTo(74 * scaleX)
            make.centerY.equalTo(im.snp.bottom)
        }
        
        let la = UILabel.createLabel(title: local("会员解锁APP内所有功能，并去除广告，给你更良好的体验"), textColor: darkTextColor, textAlignment: .left, font: UIFont.systemFont(ofSize: 16 * scaleX, weight: .medium))
        la.numberOfLines = 0
        bac.addSubview(la)
        la.snp.makeConstraints { make in
            make.left.equalTo(15 * scaleX)
            make.right.equalTo(-15 * scaleX)
            make.centerY.equalToSuperview()
        }
        
        let sc = UIScrollView()
        sc.showsHorizontalScrollIndicator = false
        sc.clipsToBounds = false
        self.view.addSubview(sc)
        sc.snp.makeConstraints { make in
            make.top.equalTo(bac.snp.bottom).offset(44 * scaleX)
            make.left.right.equalToSuperview()
            make.height.equalTo(132 * scaleX)
        }
        
        let titles = [local("连续包周"), local("月度会员"), local("年度会员"), local("永久会员")]
        var arr: [UIView] = []

        // 隐藏包周选项，只显示月度、年度、永久会员 (索引1,2,3)
        let visibleIndexes = [1, 2, 3]

        for (displayIndex, i) in visibleIndexes.enumerated() {
            let tapView = UIView()
            tapView.backgroundColor = .white
            tapView.layer.cornerRadius = 20 * scaleX
            sc.addSubview(tapView)
            tapView.snp.makeConstraints { make in
                make.top.equalTo(0)
                make.width.equalTo(100 * scaleX)
                make.height.equalTo(132 * scaleX)
            }
            arr.append(tapView)
            if i == self.chooseIndex {
                tapView.layer.borderColor = UIColor.colorWithHexString("4C79FF", alpha: 1) .cgColor
                tapView.layer.borderWidth = 2
            }
            
            tapView.isUserInteractionEnabled = true
            tapView.addTapAction {
                arr.forEach { view in
                    view.layer.borderColor = UIColor.clear.cgColor
                    view.layer.borderWidth = 2
                }
                tapView.layer.borderColor = UIColor.colorWithHexString("4C79FF", alpha: 1).cgColor
                tapView.layer.borderWidth = 2
                self.chooseIndex = i  // i 仍然是原始索引 (1,2,3)

                // 更新订阅按钮文字
                self.updateSubscribeButtonText()
            }
            
            let la1 = UILabel.createLabel(title: local(titles[i]), textColor: UIColor.colorWithHexString("666666", alpha: 1), textAlignment: .center, font: UIFont.systemFont(ofSize: 14 * scaleX))
            tapView.addSubview(la1)
            la1.snp.makeConstraints { make in
                make.top.equalTo(24 * scaleX)
                make.centerX.equalToSuperview()
            }
            
            let la2 = UILabel.createLabel(title: local(""), textColor: darkTextColor, textAlignment: .center, font: UIFont.systemFont(ofSize: 30 * scaleX, weight: .medium))
            tapView.addSubview(la2)
            la2.snp.makeConstraints { make in
                make.center.equalToSuperview()
            }
            
            let la3 = UILabel.createLabel(title: "", textColor: UIColor.colorWithHexString("666666", alpha: 1), textAlignment: .center, font: UIFont.systemFont(ofSize: 12 * scaleX))
            tapView.addSubview(la3)
            la3.snp.makeConstraints { make in
                make.bottom.equalTo(-12 * scaleX)
                make.centerX.equalToSuperview()
            }
            
            switch i {
            case 0:
                // 包周选项已隐藏，但保留变量赋值以避免编译错误
                self.weekPrice = la2
                self.OriweekPrice = la3
            case 1:
                self.monthPrice = la2
                self.OrimonthPrice = la3
            case 2:
                self.yearPrice = la2
                self.OriyearPrice = la3
            case 3:
                self.svipPrice = la2
                self.OrisvipPrice = la3
            default:
                break
            }
            
            if i == 1 {
                let im = UIImageView.createSizeFitImageView(name: "free")
                sc.addSubview(im)
                im.snp.makeConstraints { make in
                    make.left.equalTo(tapView)
                    make.height.equalTo(24 * scaleX)
                    make.width.equalTo(62 * scaleX)
                    make.centerY.equalTo(tapView.snp.top)
                }
                
                let freeTip = UILabel.createLabel(title: local("3天免费"), textColor: .white, textAlignment: .center, font: UIFont.systemFont(ofSize: 11 * scaleX))
                im.addSubview(freeTip)
                freeTip.snp.makeConstraints { make in
                    make.center.equalToSuperview()
                }
            }
        }
        
        arr.distributeViewsHorizontally(fixedSpacing: 15 * scaleX, leadSpacing: 15 * scaleX, tailSpacing: 15 * scaleX)
        
        // 创建订阅按钮
        subscribeButton = UIButton()
        subscribeButton.layer.cornerRadius = 48 * scaleX * 0.5
        subscribeButton.layer.masksToBounds = true
        subscribeButton.backgroundColor = themColor
        subscribeButton.setTitleColor(.white, for: .normal)
        subscribeButton.addTarget(self, action: #selector(goBuy), for: .touchUpInside)
        self.view.addSubview(subscribeButton)
        subscribeButton.snp.makeConstraints { make in
            make.top.equalTo(sc.snp.bottom).offset(40 * scaleX)
            make.width.equalTo(315 * scaleX)
            make.height.equalTo(48 * scaleX)
            make.centerX.equalToSuperview()
        }
        
        // 创建价格提示标签
        priceTipLabel = UILabel()
        priceTipLabel.textColor = UIColor.colorWithHexString("666666", alpha: 1)
        priceTipLabel.font = UIFont.systemFont(ofSize: 14 * scaleX)
        priceTipLabel.textAlignment = .center
        self.view.addSubview(priceTipLabel)
        priceTipLabel.snp.makeConstraints { make in
            make.top.equalTo(subscribeButton.snp.bottom).offset(10 * scaleX)
            make.centerX.equalToSuperview()
            make.left.equalTo(39 * scaleX)
            make.right.equalTo(-39 * scaleX)
            make.height.equalTo(20 * scaleX)
        }
        
        // 初始化按钮文字
        updateSubscribeButtonText()
        
//        let gl = CAGradientLayer()
//        gl.frame = button.bounds
//        gl.startPoint = CGPoint(x: 0.42, y: 0.14)
//        gl.endPoint = CGPoint(x: 0.6, y: 0.95)
//        gl.colors = [UIColor(red: 83/255.0, green: 156/255.0, blue: 248/255.0, alpha: 1.0).cgColor, UIColor(red: 169/255.0, green: 107/255.0, blue: 248/255.0, alpha: 1.0).cgColor]
//        gl.locations = [0, 1.0]
//        button.layer.insertSublayer(gl, at: 0)
        
        self.view.layoutIfNeeded()
        let explainButtonOriginY = CGFloat(subscribeButton.frame.origin.y + subscribeButton.bounds.height + 30)
        for i in 0..<3 {
            
            let screenWidth = UIScreen.main.bounds.width
            let xOffset = (screenWidth / 2 - 150) + CGFloat(110 * i) // 将 i 转换为 CGFloat
            let yOffset = CGFloat(explainButtonOriginY) // 确保 explainButtonOriginY 是 CGFloat 类型
            let frame = CGRect(x: xOffset, y: yOffset, width: 80, height: 36)
            let explainButton = UIButton(frame: frame)
            explainButton.setTitleColor(UIColor(white: 0.2, alpha: 1.0), for:.normal)
            explainButton.titleLabel?.font = UIFont.systemFont(ofSize: 11.0)
            explainButton.tag = 2000 + i
            explainButton.addTarget(self, action: #selector(explainButtonClick(_:)), for: .touchUpInside)
            self.view.addSubview(explainButton)
            switch i {
            case 0:
                explainButton.setTitle(NSLocalizedString("terms_of_use", comment: ""), for: .normal)
            case 1:
                explainButton.setTitle(NSLocalizedString("privacy_policy", comment: ""), for: .normal)
            case 2:
                explainButton.setTitle(NSLocalizedString(local("恢复订阅"), comment: ""), for: .normal)
            default:
                break
            }
        }
        
        let tipsLabel = UILabel()
        tipsLabel.text = local("• 一旦购买确定后将通过您的iTunes账号支付• 订阅将会自动更新，除非您在订阅期结束前24小时内取消订阅• 费用将在免费试用期结束前24小时内通过iTunes帐户支付• 帐户将在本期结束前24小时内收取续费费用，并确定续订费用• 购买后可在itunes用户账号中管理订阅或关闭自动更新。• 在已生效订阅期间不能取消当前订阅• 免费试用期内任何未使用的部分，如果提供，将在用户购买高级")
        tipsLabel.textColor = .gray
        tipsLabel.numberOfLines = 0
        tipsLabel.textAlignment = .left
        tipsLabel.font = UIFont.systemFont(ofSize: 10.0)
        self.view.addSubview(tipsLabel)
        tipsLabel.snp.makeConstraints { make in
            if UIDevice.current.userInterfaceIdiom == .pad{
                make.top.equalTo(subscribeButton.snp.bottom).offset(42 * scaleX)
            }
            else
            {
                make.top.equalTo(subscribeButton.snp.bottom).offset(72 * scaleX)
            }
            make.leading.equalTo(self.view.snp.leading).offset(20)
            make.width.equalTo(UIScreen.main.bounds.width - 40)
        }
        
        refreshPrice()
    }
    
    // 更新订阅按钮文字和价格提示
    private func updateSubscribeButtonText() {
        if chooseIndex == 1 {
            // 月度会员 - 显示"免费试用并订阅"和价格提示
            subscribeButton.setTitle(local("免费试用并订阅"), for: .normal)
            
            // 更新价格提示文字
            let priceString = UserDefaults.standard.string(forKey: "featureBId") ?? "¥X.XX"
            priceTipLabel.text = local("3天免费之后") + priceString + local("/月")
            priceTipLabel.isHidden = false
        } else {
            // 其他会员 - 显示"立即订阅"，隐藏价格提示
            subscribeButton.setTitle(local("立即订阅"), for: .normal)
            priceTipLabel.isHidden = true
        }
    }
    
    @objc func explainButtonClick(_ sender: UIButton) {
        let tag = sender.tag - 2000
        switch tag {
        case 0:
            if let url = URL(string: yyxyUrl) {
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            }
        case 1:
            if let url = URL(string: yyzcUrl) {
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            }
        case 2:
            rsButtonAction(nil)
        default:
            break
        }
    }
    
    @objc func refreshPrice() {
        VIPViewController.timeCount += 1
        let weekPrice = UserDefaults.standard.string(forKey: "featureAId")
        let monthPrice = UserDefaults.standard.string(forKey: "featureBId")
        let yearPrice = UserDefaults.standard.string(forKey: "featureCId")
        let svipPrice = UserDefaults.standard.string(forKey: "featureDId")
        let discountPrice = UserDefaults.standard.string(forKey: "featureDiscountId")
        
        if let weekPrice = weekPrice{
            VIPViewController.timer?.invalidate()
            VIPViewController.timer = nil
            
            // 设置价格标签
//            self.weekPrice.text = weekPrice
//            self.OriweekPrice.attributedText = getPriceAttributedText(originalPrice: weekPrice)
            
            if let monthPrice = monthPrice {
                // If coming from discount shortcut, show discounted price for month subscription
                // if from == "限时特惠" && discountPrice != nil {
                //     self.monthPrice.text = discountPrice
                //     self.OrimonthPrice.attributedText = getPriceAttributedText(originalPrice: monthPrice)
                // } else {
                    self.monthPrice.text = monthPrice
                    self.OrimonthPrice.attributedText = getPriceAttributedText(originalPrice: monthPrice)
                // }
                
                // 更新价格提示标签
                if chooseIndex == 1 {
                    priceTipLabel.text = local("3天免费之后") + monthPrice + local("/月")
                }
            }
            if let yearPrice = yearPrice {
                self.yearPrice.text = yearPrice
                self.OriyearPrice.attributedText = getPriceAttributedText(originalPrice: yearPrice)
            }
            if let svipPrice = svipPrice {
                self.svipPrice.text = svipPrice
                self.OrisvipPrice.attributedText = getPriceAttributedText(originalPrice: svipPrice)
            }
        } else {
            let alertController = UIAlertController(title: local("提示"), message: local("价格加载失败,请重新加载"), preferredStyle: .alert)
            let action = UIAlertAction(title: local("好的"), style: .default) { _ in
                SVProgressHUD.dismiss()
                self.dismiss(animated: true, completion: nil)
            }
            alertController.addAction(action)
            self.present(alertController, animated: true, completion: nil)
        }
    }
    
    @objc func goBuy() {
        SVProgressHUD.show()
        APPMakeStoreIAPManager.sharedManager.delegate = self
        
        // Use the discounted subscription if opened from discount shortcut
//        if from == "限时特惠" && chooseIndex == 1 {
//            APPMakeStoreIAPManager.sharedManager.buyFeatureDiscount()
//            return
//        }
        
        switch chooseIndex {
        case 0:
            // 包周订阅已隐藏，保留代码以备将来使用
            // APPMakeStoreIAPManager.sharedManager.buyFeatureA()
            break
        case 1:
            APPMakeStoreIAPManager.sharedManager.buyFeatureB()
        case 2:
            APPMakeStoreIAPManager.sharedManager.buyFeatureC()
        case 3:
            APPMakeStoreIAPManager.sharedManager.buyFeatureD()
        default:
            break
        }
    }
    
    func getPriceAttributedText(originalPrice: String) -> NSAttributedString {
        let priceWithoutSymbol = originalPrice.replacingOccurrences(of: "[^0-9.]", with: "", options: .regularExpression)
        let priceValue = CGFloat(Double(priceWithoutSymbol) ?? 0) * 1.5
        let currencySymbol = originalPrice.first?.description ?? ""
        // 格式化价格为两位小数
        let formattedPrice = String(format: "%.2f", priceValue)
        let strikeThroughPrice = "\(currencySymbol)\(formattedPrice)"
        
        let attributes: [NSAttributedString.Key: Any] = [
            .strikethroughStyle: NSUnderlineStyle.single.rawValue,
            .foregroundColor: UIColor.colorWithHexString("666666", alpha: 1)
        ]
        return NSAttributedString(string: strikeThroughPrice, attributes: attributes)
    }
    
    func failed() {
        SVProgressHUD.dismiss()
        
        // 添加失败埋点
        if self.from == "AI换发" && self.style != nil {
            // 根据选择的订阅类型添加埋点
            switch chooseIndex {
            case 0:
                MobClick.event("Subscribe", attributes: ["status": "\(self.style!)&周&购买失败 (拉起订阅弹窗但没支付)"])
            case 1:
                MobClick.event("Subscribe", attributes: ["status": "\(self.style!)&月&购买失败 (拉起订阅弹窗但没支付)"])
            case 2:
                MobClick.event("Subscribe", attributes: ["status": "\(self.style!)&年&购买失败 (拉起订阅弹窗但没支付)"])
            case 3:
                MobClick.event("Subscribe", attributes: ["status": "\(self.style!)&永久&购买失败 (拉起订阅弹窗但没支付)"])
            default:
                break
            }
        } else if self.from == "三连屏" {
            MobClick.event("Subscribe", attributes: ["status": "三连屏&购买失败 (拉起支付, 但没支付)"])
        }
        
        self.dismiss(animated: true, completion: nil)
    }
    
    func buySuccessBack() {
        SVProgressHUD.dismiss()
        
        // 添加成功埋点
        if self.from == "AI换发" {
            MobClick.event("Subscribe", attributes: ["result": "AI换发订阅成功"])
            
            // 根据选择的订阅类型添加埋点
            if let styleName = self.style {
                switch chooseIndex {
                case 0:
                    // 包周订阅已隐藏，保留埋点代码以备将来使用
                    // MobClick.event("Subscribe", attributes: ["status": "\(styleName)&周&购买成功"])
                    break
                case 1:
                    MobClick.event("Subscribe", attributes: ["status": "\(styleName)&月&购买成功"])
                case 2:
                    MobClick.event("Subscribe", attributes: ["status": "\(styleName)&年&购买成功"])
                case 3:
                    MobClick.event("Subscribe", attributes: ["status": "\(styleName)&永久&购买成功"])
                default:
                    break
                }
            }
        } else if self.from == "三连屏" {
            MobClick.event("Subscribe", attributes: ["result": "三连屏订阅成功"])
        } else if self.from == "挽留弹窗" {
            MobClick.event("Subscribe", attributes: ["result": "挽留弹窗订阅成功"])
        }

        // 发送订阅状态更新通知
        NotificationCenter.default.post(name: .subscriptionStatusDidUpdate, object: nil)

        self.dismiss(animated: true, completion: nil)
    }
    

    
    func productCPurchased(_ productIdentifier: String) {
        SVProgressHUD.dismiss()
        
        // 添加成功埋点
        if self.from == "AI换发" {
            MobClick.event("Subscribe", attributes: ["result": "AI换发订阅成功"])
            
            // 根据选择的订阅类型添加埋点
            if let styleName = self.style {
                switch chooseIndex {
                case 0:
                    // 包周订阅已隐藏，保留埋点代码以备将来使用
                    // MobClick.event("Subscribe", attributes: ["status": "\(styleName)&周&购买成功"])
                    break
                case 1:
                    MobClick.event("Subscribe", attributes: ["status": "\(styleName)&月&购买成功"])
                case 2:
                    MobClick.event("Subscribe", attributes: ["status": "\(styleName)&年&购买成功"])
                case 3:
                    MobClick.event("Subscribe", attributes: ["status": "\(styleName)&永久&购买成功"])
                default:
                    break
                }
            }
        } else if self.from == "三连屏" {
            MobClick.event("Subscribe", attributes: ["result": "三连屏订阅成功"])
        } else if self.from == "挽留弹窗" {
            MobClick.event("Subscribe", attributes: ["result": "挽留弹窗订阅成功"])
        }

        // 发送订阅状态更新通知
        NotificationCenter.default.post(name: .subscriptionStatusDidUpdate, object: nil)

        self.dismiss(animated: true, completion: nil)
    }
    
    func rsButtonAction(_ sender: UIButton?) {
        APPMakeStoreIAPManager.sharedManager.delegate = self
        APPMakeStoreIAPManager.sharedManager.restoreButClick {
            SVProgressHUD.show()
        }
    }
    
    // MARK: - Discount Popup
    
    private func showDiscountPopup() {
        // Get prices from UserDefaults
        guard let originalPrice = UserDefaults.standard.string(forKey: "featureBId"),
              let discountPrice = UserDefaults.standard.string(forKey: "featureDiscountId") else {
            return
        }
        
        // Create and show the popup with auto-subscribe
        let popupView = DiscountPopupView(frame: self.view.bounds, originalPrice: originalPrice, discountPrice: discountPrice)
        popupView.showAndAutoSubscribe(in: self, closeAction: { [weak self] in
            // Just close the popup
        }, subscribeAction: { [weak self] in
            // Trigger subscribe action
            SVProgressHUD.show()
            APPMakeStoreIAPManager.sharedManager.delegate = self
            
            // Use the discounted subscription if opened from discount shortcut
           
            APPMakeStoreIAPManager.sharedManager.buyFeatureDiscount()
                
            
        })
    }
    
    // Observe for restore purchases notification
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        NotificationCenter.default.addObserver(self, selector: #selector(handleRestorePurchases), name: NSNotification.Name("RestorePurchases"), object: nil)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("RestorePurchases"), object: nil)
    }
    
    @objc private func handleRestorePurchases() {
        rsButtonAction(nil)
    }
    
    // 添加处理返回操作的方法
    @objc func handleBackAction() {
        SVProgressHUD.dismiss()
        
        // 根据不同的展示方式确定返回方式
        if let navigationController = self.navigationController {
            // 使用导航控制器推出的情况
            if navigationController.viewControllers.count > 1 {
                navigationController.popViewController(animated: true)
            } else {
                // 导航控制器只有一个VC的情况，使用dismiss
                self.dismiss(animated: true, completion: nil)
            }
        } else {
            // 直接模态展示的情况
            self.dismiss(animated: true, completion: nil)
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 确保可以通过右滑或下滑手势返回
        if let navigationController = self.navigationController {
            navigationController.interactivePopGestureRecognizer?.isEnabled = true
            navigationController.interactivePopGestureRecognizer?.delegate = nil
        }
    }
}
