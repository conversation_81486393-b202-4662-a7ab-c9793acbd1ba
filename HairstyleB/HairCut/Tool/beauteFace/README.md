# PTMFilterHelper 美颜系统使用说明

## 概述

PTMFilterHelper 是基于 FaceUnity SDK 的美颜处理系统，支持完整的美颜业务功能，包括美肤、脸型、眼型、鼻子、嘴巴和眉毛的调整。

## 功能特性

### 美肤类型
- **磨皮** (blur_level): 0.0-6.0，默认6.0
- **美白** (color_level): 0.0-1.0，默认0.0
- **红润** (red_level): 0.0-1.0，默认0.0
- **法令纹** (remove_nasolabial_folds_strength): 0.0-1.0，默认0.0
- **亮眼** (eye_bright): 0.0-1.0，默认0.0
- **黑眼圈** (remove_pouch_strength): 0.0-1.0，默认0.0
- **美牙** (tooth_whiten): 0.0-1.0，默认0.0

### 脸型类型
- **v脸** (cheek_v): 0.0-1.0，默认0.0
- **窄面** (cheek_narrow): 0.0-1.0，默认0.0
- **小面** (cheek_small): 0.0-1.0，默认0.0
- **瘦脸** (cheek_thinning): 0.0-1.0，默认0.0
- **短脸** (cheek_short): 0.0-1.0，默认0.0
- **额头** (intensity_forehead): 0.0-1.0，默认0.5
- **下巴** (intensity_chin): 0.0-1.0，默认0.5
- **颧骨** (intensity_cheekbones): 0.0-1.0，默认0.0
- **下颚骨** (intensity_lower_jaw): 0.0-1.0，默认0.0

### 眼型类型
- **大眼** (eye_enlarging): 0.0-1.0，默认0.0
- **开眼角** (intensity_canthus): 0.0-1.0，默认0.0
- **眼距** (intensity_eye_space): 0.0-1.0，默认0.5
- **角度** (intensity_eye_rotate): 0.0-1.0，默认0.5
- **圆眼** (intensity_eye_circle): 0.0-1.0，默认0.0
- **亮眼** (eye_bright): 0.0-1.0，默认0.0
- **位置** (intensity_eye_height): 0.0-1.0，默认0.5
- **眼睑下至** (intensity_eye_lid): 0.0-1.0，默认0.0

### 鼻子类型
- **长鼻** (intensity_long_nose): 0.0-1.0，默认0.5
- **瘦鼻** (intensity_nose): 0.0-1.0，默认0.0
- **缩人中** (intensity_philtrum): 0.0-1.0，默认0.5

### 嘴巴类型
- **嘴型** (intensity_mouth): 0.0-1.0，默认0.5
- **微笑** (intensity_smile): 0.0-1.0，默认0.0
- **嘴唇厚度** (intensity_lip_thick): 0.0-1.0，默认0.5
- **美牙** (tooth_whiten): 0.0-1.0，默认0.0

### 眉毛类型
- **眉高** (intensity_brow_height): 0.0-1.0，默认0.5
- **眉间距** (intensity_brow_space): 0.0-1.0，默认0.5
- **眉毛粗细** (intensity_brow_thick): 0.0-1.0，默认0.5

## 使用方法

### 1. 初始化SDK

```objective-c
// 在应用启动时调用一次
[PTMFilterHelper setupSDK];
```

### 2. 设置美颜参数

```objective-c
// 设置单个参数
BOOL success = [PTMFilterHelper updateBeautyParameter:@"blur_level" value:4.0];

// 设置多个参数
[PTMFilterHelper updateBeautyParameter:@"color_level" value:0.3];
[PTMFilterHelper updateBeautyParameter:@"red_level" value:0.2];
[PTMFilterHelper updateBeautyParameter:@"cheek_v" value:0.2];
```

### 3. 应用美颜效果

```objective-c
// 使用当前配置的所有参数处理图片
UIImage *processedImage = [PTMFilterHelper processImageWithBeauty:originalImage];
```

### 4. 获取当前参数值

```objective-c
double currentValue = [PTMFilterHelper getCurrentValue:@"blur_level"];
```

### 5. 重置到默认值

```objective-c
[PTMFilterHelper resetToDefault];
```

## 配置文件说明

### 默认配置
- 位置：`HairCut/Resource/Json/beauty.json`
- 用途：项目内置的默认美颜参数配置

### 用户配置
- 位置：沙盒 Documents 目录下的 `user_beauty_config.json`
- 用途：保存用户修改后的参数值
- 自动管理：首次使用时自动从默认配置复制，后续修改保存到此文件

## 工作流程

1. **首次启动**：从项目内的 `beauty.json` 加载默认配置
2. **参数修改**：用户修改参数时，自动保存到沙盒的用户配置文件
3. **后续启动**：优先加载用户配置文件，如果不存在则使用默认配置
4. **美颜处理**：每次调用美颜时，应用所有当前配置的参数

## 技术实现

- **纯Objective-C实现**：不依赖Swift代码，避免混编问题
- **JSON配置管理**：使用NSJSONSerialization处理配置文件
- **内存缓存**：配置加载后缓存在内存中，提高性能
- **线程安全**：所有操作都在主线程进行，确保数据一致性

## 注意事项

1. **线程安全**：建议在后台线程进行图片处理，完成后回到主线程更新UI
2. **内存管理**：大图片会自动缩放到2048像素以内，避免内存问题
3. **参数范围**：请确保参数值在有效范围内，超出范围可能导致异常效果
4. **性能优化**：频繁调用时建议批量设置参数，然后统一处理

## 兼容性

保留了旧版本的滤镜接口，确保向后兼容：

```objective-c
// 旧版本接口仍然可用
UIImage *result = [PTMFilterHelper processImage:image 
                                  withFilterName:@"ziran1" 
                                     filterLevel:0.8];
```

## 示例代码

详细的使用示例请参考 `BeautyFilterDemo.swift` 文件。

## 测试

可以使用 `PTMFilterHelperTest.m` 中的测试方法验证功能：

```objective-c
// 运行所有测试
[PTMFilterHelperTest runAllTests];

// 单独测试
[PTMFilterHelperTest testBasicBeautyParameters];
[PTMFilterHelperTest testParameterUpdate];
[PTMFilterHelperTest testImageProcessing];
[PTMFilterHelperTest testConfigPersistence];
```

测试包括：
- 基础美颜参数设置和获取
- 参数更新和验证
- 图片处理功能
- 配置持久化
