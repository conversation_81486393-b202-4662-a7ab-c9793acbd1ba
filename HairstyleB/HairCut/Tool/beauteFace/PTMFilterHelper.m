#import "PTMFilterHelper.h"
#import "authpack.h"
#import <CoreVideo/CoreVideo.h>
#import <UIKit/UIKit.h>

// 导入正确的FaceUnity SDK头文件
#import <FURenderKit/FURenderKit.h>
#import <FURenderKit/FURenderer.h>

@implementation PTMFilterHelper



// 美颜配置缓存
static NSMutableDictionary *beautyConfig = nil;
static NSString *const kUserConfigFileName = @"user_beauty_config.json";

// 初始化SDK
+ (void)setupSDK {
    // 确保在主线程中执行SDK初始化
    if (![NSThread isMainThread]) {
        dispatch_sync(dispatch_get_main_queue(), ^{
            [self setupSDKOnMainThread];
        });
        return;
    }

    [self setupSDKOnMainThread];
}

// 在主线程中执行SDK初始化
+ (void)setupSDKOnMainThread {
    // 初始化FURenderKit
    FUSetupConfig *setupConfig = [[FUSetupConfig alloc] init];
    setupConfig.authPack = FUAuthPackMake(g_auth_package, sizeof(g_auth_package));
    [FURenderKit setupWithSetupConfig:setupConfig];

//    // 加载AI模型
    NSString *faceAIPath = [[NSBundle mainBundle] pathForResource:@"ai_face_processor" ofType:@"bundle"];
    if (faceAIPath) {
        [FUAIKit loadAIModeWithAIType:FUAITYPE_FACEPROCESSOR dataPath:faceAIPath];
        NSLog(@"✅ AI人脸模型加载成功");
    } else {
        NSLog(@"❌ AI人脸模型未找到");
    }

    // 初始化美颜
    NSString *beautyPath = [[NSBundle mainBundle] pathForResource:@"face_beautification" ofType:@"bundle"];
    if (beautyPath) {
        FUBeauty *beauty = [[FUBeauty alloc] initWithPath:beautyPath name:@"face_beautification"];
        [FURenderKit shareRenderKit].beauty = beauty;
        NSLog(@"✅ 美颜模块初始化成功: %@", beautyPath);
    } else {
        NSLog(@"❌ 美颜bundle未找到，请确保资源文件存在");
    }
}

// 应用美颜效果（使用当前配置的所有参数）
+ (UIImage *)processImageWithBeauty:(UIImage *)image {
    if (!image) {
        return nil;
    }

    // 确保在主线程中执行图片处理
    if (![NSThread isMainThread]) {
        __block UIImage *result = nil;
        dispatch_sync(dispatch_get_main_queue(), ^{
            result = [self processImageWithBeautyOnMainThread:image];
        });
        return result;
    }

    return [self processImageWithBeautyOnMainThread:image];
}

// 处理图片（不重新应用所有参数，使用当前FUBeauty状态）
+ (UIImage *)processImageWithCurrentBeautySettings:(UIImage *)image {
    if (!image) {
        return nil;
    }

    // 确保在主线程中执行图片处理
    if (![NSThread isMainThread]) {
        __block UIImage *result = nil;
        dispatch_sync(dispatch_get_main_queue(), ^{
            result = [self processImageWithCurrentBeautySettingsOnMainThread:image];
        });
        return result;
    }

    return [self processImageWithCurrentBeautySettingsOnMainThread:image];
}

// 在主线程中处理图片（不重新应用所有参数）
+ (UIImage *)processImageWithCurrentBeautySettingsOnMainThread:(UIImage *)image {
    // 确保SDK已初始化
    if (![FURenderKit shareRenderKit].beauty) {
        [self setupSDK];
    }

    if (![FURenderKit shareRenderKit].beauty) {
        return image; // 如果美颜道具加载失败，直接返回原图
    }

    // 检查图像尺寸，如果过大则缩小
    UIImage *processImage = [self resizeImageIfNeeded:image];

    // 直接使用当前FUBeauty状态处理图片，不重新应用参数
    return [self processImageWithFURenderer:processImage];
}

// 在主线程中执行图片处理
+ (UIImage *)processImageWithBeautyOnMainThread:(UIImage *)image {
    // 确保SDK已初始化
    if (![FURenderKit shareRenderKit].beauty) {
        [self setupSDK];
    }

    if (![FURenderKit shareRenderKit].beauty) {
        return image; // 如果美颜道具加载失败，直接返回原图
    }

    // 加载美颜配置
    [self loadBeautyConfig];

    // 检查图像尺寸，如果过大则缩小
    UIImage *processImage = [self resizeImageIfNeeded:image];

    // 注意：不需要每次都应用所有参数
    // 只有在明确需要应用所有参数时才调用
    // [self applyAllBeautyParameters];

    // 处理图片 - 使用当前已设置的参数
    return [self processImageWithFURenderer:processImage];
}

// 更新美颜参数
+ (BOOL)updateBeautyParameter:(NSString *)parameterName value:(double)value {
    return [self updateBeautyParameter:parameterName value:value saveConfig:YES];
}

// 更新美颜参数（可选择是否保存配置）
+ (BOOL)updateBeautyParameter:(NSString *)parameterName value:(double)value saveConfig:(BOOL)shouldSave {
    [self loadBeautyConfig];

    // 查找并更新参数
    BOOL found = [self updateParameterInConfig:parameterName value:value];

    if (found) {
        // 根据参数类型应用到FUBeauty
        [self applyParameterToFUBeauty:parameterName value:value];

        // 可选择是否保存到用户配置文件
        if (shouldSave) {
            // 在后台线程保存配置，避免阻塞主线程
            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                [self saveUserConfig];
            });
        }

        NSLog(@"✅ 更新美颜参数成功: %@ = %f", parameterName, value);
        return YES;
    } else {
        NSLog(@"❌ 更新美颜参数失败，未找到参数: %@", parameterName);
        return NO;
    }
}

// 批量更新美颜参数
+ (void)batchUpdateBeautyParameters:(NSArray<NSDictionary *> *)parameters {
    if (!parameters || parameters.count == 0) {
        return;
    }

    [self loadBeautyConfig];

    // 批量更新参数，不保存配置
    for (NSDictionary *param in parameters) {
        NSString *name = param[@"name"];
        NSNumber *value = param[@"value"];

        if (name && value) {
            [self updateBeautyParameter:name value:[value doubleValue] saveConfig:NO];
        }
    }

    // 最后统一保存配置
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self saveUserConfig];
    });

    NSLog(@"✅ 批量更新 %lu 个美颜参数", (unsigned long)parameters.count);
}

// 获取当前参数值
+ (double)getCurrentValue:(NSString *)parameterName {
    [self loadBeautyConfig];

    NSNumber *value = [self getParameterValue:parameterName];
    return value ? [value doubleValue] : -1.0;
}

// 根据参数名称应用到FUBeauty对象
+ (void)applyParameterToFUBeauty:(NSString *)parameterName value:(double)value {
    // 参数安全检查
    if (!parameterName || ![parameterName isKindOfClass:[NSString class]]) {
        NSLog(@"❌ 参数名无效: %@", parameterName);
        return;
    }

    if (isnan(value) || isinf(value)) {
        NSLog(@"❌ 参数值无效: %f", value);
        return;
    }

    // 确保在主线程中执行FUBeauty操作
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self applyParameterToFUBeautyOnMainThread:parameterName value:value];
        });
        return;
    }

    [self applyParameterToFUBeautyOnMainThread:parameterName value:value];
}

// 在主线程中执行FUBeauty参数设置
+ (void)applyParameterToFUBeautyOnMainThread:(NSString *)parameterName value:(double)value {
    FUBeauty *beauty = [FURenderKit shareRenderKit].beauty;
    if (!beauty) {
        NSLog(@"❌ FUBeauty对象未初始化");
        return;
    }

//    NSLog(@"🔧 应用参数: %@ = %f", parameterName, value);

    // 美肤参数
    if ([parameterName isEqualToString:@"blur_level"]) {
        beauty.blurLevel = value;
    } else if ([parameterName isEqualToString:@"color_level"]) {
        beauty.colorLevel = value;
    } else if ([parameterName isEqualToString:@"red_level"]) {
        beauty.redLevel = value;
    } else if ([parameterName isEqualToString:@"sharpen"]) {
        beauty.sharpen = value;
    } else if ([parameterName isEqualToString:@"faceThreed"]) {
        beauty.faceThreed = value;
    } else if ([parameterName isEqualToString:@"eye_bright"]) {
        beauty.eyeBright = value;
    } else if ([parameterName isEqualToString:@"tooth_whiten"]) {
        beauty.toothWhiten = value;
    } else if ([parameterName isEqualToString:@"remove_pouch_strength"]) {
        beauty.removePouchStrength = value;
    } else if ([parameterName isEqualToString:@"remove_nasolabial_folds_strength"]) {
        beauty.removeNasolabialFoldsStrength = value;
    }
    // 面部重塑参数
    else if ([parameterName isEqualToString:@"cheek_thinning"]) {
        beauty.cheekThinning = value;
    } else if ([parameterName isEqualToString:@"cheek_narrow"]) {
        beauty.cheekNarrow = value;
    } else if ([parameterName isEqualToString:@"cheek_small"]) {
        beauty.cheekSmall = value;
    } else if ([parameterName isEqualToString:@"cheek_short"]) {
        beauty.cheekShort = value;
    } else if ([parameterName isEqualToString:@"cheek_v"]) {
        beauty.cheekV = value;
    } else if ([parameterName isEqualToString:@"intensity_nose"]) {
        beauty.intensityNose = value;
    } else if ([parameterName isEqualToString:@"intensity_forehead"]) {
        beauty.intensityForehead = value;
    } else if ([parameterName isEqualToString:@"intensity_mouth"]) {
        beauty.intensityMouth = value;
    } else if ([parameterName isEqualToString:@"intensity_chin"]) {
        beauty.intensityChin = value;
    } else if ([parameterName isEqualToString:@"intensity_lower_jaw"]) {
        beauty.intensityLowerJaw = value;
    } else if ([parameterName isEqualToString:@"intensity_cheekbones"]) {
        beauty.intensityCheekbones = value;
    }
    // 眼部重塑参数
    else if ([parameterName isEqualToString:@"eye_enlarging"]) {
        beauty.eyeEnlarging = value;
    } else if ([parameterName isEqualToString:@"intensity_canthus"]) {
        beauty.intensityCanthus = value;
    } else if ([parameterName isEqualToString:@"intensity_eye_lid"]) {
        beauty.intensityEyeLid = value;
    } else if ([parameterName isEqualToString:@"intensity_eye_space"]) {
        beauty.intensityEyeSpace = value;
    } else if ([parameterName isEqualToString:@"intensity_eye_rotate"]) {
        beauty.intensityEyeRotate = value;
    } else if ([parameterName isEqualToString:@"intensity_eye_circle"]) {
        beauty.intensityEyeCircle = value;
    } else if ([parameterName isEqualToString:@"intensity_eye_height"]) {
        beauty.intensityEyeHeight = value;
    }
    // 鼻子重塑参数
    else if ([parameterName isEqualToString:@"intensity_long_nose"]) {
        beauty.intensityLongNose = value;
    } else if ([parameterName isEqualToString:@"intensity_philtrum"]) {
        beauty.intensityPhiltrum = value;
    } else if ([parameterName isEqualToString:@"intensity_smile"]) {
        beauty.intensitySmile = value;
    }
    // 嘴巴
    else if ([parameterName isEqualToString:@"intensity_lip_thick"]) {
        beauty.intensityLipThick = value;
    }
    // 眉毛重塑参数
    else if ([parameterName isEqualToString:@"intensity_brow_height"]) {
        beauty.intensityBrowHeight = value;
    } else if ([parameterName isEqualToString:@"intensity_brow_space"]) {
        beauty.intensityBrowSpace = value;
    } else if ([parameterName isEqualToString:@"intensity_brow_thick"]) {
        beauty.intensityBrowThick = value;
    } else {
        // 对于未知参数，暂时跳过（FURenderKit不支持直接设置未知参数）
        NSLog(@"⚠️ 未知的美颜参数，跳过设置: %@", parameterName);
    }

//    NSLog(@"✅ 已应用参数到FUBeauty: %@ = %f", parameterName, value);
}

// 重置所有参数到默认值
+ (void)resetToDefault {
    [self loadDefaultConfig];
    [self saveUserConfig];
    NSLog(@"✅ 已重置所有美颜参数到默认值");
}

// 应用所有美颜参数到FUBeauty
+ (void)applyAllBeautyParameters {
    if (!beautyConfig) {
        return;
    }

    NSInteger parameterCount = 0;

    // 遍历所有分类
    NSArray *categories = @[@"skinBeauty", @"faceShape", @"eyeShape", @"noseShape", @"mouthShape", @"eyebrowShape"];

    for (NSString *category in categories) {
        NSArray *parameters = beautyConfig[category];
        if ([parameters isKindOfClass:[NSArray class]]) {
            for (NSDictionary *param in parameters) {+
                if ([param isKindOfClass:[NSDictionary class]]) {
                    NSString *name = param[@"name"];
                    NSNumber *currentValue = param[@"currentValue"];

                    if (name && currentValue) {
                        // 使用新的方法应用参数到FUBeauty
                        [self applyParameterToFUBeauty:name value:[currentValue doubleValue]];
                        parameterCount++;
                    }
                }
            }
        }
    }

//    NSLog(@"✅ 已应用 %ld 个美颜参数", (long)parameterCount);
}


// 检查图像尺寸，如果过大则缩小
+ (UIImage *)resizeImageIfNeeded:(UIImage *)image {
    CGFloat maxDimension = 2048.0;
    if (image.size.width <= maxDimension && image.size.height <= maxDimension) {
        return image;
    }

    CGFloat scale;
    if (image.size.width > image.size.height) {
        scale = maxDimension / image.size.width;
    } else {
        scale = maxDimension / image.size.height;
    }

    CGSize newSize = CGSizeMake(image.size.width * scale, image.size.height * scale);
    UIGraphicsBeginImageContextWithOptions(newSize, NO, image.scale);
    [image drawInRect:CGRectMake(0, 0, newSize.width, newSize.height)];
    UIImage *resizedImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();

    NSLog(@"图像尺寸过大，已缩小至 %fx%f", newSize.width, newSize.height);
    return resizedImage;
}

// 使用FURenderKit处理图片
+ (UIImage *)processImageWithFURenderer:(UIImage *)image {
    // 确保在主线程中执行FURenderKit操作
    if (![NSThread isMainThread]) {
        __block UIImage *result = nil;
        dispatch_sync(dispatch_get_main_queue(), ^{
            result = [self processImageWithFURendererOnMainThread:image];
        });
        return result;
    }

    return [self processImageWithFURendererOnMainThread:image];
}

// 在主线程中执行FURenderKit处理
+ (UIImage *)processImageWithFURendererOnMainThread:(UIImage *)image {
    // 确保FUBeauty对象已初始化
    FUBeauty *beauty = [FURenderKit shareRenderKit].beauty;
    if (!beauty) {
        NSLog(@"❌ FUBeauty对象未初始化，返回原图");
        return image;
    }

    CVPixelBufferRef pixelBuffer = [self pixelBufferFromImage:image];
    if (!pixelBuffer) {
        NSLog(@"❌ 创建PixelBuffer失败，返回原图");
        return image;
    }

    // 使用FURenderKit处理图片
    @try {
        // 创建FURenderInput
        FURenderInput *renderInput = [[FURenderInput alloc] init];
        renderInput.pixelBuffer = pixelBuffer;

        // 渲染处理
        FURenderOutput *renderOutput = [[FURenderKit shareRenderKit] renderWithInput:renderInput];

        UIImage *processedImage = nil;
        if (renderOutput.pixelBuffer) {
            processedImage = [self imageFromPixelBuffer:renderOutput.pixelBuffer];
        }

        // 释放资源
        CVPixelBufferRelease(pixelBuffer);

        if (processedImage) {
            NSLog(@"✅ 美颜处理成功");
            return processedImage;
        } else {
            NSLog(@"❌ 处理后的图像为空，返回原图");
            return image;
        }
    } @catch (NSException *exception) {
        NSLog(@"❌ 应用美颜异常: %@", exception);

        // 释放资源
        if (pixelBuffer) {
            CVPixelBufferRelease(pixelBuffer);
        }

        return image;
    }
}

#pragma mark - Helper Methods

// 从UIImage创建CVPixelBuffer
+ (CVPixelBufferRef)pixelBufferFromImage:(UIImage *)image {
    CGSize frameSize = image.size;
    NSDictionary *options = @{
        (NSString*)kCVPixelBufferCGImageCompatibilityKey : @YES,
        (NSString*)kCVPixelBufferCGBitmapContextCompatibilityKey : @YES,
        (NSString*)kCVPixelBufferIOSurfacePropertiesKey : @{}, // 添加IOSurface属性可能会提高兼容性
    };
    
    CVPixelBufferRef pixelBuffer = NULL;
    CVReturn status = CVPixelBufferCreate(kCFAllocatorDefault,
                                         frameSize.width,
                                         frameSize.height,
                                         kCVPixelFormatType_32BGRA,
                                         (__bridge CFDictionaryRef)options,
                                         &pixelBuffer);
    
    if (status != kCVReturnSuccess) {
        NSLog(@"创建PixelBuffer失败，错误码: %d", status);
        return NULL;
    }
    
    CVPixelBufferLockBaseAddress(pixelBuffer, 0);
    void *data = CVPixelBufferGetBaseAddress(pixelBuffer);
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(data,
                                                frameSize.width,
                                                frameSize.height,
                                                8,
                                                CVPixelBufferGetBytesPerRow(pixelBuffer),
                                                colorSpace,
                                                kCGImageAlphaPremultipliedFirst | kCGBitmapByteOrder32Little);
    
    if (!context) {
        NSLog(@"创建CGContext失败");
        CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
        CVPixelBufferRelease(pixelBuffer);
        CGColorSpaceRelease(colorSpace);
        return NULL;
    }
    
    CGContextDrawImage(context, CGRectMake(0, 0, frameSize.width, frameSize.height), image.CGImage);
    
    CGColorSpaceRelease(colorSpace);
    CGContextRelease(context);
    CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
    
    return pixelBuffer;
}

// 从CVPixelBuffer创建UIImage
+ (UIImage *)imageFromPixelBuffer:(CVPixelBufferRef)pixelBuffer {
    if (!pixelBuffer) {
        return nil;
    }
    
    CVPixelBufferLockBaseAddress(pixelBuffer, kCVPixelBufferLock_ReadOnly);
    
    size_t width = CVPixelBufferGetWidth(pixelBuffer);
    size_t height = CVPixelBufferGetHeight(pixelBuffer);
    uint8_t *baseAddress = (uint8_t *)CVPixelBufferGetBaseAddress(pixelBuffer);
    size_t bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer);
    
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(baseAddress, width, height, 8, bytesPerRow, colorSpace, kCGImageAlphaPremultipliedFirst | kCGBitmapByteOrder32Little);
    
    if (!context) {
        NSLog(@"创建CGContext失败");
        CVPixelBufferUnlockBaseAddress(pixelBuffer, kCVPixelBufferLock_ReadOnly);
        CGColorSpaceRelease(colorSpace);
        return nil;
    }
    
    CGImageRef quartzImage = CGBitmapContextCreateImage(context);
    
    CVPixelBufferUnlockBaseAddress(pixelBuffer, kCVPixelBufferLock_ReadOnly);
    
    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);
    
    UIImage *image = [UIImage imageWithCGImage:quartzImage];
    CGImageRelease(quartzImage);
    
    return image;
}

#pragma mark - 配置管理方法

// 加载美颜配置
+ (void)loadBeautyConfig {
    if (beautyConfig) {
        return; // 已经加载过了
    }

    // 首先尝试加载用户配置
    if ([self loadUserConfig]) {
        return;
    }

    // 如果没有用户配置，加载默认配置
    [self loadDefaultConfig];
}

// 加载默认配置
+ (void)loadDefaultConfig {
    NSString *path = [[NSBundle mainBundle] pathForResource:@"beauty" ofType:@"json"];
    if (!path) {
        NSLog(@"❌ 找不到默认美颜配置文件 beauty.json");
        beautyConfig = [NSMutableDictionary dictionary];
        return;
    }

    NSData *data = [NSData dataWithContentsOfFile:path];
    if (!data) {
        NSLog(@"❌ 无法读取默认美颜配置文件");
        beautyConfig = [NSMutableDictionary dictionary];
        return;
    }

    NSError *error;
    NSDictionary *config = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];

    if (error || ![config isKindOfClass:[NSDictionary class]]) {
        NSLog(@"❌ 解析默认美颜配置文件失败: %@", error.localizedDescription);
        beautyConfig = [NSMutableDictionary dictionary];
        return;
    }

    beautyConfig = [config mutableCopy];
    NSLog(@"✅ 成功加载默认美颜配置");
}

// 加载用户配置
+ (BOOL)loadUserConfig {
    NSString *documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
    NSString *filePath = [documentsPath stringByAppendingPathComponent:kUserConfigFileName];

    if (![[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
        return NO;
    }

    NSData *data = [NSData dataWithContentsOfFile:filePath];
    if (!data) {
        return NO;
    }

    NSError *error;
    NSDictionary *config = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];

    if (error || ![config isKindOfClass:[NSDictionary class]]) {
        NSLog(@"❌ 解析用户美颜配置文件失败: %@", error.localizedDescription);
        return NO;
    }

    beautyConfig = [config mutableCopy];
    NSLog(@"✅ 成功加载用户美颜配置");
    return YES;
}

// 保存用户配置
+ (void)saveUserConfig {
    if (!beautyConfig) {
        return;
    }

    NSString *documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
    NSString *filePath = [documentsPath stringByAppendingPathComponent:kUserConfigFileName];

    NSError *error;
    NSData *data = [NSJSONSerialization dataWithJSONObject:beautyConfig options:NSJSONWritingPrettyPrinted error:&error];

    if (error) {
        NSLog(@"❌ 序列化用户美颜配置失败: %@", error.localizedDescription);
        return;
    }

    BOOL success = [data writeToFile:filePath atomically:YES];
    if (success) {
        NSLog(@"✅ 成功保存用户美颜配置");
    } else {
        NSLog(@"❌ 保存用户美颜配置失败");
    }
}

// 在配置中更新参数
+ (BOOL)updateParameterInConfig:(NSString *)parameterName value:(double)value {
    if (!beautyConfig) {
        return NO;
    }

    NSArray *categories = @[@"skinBeauty", @"faceShape", @"eyeShape", @"noseShape", @"mouthShape", @"eyebrowShape"];

    for (NSString *category in categories) {
        NSMutableArray *parameters = beautyConfig[category];
        if ([parameters isKindOfClass:[NSArray class]]) {
            for (NSMutableDictionary *param in parameters) {
                if ([param isKindOfClass:[NSDictionary class]]) {
                    NSString *name = param[@"name"];
                    if ([name isEqualToString:parameterName]) {
                        param[@"currentValue"] = @(value);
                        return YES;
                    }
                }
            }
        }
    }

    return NO;
}

// 获取参数值
+ (NSNumber *)getParameterValue:(NSString *)parameterName {
    if (!beautyConfig) {
        return nil;
    }

    NSArray *categories = @[@"skinBeauty", @"faceShape", @"eyeShape", @"noseShape", @"mouthShape", @"eyebrowShape"];

    for (NSString *category in categories) {
        NSArray *parameters = beautyConfig[category];
        if ([parameters isKindOfClass:[NSArray class]]) {
            for (NSDictionary *param in parameters) {
                if ([param isKindOfClass:[NSDictionary class]]) {
                    NSString *name = param[@"name"];
                    if ([name isEqualToString:parameterName]) {
                        return param[@"currentValue"];
                    }
                }
            }
        }
    }

    return nil;
}

@end
 
