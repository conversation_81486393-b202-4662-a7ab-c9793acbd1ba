#import <Foundation/Foundation.h>
@class UIImage;

NS_ASSUME_NONNULL_BEGIN

@interface PTMFilterHelper : NSObject

/**
 * 初始化美颜SDK
 */
+ (void)setupSDK;

/**
 * 应用美颜效果（使用当前配置的所有参数）
 * @param image 原始图片
 * @return 处理后的图片
 */
+ (UIImage * _Nullable)processImageWithBeauty:(UIImage * _Nonnull)image;

/**
 * 更新美颜参数
 * @param parameterName 参数名称
 * @param value 参数值
 * @return 是否更新成功
 */
+ (BOOL)updateBeautyParameter:(NSString * _Nonnull)parameterName value:(double)value;

/**
 * 更新美颜参数（可选择是否保存配置）
 * @param parameterName 参数名称
 * @param value 参数值
 * @param shouldSave 是否保存配置到文件
 * @return 是否更新成功
 */
+ (BOOL)updateBeautyParameter:(NSString * _Nonnull)parameterName value:(double)value saveConfig:(BOOL)shouldSave;

/**
 * 批量更新美颜参数
 * @param parameters 参数数组，每个元素包含 name 和 value 键
 */
+ (void)batchUpdateBeautyParameters:(NSArray<NSDictionary *> * _Nonnull)parameters;

/**
 * 获取当前参数值
 * @param parameterName 参数名称
 * @return 当前参数值，如果参数不存在返回-1
 */
+ (double)getCurrentValue:(NSString * _Nonnull)parameterName;

/**
 * 重置所有参数到默认值
 */
+ (void)resetToDefault;

/**
 * 直接应用参数到FUBeauty对象（不保存配置）
 * @param parameterName 参数名称
 * @param value 参数值
 */
+ (void)applyParameterToFUBeauty:(NSString * _Nonnull)parameterName value:(double)value;

/**
 * 处理图片（不重新应用所有参数，使用当前FUBeauty状态）
 * @param image 原始图片
 * @return 处理后的图片
 */
+ (UIImage * _Nullable)processImageWithCurrentBeautySettings:(UIImage * _Nonnull)image;


@end

NS_ASSUME_NONNULL_END
