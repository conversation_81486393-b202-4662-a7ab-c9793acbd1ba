//
//  ThreadSafeSnapKit.swift
//  HairCut
//
//  Created by AI Assistant on 2025/1/8.
//  线程安全的SnapKit约束操作工具
//

import Foundation
import SnapKit
import UIKit

// MARK: - 全局线程安全约束函数
/// 全局线程安全的makeConstraints函数
/// 使用方式: safeConstraints(view) { make in ... }
func safeConstraints<T: UIView>(_ view: T, _ closure: @escaping (ConstraintMaker) -> Void) {
    if Thread.isMainThread {
        view.snp.makeConstraints(closure)
    } else {
        DispatchQueue.main.sync {
            view.snp.makeConstraints(closure)
        }
    }
}

/// 全局线程安全的updateConstraints函数
func safeUpdateConstraints<T: UIView>(_ view: T, _ closure: @escaping (ConstraintMaker) -> Void) {
    if Thread.isMainThread {
        view.snp.updateConstraints(closure)
    } else {
        DispatchQueue.main.sync {
            view.snp.updateConstraints(closure)
        }
    }
}

/// 全局线程安全的remakeConstraints函数
func safeRemakeConstraints<T: UIView>(_ view: T, _ closure: @escaping (ConstraintMaker) -> Void) {
    if Thread.isMainThread {
        view.snp.remakeConstraints(closure)
    } else {
        DispatchQueue.main.sync {
            view.snp.remakeConstraints(closure)
        }
    }
}

/// 全局线程安全的removeConstraints函数
func safeRemoveConstraints<T: UIView>(_ view: T) {
    if Thread.isMainThread {
        view.snp.removeConstraints()
    } else {
        DispatchQueue.main.sync {
            view.snp.removeConstraints()
        }
    }
}

// MARK: - 线程安全的UI操作函数
/// 线程安全的文本设置
func safeSetText(_ view: UIView, _ text: String?) {
    if Thread.isMainThread {
        setTextInternal(view, text)
    } else {
        DispatchQueue.main.sync {
            setTextInternal(view, text)
        }
    }
}

private func setTextInternal(_ view: UIView, _ text: String?) {
    if let label = view as? UILabel {
        label.text = text
    } else if let button = view as? UIButton {
        button.setTitle(text, for: .normal)
    } else if let textField = view as? UITextField {
        textField.text = text
    } else if let textView = view as? UITextView {
        textView.text = text
    }
}

/// 线程安全的图片设置
func safeSetImage(_ view: UIView, _ image: UIImage?) {
    if Thread.isMainThread {
        setImageInternal(view, image)
    } else {
        DispatchQueue.main.sync {
            setImageInternal(view, image)
        }
    }
}

private func setImageInternal(_ view: UIView, _ image: UIImage?) {
    if let imageView = view as? UIImageView {
        imageView.image = image
    } else if let button = view as? UIButton {
        button.setImage(image, for: .normal)
    }
}

/// 线程安全的背景色设置
func safeSetBackgroundColor(_ view: UIView, _ color: UIColor?) {
    if Thread.isMainThread {
        view.backgroundColor = color
    } else {
        DispatchQueue.main.sync {
            view.backgroundColor = color
        }
    }
}

/// 线程安全的隐藏/显示设置
func safeSetHidden(_ view: UIView, _ hidden: Bool) {
    if Thread.isMainThread {
        view.isHidden = hidden
    } else {
        DispatchQueue.main.sync {
            view.isHidden = hidden
        }
    }
}

/// 线程安全的alpha设置
func safeSetAlpha(_ view: UIView, _ alpha: CGFloat) {
    if Thread.isMainThread {
        view.alpha = alpha
    } else {
        DispatchQueue.main.sync {
            view.alpha = alpha
        }
    }
}

/// 线程安全的布局更新
func safeLayoutIfNeeded(_ view: UIView) {
    if Thread.isMainThread {
        view.layoutIfNeeded()
    } else {
        DispatchQueue.main.sync {
            view.layoutIfNeeded()
        }
    }
}

/// 线程安全的setNeedsLayout
func safeSetNeedsLayout(_ view: UIView) {
    if Thread.isMainThread {
        view.setNeedsLayout()
    } else {
        DispatchQueue.main.async {
            view.setNeedsLayout()
        }
    }
}

// MARK: - 线程检查工具
/// 检查当前是否在主线程，如果不是则输出警告
func assertMainThread(function: String = #function, file: String = #file, line: Int = #line) {
    if !Thread.isMainThread {
        let fileName = (file as NSString).lastPathComponent
        print("⚠️ [AutoLayout Thread Safety Warning]")
        print("   Function: \(function)")
        print("   File: \(fileName):\(line)")
        print("   Current thread: \(Thread.current)")
        print("   This UI operation should be performed on the main thread!")
    }
}

// MARK: - 批量UI操作
/// 批量线程安全的UI操作
func safeBatchUIOperations(_ operations: @escaping () -> Void) {
    if Thread.isMainThread {
        operations()
    } else {
        DispatchQueue.main.sync {
            operations()
        }
    }
}

/// 异步批量UI操作
func safeBatchUIOperationsAsync(_ operations: @escaping () -> Void) {
    if Thread.isMainThread {
        operations()
    } else {
        DispatchQueue.main.async {
            operations()
        }
    }
}

// MARK: - 调试工具
/// 启用AutoLayout线程安全调试模式
var isAutoLayoutDebuggingEnabled = false

/// 设置AutoLayout调试模式
func setAutoLayoutDebugging(enabled: Bool) {
    isAutoLayoutDebuggingEnabled = enabled
    if enabled {
        print("🔧 AutoLayout线程安全调试模式已启用")
    }
}

/// 调试信息输出
func debugAutoLayoutOperation(operation: String, view: UIView, function: String = #function) {
    if isAutoLayoutDebuggingEnabled {
        let viewDescription = "\(type(of: view))"
        let threadInfo = Thread.isMainThread ? "主线程" : "后台线程"
        print("🔧 [AutoLayout Debug] \(operation) - \(viewDescription) - \(threadInfo) - \(function)")
    }
}
