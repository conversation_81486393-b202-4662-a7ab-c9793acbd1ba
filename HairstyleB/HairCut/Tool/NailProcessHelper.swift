//
//  NailProcessHelper.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import UIKit
import SVProgressHUD

/// 美甲处理助手类
class NailProcessHelper {
    
    /// 从指定视图控制器启动美甲处理
    /// - Parameters:
    ///   - image: 用户上传的图片
    ///   - fromViewController: 启动的视图控制器
    static func startNailProcess(with image: UIImage, from fromViewController: UIViewController) {
        let nailProcessVC = NailProcessViewController(userImage: image)
        let navController = UINavigationController(rootViewController: nailProcessVC)
        navController.modalPresentationStyle = .fullScreen
        
        fromViewController.present(navController, animated: true)
    }
    
    /// 直接处理美甲（不显示选择界面）
    /// - Parameters:
    ///   - image: 原始图片
    ///   - nailModel: 美甲模型
    ///   - completion: 完成回调
    static func processNailDirectly(
        image: UIImage,
        nailModel: NailModel,
        completion: @escaping (Result<UIImage, Error>) -> Void
    ) {
        VolcanoEngineAPI.processNailWithModel(
            image: image,
            nailModel: nailModel,
            completion: completion
        )
    }
    
    /// 批量处理美甲
    /// - Parameters:
    ///   - image: 原始图片
    ///   - nailModels: 美甲模型数组
    ///   - progressCallback: 进度回调
    ///   - completion: 完成回调
    static func batchProcessNails(
        image: UIImage,
        nailModels: [NailModel],
        progressCallback: @escaping (Int, Int) -> Void,
        completion: @escaping ([UIImage], [Error]) -> Void
    ) {
        var results: [UIImage] = []
        var errors: [Error] = []
        let group = DispatchGroup()
        
        for (index, model) in nailModels.enumerated() {
            group.enter()
            
            processNailDirectly(image: image, nailModel: model) { result in
                switch result {
                case .success(let processedImage):
                    results.append(processedImage)
                case .failure(let error):
                    errors.append(error)
                }
                
                DispatchQueue.main.async {
                    progressCallback(index + 1, nailModels.count)
                }
                
                group.leave()
            }
        }
        
        group.notify(queue: .main) {
            completion(results, errors)
        }
    }
    
    /// 验证图片是否适合美甲处理
    /// - Parameter image: 待验证的图片
    /// - Returns: 验证结果和建议
    static func validateImageForNailProcess(_ image: UIImage) -> (isValid: Bool, message: String) {
        // 检查图片尺寸
        let minSize: CGFloat = 200
        let maxSize: CGFloat = 4096
        
        if image.size.width < minSize || image.size.height < minSize {
            return (false, "图片尺寸太小，建议至少 \(Int(minSize))x\(Int(minSize)) 像素")
        }
        
        if image.size.width > maxSize || image.size.height > maxSize {
            return (false, "图片尺寸太大，建议不超过 \(Int(maxSize))x\(Int(maxSize)) 像素")
        }
        
        // 检查图片格式
        guard let _ = image.jpegData(compressionQuality: 0.8) else {
            return (false, "图片格式不支持，请使用 JPEG 或 PNG 格式")
        }
        
        return (true, "图片符合处理要求")
    }
    
    /// 预处理图片（调整尺寸、压缩等）
    /// - Parameter image: 原始图片
    /// - Returns: 预处理后的图片
    static func preprocessImage(_ image: UIImage) -> UIImage {
        let maxSize: CGFloat = 1024
        
        // 如果图片尺寸超过限制，进行缩放
        if image.size.width > maxSize || image.size.height > maxSize {
            let scale = min(maxSize / image.size.width, maxSize / image.size.height)
            let newSize = CGSize(width: image.size.width * scale, height: image.size.height * scale)
            
            UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
            image.draw(in: CGRect(origin: .zero, size: newSize))
            let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            
            return resizedImage ?? image
        }
        
        return image
    }
    
    /// 保存处理结果到相册
    /// - Parameters:
    ///   - image: 要保存的图片
    ///   - completion: 完成回调
    static func saveImageToPhotoLibrary(_ image: UIImage, completion: @escaping (Bool, Error?) -> Void) {
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        
        // 简单的成功回调（实际项目中可以使用 Photos 框架获取更详细的结果）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            completion(true, nil)
        }
    }
    
    /// 创建美甲处理的快捷操作
    /// - Parameters:
    ///   - image: 原始图片
    ///   - fromViewController: 启动的视图控制器
    ///   - quickProcess: 是否快速处理（使用默认样式）
    static func quickNailProcess(
        image: UIImage,
        from fromViewController: UIViewController,
        quickProcess: Bool = false
    ) {
        // 验证图片
        let validation = validateImageForNailProcess(image)
        if !validation.isValid {
            SVProgressHUD.showError(withStatus: validation.message)
            return
        }
        
        // 预处理图片
        let processedImage = preprocessImage(image)
        
        if quickProcess {
            // 快速处理：使用第一个可用的美甲样式
            SVProgressHUD.show(withStatus: "加载美甲样式...")
            
            NailModel.fetchNailData { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let models):
                        if let firstModel = models.first {
                            SVProgressHUD.show(withStatus: "正在处理...")
                            
                            processNailDirectly(image: processedImage, nailModel: firstModel) { result in
                                DispatchQueue.main.async {
                                    SVProgressHUD.dismiss()
                                    
                                    switch result {
                                    case .success(let resultImage):
                                        showResultImage(resultImage, from: fromViewController)
                                    case .failure(let error):
                                        SVProgressHUD.showError(withStatus: "处理失败: \(error.localizedDescription)")
                                    }
                                }
                            }
                        } else {
                            SVProgressHUD.showError(withStatus: "没有可用的美甲样式")
                        }
                    case .failure(let error):
                        SVProgressHUD.showError(withStatus: "加载失败: \(error.localizedDescription)")
                    }
                }
            }
        } else {
            // 正常流程：显示选择界面
            startNailProcess(with: processedImage, from: fromViewController)
        }
    }
    
    /// 显示处理结果图片
    /// - Parameters:
    ///   - image: 结果图片
    ///   - fromViewController: 显示的视图控制器
    private static func showResultImage(_ image: UIImage, from fromViewController: UIViewController) {
        let alert = UIAlertController(title: "美甲处理完成", message: "是否保存到相册？", preferredStyle: .alert)
        
        alert.addAction(UIAlertAction(title: "保存", style: .default) { _ in
            saveImageToPhotoLibrary(image) { success, error in
                DispatchQueue.main.async {
                    if success {
                        SVProgressHUD.showSuccess(withStatus: "保存成功")
                    } else {
                        SVProgressHUD.showError(withStatus: "保存失败")
                    }
                }
            }
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        fromViewController.present(alert, animated: true)
    }
}

// MARK: - UIViewController 扩展
extension UIViewController {
    
    /// 便捷方法：启动美甲处理
    /// - Parameter image: 用户图片
    func startNailProcess(with image: UIImage) {
        NailProcessHelper.startNailProcess(with: image, from: self)
    }
    
    /// 便捷方法：快速美甲处理
    /// - Parameter image: 用户图片
    func quickNailProcess(with image: UIImage) {
        NailProcessHelper.quickNailProcess(image: image, from: self, quickProcess: true)
    }
}
