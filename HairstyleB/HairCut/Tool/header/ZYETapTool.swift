//
//  ZYETapTool.swift
//  SearchT
//
//  Created by fs0011 on 2024/7/3.
//

import Foundation
import UIKit
extension UIView {
    // 定义一个类型别名，用于点击事件的闭包
    typealias TapAction = (() -> Void)?
    
    // 存储点击事件的闭包
    private struct AssociatedKeys {
        static var tapAction = "tapAction"
    }
    
    private var tapAction: TapAction {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.tapAction) as? TapAction ?? nil
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.tapAction, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    // 添加点击事件处理器
    func addTapAction(_ action: @escaping () -> Void) {
        isUserInteractionEnabled = true
        tapAction = action
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap))
        addGestureRecognizer(tapGesture)
    }
    
    // 点击事件处理方法
    @objc private func handleTap() {
        tapAction?()
    }
}
