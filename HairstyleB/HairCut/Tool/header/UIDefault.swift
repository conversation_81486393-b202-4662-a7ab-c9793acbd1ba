//
//  UIDefault.swift
//  SearchT
//
//  Created by fs0011 on 2024/7/2.
//

import Foundation
import SnapKit

// MARK: - 线程安全的SnapKit扩展
extension UIView {

    /// 线程安全的makeConstraints - 确保在主线程执行
    func safeSnpMakeConstraints(_ closure: @escaping (ConstraintMaker) -> Void) {
        if Thread.isMainThread {
            self.snp.makeConstraints(closure)
        } else {
            DispatchQueue.main.sync {
                self.snp.makeConstraints(closure)
            }
        }
    }

    /// 线程安全的updateConstraints - 确保在主线程执行
    func safeSnpUpdateConstraints(_ closure: @escaping (ConstraintMaker) -> Void) {
        if Thread.isMainThread {
            self.snp.updateConstraints(closure)
        } else {
            DispatchQueue.main.sync {
                self.snp.updateConstraints(closure)
            }
        }
    }

    /// 线程安全的remakeConstraints - 确保在主线程执行
    func safeSnpRemakeConstraints(_ closure: @escaping (ConstraintMaker) -> Void) {
        if Thread.isMainThread {
            self.snp.remakeConstraints(closure)
        } else {
            DispatchQueue.main.sync {
                self.snp.remakeConstraints(closure)
            }
        }
    }

    /// 线程安全的removeConstraints - 确保在主线程执行
    func safeSnpRemoveConstraints() {
        if Thread.isMainThread {
            self.snp.removeConstraints()
        } else {
            DispatchQueue.main.sync {
                self.snp.removeConstraints()
            }
        }
    }

    /// 线程安全的UI属性设置 - 防止在后台线程设置UI属性
    func safeSetText(_ text: String?) {
        if Thread.isMainThread {
            if let label = self as? UILabel {
                label.text = text
            } else if let button = self as? UIButton {
                button.setTitle(text, for: .normal)
            } else if let textField = self as? UITextField {
                textField.text = text
            } else if let textView = self as? UITextView {
                textView.text = text
            }
        } else {
            DispatchQueue.main.sync {
                if let label = self as? UILabel {
                    label.text = text
                } else if let button = self as? UIButton {
                    button.setTitle(text, for: .normal)
                } else if let textField = self as? UITextField {
                    textField.text = text
                } else if let textView = self as? UITextView {
                    textView.text = text
                }
            }
        }
    }

    /// 线程安全的图片设置
    func safeSetImage(_ image: UIImage?) {
        if Thread.isMainThread {
            if let imageView = self as? UIImageView {
                imageView.image = image
            } else if let button = self as? UIButton {
                button.setImage(image, for: .normal)
            }
        } else {
            DispatchQueue.main.sync {
                if let imageView = self as? UIImageView {
                    imageView.image = image
                } else if let button = self as? UIButton {
                    button.setImage(image, for: .normal)
                }
            }
        }
    }

    /// 线程安全的背景色设置
    func safeSetBackgroundColor(_ color: UIColor?) {
        if Thread.isMainThread {
            self.backgroundColor = color
        } else {
            DispatchQueue.main.sync {
                self.backgroundColor = color
            }
        }
    }

    /// 线程安全的隐藏/显示设置
    func safeSetHidden(_ hidden: Bool) {
        if Thread.isMainThread {
            self.isHidden = hidden
        } else {
            DispatchQueue.main.sync {
                self.isHidden = hidden
            }
        }
    }

    /// 线程安全的alpha设置
    func safeSetAlpha(_ alpha: CGFloat) {
        if Thread.isMainThread {
            self.alpha = alpha
        } else {
            DispatchQueue.main.sync {
                self.alpha = alpha
            }
        }
    }

    /// 线程安全的布局更新
    func safeLayoutIfNeeded() {
        if Thread.isMainThread {
            self.layoutIfNeeded()
        } else {
            DispatchQueue.main.sync {
                self.layoutIfNeeded()
            }
        }
    }

    /// 线程安全的setNeedsLayout
    func safeSetNeedsLayout() {
        if Thread.isMainThread {
            self.setNeedsLayout()
        } else {
            DispatchQueue.main.async {
                self.setNeedsLayout()
            }
        }
    }
}

// MARK: - AutoLayout线程安全保护
extension UIView {

    /// 检查当前线程是否为主线程，如果不是则输出警告
    private func assertMainThread(function: String = #function) {
        if !Thread.isMainThread {
            print("⚠️ [AutoLayout Thread Safety] \(function) called on background thread! This may cause crashes.")
            print("   Current thread: \(Thread.current)")
            print("   Call stack: \(Thread.callStackSymbols.prefix(5))")
        }
    }
}

extension Array where Element: UIView {
    func distributeViewsHorizontally(fixedSpacing: CGFloat, leadSpacing: CGFloat, tailSpacing: CGFloat) {
        guard let superview = self.first?.superview else {
            print("Error: Superview does not exist.")
            return
        }

        // 使用线程安全的约束设置
        for (index, view) in self.enumerated() {
            view.safeSnpMakeConstraints { make in
                if index == 0 {
                    make.left.equalTo(superview).offset(leadSpacing)
                } else {
                    make.left.equalTo(self[index - 1].snp.right).offset(fixedSpacing)
                }

                if index == self.count - 1 {
                    make.right.equalTo(superview).offset(-tailSpacing)
                }
            }
        }
    }
}


