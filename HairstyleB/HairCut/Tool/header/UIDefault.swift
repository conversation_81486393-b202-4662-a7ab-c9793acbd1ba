//
//  UIDefault.swift
//  SearchT
//
//  Created by fs0011 on 2024/7/2.
//

import Foundation
import SnapKit

extension Array where Element: UIView {
    func distributeViewsHorizontally(fixedSpacing: CGFloat, leadSpacing: CGFloat, tailSpacing: CGFloat) {
        guard let superview = self.first?.superview else {
            print("Error: Superview does not exist.")
            return
        }
        
        for (index, view) in self.enumerated() {
            view.snp.makeConstraints { make in
                if index == 0 {
                    make.left.equalTo(superview).offset(leadSpacing)
                } else {
                    make.left.equalTo(self[index - 1].snp.right).offset(fixedSpacing)
                }
                
                if index == self.count - 1 {
                    make.right.equalTo(superview).offset(-tailSpacing)
                }
            }
        }
    }
}


