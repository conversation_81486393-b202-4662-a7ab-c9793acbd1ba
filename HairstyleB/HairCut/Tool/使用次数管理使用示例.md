# 使用次数管理使用示例

## 概述
本文档展示如何在项目中使用新的使用次数管理功能。

## 功能分类

### 1. 发型编辑功能（同步处理）
- **次数限制**: 未订阅用户每日2次，订阅用户无限制
- **管理器**: `HairEditUsageManager`
- **超限处理**: 直接弹出订阅页面
- **API方法**: `processHairEditWithDynamicKeys`

### 2. 异步图生图功能（发型AI、造型AI、美甲）
- **次数限制**: 根据在线JSON配置，支持购买次数包
- **管理器**: `VolcanoUsageManager`
- **超限处理**: 弹出购买弹窗，VIP用户只显示购买按钮，非VIP用户显示购买+订阅按钮
- **API方法**: `processAsyncImageGeneration` 或 `processNail`

## 使用示例

### 1. 发型编辑页面使用

```swift
// 在发型编辑按钮点击时
@IBAction func startHairEdit(_ sender: UIButton) {
    // 检查发型编辑次数
    guard HairEditUsageManager.shared.canUseHairEdit() else {
        // 次数不足会自动弹出订阅页面
        return
    }
    
    // 显示剩余次数提示
    let remaining = HairEditUsageManager.shared.getRemainingHairEditCount()
    if remaining != Int.max && remaining <= 1 {
        showToast("今日剩余发型编辑次数: \(remaining)")
    }
    
    // 执行发型编辑
    performHairEdit()
}

private func performHairEdit() {
    guard let image = selectedImage else { return }
    
    VolcanoEngineAPI.processHairEditWithType(
        image: image,
        hairType: selectedHairType
    ) { result in
        DispatchQueue.main.async {
            switch result {
            case .success(let processedImage):
                // 处理成功，次数已自动消耗
                self.showResult(processedImage)
            case .failure(let error):
                self.showError(error.localizedDescription)
            }
        }
    }
}
```

### 2. 异步图生图功能使用（发型AI、造型AI、美甲）

```swift
// 在生成按钮点击时
@IBAction func startAsyncGeneration(_ sender: UIButton) {
    // 检查异步图生图使用次数
    let remaining = VolcanoUsageManager.shared.getRemainingCount()
    if remaining <= 3 {
        showToast("剩余生成次数: \(remaining)")
    }

    // 执行异步图生图
    performAsyncGeneration()
}

private func performAsyncGeneration() {
    guard let image = selectedImage,
          let prompt = getPrompt() else { return }

    // API会自动检查次数，不足时弹出购买弹窗
    // VIP用户弹窗只显示购买按钮，非VIP用户显示购买+订阅按钮
    VolcanoEngineAPI.processAsyncImageGeneration(
        image: image,
        prompt: prompt
    ) { result in
        DispatchQueue.main.async {
            switch result {
            case .success(let processedImage):
                // 处理成功，次数已自动消耗
                self.showResult(processedImage)
            case .failure(let error):
                if error.localizedDescription.contains("VolcanoUsageLimit") {
                    // 次数不足，已自动弹出购买弹窗
                    print("次数不足，等待用户购买")
                } else {
                    self.showError(error.localizedDescription)
                }
            }
        }
    }
}

// 美甲专用方法（如果有NailModel）
private func performNailGeneration() {
    guard let image = selectedImage,
          let nailModel = selectedNailModel else { return }

    VolcanoEngineAPI.processNailWithModel(
        image: image,
        nailModel: nailModel
    ) { result in
        // 处理结果...
    }
}
```

### 3. 订阅状态变化处理

```swift
// 在订阅成功后调用
func onSubscriptionSuccess() {
    // 刷新美甲使用次数状态
    VolcanoUsageManager.shared.refreshSubscriptionStatus()
    
    // 发型编辑次数会自动检测VIP状态，无需手动刷新
    
    // 更新UI显示
    updateUsageStatusUI()
}

// 更新使用状态UI
func updateUsageStatusUI() {
    // 发型编辑状态
    let hairEditStatus = HairEditUsageManager.shared.getHairEditStatusDescription()
    hairEditStatusLabel.text = hairEditStatus
    
    // 美甲使用状态
    let nailStatus = VolcanoUsageManager.shared.getSubscriptionStatusDescription()
    let remaining = VolcanoUsageManager.shared.getRemainingCount()
    nailStatusLabel.text = "\(nailStatus) - 剩余: \(remaining)次"
}
```

### 4. 设置页面显示使用情况

```swift
class SettingsViewController: UIViewController {
    
    @IBOutlet weak var hairEditStatusLabel: UILabel!
    @IBOutlet weak var nailUsageStatusLabel: UILabel!
    @IBOutlet weak var configInfoTextView: UITextView!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        updateUsageInfo()
    }
    
    private func updateUsageInfo() {
        // 发型编辑状态
        hairEditStatusLabel.text = HairEditUsageManager.shared.getHairEditStatusDescription()
        
        // 美甲使用状态
        let remaining = VolcanoUsageManager.shared.getRemainingCount()
        let status = VolcanoUsageManager.shared.getSubscriptionStatusDescription()
        nailUsageStatusLabel.text = "\(status) - 剩余: \(remaining)次"
        
        // 详细配置信息(调试用)
        configInfoTextView.text = VolcanoUsageManager.shared.getConfigInfo()
    }
    
    // 管理员功能：重置次数
    @IBAction func resetUsageCount(_ sender: UIButton) {
        let alert = UIAlertController(title: "重置次数", message: "确定要重置使用次数吗？", preferredStyle: .alert)
        
        alert.addAction(UIAlertAction(title: "重置发型编辑", style: .default) { _ in
            HairEditUsageManager.shared.resetDailyUsage()
            self.updateUsageInfo()
        })
        
        alert.addAction(UIAlertAction(title: "重置美甲次数", style: .default) { _ in
            VolcanoUsageManager.shared.resetMonthlyUsage()
            self.updateUsageInfo()
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
}
```

## 配置管理

### 在线配置更新
```swift
// 手动刷新在线配置
VolcanoUsageManager.shared.refreshOnlineConfig { success in
    if success {
        print("配置更新成功")
        // 更新UI
        self.updateUsageInfo()
    } else {
        print("配置更新失败")
    }
}
```

### 购买次数包
```swift
// 手动触发购买次数包
VolcanoEngineAPI.purchaseUsagePackage { success in
    if success {
        print("购买成功，已增加10次使用次数")
        // 更新UI
        self.updateUsageInfo()
    } else {
        print("购买失败或取消")
    }
}
```

## 注意事项

1. **自动化处理**: API调用会自动检查次数和处理超限情况，无需手动检查
2. **状态同步**: 订阅状态变化后记得调用刷新方法
3. **UI更新**: 在适当的时机更新UI显示的使用状态
4. **错误处理**: 注意区分次数不足和其他API错误
5. **测试**: 可以使用管理员功能重置次数进行测试

## 产品ID配置

确保在APPMakeStoreIAPManager中正确配置了次数购买的产品ID：
```swift
static let usagePackageId = "com.Fengyin.Camera.Frequency.package"
```

并在App Store Connect中创建对应的内购产品。
