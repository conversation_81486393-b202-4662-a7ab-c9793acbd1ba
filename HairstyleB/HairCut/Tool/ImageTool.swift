//
//  ImageTool.swift
//  HairCut
//
//  Created by Bigger on 2024/8/9.
//

import Foundation
import UIKit

class ImageTool {
    func getFixSize(_ size: CGSize, maxSize: CGFloat) -> CGSize {
        var pSize = size
        for _ in 0...800 {
            if pSize.width * pSize.height <= maxSize {
                break
            }
            pSize = CGSizeMake(pSize.width * 0.99, pSize.height * 0.99)
        }
        let width = pSize.width
        let height = pSize.height
        return CGSizeMake(width, height)
    }
}
