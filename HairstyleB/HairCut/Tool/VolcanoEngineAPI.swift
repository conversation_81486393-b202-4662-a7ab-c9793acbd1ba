//
//  VolcanoEngineAPI.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import Foundation
import UIKit
import Alamofire
import CommonCrypto

/// 次数购买代理
class UsagePurchaseDelegate: NSObject, MKStoreKitDelegate {
    private let completion: (Bool) -> Void

    init(completion: @escaping (Bool) -> Void) {
        self.completion = completion
        super.init()
    }

    func productCPurchased(_ productIdentifier: String) {
        if productIdentifier == APPMakeStoreIAPManager.usagePackageId {
            // 次数包购买成功，增加次数
            VolcanoUsageManager.shared.purchaseUsagePackage()
            completion(true)
        } else {
            // 其他产品ID（订阅产品），不是次数包购买，返回false
            completion(false)
        }
    }

    func failed() {
        completion(false)
    }

    func buySuccessBack() {
        // 购买成功回调 - 这个方法在某些情况下会被调用而不是productCPurchased
        // 为了确保状态一致，这里也增加次数并调用completion
        VolcanoUsageManager.shared.purchaseUsagePackage()
        completion(true)
    }

    func buycancelBack() {
        completion(false)
    }
}

/// 火山引擎图生图API工具类
class VolcanoEngineAPI {

    // 保持购买代理的强引用
    private static var currentPurchaseDelegate: UsagePurchaseDelegate?
    
    // MARK: - 配置信息
    private static var accessKeyId: String = ""
    private static var secretAccessKey: String = ""


    private static let baseURL = "https://visual.volcengineapi.com"
    private static let action = "CVSync2AsyncSubmitTask"
    private static let version = "2022-08-31"
    private static let region = "cn-north-1"
    private static let service = "cv"

    // MARK: - 密钥获取配置
    private static let keyQueryURL = "https://ukey.godimage.mobi:8886/authkey/query"
    private static let publicKey = "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCblVmnev6bID5A9PgvPpgaH7aY\nCoroVm8ejy8oClnUprPtLLbfQ/LJV7jjFIqac2yxYgOOX10J+cDeU3GMMggoXs1w\nABBAX07E7vM4TMmMPLurckrmXn/H02Hi0loU5S6UQYyFm1TCHIlWrNBgpRHh4AO8\nxJl5pn03BZ9xbqJZRwIDAQAB\n-----END PUBLIC KEY-----"
    
    // MARK: - 请求参数结构
    struct NailProcessRequest {
        let originalImage: UIImage
        let prompt: String
        let scale: Double = 1     // 文本描述影响程度，默认1
        let seed: Int = -1          // 随机种子，默认-1（随机）
    }

    // MARK: - 发型编辑请求参数结构
    struct HairEditRequest {
        let originalImage: UIImage
        let hairType: Int           // 发型类型
        let returnUrl: Bool = false // 是否返回图片链接
    }

    // MARK: - 提交任务响应结构
    struct SubmitTaskResponse: Codable {
        let code: Int
        let data: TaskData?
        let message: String
        let request_id: String?
        let status: Int
        let time_elapsed: String?

        struct TaskData: Codable {
            let task_id: String
        }
    }

    // MARK: - 查询结果响应结构
    struct QueryResultResponse: Codable {
        let code: Int
        let data: ResultData?
        let message: String
        let request_id: String?
        let status: Int
        let time_elapsed: String?

        struct ResultData: Codable {
            let binary_data_base64: [String]?
            let image_urls: [String]?
            let resp_data: String?
            let status: String  // in_queue, generating, done, not_found, expired
        }
    }

    // MARK: - 发型编辑响应结构（同步）
    struct HairEditResponse: Codable {
        let code: Int
        let data: HairEditData?
        let message: String
        let request_id: String?
        let status: Int
        let time_elapsed: String?

        struct HairEditData: Codable {
            let binary_data_base64: [String]?
            let image_urls: [String]?
        }
    }
    
    // MARK: - 密钥获取方法
    private static func fetchAPIKeys(completion: @escaping (Result<(String, String), Error>) -> Void) {
        // 构建参数
        let timestamp = Int(Date().timeIntervalSince1970)
        let parameters: [String: Any] = [
            "appId": "com.fullstack.api",
            "appKeys": ["volcengine_ai_canvas"],
            "timestamp": timestamp
        ]

        // 转换为JSON字符串（参考HttpTool的实现）
        guard let jsonData = try? JSONSerialization.data(withJSONObject: parameters, options: []),
              let datastr = String(data: jsonData, encoding: .utf8) else {
            completion(.failure(NSError(domain: "VolcanoEngineAPI", code: -1, userInfo: [NSLocalizedDescriptionKey: "参数转换失败"])))
            return
        }

        // RSA加密（使用encryptString方法，参考HttpTool的实现）
        guard let paraEncryptString = RSA.encryptString(datastr, publicKey: publicKey) else {
            completion(.failure(NSError(domain: "VolcanoEngineAPI", code: -2, userInfo: [NSLocalizedDescriptionKey: "RSA加密失败"])))
            return
        }

        // 构建请求参数
        let requestParams = ["para": paraEncryptString]

        print("🔑 ===== 密钥获取请求详情 =====")
        print("📍 URL: \(keyQueryURL)")
        print("🔧 Method: POST")
        print("📦 Parameters: \(requestParams)")
        print("🔑 ===== 密钥获取请求结束 =====\n")

        // 发送请求（使用multipartFormData，参考HttpTool的实现）
        let headers: HTTPHeaders = [
            "Content-Type": "application/json"
        ]

        AF.upload(multipartFormData: { multipartFormData in
            // 将 para 参数作为表单字段添加
            if let paraData = paraEncryptString.data(using: .utf8) {
                multipartFormData.append(paraData, withName: "para")
            }
        }, to: keyQueryURL, method: .post, headers: headers).response { response in
            print("🔑 ===== 密钥获取响应详情 =====")
            print("📊 Status Code: \(response.response?.statusCode ?? 0)")

            if let data = response.data {
                do {
                    let json = try JSONSerialization.jsonObject(with: data, options: [])
                    print("📦 Response: \(json)")

                    // 解析响应，提取密钥信息
                    if let jsonDict = json as? [String: Any] {
                        print("🔍 JSON结构: \(jsonDict)")

                        // 检查返回码
                        guard let code = jsonDict["code"] as? Int, code == 0 else {
                            let msg = jsonDict["msg"] as? String ?? "未知错误"
                            completion(.failure(NSError(domain: "VolcanoEngineAPI", code: -3, userInfo: [NSLocalizedDescriptionKey: "密钥获取失败: \(msg)"])))
                            return
                        }

                        // 解析data字段
                        guard let data = jsonDict["data"] as? [String: Any],
                              let appKeys = data["appKeys"] as? [[String: Any]],
                              let firstAppKey = appKeys.first,
                              let appSecretString = firstAppKey["appSecret"] as? String else {
                            completion(.failure(NSError(domain: "VolcanoEngineAPI", code: -4, userInfo: [NSLocalizedDescriptionKey: "密钥数据格式错误"])))
                            return
                        }

                        // 解析appSecret中的JSON字符串
                        guard let appSecretData = appSecretString.data(using: .utf8),
                              let appSecretJson = try? JSONSerialization.jsonObject(with: appSecretData) as? [String: Any],
                              let accessKeyId = appSecretJson["accesskey_id"] as? String,
                              let accessKeySecret = appSecretJson["accesskey_secret"] as? String else {
                            completion(.failure(NSError(domain: "VolcanoEngineAPI", code: -5, userInfo: [NSLocalizedDescriptionKey: "密钥解析失败"])))
                            return
                        }

                        print("✅ 密钥获取成功:")
                        print("   AccessKey ID: \(accessKeyId)")
                        print("   AccessKey Secret: \(accessKeySecret)")

                        // 返回解析出的密钥
                        completion(.success((accessKeyId, accessKeySecret)))
                    } else {
                        completion(.failure(NSError(domain: "VolcanoEngineAPI", code: -6, userInfo: [NSLocalizedDescriptionKey: "响应格式错误"])))
                    }
                } catch {
                    print("❌ JSON解析错误: \(error)")
                    completion(.failure(error))
                }
            } else if let error = response.error {
                print("❌ 网络错误: \(error)")
                completion(.failure(error))
            } else {
                completion(.failure(NSError(domain: "VolcanoEngineAPI", code: -5, userInfo: [NSLocalizedDescriptionKey: "未知错误"])))
            }
            print("🔑 ===== 密钥获取响应结束 =====\n")
        }
    }

    // MARK: - 美甲处理方法（异步流程）
    static func processNail(
        request: NailProcessRequest,
        completion: @escaping (Result<UIImage, Error>) -> Void
    ) {
        // 检查美甲使用次数
        guard VolcanoUsageManager.shared.canUse() else {
            // 次数不足，立即调用失败回调隐藏加载状态，然后显示购买弹窗
            let error = NSError(domain: "VolcanoUsageLimit", code: -1, userInfo: [NSLocalizedDescriptionKey: "使用次数不足"])
            completion(.failure(error))

            // 延迟显示购买弹窗，确保加载状态已经隐藏
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                showUsagePurchasePopup(from: "AI美甲", completion: {
                    // 购买成功后重新执行
                    processNail(request: request, completion: completion)
                })
            }
            return
        }

        // 第一步：获取密钥
        fetchAPIKeys { keyResult in
            switch keyResult {
            case .success(let (keyId, secretKey)):
                // 更新美甲功能的动态密钥
                accessKeyId = keyId
                secretAccessKey = secretKey

                // 第二步：提交任务
                submitTask(request: request) { result in
                    switch result {
                    case .success(let taskId):
                        // 第三步：轮询查询结果
                        pollTaskResult(taskId: taskId, completion: completion)
                    case .failure(let error):
                        completion(.failure(error))
                    }
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }



    // MARK: - 发型编辑处理方法（使用动态密钥）
    static func processHairEditWithDynamicKeys(
        request: HairEditRequest,
        completion: @escaping (Result<UIImage, Error>) -> Void
    ) {
        // 检查发型编辑使用次数
        guard HairEditUsageManager.shared.canUseHairEdit() else {
            // 次数不足，先隐藏HUD，然后弹出订阅页面
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()
                VolcanoEngineAPI.showSubscriptionPage(from: "发型编辑")
            }
            completion(.failure(NSError(domain: "HairEditUsageLimit", code: 1002, userInfo: [NSLocalizedDescriptionKey: local("发型编辑次数不足，请订阅会员")])))
            return
        }

        // 第一步：获取密钥
        fetchAPIKeys { keyResult in
            switch keyResult {
            case .success(let (keyId, secretKey)):
                print("🔑 动态密钥获取成功，开始发型编辑:")
                print("   Dynamic AccessKey ID: \(keyId)")
                print("   Dynamic AccessKey Secret: \(secretKey)")

                // 第二步：执行发型编辑（使用动态密钥）
                executeHairEditWithDynamicKeys(request: request, accessKeyId: keyId, secretAccessKey: secretKey, completion: completion)
            case .failure(let error):
                print("❌ 动态密钥获取失败: \(error.localizedDescription)")
                completion(.failure(error))
            }
        }
    }

    // MARK: - 执行发型编辑（使用动态密钥）
    private static func executeHairEditWithDynamicKeys(
        request: HairEditRequest,
        accessKeyId: String,
        secretAccessKey: String,
        completion: @escaping (Result<UIImage, Error>) -> Void
    ) {
        // 1. 准备图片数据
        guard let imageData = request.originalImage.jpegData(compressionQuality: 0.8) else {
            completion(.failure(NSError(domain: "ImageError", code: -1, userInfo: [NSLocalizedDescriptionKey: "图片数据转换失败"])))
            return
        }

        let base64Image = imageData.base64EncodedString()

        // 2. 构建请求参数（确保顺序一致）
        var parameters: [String: Any] = [:]
        parameters["req_key"] = "hair_style"
        parameters["hair_type"] = request.hairType
        parameters["return_url"] = request.returnUrl
        parameters["binary_data_base64"] = [base64Image]

        // 3. 生成签名和请求头（使用动态密钥）
        let timestamp = Date()
        let headers = generateHeadersWithDynamicKeys(timestamp: timestamp, body: parameters, action: "CVProcess", accessKeyId: accessKeyId, secretAccessKey: secretAccessKey)

        // 4. 发送同步请求
        let url = "\(baseURL)/?Action=CVProcess&Version=2022-08-31"

        // 打印请求信息
        print("🎨 ===== 动态密钥发型编辑请求详情 =====")
        print("📍 URL: \(url)")
        print("🔧 Method: POST")
        print("📋 Headers:")
        for (key, value) in headers {
            print("   \(key): \(value)")
        }
        print("📦 Body Parameters:")
        for (key, value) in parameters {
            if key == "binary_data_base64" {
                if let array = value as? [String], let first = array.first {
                    print("   \(key): [\"base64_length_\(first.count)\"]")
                } else {
                    print("   \(key): \(value)")
                }
            } else {
                print("   \(key): \(value)")
            }
        }
        print("🎨 ===== 动态密钥请求详情结束 =====\n")

        AF.request(
            url,
            method: .post,
            parameters: parameters,
            encoding: JSONEncoding.default,
            headers: HTTPHeaders(headers)
        )
        .validate()
        .responseDecodable(of: HairEditResponse.self) { response in
            // 打印响应详情
            print("📥 ===== 动态密钥发型编辑响应 =====")
            print("🔢 HTTP Status: \(response.response?.statusCode ?? -1)")

            if let data = response.data, let jsonString = String(data: data, encoding: .utf8) {
                // 只打印前500个字符
                let truncatedJson = jsonString.count > 500 ? String(jsonString.prefix(500)) + "..." : jsonString
                print("📄 Response: \(truncatedJson)")
            }

            if let error = response.error {
                print("❌ Error: \(error.localizedDescription)")
            }
            print("📥 ===== 动态密钥响应结束 =====\n")

            switch response.result {
            case .success(let apiResponse):
                print("✅ 动态密钥发型编辑解析成功: code=\(apiResponse.code), message=\(apiResponse.message)")
                if apiResponse.code == 10000 {
                    handleHairEditResult(apiResponse, completion: completion)
                } else {
                    print("❌ 动态密钥API返回错误: code=\(apiResponse.code), message=\(apiResponse.message)")
                    let error = NSError(domain: "APIError", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: apiResponse.message])
                    completion(.failure(error))
                }
            case .failure(let error):
                print("❌ 动态密钥发型编辑请求失败: \(error.localizedDescription)")
                completion(.failure(error))
            }
        }
    }

    // MARK: - 生成请求头和签名（使用动态密钥）- 逻辑与静态方法完全一致
    private static func generateHeadersWithDynamicKeys(timestamp: Date, body: [String: Any], action: String, accessKeyId: String, secretAccessKey: String) -> [String: String] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd'T'HHmmss'Z'"
        dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
        let dateString = dateFormatter.string(from: timestamp)

        // 计算请求体的SHA256哈希 - 与静态方法完全相同
        let bodyData: Data
        do {
            bodyData = try JSONSerialization.data(withJSONObject: body)
        } catch {
            bodyData = Data()
        }
        let contentSha256 = sha256(bodyData).map { String(format: "%02x", $0) }.joined()

        // 构建请求头 - 与静态方法完全相同
        let headers: [String: String] = [
            "Content-Type": "application/json",
            "Host": "visual.volcengineapi.com",
            "X-Content-Sha256": contentSha256,
            "X-Date": dateString,
            "Authorization": generateAuthorizationWithDynamicKeys(timestamp: timestamp, body: body, action: action, contentSha256: contentSha256, accessKeyId: accessKeyId, secretAccessKey: secretAccessKey)
        ]

        return headers
    }

    // MARK: - 生成授权签名（使用动态密钥）- 逻辑与静态方法完全一致
    private static func generateAuthorizationWithDynamicKeys(timestamp: Date, body: [String: Any], action: String, contentSha256: String, accessKeyId: String, secretAccessKey: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd'T'HHmmss'Z'"
        dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
        let dateString = dateFormatter.string(from: timestamp)

        dateFormatter.dateFormat = "yyyyMMdd"
        let dateOnly = dateFormatter.string(from: timestamp)

        // 构建签名字符串 - 与静态方法完全相同
        let algorithm = "HMAC-SHA256"
        let credentialScope = "\(dateOnly)/\(region)/\(service)/request"

        // 构建查询字符串 - 与静态方法完全相同
        let queryString = "Action=\(urlEncode(action))&Version=\(urlEncode(version))"

        // 构建规范请求 - 与静态方法完全相同
        let canonicalRequest = """
        POST
        /
        \(queryString)
        content-type:application/json
        host:visual.volcengineapi.com
        x-content-sha256:\(contentSha256)
        x-date:\(dateString)

        content-type;host;x-content-sha256;x-date
        \(contentSha256)
        """

        print("🔐 ===== 动态密钥签名调试信息 =====")
        print("📅 Date String: \(dateString)")
        print("📅 Date Only: \(dateOnly)")
        print("🔗 Query String: \(queryString)")
        print("📄 Content SHA256: \(contentSha256)")
        print("📋 Canonical Request:")
        print(canonicalRequest)
        print("🔐 ===== 动态密钥签名调试结束 =====\n")

        let canonicalRequestHash = sha256(canonicalRequest.data(using: String.Encoding.utf8)!).map { String(format: "%02x", $0) }.joined()

        let stringToSign = """
        \(algorithm)
        \(dateString)
        \(credentialScope)
        \(canonicalRequestHash)
        """

        print("🔐 Dynamic String To Sign:")
        print(stringToSign)

        // 计算签名密钥 - 与静态方法完全相同，只是使用传入的动态密钥
        let kDate = hmacSHA256(key: secretAccessKey.data(using: String.Encoding.utf8)!, data: dateOnly.data(using: String.Encoding.utf8)!)
        let kRegion = hmacSHA256(key: kDate, data: region.data(using: String.Encoding.utf8)!)
        let kService = hmacSHA256(key: kRegion, data: service.data(using: String.Encoding.utf8)!)
        let kSigning = hmacSHA256(key: kService, data: "request".data(using: String.Encoding.utf8)!)

        let signature = hmacSHA256(key: kSigning, data: stringToSign.data(using: String.Encoding.utf8)!).map { String(format: "%02x", $0) }.joined()

        print("🔐 Dynamic Final Signature: \(signature)\n")

        let credential = "\(accessKeyId)/\(credentialScope)"

        return "\(algorithm) Credential=\(credential), SignedHeaders=content-type;host;x-content-sha256;x-date, Signature=\(signature)"
    }

    // MARK: - 提交任务
    private static func submitTask(
        request: NailProcessRequest,
        completion: @escaping (Result<String, Error>) -> Void
    ) {
        // 1. 准备图片数据
        guard let imageData = request.originalImage.jpegData(compressionQuality: 0.8) else {
            completion(.failure(NSError(domain: "ImageError", code: -1, userInfo: [NSLocalizedDescriptionKey: "图片数据转换失败"])))
            return
        }

        let base64Image = imageData.base64EncodedString()

        // 2. 构建提交任务的请求参数
        let parameters: [String: Any] = [
            "req_key": "seededit_v3.0",
            "binary_data_base64": [base64Image],
            "prompt": request.prompt,
            "scale": request.scale,
            "seed": request.seed
        ]

        // 3. 生成签名和请求头
        let timestamp = Date()
        let headers = generateHeaders(timestamp: timestamp, body: parameters, action: "CVSync2AsyncSubmitTask")

        // 4. 发送提交任务请求
        let url = "\(baseURL)/?Action=CVSync2AsyncSubmitTask&Version=2022-08-31"

        // 打印简化的请求信息
        print("🔗 ===== 提交任务请求详情 =====")
        print("📍 URL: \(url)")
        print("🔧 Method: POST")
        print("📋 Headers:")
        for (key, value) in headers {
            print("   \(key): \(value)")
        }
        print("📦 Body Parameters:")
        for (key, value) in parameters {
            if key == "binary_data_base64" {
                if let array = value as? [String], let first = array.first {
                    print("   \(key): [\"base64_length_\(first.count)\"]")
                } else {
                    print("   \(key): \(value)")
                }
            } else {
                print("   \(key): \(value)")
            }
        }
        print("🔗 ===== 请求详情结束 =====\n")

        AF.request(
            url,
            method: .post,
            parameters: parameters,
            encoding: JSONEncoding.default,
            headers: HTTPHeaders(headers)
        )
        .validate()
        .responseDecodable(of: SubmitTaskResponse.self) { response in
            // 打印简化的响应详情
            print("📥 ===== 提交任务响应 =====")
            print("🔢 HTTP Status: \(response.response?.statusCode ?? -1)")

            if let data = response.data, let jsonString = String(data: data, encoding: .utf8) {
                // 只打印前500个字符
                let truncatedJson = jsonString.count > 500 ? String(jsonString.prefix(500)) + "..." : jsonString
                print("📄 Response: \(truncatedJson)")
            }

            if let error = response.error {
                print("❌ Error: \(error.localizedDescription)")
            }
            print("📥 ===== 响应结束 =====\n")

            switch response.result {
            case .success(let apiResponse):
                print("✅ 解析成功: code=\(apiResponse.code), message=\(apiResponse.message)")
                if apiResponse.code == 10000, let taskId = apiResponse.data?.task_id {
                    print("🎯 获取到task_id: \(taskId)")
                    completion(.success(taskId))
                } else {
                    print("❌ API返回错误: code=\(apiResponse.code), message=\(apiResponse.message)")
                    let error = NSError(domain: "APIError", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: apiResponse.message])
                    completion(.failure(error))
                }
            case .failure(let error):
                print("❌ 请求失败: \(error.localizedDescription)")
                completion(.failure(error))
            }
        }
    }

    // MARK: - 轮询查询结果
    private static func pollTaskResult(
        taskId: String,
        completion: @escaping (Result<UIImage, Error>) -> Void,
        maxAttempts: Int = 30,
        currentAttempt: Int = 0
    ) {
        if currentAttempt >= maxAttempts {
            completion(.failure(NSError(domain: "TimeoutError", code: -1, userInfo: [NSLocalizedDescriptionKey: "任务处理超时"])))
            return
        }

        queryTaskResult(taskId: taskId) { result in
            switch result {
            case .success(let queryResponse):
                switch queryResponse.data?.status {
                case "done":
                    // 任务完成，解析结果
                    if queryResponse.code == 10000 {
                        handleQueryResult(queryResponse, completion: completion)
                    } else {
                        let error = NSError(domain: "APIError", code: queryResponse.code, userInfo: [NSLocalizedDescriptionKey: queryResponse.message])
                        completion(.failure(error))
                    }
                case "in_queue", "generating":
                    // 任务还在处理中，继续轮询
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                        pollTaskResult(taskId: taskId, completion: completion, maxAttempts: maxAttempts, currentAttempt: currentAttempt + 1)
                    }
                case "not_found":
                    completion(.failure(NSError(domain: "APIError", code: -1, userInfo: [NSLocalizedDescriptionKey: "任务未找到"])))
                case "expired":
                    completion(.failure(NSError(domain: "APIError", code: -1, userInfo: [NSLocalizedDescriptionKey: "任务已过期"])))
                default:
                    completion(.failure(NSError(domain: "APIError", code: -1, userInfo: [NSLocalizedDescriptionKey: "未知任务状态"])))
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    // MARK: - 查询任务结果
    private static func queryTaskResult(
        taskId: String,
        completion: @escaping (Result<QueryResultResponse, Error>) -> Void
    ) {
        let parameters: [String: Any] = [
            "req_key": "seededit_v3.0",
            "task_id": taskId,
            "req_json": "{\"return_url\":false}"
        ]

        let timestamp = Date()
        let headers = generateHeaders(timestamp: timestamp, body: parameters, action: "CVSync2AsyncGetResult")

        let url = "\(baseURL)/?Action=CVSync2AsyncGetResult&Version=2022-08-31"

        // 打印简化的查询请求详情
        print("🔍 ===== 查询任务请求 =====")
        print("📍 URL: \(url)")
        print("📦 Task ID: \(taskId)")
        print("🔍 ===== 查询请求结束 =====\n")

        AF.request(
            url,
            method: .post,
            parameters: parameters,
            encoding: JSONEncoding.default,
            headers: HTTPHeaders(headers)
        )
        .validate()
        .responseDecodable(of: QueryResultResponse.self) { response in
            // 打印简化的查询响应
            print("📥 ===== 查询任务响应 =====")
            print("🔢 HTTP Status: \(response.response?.statusCode ?? -1)")

            if let error = response.error {
                print("❌ Error: \(error.localizedDescription)")
            }
            print("📥 ===== 查询响应结束 =====\n")

            switch response.result {
            case .success(let apiResponse):
                print("✅ 查询解析成功: code=\(apiResponse.code), status=\(apiResponse.data?.status ?? "unknown")")
                completion(.success(apiResponse))
            case .failure(let error):
                print("❌ 查询请求失败: \(error.localizedDescription)")
                completion(.failure(error))
            }
        }
    }

    // MARK: - 处理查询结果
    private static func handleQueryResult(
        _ response: QueryResultResponse,
        completion: @escaping (Result<UIImage, Error>) -> Void
    ) {
        // 获取处理后的图片
        guard let base64Images = response.data?.binary_data_base64,
              let firstBase64 = base64Images.first,
              let imageData = Data(base64Encoded: firstBase64),
              let processedImage = UIImage(data: imageData) else {
            completion(.failure(NSError(domain: "ImageError", code: -1, userInfo: [NSLocalizedDescriptionKey: "图片解析失败"])))
            return
        }

        // 美甲处理成功后消耗使用次数
        _ = VolcanoUsageManager.shared.consumeUsage()

        completion(.success(processedImage))
    }

    // MARK: - 处理发型编辑结果
    private static func handleHairEditResult(
        _ response: HairEditResponse,
        completion: @escaping (Result<UIImage, Error>) -> Void
    ) {
        // 获取处理后的图片
        guard let base64Images = response.data?.binary_data_base64,
              let firstBase64 = base64Images.first,
              let imageData = Data(base64Encoded: firstBase64),
              let processedImage = UIImage(data: imageData) else {
            completion(.failure(NSError(domain: "ImageError", code: -1, userInfo: [NSLocalizedDescriptionKey: "发型编辑图片解析失败"])))
            return
        }

        // 成功处理后消耗发型编辑次数
        _ = HairEditUsageManager.shared.consumeHairEditUsage()

        completion(.success(processedImage))
    }

    // MARK: - 生成请求头和签名
    private static func generateHeaders(timestamp: Date, body: [String: Any], action: String = "CVSync2AsyncSubmitTask") -> [String: String] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd'T'HHmmss'Z'"
        dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
        let dateString = dateFormatter.string(from: timestamp)

        // 计算请求体的SHA256哈希
        let bodyData: Data
        do {
            bodyData = try JSONSerialization.data(withJSONObject: body)
        } catch {
            bodyData = Data()
        }
        let contentSha256 = sha256(bodyData).map { String(format: "%02x", $0) }.joined()

        // 构建请求头
        let headers: [String: String] = [
            "Content-Type": "application/json",
            "Host": "visual.volcengineapi.com",
            "X-Content-Sha256": contentSha256,
            "X-Date": dateString,
            "Authorization": generateAuthorization(timestamp: timestamp, body: body, action: action, contentSha256: contentSha256)
        ]

        return headers
    }

    // MARK: - 生成授权签名
    private static func generateAuthorization(timestamp: Date, body: [String: Any], action: String, contentSha256: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd'T'HHmmss'Z'"
        dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
        let dateString = dateFormatter.string(from: timestamp)

        dateFormatter.dateFormat = "yyyyMMdd"
        let dateOnly = dateFormatter.string(from: timestamp)

        // 构建签名字符串
        let algorithm = "HMAC-SHA256"
        let credentialScope = "\(dateOnly)/\(region)/\(service)/request"

        // 构建查询字符串（包含Action和Version）- 需要URL编码
        let queryString = "Action=\(urlEncode(action))&Version=\(urlEncode(version))"

        // 构建规范请求 - 按照Java代码的格式，注意headers按字母顺序排列
        let canonicalRequest = """
        POST
        /
        \(queryString)
        content-type:application/json
        host:visual.volcengineapi.com
        x-content-sha256:\(contentSha256)
        x-date:\(dateString)

        content-type;host;x-content-sha256;x-date
        \(contentSha256)
        """

        print("🔐 ===== 签名调试信息 =====")
        print("📅 Date String: \(dateString)")
        print("📅 Date Only: \(dateOnly)")
        print("🔗 Query String: \(queryString)")
        print("📄 Content SHA256: \(contentSha256)")
        print("📋 Canonical Request:")
        print(canonicalRequest)
        print("🔐 ===== 签名调试结束 =====\n")

        // 生成签名
        let canonicalRequestHash = sha256(canonicalRequest.data(using: String.Encoding.utf8)!).map { String(format: "%02x", $0) }.joined()

        let stringToSign = """
        \(algorithm)
        \(dateString)
        \(credentialScope)
        \(canonicalRequestHash)
        """

        print("🔐 String To Sign:")
        print(stringToSign)

        // 计算签名密钥 - 按照Java代码，不需要VOLC前缀
        let kDate = hmacSHA256(key: secretAccessKey.data(using: String.Encoding.utf8)!, data: dateOnly.data(using: String.Encoding.utf8)!)
        let kRegion = hmacSHA256(key: kDate, data: region.data(using: String.Encoding.utf8)!)
        let kService = hmacSHA256(key: kRegion, data: service.data(using: String.Encoding.utf8)!)
        let kSigning = hmacSHA256(key: kService, data: "request".data(using: String.Encoding.utf8)!)

        let signature = hmacSHA256(key: kSigning, data: stringToSign.data(using: String.Encoding.utf8)!).map { String(format: "%02x", $0) }.joined()

        print("🔐 Final Signature: \(signature)\n")

        let credential = "\(accessKeyId)/\(credentialScope)"

        return "\(algorithm) Credential=\(credential), SignedHeaders=content-type;host;x-content-sha256;x-date, Signature=\(signature)"
    }
    
    // MARK: - SHA256哈希
    private static func sha256(_ data: Data) -> Data {
        var hash = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
        data.withUnsafeBytes {
            _ = CC_SHA256($0.baseAddress, CC_LONG(data.count), &hash)
        }
        return Data(hash)
    }
    
    // MARK: - HMAC-SHA256
    private static func hmacSHA256(key: Data, data: Data) -> Data {
        var result = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
        key.withUnsafeBytes { keyBytes in
            data.withUnsafeBytes { dataBytes in
                CCHmac(CCHmacAlgorithm(kCCHmacAlgSHA256), keyBytes.baseAddress, key.count, dataBytes.baseAddress, data.count, &result)
            }
        }
        return Data(result)
    }

    // MARK: - URL编码
    private static func urlEncode(_ string: String) -> String {
        let allowedCharacters = CharacterSet(charactersIn: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.~")
        return string.addingPercentEncoding(withAllowedCharacters: allowedCharacters) ?? string
    }
}

// MARK: - 便捷扩展
extension VolcanoEngineAPI {

    /// 便捷方法：处理美甲
    /// - Parameters:
    ///   - image: 原始图片
    ///   - nailModel: 美甲模型
    ///   - completion: 完成回调
    static func processNailWithModel(
        image: UIImage,
        nailModel: NailModel,
        completion: @escaping (Result<UIImage, Error>) -> Void
    ) {
        let request = NailProcessRequest(
            originalImage: image,
            prompt: nailModel.prompt
        )

        processNail(request: request, completion: completion)
    }

    /// 统一的异步图生图处理方法（用于发型、造型AI、美甲）
    /// - Parameters:
    ///   - image: 原始图片
    ///   - prompt: 提示词
    ///   - completion: 完成回调
    static func processAsyncImageGeneration(
        image: UIImage,
        prompt: String,
        completion: @escaping (Result<UIImage, Error>) -> Void
    ) {
        let request = NailProcessRequest(
            originalImage: image,
            prompt: prompt
        )

        processNail(request: request, completion: completion)
    }

    /// 便捷方法：处理发型编辑
    /// - Parameters:
    ///   - image: 原始图片
    ///   - hairType: 发型类型
    ///   - completion: 完成回调
    static func processHairEditWithType(
        image: UIImage,
        hairType: Int,
        completion: @escaping (Result<UIImage, Error>) -> Void
    ) {
        let request = HairEditRequest(
            originalImage: image,
            hairType: hairType
        )

        processHairEditWithDynamicKeys(request: request, completion: completion)
    }

    /// 显示订阅页面
    static func showSubscriptionPage(from source: String) {
        guard let rootViewController = UIApplication.shared.keyWindow?.rootViewController else {
            print("❌ 无法获取根视图控制器")
            return
        }

        // 获取最顶层的视图控制器
        var topViewController = rootViewController
        while let presentedViewController = topViewController.presentedViewController {
            topViewController = presentedViewController
        }

        let vipVC = VIPViewController()
        vipVC.from = source
        vipVC.modalPresentationStyle = .fullScreen
        topViewController.present(vipVC, animated: true)
    }

    /// 显示次数购买弹窗
    static func showUsagePurchasePopup(from: String = "", completion: @escaping () -> Void) {
        guard let rootViewController = UIApplication.shared.keyWindow?.rootViewController else {
            print("❌ 无法获取根视图控制器")
            return
        }

        // 获取最顶层的视图控制器
        var topViewController = rootViewController
        while let presentedViewController = topViewController.presentedViewController {
            topViewController = presentedViewController
        }

        let popupView = UsagePurchasePopupView()
        
        // 设置来源，用于友盟埋点
        popupView.sourceFrom = from

        // 获取次数购买产品价格
        let purchasePrice = UserDefaults.standard.string(forKey: "com.Fengyin.Camera.Frequency.package") ?? "¥10"
        popupView.updatePurchasePrice(purchasePrice)

        // 检查是否为VIP用户，更新布局
        let isVIP = APPMakeStoreIAPManager.featureVip()
        popupView.updateLayoutForVIPStatus(isVIP)

        // 设置回调
        popupView.onPurchaseButtonTapped = {
            print("🔥 showUsagePurchasePopup的onPurchaseButtonTapped回调被触发了！")
            // 执行次数购买
            purchaseUsagePackage { success in
                print("🔥 purchaseUsagePackage完成，结果: \(success)")
                if success {
                    popupView.hide()
                    completion()
                } else {
                    // 购买失败时也要有反应，可以选择隐藏弹窗或保持弹窗让用户重试
                    // 这里选择保持弹窗，让用户可以重试
                    print("💰 次数购买失败，弹窗保持显示供用户重试")
                }
            }
        }

        popupView.onSubscribeButtonTapped = {
            popupView.hide()
            showSubscriptionPage(from: "异步图生图次数不足")
        }

        // 显示弹窗
        popupView.show()
    }

    /// 执行次数购买
    static func purchaseUsagePackage(completion: @escaping (Bool) -> Void) {
        print("🔥 purchaseUsagePackage方法被调用了！")

        // 显示加载指示器
        DispatchQueue.main.async {
            SVProgressHUD.show(withStatus: local("正在购买..."))
            print("🔥 显示购买加载指示器")
        }

        // 创建并保持代理的强引用
        currentPurchaseDelegate = UsagePurchaseDelegate(completion: { success in
            // 隐藏加载指示器
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()
                if success {
                    SVProgressHUD.showSuccess(withStatus: local("购买成功"))
                } else {
                    SVProgressHUD.showError(withStatus: local("购买失败"))
                }
            }
            completion(success)
            // 购买完成后清除引用
            currentPurchaseDelegate = nil
        })

        // 调用内购
        print("🔥 设置内购代理并调用buyFeature")
        APPMakeStoreIAPManager.sharedManager.delegate = currentPurchaseDelegate
        print("🔥 调用buyFeature: \(APPMakeStoreIAPManager.usagePackageId)")
        APPMakeStoreIAPManager.sharedManager.buyFeature(APPMakeStoreIAPManager.usagePackageId)
    }

    /// 生成Postman测试用的请求信息
    /// - Parameter image: 测试图片
    static func generatePostmanTestInfo(with image: UIImage) {
        print("\n🚀 ===== POSTMAN测试信息 =====")

        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            print("❌ 图片数据转换失败")
            return
        }

        let base64Image = imageData.base64EncodedString()

        // 构建请求参数
        let parameters: [String: Any] = [
            "req_key": "seededit_v3.0",
            "binary_data_base64": [base64Image],
            "prompt": "添加粉色渐变美甲效果",
            "scale": 1,
            "seed": -1
        ]

        // 生成签名和请求头
        let timestamp = Date()
        let headers = generateHeaders(timestamp: timestamp, body: parameters, action: "CVSync2AsyncSubmitTask")

        print("📍 URL:")
        print("\(baseURL)/?Action=CVSync2AsyncSubmitTask&Version=2022-08-31")

        print("\n🔧 Method: POST")

        print("\n📋 Headers:")
        for (key, value) in headers {
            print("\(key): \(value)")
        }

        print("\n📦 Body (JSON):")
        if let jsonData = try? JSONSerialization.data(withJSONObject: parameters, options: .prettyPrinted),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            print(jsonString)
        }

        print("\n📝 Postman设置步骤:")
        print("1. 新建POST请求")
        print("2. 设置URL: \(baseURL)/?Action=CVSync2AsyncSubmitTask&Version=2022-08-31")
        print("3. 在Headers中添加上述所有header")
        print("4. 在Body中选择raw -> JSON，粘贴上述JSON")
        print("5. 发送请求")

        print("\n🔍 查询任务的URL:")
        print("\(baseURL)/?Action=CVSync2AsyncGetResult&Version=2022-08-31")

        print("\n📦 查询任务的Body示例:")
        let queryParams = [
            "req_key": "seededit_v3.0",
            "task_id": "YOUR_TASK_ID_HERE",
            "req_json": "{\"return_url\":false}"
        ]
        if let queryJsonData = try? JSONSerialization.data(withJSONObject: queryParams, options: .prettyPrinted),
           let queryJsonString = String(data: queryJsonData, encoding: .utf8) {
            print(queryJsonString)
        }

        print("🚀 ===== POSTMAN测试信息结束 =====\n")
    }
}
