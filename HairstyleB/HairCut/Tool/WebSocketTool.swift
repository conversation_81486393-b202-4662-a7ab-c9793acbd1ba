//
//  WebSocketTool.swift
//  HairCut
//
//  Created by <PERSON> on 2024/8/4.
//

import Foundation
import Starscream
import Reachability

protocol WebSocketToolProtocol: AnyObject {
    /// 建立连接成功通知
    func webSocketDidConnect(websocket: WebSocketTool)
    /// 断开链接通知,参数 `isReconnecting` 表示是否处于等待重新连接状态。
    func webSocketDidDisconnect(websocket: WebSocketTool, errorString: String, errorCode: UInt16)
    /// 接收到消息后的回调(String)
    func webSocketDidReceiveMessage(websocket: WebSocketTool, text: String)
    ///  监听网络状态变化
    func webSocketViabilityChanged(websocket: WebSocketTool, viabilityValue: Bool)
    ///  遇到错误
    func webSocketGetError(websocket: WebSocketTool, error:Error?)
}

enum WebSocketConnectType {
    case closed
    case connect
    case disconnect
    case reconnecting
}

struct WebSocketConifg {
    /// 请求链接
    var url = ""
    /// 心跳时间间隔
    var heartbeatInterval = 1.0
    /// 重连次数
    var reConnectAllowCount = 5
}

class WebSocketTool: NSObject {
    
    public static let shared = WebSocketTool()
    private var webSocketConifg: WebSocketConifg?
    public var isConnected = false
    var connectType: WebSocketConnectType = .closed
    private var socket: WebSocket?
    weak var delegate: WebSocketToolProtocol?
    
    /// 重连次数
    private var reConnectCount: Int = 0
    
    ///心跳包定时器
    private var heartBeatTimer: Timer?
    
    public var heartBeatMessage: String = ""
    
    /// 是否联网
    public var isAvailable: Bool {
        let reachability = try? Reachability()
        guard reachability?.connection == .wifi || reachability?.connection == .cellular else {
            return false
        }
        return true
    }
    
    override init() {
         
    }
}

// MARK: - 操作函数
typealias WebSocketToolAction = WebSocketTool
extension WebSocketToolAction {
    /// 开始连接
    public func connectSocket(config: WebSocketConifg) {
        
        guard let url =  URL(string: config.url) else {
            return
        }
        self.webSocketConifg = config
        var request = URLRequest(url: url)
        request.timeoutInterval = 5
        self.socket = WebSocket(request: request)
        self.socket?.delegate = self
        self.socket?.connect()
    }
    
    /// 重新连接
    public func reConnectSocket() {
        if self.reConnectCount > self.webSocketConifg?.reConnectAllowCount ?? 5 {
            self.reConnectCount = 0
            return
        }
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 5) {
            guard let config = self.webSocketConifg, self.connectType == .reconnecting else {
                return
            }
            
            self.connectSocket(config: config)
            self.reConnectCount += 1
        }
    }
    
    /// 取消连接
    public func disConnect() {
        self.socket?.disconnect()
        self.connectType = .disconnect
    }
    
    /// 发送Data
    public func sendData(input: Any) {
        guard self.isAvailable == true else {
            printLog(message: "websocket没有网络")
            return
        }
        
        if let data = input as? Data {
            self.socket?.write(data: data, completion: {
                printLog(message: "websocket sendData callback")
            })
        } else if let data = input as? String {
            self.socket?.write(string: data, completion: {
                printLog(message: "websocket send string completion: \(data)")
            })
        } else {
            printLog(message: "websocket sendData类型无法识别")
        }
    }
    
    /// 初始化心跳
    public func initHeartBeat(message: String) {
        guard var timer = self.heartBeatTimer, let heartbeatInterval = self.webSocketConifg?.heartbeatInterval as? Double else {
            return
        }
        self.heartBeatMessage = message
        timer = Timer(timeInterval: TimeInterval(heartbeatInterval), target: self, selector: #selector(sendHeartBeat), userInfo: nil, repeats: true)
        RunLoop.current.add(timer, forMode: RunLoop.Mode.common)
    }
    
    //关闭心跳定时器
    private func destoryHeartBeat() {
        self.heartBeatTimer?.invalidate()
        self.heartBeatTimer = nil
    }
    
    /// 心跳
    @objc private func sendHeartBeat() {
        if self.isConnected {
//            if text.data(using: String.Encoding.utf8) != nil {
            self.sendData(input: self.heartBeatMessage)
//            }
        } else{
            
        }
    }
}

// MARK: - WebSocket接收状态处理
typealias WebSocketToolDelegate = WebSocketTool
extension WebSocketToolDelegate: WebSocketDelegate {
    
    func didReceive(event: Starscream.WebSocketEvent, client: Starscream.WebSocketClient) {
        printLog(message: "websocket-didReceive:\(event)")
        switch event {
        case .connected(_):
            self.isConnected = true
            self.delegate?.webSocketDidConnect(websocket: self)
            break
        case .disconnected(let errorString, let errorCode):
            self.isConnected = false
            self.connectType = .disconnect
            self.delegate?.webSocketDidDisconnect(websocket: self, errorString: errorString, errorCode: errorCode)
            self.destoryHeartBeat()
            break
        case .text(let string):
            self.delegate?.webSocketDidReceiveMessage(websocket: self, text: string)
            break
        case .binary(let data):
            printLog(message: "websocket-binary:\(data)")
            break
        case .pong(let data):
            printLog(message: "websocket-pong:\(String(describing: data))")
            break
        case .ping(let data):
            printLog(message: "websocket-ping:\(String(describing: data))")
            break
        case .error(let error):
            self.isConnected = false
            self.handleError(error)
            self.delegate?.webSocketGetError(websocket: self, error: error)
        case .viabilityChanged(let bool):
            self.delegate?.webSocketViabilityChanged(websocket: self, viabilityValue: bool)
            break
        case .reconnectSuggested(_):
            break
        case .cancelled:
            isConnected = false
        case .peerClosed:
            break
        }
    }
    
    private func handleError(_ error: Error?) {
        if let obj = error as? WSError {
            printLog(message: "websocket encountered an error: \(obj.message)")
        } else if let obj = error {
            printLog(message: "websocket encountered an error: \(obj.localizedDescription)")
        } else {
            printLog(message: "websocket encountered an error")
        }
    }
}
