//
//  BeautyFilterManager.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import UIKit

/// 美颜滤镜管理器 - 支持临时预览和确认保存
class BeautyFilterManager {
    
    static let shared = BeautyFilterManager()
    
    // MARK: - Properties
    private var temporaryParameters: [String: Double] = [:]
    private var confirmedParameters: [String: Double] = [:]
    private var isPreviewMode = false
    
    private init() {
        setupSDK()
    }
    
    // MARK: - SDK Setup
    private func setupSDK() {
        PTMFilterHelper.setupSDK()
    }
    
    // MARK: - Preview Mode Methods
    
    /// 开始预览模式 - 备份当前确认的参数
    func startPreviewMode() {
        guard !isPreviewMode else {
            print("⚠️ 已经在预览模式中")
            return
        }

        isPreviewMode = true
        temporaryParameters.removeAll()

        // 备份当前确认的参数
        let config = BeautyConfigManager.shared.loadConfig()
        confirmedParameters.removeAll()

        // 备份所有分类的参数
        for parameter in config.skinBeauty {
            confirmedParameters[parameter.name] = parameter.currentValue
        }
        for parameter in config.faceShape {
            confirmedParameters[parameter.name] = parameter.currentValue
        }
        for parameter in config.eyeShape {
            confirmedParameters[parameter.name] = parameter.currentValue
        }
        for parameter in config.noseShape {
            confirmedParameters[parameter.name] = parameter.currentValue
        }
        for parameter in config.mouthShape {
            confirmedParameters[parameter.name] = parameter.currentValue
        }
        for parameter in config.eyebrowShape {
            confirmedParameters[parameter.name] = parameter.currentValue
        }

        print("🎨 开始预览模式，已备份 \(confirmedParameters.count) 个参数")
    }
    
    /// 设置临时预览参数 - 不保存到配置
    func setTemporaryParameter(_ name: String, value: Double) {
        guard isPreviewMode else {
            print("⚠️ 不在预览模式，无法设置临时参数")
            return
        }
        
        temporaryParameters[name] = value

        // 立即应用到美颜SDK进行预览（不保存配置）
        PTMFilterHelper.applyParameter(toFUBeauty: name, value: value)
        print("✅ 临时设置参数 \(name) = \(value)")
    }
    
    /// 获取当前预览参数值
    func getTemporaryParameter(_ name: String) -> Double? {
        return temporaryParameters[name]
    }
    
    /// 处理图片 - 应用当前临时参数
    func processImageWithTemporaryParameters(_ image: UIImage) -> UIImage? {
        guard isPreviewMode else {
            print("⚠️ 不在预览模式，使用确认的参数处理图片")
            return PTMFilterHelper.processImage(withBeauty: image)
        }

        // 确保在主线程中执行FUBeauty操作
        if Thread.isMainThread {
            return processImageOnMainThread(image)
        } else {
            var result: UIImage?
            DispatchQueue.main.sync {
                result = self.processImageOnMainThread(image)
            }
            return result
        }
    }

    /// 在主线程中处理图片
    private func processImageOnMainThread(_ image: UIImage) -> UIImage? {
        // 在预览模式下，只应用修改过的临时参数到FUBeauty对象
        for (name, value) in temporaryParameters {
            PTMFilterHelper.applyParameter(toFUBeauty: name, value: value)
        }

        // 使用优化的方法处理图片 - 不重新应用所有参数，只使用当前FUBeauty状态
        let processedImage = PTMFilterHelper.processImage(withCurrentBeautySettings: image)

        print("🖼️ 应用了 \(temporaryParameters.count) 个修改的参数，使用当前美颜状态处理图片")
        return processedImage
    }
    
    /// 确认保存 - 将临时参数保存到配置
    func confirmParameters() {
        guard isPreviewMode else {
            print("⚠️ 不在预览模式，无法确认参数")
            return
        }
        
        // 更新配置中的参数值
        var config = BeautyConfigManager.shared.loadConfig()
        
        for (parameterName, value) in temporaryParameters {
            // 更新对应分类中的参数
            updateParameterInConfig(&config, parameterName: parameterName, value: value)
        }
        
        // 保存配置
        BeautyConfigManager.shared.saveConfig(config)
        
        // 更新确认的参数
        for (name, value) in temporaryParameters {
            confirmedParameters[name] = value
        }
        
        print("✅ 确认保存 \(temporaryParameters.count) 个参数")
        
        // 结束预览模式
        endPreviewMode()
    }
    
    /// 取消预览 - 恢复到确认的参数
    func cancelPreview() {
        guard isPreviewMode else {
            print("⚠️ 不在预览模式，无法取消预览")
            return
        }

        // 恢复所有确认的参数（只应用到FUBeauty，不保存配置）
        for (name, value) in confirmedParameters {
            PTMFilterHelper.applyParameter(toFUBeauty: name, value: value)
        }

        print("🔄 取消预览，恢复 \(confirmedParameters.count) 个确认参数")

        // 结束预览模式
        endPreviewMode()
    }
    
    /// 重置所有参数为默认值
    func resetToDefaults() {
        guard isPreviewMode else {
            print("⚠️ 不在预览模式，无法重置参数")
            return
        }
        
        let config = BeautyConfigManager.shared.loadConfig()
        temporaryParameters.removeAll()
        
        // 重置所有分类的参数为默认值
        let allParameters = config.skinBeauty + config.faceShape + config.eyeShape + 
                           config.noseShape + config.mouthShape + config.eyebrowShape
        
        for parameter in allParameters {
            temporaryParameters[parameter.name] = parameter.defaultValue
            PTMFilterHelper.updateBeautyParameter(parameter.name, value: parameter.defaultValue)
        }
        
        print("🔄 重置 \(allParameters.count) 个参数为默认值")
    }
    
    /// 结束预览模式
    private func endPreviewMode() {
        isPreviewMode = false
        temporaryParameters.removeAll()
        confirmedParameters.removeAll()
        print("🏁 结束预览模式")
    }
    
    // MARK: - Helper Methods
    
    private func updateParameterInConfig(_ config: inout BeautyConfig, parameterName: String, value: Double) {
        // 在美肤参数中查找
        for i in 0..<config.skinBeauty.count {
            if config.skinBeauty[i].name == parameterName {
                config.skinBeauty[i].currentValue = value
                return
            }
        }
        
        // 在脸型参数中查找
        for i in 0..<config.faceShape.count {
            if config.faceShape[i].name == parameterName {
                config.faceShape[i].currentValue = value
                return
            }
        }
        
        // 在眼型参数中查找
        for i in 0..<config.eyeShape.count {
            if config.eyeShape[i].name == parameterName {
                config.eyeShape[i].currentValue = value
                return
            }
        }
        
        // 在鼻子参数中查找
        for i in 0..<config.noseShape.count {
            if config.noseShape[i].name == parameterName {
                config.noseShape[i].currentValue = value
                return
            }
        }
        
        // 在嘴巴参数中查找
        for i in 0..<config.mouthShape.count {
            if config.mouthShape[i].name == parameterName {
                config.mouthShape[i].currentValue = value
                return
            }
        }
        
        // 在眉毛参数中查找
        for i in 0..<config.eyebrowShape.count {
            if config.eyebrowShape[i].name == parameterName {
                config.eyebrowShape[i].currentValue = value
                return
            }
        }
        
        print("⚠️ 未找到参数: \(parameterName)")
    }
    
    // MARK: - Status Methods
    
    /// 是否在预览模式
    var inPreviewMode: Bool {
        return isPreviewMode
    }
    
    /// 获取临时参数数量
    var temporaryParameterCount: Int {
        return temporaryParameters.count
    }
    
    /// 获取所有临时参数
    var allTemporaryParameters: [String: Double] {
        return temporaryParameters
    }
}

// MARK: - Debug Methods
extension BeautyFilterManager {
    
    /// 打印当前状态
    func printCurrentStatus() {
        print("📊 BeautyFilterManager 状态:")
        print("   预览模式: \(isPreviewMode)")
        print("   临时参数: \(temporaryParameters.count) 个")
        print("   确认参数: \(confirmedParameters.count) 个")
        
        if !temporaryParameters.isEmpty {
            print("   临时参数详情:")
            for (name, value) in temporaryParameters {
                print("     \(name): \(value)")
            }
        }
    }
}
