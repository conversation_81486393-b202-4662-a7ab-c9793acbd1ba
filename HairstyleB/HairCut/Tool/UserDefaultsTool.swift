//
//  UserDefaultsTool.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/8/13.
//

import Foundation

class UserDefaultsTool {
    public static func saveTemporaryString(key: String, value: String) {
        let defaults = UserDefaults.standard
        defaults.set(value, forKey: key)
    }

    public static func readTemporaryString(key: String) -> String? {
        let defaults = UserDefaults.standard
        return defaults.string(forKey: key)
    }

    public static func deleteTemporaryString(key: String) {
        let defaults = UserDefaults.standard
        defaults.removeObject(forKey: key)
    }
}
