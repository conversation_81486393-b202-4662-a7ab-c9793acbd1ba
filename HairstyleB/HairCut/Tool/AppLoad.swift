//
//  AppLoad.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/7/29.
//

import Foundation
import UIKit
import AppTrackingTransparency
import AdSupport
import Promises

class AppLoad {
    public static func initAllClass() {
        _ = WebSocketTool.shared
        _ = HttpTool.shared
    }
    
    public static func resetTabbarVC(_ isNeedFullScreenAd: Bool = false) {
        let window: UIWindow? = (UIApplication.shared.delegate as? AppDelegate)?.window
        let tabbarVC = TabbarVC()
        tabbarVC.isNeedFullScreenAd = isNeedFullScreenAd
        window?.rootViewController = UINavigationController(rootViewController: tabbarVC)
    }
    
    public static func resetWelcomeVC() {
        let window: UIWindow? = (UIApplication.shared.delegate as? AppDelegate)?.window
        let welcomeVC = WelcomeVC()
        window?.rootViewController = UINavigationController(rootViewController: welcomeVC)
    }
    
    public static func resetLaunchVC() {
        let window: UIWindow? = (UIApplication.shared.delegate as? AppDelegate)?.window
        let lanuchVC = LaunchScreenVC()
        window?.rootViewController = UINavigationController(rootViewController: lanuchVC)
    }
    
    public static func getCurrentUivewController() -> UIViewController? {
        if let rootViewController = UIApplication.shared.keyWindow?.rootViewController {
            // 当前控制器为rootViewController或者其子控制器
            let currentViewController = rootViewController
            if ((currentViewController as? UINavigationController) != nil) {
                return currentViewController.children.last
            }
            return currentViewController
        }
        return nil
    }
    
    public static func showActionAlert(title: String? = nil, message:String) {
        let actionSheet = UIAlertController(title: title, message: message, preferredStyle: .alert)
        actionSheet.popoverPresentationController?.sourceView = self.getCurrentUivewController()?.view
        let action = UIAlertAction(title: "confirm".localized, style: .default, handler: nil)
//        action.setValue(UIColor.black, forKey: "titleTextColor")
        actionSheet.addAction(action)
        guard let viewcontroller = self.getCurrentUivewController() else {
            return
        }
        viewcontroller.present(actionSheet, animated: true, completion: nil)
    }
    
    public static func idfa() -> Promise<String> {
        return Promise<String> { (resolve, reject) in
            if #available(iOS 14, *) {
                ATTrackingManager.requestTrackingAuthorization { status in
                    if status == .authorized {
                        let idfa = ASIdentifierManager.shared().advertisingIdentifier.uuidString
                        resolve(idfa)
                    }
                }
            } else {
                if ASIdentifierManager.shared().isAdvertisingTrackingEnabled {
                   let idfa = ASIdentifierManager.shared().advertisingIdentifier.uuidString
                    resolve(idfa)
               }
            }
        }
    }
}
