# 火山引擎次数管理完整规则

## 📊 核心计算公式

```swift
剩余次数 = 基础次数 + 购买次数包 - 当月已使用次数
剩余次数 = getCurrentMonthTotalCount() + getPurchasedCount() - currentMonthUsage
```

## 📋 基础配置参数

| 参数 | 值 | 说明 |
|------|----|----|
| `defaultFreeCount` | **1次** | 未订阅用户每月基础次数 |
| `freeTrialCount` | **10次** | 免费试用期基础次数 |
| `subscribedCount` | **20次** | 正式订阅用户每月基础次数 |
| `resetDay` | **1号** | 每月重置日期 |
| 购买次数包 | **+10次** | 每次购买固定增加10次 |

## 🔄 用户状态定义

| 状态 | 枚举值 | 基础次数 | 说明 |
|------|--------|----------|------|
| 未订阅 | `.none` | **1次/月** | 普通用户 |
| 免费试用 | `.freeTrial` | **10次/3天** | 3天免费试用期 |
| 正式订阅 | `.subscribed` | **20次/月** | 月费/年费/永久用户 |

## 🎯 状态转换触发条件和次数处理

| 转换场景 | 触发条件 | 次数处理 | 代码位置 |
|----------|----------|----------|----------|
| **开始免费试用** | 用户订阅且在3天试用期内 | `currentMonthUsage = 0`<br/>**给予10次** | `checkAndUpdateSubscriptionStatus()` |
| **免费试用 → 正式订阅** | VIP有效期>7天 或 试用期结束后有VIP | `currentMonthUsage = 0`<br/>**给予20次** | `checkAndUpdateSubscriptionStatus()` |
| **未订阅 → 正式订阅** | 直接购买月费/年费/永久 | `currentMonthUsage = 0`<br/>**给予20次** | `checkAndUpdateSubscriptionStatus()` |
| **订阅用户月度重置** | 距离上次订阅≥1个月 | `currentMonthUsage = 0`<br/>**重新给予20次** | `checkAndUpdateSubscriptionStatus()` |
| **每月固定重置** | 每月1号重置 | `currentMonthUsage = 0`<br/>**重新给予对应次数** | `checkAndResetMonthlyUsage()` |
| **VIP过期** | VIP过期且无有效订阅 | `currentMonthUsage = 0`<br/>**降为1次** | `checkAndUpdateSubscriptionStatus()` |
| **购买次数包** | 用户购买次数包 | 购买次数 `+10次`<br/>**不影响基础次数** | `purchaseUsagePackage()` |

## 📊 详细状态转换表

| 当前状态 | 操作 | 新状态 | 基础次数变化 | 已用次数 | 购买次数 | 最终剩余次数 |
|----------|------|--------|--------------|----------|----------|--------------|
| `.none` | 开始免费试用 | `.freeTrial` | 1→10 | 重置为0 | 保持不变 | **10 + 购买次数** |
| `.none` | 直接订阅月费/年费 | `.subscribed` | 1→20 | 重置为0 | 保持不变 | **20 + 购买次数** |
| `.freeTrial` | 试用期内订阅年费 | `.subscribed` | 10→20 | 重置为0 | 保持不变 | **20 + 购买次数** |
| `.freeTrial` | 试用期结束且有VIP | `.subscribed` | 10→20 | 重置为0 | 保持不变 | **20 + 购买次数** |
| `.freeTrial` | 试用期结束且无VIP | `.none` | 10→1 | 重置为0 | 保持不变 | **1 + 购买次数** |
| `.subscribed` | 月度重置 | `.subscribed` | 20→20 | 重置为0 | 保持不变 | **20 + 购买次数** |
| `.subscribed` | VIP过期 | `.none` | 20→1 | 重置为0 | 保持不变 | **1 + 购买次数** |
| 任何状态 | 购买次数包 | 状态不变 | 基础次数不变 | 已用次数不变 | +10次 | **基础次数 + 购买次数 - 已用次数** |

## 💡 具体使用场景示例

### 场景A：新用户免费试用后订阅年费

| 时间 | 操作 | 状态 | 基础次数 | 购买次数 | 已用次数 | 剩余次数 |
|------|------|------|----------|----------|----------|----------|
| Day 1 | 注册使用 | `.none` | 1 | 0 | 0 | **1** |
| Day 1 | 用完免费次数 | `.none` | 1 | 0 | 1 | **0** |
| Day 2 | 开始免费试用 | `.freeTrial` | 10 | 0 | **0** | **10** |
| Day 3 | 使用5次 | `.freeTrial` | 10 | 0 | 5 | **5** |
| Day 3 | 订阅年费 | `.subscribed` | 20 | 0 | **0** | **20** |
| Day 30 | 使用15次 | `.subscribed` | 20 | 0 | 15 | **5** |
| 下月1号 | 月度重置 | `.subscribed` | 20 | 0 | **0** | **20** |

### 场景B：订阅用户购买次数包

| 时间 | 操作 | 状态 | 基础次数 | 购买次数 | 已用次数 | 剩余次数 |
|------|------|------|----------|----------|----------|----------|
| Day 1 | 直接订阅月费 | `.subscribed` | 20 | 0 | **0** | **20** |
| Day 15 | 使用18次 | `.subscribed` | 20 | 0 | 18 | **2** |
| Day 15 | 购买次数包 | `.subscribed` | 20 | **10** | 18 | **12** |
| Day 20 | 使用8次 | `.subscribed` | 20 | 10 | 26 | **4** |
| 下月1号 | 月度重置 | `.subscribed` | 20 | 10 | **0** | **30** |

### 场景C：免费试用期内多次购买

| 时间 | 操作 | 状态 | 基础次数 | 购买次数 | 已用次数 | 剩余次数 |
|------|------|------|----------|----------|----------|----------|
| Day 1 | 开始免费试用 | `.freeTrial` | 10 | 0 | **0** | **10** |
| Day 2 | 购买次数包 | `.freeTrial` | 10 | **10** | 0 | **20** |
| Day 2 | 使用15次 | `.freeTrial` | 10 | 10 | 15 | **5** |
| Day 3 | 订阅年费 | `.subscribed` | 20 | 10 | **0** | **30** |

## ✅ 重要规则总结

### 次数给予规则
1. **状态转换时重置已用次数**：任何状态转换都会将`currentMonthUsage`重置为0
2. **基础次数按新状态计算**：转换后按新状态的基础次数计算
3. **购买次数永久保留**：购买的次数包不会因状态转换而丢失
4. **月度自动重置**：每月1号或订阅满月时自动重置已用次数

### 购买次数包规则
1. **固定增加10次**：每次购买固定增加10次
2. **累计计算**：多次购买会累计
3. **跨状态保留**：状态转换时购买次数不会丢失
4. **不会过期**：购买的次数包永久有效

### 计算优先级
1. **先检查订阅状态**：`checkAndUpdateSubscriptionStatus()`
2. **再检查月度重置**：`checkAndResetMonthlyUsage()`
3. **最后计算剩余次数**：`基础次数 + 购买次数 - 已用次数`

## 🔧 关键代码位置

### 主要方法
- `getRemainingCount()` - 获取剩余次数
- `consumeUsage()` - 消耗一次使用次数
- `purchaseUsagePackage()` - 购买次数包
- `checkAndUpdateSubscriptionStatus()` - 检查并更新订阅状态
- `checkAndResetMonthlyUsage()` - 检查并重置月度使用次数

### 配置文件
- 在线配置：`https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/volcanoconfig.json`
- 本地默认配置：`VolcanoUsageConfig` 结构体

### 存储键值
- 用户记录：`VolcanoUsageUserRecord`
- 购买次数：`VolcanoPurchasedCount`
- VIP过期时间：`VipExpiresDate`

## 🚨 特殊情况处理

### 免费试用转正式订阅的次数处理
**重要**：用户在免费试用期间订阅年费/月费时，会立即转为正式订阅状态，重置已用次数为0，给予完整的20次基础次数。

```swift
// 关键逻辑：从任何状态转为正式订阅都重置次数
userRecord.currentMonthUsage = 0
print("🎉 用户从\(previousStatus)转为正式订阅，重置次数给予20次基础次数")
```

### VIP有效期判断逻辑
- **VIP有效期 > 7天**：认为是正式订阅（月费/年费）
- **VIP有效期 ≤ 7天**：可能是免费试用期

### 次数不足时的处理
1. **检查次数**：`VolcanoUsageManager.shared.canUse()`
2. **次数不足**：弹出购买弹窗 `showUsagePurchasePopup()`
3. **返回失败**：`completion(.failure(error))` 其中 `error.domain == "VolcanoUsageLimit"`

## 🔍 API调用流程

### 美甲/发型AI处理流程
```swift
1. 检查次数：VolcanoUsageManager.shared.canUse()
2. 次数不足：显示购买弹窗 + 返回失败回调
3. 次数充足：提交任务到火山引擎
4. 轮询结果：等待处理完成
5. 成功获取图片：消耗一次使用次数
6. 失败：不消耗次数
```

### 次数消耗时机
- ✅ **成功时消耗**：只有在成功获取处理后的图片时才消耗次数
- ❌ **失败不消耗**：任务失败、网络错误、图片解析失败等情况不消耗次数

## 📱 产品ID配置

### 订阅产品ID
| 产品ID | 说明 | 免费试用 |
|--------|------|----------|
| `com.hairstyle.week` | 周订阅 | 无 |
| `com.FoshanFullstack.hairstyle.month` | 月订阅 | 前3天免费 |
| `com.FoshanFullstack.hairstyle.week.Nofree` | 周订阅 | 前3天不免费 |
| `com.FoshanFullstack.hairstyle.year` | 年订阅 | 无 |
| `com.hairstyle.permanent` | 永久订阅 | 无 |
| `com.FoshanFullstack.hairstyle.month.discount` | 挽留弹窗月订阅 | 无 |

### 次数包产品ID
| 产品ID | 说明 | 次数 |
|--------|------|------|
| `com.Fengyin.Camera.Frequency.package` | 次数包 | +10次 |

## 🧪 测试场景

### 基础功能测试
1. **未订阅用户**：验证每月1次限制
2. **免费试用**：验证3天10次限制
3. **正式订阅**：验证每月20次限制
4. **购买次数包**：验证+10次累计
5. **月度重置**：验证每月1号重置

### 状态转换测试
1. **免费试用 → 年费订阅**：验证次数重置为20次
2. **月费 → 年费升级**：验证不重复给予次数
3. **VIP过期**：验证降级为1次/月
4. **恢复购买**：验证状态正确恢复

### 边界情况测试
1. **试用期最后一天订阅**：验证立即转为正式订阅
2. **多次购买次数包**：验证累计计算
3. **网络异常**：验证失败不消耗次数
4. **时区变更**：验证月度重置正确性

## 🔧 故障排查

### 常见问题
1. **次数显示不正确**：检查订阅状态同步
2. **重复给予次数**：检查状态转换逻辑
3. **次数未重置**：检查月度重置条件
4. **购买次数丢失**：检查UserDefaults存储

### 调试方法
```swift
// 获取详细配置信息
let configInfo = VolcanoUsageManager.shared.getConfigInfo()
print(configInfo)

// 手动刷新订阅状态
VolcanoUsageManager.shared.refreshSubscriptionStatus()

// 管理员重置功能
VolcanoUsageManager.shared.resetMonthlyUsage()
VolcanoUsageManager.shared.resetPurchasedCount()
```

---

**文档版本**：v1.0
**最后更新**：2025-07-22
**维护者**：开发团队
