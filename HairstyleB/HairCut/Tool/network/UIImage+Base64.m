//
//  UIImage+Base64.m
//  speakingPractice
//
//  Created by fs0011 on 2023/6/13.
//

#import "UIImage+Base64.h"

@implementation UIImage (Base64)
+ (NSString *)base64StringFromImage:(UIImage *)image {
    NSData *imageData = UIImagePNGRepresentation(image); // 或者使用UIImageJPEGRepresentation方法，具体取决于您的图片格式
    NSString *base64String = [imageData base64EncodedStringWithOptions:0];
    return base64String;
}

+ (UIImage *)imageFromBase64String:(NSString *)base64String {
    NSData *imageData = [[NSData alloc] initWithBase64EncodedString:base64String options:NSDataBase64DecodingIgnoreUnknownCharacters];
    UIImage *image = [UIImage imageWithData:imageData];
    return image;
}
@end
