# Java代码对比修复说明

## 🔍 对比分析

基于官方Java代码，我发现了以下关键问题并进行了修复：

### 1. 签名密钥生成错误

#### Java代码（正确）
```java
private byte[] genSigningSecretKeyV4(String secretKey, String date, String region, String service) throws Exception {
    byte[] kDate = hmacSHA256((secretKey).getBytes(), date);  // 直接使用secretKey
    byte[] kRegion = hmacSHA256(kDate, region);
    byte[] kService = hmacSHA256(kRegion, service);
    return hmacSHA256(kService, "request");
}
```

#### 修复前（错误）
```swift
let kDate = hmacSHA256(key: "VOLC\(secretAccessKey)".data(using: .utf8)!, ...)  // 错误地添加了VOLC前缀
```

#### 修复后（正确）
```swift
let kDate = hmacSHA256(key: secretAccessKey.data(using: .utf8)!, ...)  // 直接使用secretAccessKey
```

### 2. URL编码问题

#### Java代码（正确）
```java
SortedMap<String, String> realQueryList = new TreeMap<>(queryList);
realQueryList.put("Action", action);
realQueryList.put("Version", version);
StringBuilder querySB = new StringBuilder();
for (String key : realQueryList.keySet()) {
    querySB.append(signStringEncoder(key)).append("=").append(signStringEncoder(realQueryList.get(key))).append("&");
}
```

#### 修复前（错误）
```swift
let queryString = "Action=\(action)&Version=\(version)"  // 没有URL编码
```

#### 修复后（正确）
```swift
let queryString = "Action=\(urlEncode(action))&Version=\(urlEncode(version))"  // 添加URL编码
```

### 3. 规范请求格式

#### Java代码（正确）
```java
String canonicalStringBuilder = method + "\n" + path + "\n" + querySB + "\n" +
        "host:" + host + "\n" +
        "x-date:" + xDate + "\n" +
        "x-content-sha256:" + xContentSha256 + "\n" +
        "content-type:" + contentType + "\n" +
        "\n" +
        signHeader + "\n" +
        xContentSha256;
```

#### 修复后（对应）
```swift
let canonicalRequest = """
POST
/
\(queryString)
content-type:application/json
host:visual.volcengineapi.com
x-content-sha256:\(contentSha256)
x-date:\(dateString)

content-type;host;x-content-sha256;x-date
\(contentSha256)
"""
```

### 4. SignedHeaders顺序

#### Java代码（正确）
```java
String signHeader = "host;x-date;x-content-sha256;content-type";  // 按字母顺序
```

#### 修复后（正确）
```swift
return "HMAC-SHA256 Credential=\(credential), SignedHeaders=content-type;host;x-content-sha256;x-date, Signature=\(signature)"
```

### 5. 请求头设置

#### Java代码（正确）
```java
conn.setRequestProperty("Host", host);
conn.setRequestProperty("X-Date", xDate);
conn.setRequestProperty("X-Content-Sha256", xContentSha256);
conn.setRequestProperty("Content-Type", contentType);
```

#### 修复后（对应）
```swift
let headers: [String: String] = [
    "Content-Type": "application/json",
    "Host": "visual.volcengineapi.com",
    "X-Content-Sha256": contentSha256,
    "X-Date": dateString,
    "Authorization": generateAuthorization(...)
]
```

## 🔧 关键修复点

### 1. 移除VOLC前缀
- **问题**: 签名密钥生成时错误地添加了"VOLC"前缀
- **修复**: 直接使用原始的secretAccessKey

### 2. 添加URL编码
- **问题**: 查询参数没有进行URL编码
- **修复**: 添加urlEncode方法，对Action和Version进行编码

### 3. 修正SignedHeaders顺序
- **问题**: SignedHeaders的顺序不正确
- **修复**: 按字母顺序排列：content-type;host;x-content-sha256;x-date

### 4. 添加X-Content-Sha256头
- **问题**: 缺少X-Content-Sha256请求头
- **修复**: 计算请求体的SHA256哈希并添加到请求头

## 🧪 测试验证

### 运行测试
```swift
// 测试修复后的签名
self.testSignatureDebug()
// 选择 "测试完整请求"
```

### 预期结果
- ✅ 不再出现"SignatureDoesNotMatch"错误
- ✅ 能够成功提交任务并获取task_id
- ✅ 签名调试信息显示正确的计算过程

### 调试输出示例
```
🔐 ===== 签名调试信息 =====
📅 Date String: 20250714T123456Z
📅 Date Only: 20250714
🔗 Query String: Action=CVSync2AsyncSubmitTask&Version=2022-08-31
📄 Content SHA256: a1b2c3d4e5f6...
📋 Canonical Request:
POST
/
Action=CVSync2AsyncSubmitTask&Version=2022-08-31
content-type:application/json
host:visual.volcengineapi.com
x-content-sha256:a1b2c3d4e5f6...
x-date:20250714T123456Z

content-type;host;x-content-sha256;x-date
a1b2c3d4e5f6...

🔐 String To Sign:
HMAC-SHA256
20250714T123456Z
20250714/cn-north-1/cv/request
canonical_request_hash...

🔐 Final Signature: final_signature_hash...
```

## 📋 验证清单

- [ ] 签名密钥生成不包含VOLC前缀
- [ ] 查询参数进行了URL编码
- [ ] SignedHeaders按字母顺序排列
- [ ] 包含X-Content-Sha256请求头
- [ ] 规范请求格式与Java代码一致
- [ ] 能够成功调用API不报签名错误

## 🚀 下一步

1. **运行测试**: 使用修复后的代码进行API调用测试
2. **验证签名**: 检查控制台输出的签名调试信息
3. **Postman验证**: 使用生成的信息在Postman中验证
4. **完整流程**: 测试美甲处理的完整异步流程

---

💡 **重要**: 这次修复完全基于火山引擎官方Java代码，确保了签名算法的正确性。现在应该能够成功调用API了！
