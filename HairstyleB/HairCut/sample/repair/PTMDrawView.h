//
//  PTMDrawView.h
//  photoTimeMachine
//
//  Created by fs0011 on 2023/6/14.
//

#import <UIKit/UIKit.h>
#import "MagnifyingGlassView.h"
NS_ASSUME_NONNULL_BEGIN

@interface PTMDrawView : UIView
@property(nonatomic,assign)CGFloat lineWidth;
@property MagnifyingGlassView* magnifyingGlassView;
@property (nonatomic, strong) UIColor *lineColor;
@property (nonatomic, assign)BOOL isEarser;
//清屏
- (void)clear;
 
//回退
- (void) back;
//下一步
- (void)next;
 
//橡皮擦
- (void) eraser;

- (UIImage *)renderImage:(UIImage*)ori;
- (UIImage*)imageRedToRed:(UIImage*) image percent:(float*)percent;
- (UIImage *)scaleImage:(UIImage *)image toSize:(CGSize)size;
@end

NS_ASSUME_NONNULL_END
