//
//  PTMPhotoRepairView.m
//  photoTimeMachine
//
//  Created by fs0011 on 2023/6/13.
//

#import "PTMPhotoRepairView.h"
#import "UIButton+color.h"
@implementation PTMPhotoRepairView
{
    UIView* _smartRepair;
    UIView* _scratchRepair;
    UIScrollView* _facialRetouching;
    UIView* _groupPhotoRepair;
    
}
/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */
-(instancetype)init
{
    if(self == [super init])
    {
        [self layout];
    }
    return self;
}

- (void)layout
{
    _smartRepair  = [UIView new];
    _smartRepair.backgroundColor = [UIColor whiteColor];
    [self addSubview:_smartRepair];
    [_smartRepair mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(0);
        make.height.mas_equalTo((56+56+20)*scaleX);
    }];
    
    UILabel* la = [UILabel createLabelWithTitle:local(@"智能高清修复，消除噪点，增强画质") textColor:[UIColor colorWithHexString:@"#272727" alpha:1] textAlignment:NSTextAlignmentCenter font:standFont];
    la.numberOfLines = 0;
    [_smartRepair addSubview:la];
    [la mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.mas_equalTo(0);
        make.width.mas_equalTo(310*scaleX);
    }];
    
    _scratchRepair  = [UIView new];
    _scratchRepair.backgroundColor = [UIColor whiteColor];
    [self addSubview:_scratchRepair];
    [_scratchRepair mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(0);
        make.height.mas_equalTo((56+56+20)*scaleX);
    }];
    
    NSArray* arr = @[@"手动修复"];
    NSMutableArray* mutiArr = [NSMutableArray array];
    for (NSString* btnStr in arr) {
        UIButton* btn = [UIButton createButtonWithTitle:local(btnStr) color:[UIColor colorWithHexString:@"272727" alpha:1] font:smallFont ImageName:btnStr];
        btn.layer.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0].CGColor;
        btn.layer.cornerRadius = 15;
        btn.layer.shadowColor = [[UIColor blackColor] colorWithAlphaComponent:0.15].CGColor;
        btn.layer.shadowOffset = CGSizeMake(0,8);
        btn.layer.shadowOpacity = 1;
        btn.layer.shadowRadius = 8;
        [_scratchRepair addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            
            make.width.mas_equalTo(90*scaleX);
            make.height.mas_equalTo(70*scaleX);
            make.centerY.mas_equalTo(0);
            make.centerX.mas_equalTo(0);
        }];
        [btn.superview layoutIfNeeded];
        [btn setImagePositionWithType:SSImagePositionTypeTop spacing:4*scaleX];
        [mutiArr addObject:btn];
        [btn bk_whenTapped:^{
            [mutiArr bk_each:^(id  _Nonnull obj) {
                UIButton* eachBtn = obj;
                [eachBtn setImageTinColor:[UIColor colorWithHexString:@"#272727" alpha:1]];
            }];
            [btn setImageTinColor:[UIColor colorWithHexString:@"#FF8399" alpha:1]];
            
            if([btnStr isEqualToString:@"手动修复"]&&self.actionCallBack)
            {
                self.actionCallBack(@"手动修复");
            }
            
            //            if([_chooseType containsObject:btnStr])
            //            {
            //                btn.layer.borderWidth = 1;
            //                btn.layer.borderColor = [UIColor clearColor].CGColor;
            //                [_chooseType removeObject:btnStr];
            //                UIImageView* im = [self viewWithTag:100+[arr indexOfObject:btnStr]];
            //                [im removeFromSuperview];
            //            }
            //            else
            //            {
            //                btn.layer.borderWidth = 1;
            //                btn.layer.borderColor = [UIColor colorWithHexString:@"#FF65A9" alpha:1].CGColor;
            //                [_chooseType addObject:btnStr];
            //                UIImageView* im = [UIImageView createSizeFitImageviewName:@"多选_选中"];
            //                [self addSubview:im];
            //                im.tag  = 100+[arr indexOfObject:btnStr];
            //                [im mas_makeConstraints:^(MASConstraintMaker *make) {
            //                    make.width.height.mas_equalTo(18*scaleX);
            //                    make.centerX.mas_equalTo(btn);
            //                    make.centerY.mas_equalTo(btn.mas_bottom);
            //                }];
            //            }
            
        }];
    }
    //    [mutiArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:20*scaleX leadSpacing:88*scaleX tailSpacing:88*scaleX];
    
    
    _facialRetouching  = [UIScrollView new];
    _facialRetouching.backgroundColor = [UIColor whiteColor];
    [self addSubview:_facialRetouching];
    [_facialRetouching mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(0);
        make.height.mas_equalTo((56+56+20)*scaleX);
    }];
    
    arr = @[@"高清修复",@"照片上色"];
    mutiArr = [NSMutableArray array];
    for (NSString* btnStr in arr) {
        UIButton* btn = [UIButton createButtonWithTitle:local(btnStr) color:[UIColor colorWithHexString:@"272727" alpha:1] font:smallFont ImageName:[NSString stringWithFormat:@"%@-精修",btnStr]];
        btn.layer.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0].CGColor;
        btn.layer.cornerRadius = 15;
        btn.layer.shadowColor = [[UIColor blackColor] colorWithAlphaComponent:0.15].CGColor;
        btn.layer.shadowOffset = CGSizeMake(0,8);
        btn.layer.shadowOpacity = 1;
        btn.layer.shadowRadius = 8;
        [_facialRetouching addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            
            make.width.mas_equalTo(90*scaleX);
            make.height.mas_equalTo(70*scaleX);
            make.centerY.mas_equalTo(0);
        }];
        [btn.superview layoutIfNeeded];
        [btn setImagePositionWithType:SSImagePositionTypeTop spacing:4*scaleX];
        [mutiArr addObject:btn];
        [btn bk_whenTapped:^{
            [mutiArr bk_each:^(id  _Nonnull obj) {
                UIButton* eachBtn = obj;
                [eachBtn setImageTinColor:[UIColor colorWithHexString:@"#272727" alpha:1]];
            }];
            [btn setImageTinColor:[UIColor colorWithHexString:@"#FF8399" alpha:1]];
            if(self.actionCallBack)
            {
                self.actionCallBack(btnStr);
            }
            //            if([_chooseType containsObject:btnStr])
            //            {
            //                btn.layer.borderWidth = 1;
            //                btn.layer.borderColor = [UIColor clearColor].CGColor;
            //                [_chooseType removeObject:btnStr];
            //                UIImageView* im = [self viewWithTag:100+[arr indexOfObject:btnStr]];
            //                [im removeFromSuperview];
            //            }
            //            else
            //            {
            //                btn.layer.borderWidth = 1;
            //                btn.layer.borderColor = [UIColor colorWithHexString:@"#FF65A9" alpha:1].CGColor;
            //                [_chooseType addObject:btnStr];
            //                UIImageView* im = [UIImageView createSizeFitImageviewName:@"多选_选中"];
            //                [self addSubview:im];
            //                im.tag  = 100+[arr indexOfObject:btnStr];
            //                [im mas_makeConstraints:^(MASConstraintMaker *make) {
            //                    make.width.height.mas_equalTo(18*scaleX);
            //                    make.centerX.mas_equalTo(btn);
            //                    make.centerY.mas_equalTo(btn.mas_bottom);
            //                }];
            //            }
            
        }];
    }
    [mutiArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:10*scaleX leadSpacing:16*scaleX tailSpacing:16*scaleX];
    
    
    _groupPhotoRepair  = [UIView new];
    _groupPhotoRepair.backgroundColor = [UIColor whiteColor];
    [self addSubview:_groupPhotoRepair];
    [_groupPhotoRepair mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(0);
        make.height.mas_equalTo((56+56+20)*scaleX);
    }];
    
    la = [UILabel createLabelWithTitle:local(@"适用于修复模糊、低画质、泛黄的集体照") textColor:[UIColor colorWithHexString:@"#272727" alpha:1] textAlignment:NSTextAlignmentCenter font:standFont];
    la.numberOfLines = 0;
    [_groupPhotoRepair addSubview:la];
    [la mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.mas_equalTo(0);
        make.width.mas_equalTo(310*scaleX);
    }];
    
    arr = @[@"智能修复",@"划痕专修",@"脸部精修",@"集体照修复"];
    mutiArr = [NSMutableArray array];
    for (NSString* str in arr) {
        UIButton* btn = [UIButton createButtonWithTitle:local(str) color:[UIColor colorWithHexString:@"272727" alpha:1] font:smallFont ImageName:[NSString stringWithFormat:@"%@-照片",str]];
        [self addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(20*scaleX+(56+56+20)*scaleX);
            make.bottom.mas_equalTo(-(20+24)*scaleX);
        }];
        [btn.superview layoutIfNeeded];
        [btn setImagePositionWithType:SSImagePositionTypeTop spacing:4*scaleX];
        [mutiArr addObject:btn];
        [btn bk_whenTapped:^{
            [mutiArr bk_each:^(id  _Nonnull obj) {
                UIButton* eachBtn = obj;
                [eachBtn setImageTinColor:[UIColor colorWithHexString:@"#272727" alpha:1]];
            }];
            [btn setImageTinColor:[UIColor colorWithHexString:@"#FF8399" alpha:1]];
            switch ([arr indexOfObject:str]) {
                case 0:
                {
                    [self bringSubviewToFront:_smartRepair];
                }
                    break;
                case 1:
                {
                    [self bringSubviewToFront:_scratchRepair];
                }
                    break;
                case 2:
                {
                    [self bringSubviewToFront:_facialRetouching];
                }
                    break;
                case 3:
                {
                    [self bringSubviewToFront:_groupPhotoRepair];
                }
                    break;
                    
                default:
                    break;
            }
            if(self.actionCallBack)
            {
                self.actionCallBack(str);
            }
        }];
    }
    [mutiArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:0 leadSpacing:0 tailSpacing:0];
}
@end
