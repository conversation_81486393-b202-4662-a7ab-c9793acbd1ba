//
//  PTMPaint.m
//  photoTimeMachine
//
//  Created by fs0011 on 2024/4/7.
//

#import "PTMPaint.h"

@implementation PTMPaint
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        UIPinchGestureRecognizer *pinchGestureRecognizer = [[UIPinchGestureRecognizer alloc] initWithTarget:self action:@selector(handlePinch:)];
        [self addGestureRecognizer:pinchGestureRecognizer];
        
        UIPanGestureRecognizer *panGestureRecognizer = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePan:)];
               panGestureRecognizer.minimumNumberOfTouches = 2;
               panGestureRecognizer.maximumNumberOfTouches = 2;
               panGestureRecognizer.delegate = self;
               [self addGestureRecognizer:panGestureRecognizer];
    }
    return self;
}

- (void)handlePinch:(UIPinchGestureRecognizer *)pinchGestureRecognizer {
    if (pinchGestureRecognizer.state == UIGestureRecognizerStateBegan || pinchGestureRecognizer.state == UIGestureRecognizerStateChanged) {
        [self adjustAnchorPointForGestureRecognizer:pinchGestureRecognizer];
        
        CGFloat scale = pinchGestureRecognizer.scale;
        self.transform = CGAffineTransformScale(self.transform, scale, scale);
        
        pinchGestureRecognizer.scale = 1.0;
        self.drawView.magnifyingGlassView.hidden = YES;
    }
}

- (void)handlePan:(UIPanGestureRecognizer *)panGestureRecognizer {
    if (panGestureRecognizer.state == UIGestureRecognizerStateBegan || panGestureRecognizer.state == UIGestureRecognizerStateChanged) {
        CGPoint translation = [panGestureRecognizer translationInView:self.superview];
        CGPoint newCenter = CGPointMake(self.center.x + translation.x, self.center.y + translation.y);
        
        self.center = newCenter;
        [panGestureRecognizer setTranslation:CGPointZero inView:self.superview];
        self.drawView.magnifyingGlassView.hidden = YES;
    }
}

- (void)adjustAnchorPointForGestureRecognizer:(UIGestureRecognizer *)gestureRecognizer {
    if (gestureRecognizer.state == UIGestureRecognizerStateBegan) {
        CGPoint locationInView = [gestureRecognizer locationInView:self];
        CGPoint locationInSuperview = [gestureRecognizer locationInView:self.superview];
        
        self.layer.anchorPoint = CGPointMake(locationInView.x / self.bounds.size.width, locationInView.y / self.bounds.size.height);
        self.center = locationInSuperview;
    }
}

#pragma mark - UIGestureRecognizerDelegate

// 允许两个手势同时识别
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    return YES;
}

@end
