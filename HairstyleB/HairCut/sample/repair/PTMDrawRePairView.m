//
//  PTMDrawRePairView.m
//  photoTimeMachine
//
//  Created by fs0011 on 2023/6/14.
//

#import "PTMDrawRePairView.h"
#import "PTMMidSlider.h"
#import "UIButton+color.h"
@implementation PTMDrawRePairView
-(instancetype)init
{
    if(self = [super init])
    {
        
        self.backgroundColor = [UIColor whiteColor];
        [self mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(SCREEN_WIDTH);
        }];
        [self layout];
    }
    return self;
}

- (void)layout
{
    UIButton* next = [UIButton createButtonWithBGImageName:@"下一步"];
    [self addSubview:next];
    [next mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-16*scaleX);
        make.top.mas_equalTo(16*scaleX);
        make.width.height.mas_equalTo(24*scaleX);
    }];
    [next bk_whenTapped:^{
        if(self.nextAct)
        {
            self.nextAct();
        }
    }];
    
    UIButton* prev = [UIButton createButtonWithBGImageName:@"上一步"];
    [self addSubview:prev];
    [prev mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(next.mas_left).offset(-24*scaleX);
        make.top.mas_equalTo(16*scaleX);
        make.width.height.mas_equalTo(24*scaleX);
    }];
    [prev bk_whenTapped:^{
        if(self.backAct)
        {
            self.backAct();
        }
    }];
    
    UISlider* slider = [UISlider new];
    [self addSubview:slider];
    slider.minimumTrackTintColor = [UIColor colorWithHexString:@"#FF8399" alpha:1];
    slider.maximumTrackTintColor = [UIColor colorWithHexString:@"#F7F7F7" alpha:1];
    // 设置 UISlider 的选中状态背景颜色
    
    // 设置 UISlider 的 thumbImage
    slider.minimumValue = 0;
    slider.maximumValue = 100;
    slider.value = 20;
    [slider setThumbImage:[UIImage imageNamed:@"椭圆形"] forState:UIControlStateNormal];
    slider.layer.cornerRadius = 3*scaleX;
    [slider mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(293*scaleX);
        make.top.mas_equalTo(prev.mas_bottom).offset(34*scaleX);
        make.centerX.mas_equalTo(0);
        make.height.mas_equalTo(20*scaleX);
    }];
    [slider bk_addEventHandler:^(id  _Nonnull sender) {
        if(self.lineWidth)
        {
            self.lineWidth(@(slider.value));
        }
    } forControlEvents:UIControlEventValueChanged];
    NSMutableArray* mutiArr = [NSMutableArray array];
    UIButton* brush = [UIButton createButtonWithNormalImageName:@"画笔" normalTitle:local(@"画笔") normalColor:[UIColor colorWithHexString:@"272727" alpha:1] seletedName:@"画笔-选中" seletedTitle:local(@"画笔") seletColor:[UIColor colorWithHexString:@"FF8399" alpha:1] font:smallFont];
    [self addSubview:brush];
    [brush setImagePositionWithType:SSImagePositionTypeTop spacing:4*scaleX];
    [brush mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.mas_centerX).multipliedBy(2*1.0/3);
        make.top.mas_equalTo(slider.mas_bottom).offset((31+20)*scaleX);
        make.bottom.mas_equalTo(-(20+24)*scaleX);
    }];
    [brush bk_whenTapped:^{
        [mutiArr bk_each:^(id  _Nonnull obj) {
            UIButton* eachBtn = obj;
            [eachBtn setImageTinColor:[UIColor colorWithHexString:@"#272727" alpha:1]];
        }];
        [brush setImageTinColor:[UIColor colorWithHexString:@"#FF8399" alpha:1]];
        if(self.chooseType)
        {
            self.chooseType(@(0));
        }
    }];
    [mutiArr addObject:brush];
    
    UIButton* eraser = [UIButton createButtonWithNormalImageName:@"橡皮" normalTitle:local(@"橡皮") normalColor:[UIColor colorWithHexString:@"272727" alpha:1] seletedName:@"橡皮-选中" seletedTitle:@"橡皮" seletColor:[UIColor colorWithHexString:@"FF8399" alpha:1] font:smallFont];
    [self addSubview:eraser];
    [eraser setImagePositionWithType:SSImagePositionTypeTop spacing:4*scaleX];
    [eraser mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.mas_centerX).multipliedBy(4*1.0/3);
        make.top.mas_equalTo(slider.mas_bottom).offset((31+20)*scaleX);
    }];
    [mutiArr addObject:eraser];
    [eraser bk_whenTapped:^{
        [mutiArr bk_each:^(id  _Nonnull obj) {
            UIButton* eachBtn = obj;
            [eachBtn setImageTinColor:[UIColor colorWithHexString:@"#272727" alpha:1]];
        }];
        [eraser setImageTinColor:[UIColor colorWithHexString:@"#FF8399" alpha:1]];
        
        if(self.chooseType)
        {
            self.chooseType(@(1));
        }
    }];
}


/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
