//
//  PTMFreeRepairView.m
//  photoTimeMachine
//
//  Created by fs0011 on 2023/6/13.
//

#import "PTMFreeRepairView.h"

@implementation PTMFreeRepairView

- (instancetype)init
{
    if(self == [super init])
    {
        _chooseType = [NSMutableArray array];
        [self layout];
        
    }
    return self;
}

- (void)layout
{
    NSArray* arr = @[@"高清细节修复",@"划痕破损修复",@"照片上色-修复"];
    NSMutableArray* mutiArr = [NSMutableArray array];
    for (NSString* btnStr in arr) {
        UIButton* btn = [UIButton createButtonWithTitle:local(btnStr) color:[UIColor colorWithHexString:@"272727" alpha:1] font:smallFont ImageName:btnStr];
        btn.layer.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0].CGColor;
        btn.layer.cornerRadius = 15;
        btn.layer.shadowColor = [[UIColor blackColor] colorWithAlphaComponent:0.15].CGColor;
        btn.layer.shadowOffset = CGSizeMake(0,8);
        btn.layer.shadowOpacity = 1;
        btn.layer.shadowRadius = 8;
        [self addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(32*scaleX);
            make.width.mas_equalTo(90*scaleX);
            make.height.mas_equalTo(70*scaleX);
            make.bottom.mas_equalTo(-32*scaleX);
        }];
        [btn.superview layoutIfNeeded];
        [btn setImagePositionWithType:SSImagePositionTypeTop spacing:4*scaleX];
        [mutiArr addObject:btn];
        [btn bk_whenTapped:^{
            if([_chooseType containsObject:btnStr])
            {
                btn.layer.borderWidth = 1;
                btn.layer.borderColor = [UIColor clearColor].CGColor;
                [_chooseType removeObject:btnStr];
                UIImageView* im = [self viewWithTag:100+[arr indexOfObject:btnStr]];
                [im removeFromSuperview];
            }
            else
            {
                btn.layer.borderWidth = 1;
                btn.layer.borderColor = [UIColor colorWithHexString:@"#FF65A9" alpha:1].CGColor;
                [_chooseType addObject:btnStr];
                UIImageView* im = [UIImageView createSizeFitImageviewName:@"多选_选中"];
                [self addSubview:im];
                im.tag  = 100+[arr indexOfObject:btnStr];
                [im mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.width.height.mas_equalTo(18*scaleX);
                    make.centerX.mas_equalTo(btn);
                    make.centerY.mas_equalTo(btn.mas_bottom);
                }];
            }
            
        }];
    }
    [mutiArr mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:20*scaleX leadSpacing:32*scaleX tailSpacing:32*scaleX];
    
}
/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
