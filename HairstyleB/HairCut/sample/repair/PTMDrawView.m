//
//  PTMDrawView.m
//  photoTimeMachine
//
//  Created by fs0011 on 2023/6/14.
//

#import "PTMDrawView.h"
#import "PTMBezierPath.h"

@interface PTMDrawView()
//@property (nonatomic, strong) UIBezierPath * path;

@property (nonatomic, strong) NSMutableArray *paths;
@property (nonatomic, strong) NSMutableArray *orginPaths;



@property UIView* viewToMagnify;
@end
@implementation PTMDrawView

//懒加载
- (NSMutableArray *)paths
{
    if(_paths == nil)
    {
        _paths = [NSMutableArray array];
    }
    return _paths;
}

- (NSMutableArray *)orginPaths
{
    if(_orginPaths == nil)
    {
        _orginPaths = [NSMutableArray array];
    }
    return _orginPaths;
}

//实现清屏方法
- (void)clear
{
    //删除数组中的路径
    [self.paths removeAllObjects];
    //执行重绘
    [self setNeedsDisplay];
}

//实现下一步
- (void)next
{
    //数组中的下一个途径
    for (PTMBezierPath* pz in _orginPaths) {
        if(![_paths containsObject:pz])
        {
            [_paths addObject:pz];
            break;
        }
    }
    //执行重绘
    [self setNeedsDisplay];
    
}

//实现回退方法
- (void)back
{
    //删除数组中的最后一个路径
    [_paths removeLastObject];
    //    _paths = [NSMutableArray arrayWithArray:_orginPaths];
    
    //执行重绘
    [self setNeedsDisplay];
    
}


//实现橡皮擦功能
- (void)eraser
{
    //设置线的颜色
    self.lineColor = self.backgroundColor;
    self.isEarser = YES;
}

//手指按下
- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    //1.获取触摸对象
    UITouch * touch = touches.anyObject;
    
    //2.获取触摸点
    CGPoint locP = [touch locationInView:touch.view];
    
    //3.创建绘图路径
    PTMBezierPath * path = [[PTMBezierPath alloc] init];
    path.isErase = self.isEarser;
    //3.1设置线宽
    path.lineWidth = self.lineWidth;
    
    //3.2设置颜色
    path.lineColor = self.lineColor;
    
    //4.添加子路径
    path.points = [NSMutableArray array];
    [path.points addObject:@(locP)];
//    [path moveToPoint:locP];
    
    //5.把path添加到数组中
    [self.paths addObject:path];
    _orginPaths = [NSMutableArray arrayWithArray:self.paths];
    [self setNeedsDisplay];
    
    CGPoint locPc = [touch locationInView:touch.view.superview.superview];
    NSLog(@"%lf,%lf",locPc.x,locPc.y);
    if(locPc.y<120+15+48+KDNavH&&locPc.x<120+15)
    {
        self.magnifyingGlassView.frame = CGRectMake(SCREEN_WIDTH-120-15, 15, 120, 120);
    }
    else if(locPc.y<120+15+48+KDNavH&&locPc.x>SCREEN_WIDTH-(120+15))
    {
        self.magnifyingGlassView.frame = CGRectMake(0+15, 15, 120, 120);
    }
    [self updateMagnifyingGlassWithTouches:touches];
    
}
//手指移动
- (void)touchesMoved:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    //1.获取触摸对象
    UITouch * touch = touches.anyObject;
    
    //2.获取触摸点
    CGPoint locP = [touch locationInView:touch.view];
    
    //3.添加子路径
//    [[self.paths lastObject] addLineToPoint:locP];
    PTMBezierPath* bz = [self.paths lastObject];
    [bz.points addObject:@(locP)];
    //执行重绘
    [self setNeedsDisplay];
    
    
    
    
    CGPoint locPc = [touch locationInView:touch.view.superview.superview];
    NSLog(@"locPc %lf,%lf",locPc.x,locPc.y);
    if(locPc.y<120+15+48+KDNavH&&locPc.x<120+15)
    {
        self.magnifyingGlassView.frame = CGRectMake(SCREEN_WIDTH-120-15, 15, 120, 120);
    }
    else if(locPc.y<120+15+48+KDNavH&&locPc.x>SCREEN_WIDTH-(120+15))
    {
        self.magnifyingGlassView.frame = CGRectMake(0+15, 15, 120, 120);
    }
    [self updateMagnifyingGlassWithTouches:touches];
}
//手指抬起







- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    self.magnifyingGlassView.hidden = YES; // 移除放大镜
}

- (void)updateMagnifyingGlassWithTouches:(NSSet<UITouch *> *)touches {
    UITouch *touch = [touches anyObject];
    CGPoint touchPoint = [touch locationInView:self.superview]; // 假设viewToMagnify是你的UIImageView
    // 注意，这里是在父视图的坐标系统中获取触摸点
    
    
    if (!self.magnifyingGlassView) {
        self.magnifyingGlassView = [[MagnifyingGlassView alloc] initWithFrame:CGRectMake(0+15, 15, 120, 120)]; // 100x100为放大镜尺寸
        self.magnifyingGlassView.viewToMagnify = self.superview.superview;
        [self.superview.superview addSubview:self.magnifyingGlassView];
    }
    
    self.magnifyingGlassView.touchPoint = touchPoint;
    self.magnifyingGlassView.hidden = YES;

        self.magnifyingGlassView.hidden = NO;
    if (!CGRectContainsPoint(self.frame, touchPoint)) {
        self.magnifyingGlassView.hidden = YES;
    }
    [self.superview bringSubviewToFront:self.magnifyingGlassView];
}

//- (void)drawRect:(CGRect)rect {
//    // Drawing code
//    //绘图
//    for (PTMBezierPath * path in self.paths)
//    {
//        //设置颜色
//        [path.lineColor set];
//        
//        //设置线头样式
//        path.lineCapStyle = kCGLineCapRound;
//        
//        //设置连接处的样式
//        path.lineJoinStyle = kCGLineJoinRound;
//        
//        if (path.isErase) {
//            // 橡皮擦
//            [[UIColor clearColor] setStroke];
//            [path strokeWithBlendMode:kCGBlendModeCopy alpha:1.0];
//        } else {
//            // 画线
//            [path.lineColor setStroke];
//            [path strokeWithBlendMode:kCGBlendModeNormal alpha:1.0];
//        }
//        
//        [path stroke];
//    }
//}
- (void)drawRect:(CGRect)rect {
    // Drawing code
    //获得上下文
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    CGContextClearRect(ctx, rect);

    //保存现在的上下文状态到栈中
    CGContextSaveGState(ctx);

    CGContextSetLineCap(ctx, kCGLineCapRound);
    // 3.3,设置线段转折点的样式
    CGContextSetLineJoin(ctx, kCGLineJoinRound);
    //你原本的绘制代码...
    for (PTMBezierPath * path in self.paths) {
      for (int i=0; i<path.points.count ;i++) {
            CGPoint pointRect = [path.points[i] CGPointValue];

            if (i == 0) {
                // 画一条线的起点
                CGContextMoveToPoint(ctx, pointRect.x , pointRect.y );
            } else {
                // 连线
                CGContextAddLineToPoint(ctx, pointRect.x , pointRect.y );
            }
        }
        // 设置线宽
        CGFloat linew = path.lineWidth;
        if (linew <= 0.5) {
            linew = 0.5;
        }
        CGContextSetLineWidth(ctx, linew);
        // 设置颜色
        CGColorRef color = [UIColor colorWithHexString:@"#FF8399" alpha:0.3].CGColor;
        if (path.isErase) {
            color = [UIColor clearColor].CGColor;
        }
        CGContextSetStrokeColorWithColor(ctx, color);
        CGContextSetBlendMode(ctx, kCGBlendModeCopy);
        CGContextStrokePath(ctx);
    }

    //恢复到之前的上下文状态
    CGContextRestoreGState(ctx);
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */
- (UIImage *)renderImage:(UIImage*)ori
{
    UIGraphicsBeginImageContextWithOptions(self.bounds.size, NO, ori.scale);
    CGContextRef context = UIGraphicsGetCurrentContext();
    [self.layer renderInContext:context];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

void CProviderReleaseData(void *info, const void *data, size_t size) {
    free((void *)data);
}

- (UIImage*)imageRedToRed:(UIImage*) image percent:(float*)percent
{
    
    BOOL isbool = NO;
    // 分配内存
    const int imageWidth = image.size.width;
    const int imageHeight = image.size.height;
    size_t      bytesPerRow = imageWidth * 4;
    uint32_t* rgbImageBuf = (uint32_t*)malloc(bytesPerRow * imageHeight);
    memset(rgbImageBuf, 0, bytesPerRow * imageHeight);
    // 创建context
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(rgbImageBuf, imageWidth, imageHeight, 8, bytesPerRow, colorSpace,
                                                 kCGBitmapByteOrder32Little | kCGImageAlphaNoneSkipLast);
    CGContextDrawImage(context, CGRectMake(0, 0, imageWidth, imageHeight), image.CGImage);
    
    // 遍历像素
    long pixelNum = imageWidth * imageHeight;
    long pixCount = 0;
    uint32_t* pCurPtr = rgbImageBuf;
    for (int i = 0; i < pixelNum; i++, pCurPtr++)
    {
        if ((*pCurPtr & 0xFFFFFF00) >= 0) // black to alpha
        {
            uint8_t* ptr = (uint8_t*)pCurPtr;
            if((ptr[3] > 1))
            {
                isbool = YES;
                ptr[3] = 255; //  white
                ptr[2] = 255;
                ptr[1] = 255;
                pixCount++;
            }else{
                ptr[3] = 0; //  white
                ptr[2] = 0;
                ptr[1] = 0;
            }
        }
    }
    *percent = (float)pixCount/pixelNum;
    
    // 将内存转成image
    CGDataProviderRef dataProvider = CGDataProviderCreateWithData(NULL, rgbImageBuf, bytesPerRow * imageHeight, CProviderReleaseData);
    CGImageRef imageRef = CGImageCreate(imageWidth, imageHeight, 8, 32, bytesPerRow, colorSpace,
                                        kCGImageAlphaLast | kCGBitmapByteOrder32Little, dataProvider,
                                        NULL, true, kCGRenderingIntentDefault);
    CGDataProviderRelease(dataProvider);
    
    UIImage* resultUIImage = [UIImage imageWithCGImage:imageRef];
    
    // 释放
    CGImageRelease(imageRef);
    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);
    // free(rgbImageBuf) 创建dataProvider时已提供释放函数，这里不用free
    if (isbool&&pixCount>0) {
        return resultUIImage;
    }else{
        return nil;
    }
    
}

- (UIImage *)scaleImage:(UIImage *)image toSize:(CGSize)size {
    UIGraphicsBeginImageContextWithOptions(size, NO, image.scale);
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    UIImage *scaledImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return scaledImage;
}



@end
