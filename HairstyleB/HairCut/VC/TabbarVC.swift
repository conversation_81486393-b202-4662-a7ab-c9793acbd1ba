//
//  TabbarVC.swift
//  HairCut
//
//  Created by Bigger on 2024/7/29.
//

import Foundation
import UIKit
import BUAdSDK

class TabbarVC: UITabBarController ,UITabBarControllerDelegate{
    public var isNeedFullScreenAd = false
    var fullscreenAd = BUNativeExpressFullscreenVideoAd()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        if #available(iOS 18.0, *) {
            self.mode = .tabBar
        }
        
        if #available(iOS 17.0, *) {
            traitOverrides.horizontalSizeClass = .unspecified
        }
        
        
        self.navigationController?.navigationBar.isHidden = true
        self.view.backgroundColor = UIColor.white
        self.tabBar.isTranslucent = false
        let homeVC = HomeVC()
        let homeSelectedImage = UIImage(named: "tabbar_home_on")?.withRenderingMode(.alwaysOriginal)
        homeVC.tabBarItem.selectedImage = homeSelectedImage
        let homeUnSelectedImage = UIImage(named: "tabbar_home_off")?.withRenderingMode(.alwaysOriginal)
        homeVC.tabBarItem.image = homeUnSelectedImage
        homeVC.tabBarItem.title = "home".localized
        homeVC.tabBarItem.setTitleTextAttributes([.foregroundColor: UIColor(valueRGB: 0x999999)], for: .normal)
        homeVC.tabBarItem.setTitleTextAttributes([.foregroundColor: UIColor(valueRGB: 0x333333)], for: .selected)
        
        let hairstyleVC = HairStyleVC()
        let hairstyleSeletedImage = UIImage(named: "tabbar_hairstyle_on")?.withRenderingMode(.alwaysOriginal)
        hairstyleVC.tabBarItem.selectedImage = hairstyleSeletedImage
        let hairstyleUnSeletedImage = UIImage(named: "tabbar_hairstyle_off")?.withRenderingMode(.alwaysOriginal)
        hairstyleVC.tabBarItem.image = hairstyleUnSeletedImage
        hairstyleVC.tabBarItem.title = "hairstyle".localized
        hairstyleVC.tabBarItem.setTitleTextAttributes([.foregroundColor: UIColor(valueRGB: 0x999999)], for: .normal)
        hairstyleVC.tabBarItem.setTitleTextAttributes([.foregroundColor: UIColor(valueRGB: 0x333333)], for: .selected)
        
        let settingVC = SettingVC()
        let settingSeletedImage = UIImage(named: "tabbar_setting_on")?.withRenderingMode(.alwaysOriginal)
        settingVC.tabBarItem.selectedImage = settingSeletedImage
        let settingUnSeletedImage = UIImage(named: "tabbar_setting_off")?.withRenderingMode(.alwaysOriginal)
        settingVC.tabBarItem.image = settingUnSeletedImage
        settingVC.tabBarItem.title = "setting".localized
        settingVC.tabBarItem.setTitleTextAttributes([.foregroundColor: UIColor(valueRGB: 0x999999)], for: .normal)
        settingVC.tabBarItem.setTitleTextAttributes([.foregroundColor: UIColor(valueRGB: 0x333333)], for: .selected)
        
        addChild(UINavigationController(rootViewController: homeVC))
        addChild(UINavigationController(rootViewController: hairstyleVC))
        addChild(UINavigationController(rootViewController: settingVC))
        self.selectedIndex = 0
        
        self.setTabbarColor()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.navigationBar.isHidden = true
        self.setTabbarColor()
        showFullScreenAd()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.navigationController?.navigationBar.isHidden = true
    }
    
    private func setTabbarColor() {
        self.tabBar.tintColor = UIColor(valueRGB: 0x333333)
        self.tabBar.unselectedItemTintColor =  UIColor(valueRGB: 0x999999)
    }
    
    private func showFullScreenAd() {
        if !self.isNeedFullScreenAd {
            return
        }
        
        self.fullscreenAd = BUNativeExpressFullscreenVideoAd(slotID: AdsCNSetting.insertFullViewId)
        self.fullscreenAd.delegate = self
        self.fullscreenAd.loadData()
    }
    
    deinit {
        printLog(message: "deinit")
    }
    
    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        print("选中了标签：\(viewController.title ?? "")")
        if let index = tabBarController.viewControllers?.firstIndex(of: viewController) {
            print("选中了标签索引：\(index)")
            if index==1
            {
                MobClick.event("HOME", attributes: ["source": "发型库"])
            }
        }
    }
    
}

typealias TabbarVCAD = TabbarVC
extension TabbarVCAD: BUNativeExpressFullscreenVideoAdDelegate {
    func nativeExpressFullscreenVideoAdDidLoad(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        printLog(message: "全屏广告加载成功")
        self.isNeedFullScreenAd = false
    }
    
    func nativeExpressFullscreenVideoAd(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd, didFailWithError error: (any Error)?) {
        printLog(message: "全屏广告错误码:\(String(describing: error))")
    }
    
    func nativeExpressFullscreenVideoAdViewRenderFail(_ rewardedVideoAd: BUNativeExpressFullscreenVideoAd, error: (any Error)?) {
        printLog(message: "全屏广告渲染失败:\(String(describing: error))")
    }
    
    func nativeExpressFullscreenVideoAdDidDownLoadVideo(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        printLog(message: "全屏广告下载完成")
        //根控制器一直都是TabbarVC，可以直接赋值self；下载缓慢时，即使点击到其他页面，下载完成后也会马上弹全屏广告
        DispatchQueue.main.async {
            self.fullscreenAd.show(fromRootViewController: self)
            self.isNeedFullScreenAd = false
        }
    }
    
    func nativeExpressFullscreenVideoAdDidClose(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        printLog(message:"全屏广告已关闭")
    }
}
