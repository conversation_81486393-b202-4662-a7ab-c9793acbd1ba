//
//  LaunchScreenVC.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/7/31.
//

import Foundation
import UIKit
import SnapKit

class LaunchScreenVC: UIViewController {
    private let launchScreenView = LaunchScreenView()
    private var timer: Timer?
    private var count = 0
    private var targetCount = 0
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.view.addSubview(self.launchScreenView)
        self.launchScreenView.snp.makeConstraints { make in
            make.left.width.height.equalToSuperview()
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        let randomNumber = Int.random(in: 3...5)
        self.targetCount = 2
        self.timer = Timer.scheduledTimer(timeInterval: 0.5, target: self, selector: #selector(timeCountAction), userInfo: nil, repeats: true)
        self.timer?.fire()
    }
    
    override func viewWillAppear(_ animated: <PERSON><PERSON>) {
        super.viewWillAppear(animated)
        self.navigationController?.isNavigationBarHidden = true
    }
    
    deinit {
        printLog(message: "launch deint")
    }
    
    @objc func timeCountAction() {
        if self.count <= self.targetCount {
            self.count += 1
            let displayedCount: Int
            
            if count <= 4 {
                displayedCount = Int(self.count)
            } else {
                let remainder = self.count % 4
                displayedCount = remainder == 0 ? 4 : remainder
            }
            self.launchScreenView.count = displayedCount
            return
        }
        
        self.timer?.invalidate()
        
        // 检查用户是否已订阅
        let isSubscribed = APPMakeStoreIAPManager.featureVip()
        let isFirstUse = UserDefaultsTool.readTemporaryString(key: UserDefaultsConst.firstUse) == nil
        
        // 如果是首次使用或者用户没有订阅，显示欢迎页面
        if isFirstUse || !isSubscribed {
            AppLoad.resetWelcomeVC()
        } else {
            // 如果用户已订阅，直接进入主页
            AppLoad.resetTabbarVC(true)
            GoodRepulationTool.showGoodRepulation()
        }
    }
}
