//
//  FaceShapeTestVC.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/1.
//

import Foundation
import UIKit
import SnapKit
import Promises
import SwiftyJSON

class FaceShapeTestVC: UIViewController {
    private let faceShapeTestView = FaceShapeTestView()
    private var loadingView: UIView? = nil
    private var activityIndicator: UIActivityIndicatorView? = nil
    private let faceShapeSliderResultView = FaceShapeSliderResultView()
    private var navigationRightButton: UIButton? = nil
    
    private let pickImageTool = PickImageTool()
    
    public var shapeArr = [FaceShape]()
    public var imageResult: [FaceTestCellData] = [] {
        willSet {
            self.faceShapeSliderResultView.resultImageArr = newValue
        }
    }
    private var token: String? = nil
    public var sourceImage: UIImage? {
        willSet {
            self.faceShapeTestView.sourceImage = newValue
        }
    }

    private var faceData: FaceTestData? {
        willSet {
            self.faceShapeTestView.faceTestData = newValue
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setLeftNavigationBar()
        self.setRightNavigationBarButton(buttonString: "save".localized)
        self.faceShapeTestView.delegate = self
        self.view.addSubview(self.faceShapeTestView)
        self.faceShapeTestView.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
        self.startFaceTest()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.navigationBar.isTranslucent = false
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.navigationController?.navigationBar.isTranslucent = true
    }
    
    deinit {
        printLog(message: "deinit")
    }
}

typealias FaceShapeResult = FaceShapeTestVC
extension FaceShapeResult: FaceShapeTestViewDelegate {
    private func startFaceTest() {
        self.showLoadingView()
        self.getToken().then { token in
            return self.getFaceTextMessage()
        }.then { result in
            return self.buildFaceData(result: result)
        }.then({ result in
            self.imageResult = result
            self.hideLoadingView()
            GoodRepulationTool.showGoodRepulation(isNeedResult: true)
        }).catch { error in
            self.hideLoadingView()
            printLog(message: "获取脸形检测失败:\(error)")
            AppLoad.showActionAlert(message: "face_test_fail".localized)
        }
    }
    
    private func buildFaceData(result: [String: Any]) -> Promise<[FaceTestCellData]> {
        return Promise<[FaceTestCellData]> {[weak self] (resolve, reject) in
            guard let genderDictionary = result["gender"] as? [String: Any],
                  let gender = genderDictionary["type"] as? String,
                  let age = result["age"] as? Int64,
                let beauty = result["beauty"] as? Double,
                let expressionDictionary = result["expression"] as? [String: Any],
                let expression = expressionDictionary["type"] as? String,
                let glassessDictionary = result["glasses"] as? [String: Any],
                let glassess = glassessDictionary["type"] as? String,
                let faceShapeDictionary = result["face_shape"] as? [String: Any],
                    let faceShape = faceShapeDictionary["type"] as? String,
                  let landmark150 = result["landmark150"] as? [String: Any],    
                    let eyeLeftEyeBallCenter = landmark150["eye_left_eyeball_center"] as? [String: Any],
                  let eyeLeftEyeBallX = eyeLeftEyeBallCenter["x"] as? Double,
                  let eyeLeftEyeBallY = eyeLeftEyeBallCenter["y"] as? Double,
                    let eyeRightEyeBallCenter = landmark150["eye_right_eyeball_center"] as? [String: Any],
                  let eyeRightEyeBallX = eyeRightEyeBallCenter["x"] as? Double,
                  let eyeRightEyeBallY = eyeRightEyeBallCenter["y"] as? Double,
                  let location = result["location"] as? [String: Any],
                  let rotation = location["rotation"] as? Int64
            else {
                var error = HttpError()
                error.statusMesg = "解析百度接口数据失败"
                reject(error)
                return
            }
            
            let leftEye = CGPoint(x: eyeLeftEyeBallX, y: eyeLeftEyeBallY)
            let rightEye = CGPoint(x: eyeRightEyeBallX, y: eyeRightEyeBallY)
            self?.faceData = FaceTestData(gender: FaceTestGender(rawValue: gender) ?? .none, age: Int(age), glassess: FaceTestGlasses(rawValue: glassess) ?? .none, faceshape: FaceShape(rawValue: faceShape) ?? .noChoose, beauty: Int(beauty), expression: FaceTestExpression(rawValue: expression) ?? .none, letfEyeCenter: rightEye, rightEyeCenter: leftEye, rotation: rotation)
            guard let faceTestResultFileURL = Bundle.main.url(forResource: "FaceTestResultImageMatch", withExtension: "json"), let imageIdArray = self?.getFaceShapeImageIdArray(faceShape: self?.faceData?.faceshape ?? .noChoose) else {
                var error = HttpError()
                error.statusMesg = "解析FaceTestResultImageMatch.json失败"
                reject(error)
                return
            }
            
            var result = [FaceTestCellData]()
            imageIdArray.forEach { imageId in
                if let imageHash = self?.getMatchImageHash(fileUrl: faceTestResultFileURL, imageId: imageId, gender: self?.faceData?.gender ?? .none) {
                    let imageUrlString = "\(HttpConst.faceTestResultDomain)\(imageHash)"
                    let cellData = FaceTestCellData(leftEye: leftEye, rightEye: rightEye, rotation: rotation, hairImageUrlString: imageUrlString, image: self?.sourceImage)
                    result.append(cellData)
                }
            }
            resolve(result)
        }
    }
    
    /// 通过本地FaceTestResultImageMatch.json解析对应的图片hash值
    private func getMatchImageHash(fileUrl: URL, imageId: String, gender: FaceTestGender) -> String? {
        
        let genderString: String
        switch gender {
        case .none:
            genderString = ""
        case .male:
            genderString = "0"
        case .female:
            genderString = "1"
        }
        
        do {
                let data = try Data(contentsOf: fileUrl)
                let jsonData = try JSON(data: data)
                guard let hairStyles = jsonData["HairStyle"].array else {
                    return nil
                }
                
                for hairStyleJson in hairStyles {
                    if let hairId = hairStyleJson["hair_id"].string,
                        let style = hairStyleJson["style"].string,
                        hairId == imageId && style == genderString {
                        return hairStyleJson["big"].string
                    }
                }
            } catch {
                printLog(message: "getMatchImageHash解析JSON失败: \(error)")
                return nil
            }
            
            return nil
    }
    
    /// 通过本地FaceTestResult.json获取对应照片hair_id字符串数组
    private func getFaceShapeImageIdArray(faceShape: FaceShape) -> [String]? {
        guard let fileURL = Bundle.main.url(forResource: "FaceTestResult", withExtension: "json") else {
            return nil
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                let result = json["result"] as? [[String: Any]] {
                for item in result {
                    guard let feature = item["feature"] as? String,
                          let hairId = item["hair_id"] as? [String] else {
                        continue
                    }
                    
                    switch (feature, faceShape) {
                    case ("0", .oval),
                         ("1", .heart),
                         ("2", .triangle),
                         ("3", .round),
                         ("4", .square):
                        return hairId
                    default:
                        continue
                    }
                }
            }
        } catch {
            printLog(message: "getFaceShapeImageIdArray解析JSON失败: \(error)")
        }
        
        return nil
    }
    
    func resultButtonAction() {
        self.faceShapeSliderResultView.delegate = self
        self.view.window?.addSubview(self.faceShapeSliderResultView)
        self.faceShapeSliderResultView.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
    }
    
    func updateFaceShape(faceShape: FaceShape) {
        guard let faceTestResultFileURL = Bundle.main.url(forResource: "FaceTestResultImageMatch", withExtension: "json"), let imageIdArray = self.getFaceShapeImageIdArray(faceShape: faceShape), let pfaceData = self.faceData else {
            var error = HttpError()
            error.statusMesg = "刷新时，解析FaceTestResultImageMatch.json失败"
            return
        }
        var result = [FaceTestCellData]()
        imageIdArray.forEach { imageId in
            if let imageHash = self.getMatchImageHash(fileUrl: faceTestResultFileURL, imageId: imageId, gender: pfaceData.gender) {
                let imageUrlString = "\(HttpConst.faceTestResultDomain)\(imageHash)"
                let cellData = FaceTestCellData(leftEye: pfaceData.letfEyeCenter, rightEye: pfaceData.rightEyeCenter, rotation: pfaceData.rotation, hairImageUrlString: imageUrlString, image: self.sourceImage)
                result.append(cellData)
            }
        }
        self.imageResult = result
    }
}

typealias FaceShapeTestBaiDu = FaceShapeTestVC
extension FaceShapeTestBaiDu {
    private func getToken() -> Promise<String> {
        return Promise<String> {[weak self] (resolve, reject) in
            var config = HttpConfig()
            config.url = HttpConst.badiduToken + "?grant_type=client_credentials&client_id=\(BaiduTokenConst.apiKey)&client_secret=\(BaiduTokenConst.secrectKey)"
            config.method = .post
            config.params = ["grant_type": BaiduTokenConst.grantType,"client_id": BaiduTokenConst.apiKey, "client_secret": BaiduTokenConst.secrectKey]
            HttpTool.shared.request(config: config).then { result in
                let token = result.json["access_token"].stringValue
                self?.token = token
                resolve(token)
            }.catch { error in
                reject(error)
            }
        }
    }
    
    private func getFaceTextMessage() -> Promise<[String: Any]> {
        return Promise<[String: Any]> {[weak self] (resolve, reject) in
            var config = HttpConfig()
            //上传限制10mb,像素小于1920x1080
            guard let token = self?.token, let image = self?.sourceImage else {
                var error = HttpError()
                error.statusMesg = "token未空或者base64不存在"
                reject(error)
                return
            }
            
            var imageBase64: String?
            let fixSize = ImageTool().getFixSize(image.size, maxSize: 800 * 800)
            if image.size.width > fixSize.width || image.size.height > fixSize.height {
                let newImage = image.createImage(fixSize)
                imageBase64 = newImage?.base64EncodedString()
                //重新赋值压缩后的图片
                self?.sourceImage = newImage
            } else {
                imageBase64 = image.base64EncodedString()
            }
            
            config.url = HttpConst.faceTestV3 + "?access_token=\(token)"
            config.method = .post
            config.params = ["image": imageBase64 ?? "","image_type": "BASE64", "face_field": "age,expression,gender,face_shape,glasses,beauty,landmark150"]
            //目前只做第一个人脸识别，接口支持多个
            HttpTool.shared.request(config: config).then { result in
                guard let result = result.json["result"].dictionaryObject, let faceList = result["face_list"] as? [[String: Any]], let targetResult = faceList.first else {
                    resolve([String: Any]())
                    return
                }
                resolve(targetResult)
            }.catch { error in
                reject(error)
            }
        }
    }
}

typealias FaceShapeLoadingAction = FaceShapeTestVC
extension FaceShapeLoadingAction: FaceShapeSliderResultViewDelegate {
    func selectHair(hairUrlString: String) {
        self.faceShapeTestView.faceHairUrlString = hairUrlString
        self.faceShapeSliderResultView.removeFromSuperview()
    }
    
    private func showLoadingView() {
        let loadingView = UIView(frame: self.view.frame)
        self.loadingView = loadingView
        loadingView.backgroundColor = UIColor(valueRGB: 0xF9F9F9).withAlphaComponent(0.8)
        self.view.addSubview(loadingView)
        let activityIndicator =  UIActivityIndicatorView(frame: CGRect(x: 0, y: 0, width: 20, height: 20))
        self.activityIndicator = activityIndicator
        activityIndicator.center = CGPoint(x: self.view.frame.width / 2, y: (self.view.frame.height - (self.navigationController?.navigationBar.frame.height ?? 0) - UIApplication.shared.statusBarFrame.height) / 2)
        loadingView.addSubview(activityIndicator)
        activityIndicator.startAnimating()
        self.navigationRightButton?.isEnabled = false
        let tips = UILabel(frame: CGRect(x: 20, y: self.activityIndicator!.frame.size.height + self.activityIndicator!.frame.origin.y + 10, width: self.loadingView!.frame.size.width - 40, height: 50))
        tips.text = "loading_tips".localized
        tips.textColor = UIColor(valueRGB: 0x333333)
        tips.numberOfLines = 0
        tips.textAlignment = .center
        self.loadingView?.addSubview(tips)
    }
    
    private func hideLoadingView() {
        self.activityIndicator?.stopAnimating()
        self.loadingView?.removeFromSuperview()
        self.navigationRightButton?.isEnabled = true
    }
}

typealias FaceShapeTestRightNavigationBar = FaceShapeTestVC
extension FaceShapeTestRightNavigationBar {
    private func setRightNavigationBarButton(
        buttonString: String,
        buttonBackgroundColor: UIColor = UIColor(valueRGB: 0xFFEC53),
        buttonTitleColor: UIColor = UIColor(valueRGB: 0x333333),
        buttonFont: UIFont = UIFont.systemFont(ofSize: 13),
        buttonRect: CGRect = CGRect(x: 0, y: 0, width: 52, height: 26),
        buttonCornerRadius: CGFloat = 13)
    {
        let button = UIButton(type: .custom)
        self.navigationRightButton = button
        button.layer.cornerRadius = buttonCornerRadius
        button.frame = buttonRect
        button.setTitle(buttonString, for: .normal)
        button.setTitleColor(buttonTitleColor, for: .normal)
        button.backgroundColor = buttonBackgroundColor
        button.titleLabel?.font = buttonFont
        button.addTarget(self, action: #selector(buttonClick), for: .touchUpInside)
        self.navigationItem.rightBarButtonItem = UIBarButtonItem(customView: button)
        button.isEnabled = false
    }
    
    @objc func buttonClick() {
        MobClick.event("Face_shape_test", attributes: ["source": "保存"])
        guard let image = self.getResultImage(), let croppedImage = image.sd_croppedImage(with: self.faceShapeTestView.imageView.frame) else {
            AppLoad.showActionAlert(message: "save_fail".localized)
            return
        }
        self.pickImageTool.saveImageToAlbum(croppedImage) { success in
            AppLoad.showActionAlert(message: success == true ? "save_success".localized : "save_fail".localized)
        }
    }
    
    private func getResultImage() -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(self.faceShapeTestView.imageActionView.bounds.size, false, UIScreen.main.scale)
        defer { UIGraphicsEndImageContext() }
        
        if let context = UIGraphicsGetCurrentContext() {
                view.layer.render(in: context)
                let image = UIGraphicsGetImageFromCurrentImageContext()
                return image
            }
        return nil
    }
}
