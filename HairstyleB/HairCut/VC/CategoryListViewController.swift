import UIKit
import SnapKit
import JXSegmentedView

// 自定义瀑布流布局协议
protocol WaterfallLayoutDelegate: AnyObject {
    func collectionView(_ collectionView: UICollectionView, layout: WaterfallLayout, heightForItemAt indexPath: IndexPath, with width: CGFloat) -> CGFloat
}

// MARK: - 瀑布流布局
class WaterfallLayout: UICollectionViewLayout {
    weak var delegate: WaterfallLayoutDelegate?
    var columnCount: Int = 2
    var columnSpacing: CGFloat = 6
    var rowSpacing: CGFloat = 6
    var sectionInsets: UIEdgeInsets = .zero
    private var cache: [UICollectionViewLayoutAttributes] = []
    private var contentHeight: CGFloat = 0
    private var contentWidth: CGFloat {
        guard let collectionView = collectionView else { return 0 }
        // 返回可用宽度，不减去边距（边距在xOffset中处理）
        return collectionView.bounds.width
    }

    override var collectionViewContentSize: CGSize {
        return CGSize(width: collectionView?.bounds.width ?? 0, height: contentHeight + sectionInsets.bottom)
    }

    override func prepare() {
        guard cache.isEmpty, let collectionView = collectionView else { return }

        // 使用与CategoryViewController相同的计算方式
        // width = (屏幕宽度 - 16 - 16 - 5) / 2
        let columnWidth = (contentWidth - 16 - 16 - columnSpacing) / CGFloat(columnCount)
        var xOffset: [CGFloat] = (0..<columnCount).map { 16 + CGFloat($0) * (columnWidth + columnSpacing) }  // 左边距16

        // 调试信息
        print("🔍 WaterfallLayout Debug:")
        print("   contentWidth: \(contentWidth)")
        print("   columnWidth: \(columnWidth)")
        print("   columnSpacing: \(columnSpacing)")
        print("   xOffset: \(xOffset)")
        var columnHeights = Array(repeating: CGFloat(0), count: columnCount)  // 顶部不需要额外间距

        for item in 0..<collectionView.numberOfItems(inSection: 0) {
            let indexPath = IndexPath(item: item, section: 0)
            let width = columnWidth
            let itemHeight = delegate?.collectionView(collectionView, layout: self, heightForItemAt: indexPath, with: width) ?? CGFloat(0)
            let height = itemHeight

            // 找到最短列
            var column = 0
            if let minIndex = columnHeights.firstIndex(of: columnHeights.min() ?? CGFloat(0)) {
                column = minIndex
            }

            let frame = CGRect(x: xOffset[column], y: columnHeights[column], width: width, height: height)
            let attributes = UICollectionViewLayoutAttributes(forCellWith: indexPath)
            attributes.frame = frame
            cache.append(attributes)

            columnHeights[column] = columnHeights[column] + height + rowSpacing
            contentHeight = max(contentHeight, columnHeights[column])
        }
    }

    override func layoutAttributesForElements(in rect: CGRect) -> [UICollectionViewLayoutAttributes]? {
        return cache.filter { $0.frame.intersects(rect) }
    }

    override func layoutAttributesForItem(at indexPath: IndexPath) -> UICollectionViewLayoutAttributes? {
        return cache.first { $0.indexPath == indexPath }
    }

    override func invalidateLayout() {
        super.invalidateLayout()
        cache.removeAll()
        contentHeight = 0
    }
}

protocol CategoryListViewControllerDelegate: AnyObject {
    func didSelectModel(_ model: AIHairModel)
    func didSelectNailModel(_ model: AIHairModel) // 新增美甲模型选择方法
}

class CategoryListViewController: UIViewController, JXSegmentedListContainerViewListDelegate {
    
    public var collectionView: UICollectionView!
    public var models: [AIHairModel] = []
    weak var delegate: CategoryListViewControllerDelegate?
    
    // MARK: - 生命周期方法
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCollectionView()
    }
    
    // MARK: - JXSegmentedListContainerViewListDelegate
    func listView() -> UIView {
        return view
    }
    
    // MARK: - 私有方法
    private func setupCollectionView() {
        let layout = WaterfallLayout()
        layout.columnCount = 2
        layout.columnSpacing = 5  // 中间间距5
        layout.rowSpacing = 6
        layout.sectionInsets = .zero  // 不使用sectionInsets，直接在布局计算中处理边距
        layout.delegate = self

        collectionView = UICollectionView(frame: view.bounds, collectionViewLayout: layout)
        collectionView.showsVerticalScrollIndicator = false
        collectionView.backgroundColor = UIColor.white
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(HotHairCutCollectionViewCell.self, forCellWithReuseIdentifier: HotHairCutCollectionViewCell.identifier)
        
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    // 计算文字高度
    private func textHeight(text: String, font: UIFont, width: CGFloat) -> CGFloat {
        let bounding = (text as NSString).boundingRect(with: CGSize(width: width, height: CGFloat.greatestFiniteMagnitude), options: [.usesLineFragmentOrigin, .usesFontLeading], attributes: [.font: font], context: nil)
        return ceil(bounding.height)
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDataSource
extension CategoryListViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return models.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HotHairCutCollectionViewCell.identifier, for: indexPath) as? HotHairCutCollectionViewCell else {
            return UICollectionViewCell()
        }
        
        let model = models[indexPath.row]
        cell.imageString = model.image
        cell.aspectRatio = model.aspectRatio
        if isChinese {
            cell.titleString = model.name
        } else {
            cell.titleString = model.name_en
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let model = models[indexPath.row]

        print("🔍 CategoryListViewController: 点击了模型 - name: \(model.name), type: \(model.type), tag: \(model.tag), ID: \(model.ID)")

        // 检查是否是美甲类型 (tag包含"3"或者type为3)
        if model.tag.contains("3") || model.type == 3 {
            print("💅 CategoryListViewController: 识别为美甲类型，调用 didSelectNailModel")
            delegate?.didSelectNailModel(model)
        } else {
            print("✂️ CategoryListViewController: 识别为发型类型，调用 didSelectModel")
            delegate?.didSelectModel(model)
        }
    }
}

// MARK: - WaterfallLayoutDelegate
extension CategoryListViewController: WaterfallLayoutDelegate {
    func collectionView(_ collectionView: UICollectionView, layout: WaterfallLayout, heightForItemAt indexPath: IndexPath, with width: CGFloat) -> CGFloat {
        let model = models[indexPath.item]
        let imageHeight = width * model.aspectRatio
        let title = isChinese ? model.name : model.name_en
        let labelHeight = textHeight(text: title, font: UIFont.systemFont(ofSize: 14), width: width - 20)
        // 8 is spacing, 4 padding top/bottom
        return imageHeight + 8 + labelHeight + 4
    }
} 
