//
//  AIHairJSON.swift
//  HairCut
//
//  Created by fs0011 on 2025/1/17.
//

import Foundation


func modifyJSON(
    json: [String: Any],
    image: UIImage,
    name: String,
    positivePrompt: String,
    negativePrompt: String,
    expand: Int? = nil,
    dilation: Int? = nil,
    denoise: Int? = nil
) -> [String: Any]? {
    // 1. 修改 client_id 为时间戳 + 随机字母
    let timestamp = Int(Date().timeIntervalSince1970)
    let randomLetters = String((0..<6).map { _ in "abcdefghijklmnopqrstuvwxyz".randomElement()! })
    let clientID = "\(timestamp)\(randomLetters)"
    
    // 2. 修改 138 节点的 image 值
    guard var prompt = json["prompt"] as? [String: Any],
          var node138 = prompt["138"] as? [String: Any],
          var inputs138 = node138["inputs"] as? [String: Any] else {
        return nil
    }
    inputs138["image"] = name
    node138["inputs"] = inputs138
    prompt["138"] = node138
    
//    // 3. 修改 136 节点的 width 和 height 值（根据图片实际尺寸动态计算）
//    guard var node136 = prompt["136"] as? [String: Any],
//          var inputs136 = node136["inputs"] as? [String: Any] else {
//        return nil
//    }
//    let imageSize = image.size // 获取图片的实际尺寸
//    let fixSize = ImageTool().getFixSize(imageSize, maxSize: 800 * 800) // 计算缩放后的尺寸
//    inputs136["width"] = Int(fixSize.width)
//    inputs136["height"] = Int(fixSize.height)
//    node136["inputs"] = inputs136
//    prompt["136"] = node136
    
    // 4. 修改 207 节点的 string 值（正向提示词）
    guard var node207 = prompt["207"] as? [String: Any],
          var inputs207 = node207["inputs"] as? [String: Any] else {
        return nil
    }
    inputs207["string"] = positivePrompt
    node207["inputs"] = inputs207
    prompt["207"] = node207
    
    // 5. 修改 311 节点的 string 值（负面提示词）
    guard var node311 = prompt["311"] as? [String: Any],
          var inputs311 = node311["inputs"] as? [String: Any] else {
        return nil
    }
    inputs311["string"] = negativePrompt
    node311["inputs"] = inputs311
    prompt["311"] = node311
    
    
    // 7. 修改 233 节点的 expand 值（发型区域）
    guard var node233 = prompt["233"] as? [String: Any],
          var inputs233 = node233["inputs"] as? [String: Any] else {
        return nil
    }
    inputs233["expand"] = expand ?? HairCutConfig.shared.expand
    node233["inputs"] = inputs233
    prompt["233"] = node233
    
    // 8. 修改 260 节点的 dilation 值（脸部蒙版）
    guard var node260 = prompt["260"] as? [String: Any],
          var inputs260 = node260["inputs"] as? [String: Any] else {
        return nil
    }
    inputs260["dilation"] = dilation ?? HairCutConfig.shared.dilation
    node260["inputs"] = inputs260
    prompt["260"] = node260
    
    // 9. 修改 156 节点的 denoise 值（绘画强度）
    guard var node156 = prompt["156"] as? [String: Any],
          var inputs156 = node156["inputs"] as? [String: Any] else {
        return nil
    }
    inputs156["denoise"] = denoise ?? HairCutConfig.shared.denoise
    node156["inputs"] = inputs156
    prompt["156"] = node156
    
    
    // 6. 更新 JSON
    var modifiedJSON = json
    modifiedJSON["client_id"] = clientID
    modifiedJSON["prompt"] = prompt
    
    return modifiedJSON
}

// 添加一个新的函数来处理带遮罩的JSON修改
func modifyJSONWithMask(
    json: [String: Any],
    image: UIImage,
    name: String,
    maskName: String,
    positivePrompt: String,
    negativePrompt: String,
    expand: Int? = nil,
    dilation: Int? = nil,
    denoise: Int? = nil
) -> [String: Any]? {
    // 1. 修改 client_id 为时间戳 + 随机字母
    let timestamp = Int(Date().timeIntervalSince1970)
    let randomLetters = String((0..<6).map { _ in "abcdefghijklmnopqrstuvwxyz".randomElement()! })
    let clientID = "\(timestamp)\(randomLetters)"
    
    // 复制原始JSON
    var modifiedJSON = json
    
    // 2. 检查和修改prompt节点
    guard var prompt = json["prompt"] as? [String: Any] else {
        printLog(message: "无法找到prompt节点")
        return nil
    }
    
    // 3. 修改 138 节点的 image 值
    if var node138 = prompt["138"] as? [String: Any],
       var inputs138 = node138["inputs"] as? [String: Any] {
        inputs138["image"] = name
        node138["inputs"] = inputs138
        prompt["138"] = node138
    } else {
        printLog(message: "无法找到138节点")
    }
    
    // 4. 修改 325 节点的 image 值为mask图片名称
    if var node325 = prompt["325"] as? [String: Any],
       var inputs325 = node325["inputs"] as? [String: Any] {
        inputs325["image"] = maskName
        node325["inputs"] = inputs325
        prompt["325"] = node325
        printLog(message: "修改了节点325的image值为mask图片: \(maskName)")
    } else {
        printLog(message: "无法找到325节点")
    }
    
    // 5. 确保mask路径处于活跃状态
    if var node274 = prompt["274"] as? [String: Any] {
        // 这里可以添加任何需要修改的mask节点参数
        prompt["274"] = node274
        printLog(message: "确保节点274(mask处理相关)处于活跃状态")
    }
    
    // 6. 修改 207 节点的 string 值（正向提示词）
    if var node207 = prompt["207"] as? [String: Any],
       var inputs207 = node207["inputs"] as? [String: Any] {
        inputs207["string"] = positivePrompt
        node207["inputs"] = inputs207
        prompt["207"] = node207
    } else {
        printLog(message: "无法找到207节点(正向提示词)")
    }
    
    // 7. 修改 311 节点的 string 值（负面提示词）
    if var node311 = prompt["311"] as? [String: Any],
       var inputs311 = node311["inputs"] as? [String: Any] {
        inputs311["string"] = negativePrompt
        node311["inputs"] = inputs311
        prompt["311"] = node311
    } else {
        printLog(message: "无法找到311节点(负向提示词)")
    }
    
    // 8. 修改 233 节点的 expand 值（发型区域）
    if var node233 = prompt["233"] as? [String: Any],
       var inputs233 = node233["inputs"] as? [String: Any] {
        inputs233["expand"] = expand ?? HairCutConfig.shared.expand
        node233["inputs"] = inputs233
        prompt["233"] = node233
    }
    
    // 9. 修改 260 节点的 dilation 值（脸部蒙版）
    if var node260 = prompt["260"] as? [String: Any],
       var inputs260 = node260["inputs"] as? [String: Any] {
        inputs260["dilation"] = dilation ?? HairCutConfig.shared.dilation
        node260["inputs"] = inputs260
        prompt["260"] = node260
    }
    
    // 10. 修改 156 节点的 denoise 值（绘画强度）
    if var node156 = prompt["156"] as? [String: Any],
       var inputs156 = node156["inputs"] as? [String: Any] {
        inputs156["denoise"] = denoise ?? HairCutConfig.shared.denoise
        node156["inputs"] = inputs156
        prompt["156"] = node156
    }
    
    // 11. 更新 JSON
    modifiedJSON["client_id"] = clientID
    modifiedJSON["prompt"] = prompt
    modifiedJSON["use_mask"] = true
    
    return modifiedJSON
}
