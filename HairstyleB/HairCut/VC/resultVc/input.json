{"client_id": "2323", "prompt": {"136": {"inputs": {"width": 700, "height": 987, "batch_size": 1, "color": 0}, "class_type": "EmptyImage", "_meta": {"title": "空图像"}}, "137": {"inputs": {"invert_mask": true, "blend_mode": "normal", "opacity": 100, "x_percent": 50, "y_percent": 50, "mirror": "None", "scale": 1, "aspect_ratio": 1, "rotate": 0, "transform_method": "lanc<PERSON>s", "anti_aliasing": 0, "background_image": ["136", 0], "layer_image": ["138", 0]}, "class_type": "LayerUtility: ImageBlendAdvance", "_meta": {"title": "LayerUtility: ImageBlendAdvance"}}, "138": {"inputs": {"image": "54-1.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "图片输入"}}, "142": {"inputs": {"ckpt_name": "juggernautXL_v9Rdphoto2Lightning.safetensors", "vae_name": "Baked VAE", "clip_skip": -2, "lora_name": "None", "lora_model_strength": 2.3000000000000003, "lora_clip_strength": 1, "positive": ["297", 0], "negative": ["297", 1], "token_normalization": "length+mean", "weight_interpretation": "comfy++", "empty_latent_width": 192, "empty_latent_height": 192, "batch_size": 1}, "class_type": "Efficient Loader", "_meta": {"title": "Efficient Loader"}}, "153": {"inputs": {"combined": false, "crop_factor": 4, "bbox_fill": false, "drop_size": 8, "contour_fill": false, "mask": ["227", 0]}, "class_type": "MaskToSEGS", "_meta": {"title": "MASK to SEGS"}}, "154": {"inputs": {"model": ["142", 0], "clip": ["142", 5], "vae": ["142", 4], "positive": ["142", 1], "negative": ["142", 2]}, "class_type": "ToBasicPipe", "_meta": {"title": "ToBasicPipe"}}, "156": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 800, "seed": 5, "steps": 6, "cfg": 2, "sampler_name": "dpmpp_sde", "scheduler": "karras", "denoise": 0.8, "feather": 15, "noise_mask": true, "force_inpaint": true, "wildcard": "", "refiner_ratio": 0.2, "cycle": 1, "inpaint_model": true, "noise_mask_feather": 20, "image": ["137", 0], "segs": ["153", 0], "basic_pipe": ["154", 0]}, "class_type": "DetailerForEachPipe", "_meta": {"title": "Detailer (SEGS/pipe)"}}, "157": {"inputs": {"images": ["156", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "207": {"inputs": {"string": "long hair, medium-length hair, wavy hair, layered hair, voluminous hair, middle part, natural black hair, warm brown hair, soft brown highlights, golden highlights, chestnut brown hair, shiny hair, smooth hair texture, healthy hair, silky hair, natural highlights, elegant hairstyle, trendy hairstyle, romantic hairstyle, feminine hair, glamorous hair, soft lighting, hair strands details, flowing hair, natural hair movement, realistic hair texture, highly detailed, ultra-realistic, 8k resolution"}, "class_type": "String Literal", "_meta": {"title": "String Literal"}}, "225": {"inputs": {"mask": ["227", 0]}, "class_type": "MaskToImage", "_meta": {"title": "遮罩转换为图像"}}, "226": {"inputs": {"images": ["225", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "227": {"inputs": {"smoothness": 12, "mask": ["274", 0]}, "class_type": "SmoothMask", "_meta": {"title": "Smooth Mask ♾️Mixlab"}}, "228": {"inputs": {"face": false, "hair": true, "body": false, "clothes": false, "accessories": false, "background": false, "confidence": 0.4, "detail_method": "VITMatte", "detail_erode": 6, "detail_dilate": 15, "black_point": 0.01, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "images": ["137", 0]}, "class_type": "LayerMask: PersonMaskUltra V2", "_meta": {"title": "LayerMask: PersonMaskUltra V2(Advance)"}}, "229": {"inputs": {"images": ["228", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "231": {"inputs": {"masks": ["274", 0]}, "class_type": "Convert Masks to Images", "_meta": {"title": "Convert Masks to Images"}}, "232": {"inputs": {"images": ["231", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "233": {"inputs": {"expand": 20, "tapered_corners": true, "mask": ["228", 1]}, "class_type": "GrowMask", "_meta": {"title": "扩展遮罩"}}, "234": {"inputs": {"masks_a": ["233", 0], "masks_b": ["260", 0]}, "class_type": "Masks Subtract", "_meta": {"title": "Masks Subtract"}}, "235": {"inputs": {"sam_model": "sam_vit_h (2.56GB)", "grounding_dino_model": "GroundingDINO_SwinT_OGC (694MB)", "threshold": 0.3, "detail_method": "VITMatte", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.15, "white_point": 0.99, "process_detail": true, "prompt": "face", "device": "cuda", "max_megapixels": 2, "cache_model": true, "image": ["137", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V2", "_meta": {"title": "LayerMask: SegmentAnythingUltra V2(Advance)"}}, "236": {"inputs": {"method": "VITMatte", "mask_grow": 0, "fix_gap": 0, "fix_threshold": 0.75, "edge_erode": 6, "edte_dilate": 6, "black_point": 0.01, "white_point": 0.99, "device": "cuda", "max_megapixels": 2, "image": ["235", 0], "mask": ["235", 1]}, "class_type": "LayerMask: MaskEdgeUltraDetail V2", "_meta": {"title": "LayerMask: MaskEdgeUltraDetail V2"}}, "237": {"inputs": {"images": ["239", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "238": {"inputs": {"images": ["235", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "239": {"inputs": {"mask": ["260", 0]}, "class_type": "MaskToImage", "_meta": {"title": "遮罩转换为图像"}}, "240": {"inputs": {"mask": ["228", 1]}, "class_type": "MaskToImage", "_meta": {"title": "遮罩转换为图像"}}, "241": {"inputs": {"images": ["240", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "254": {"inputs": {"images": ["137", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "255": {"inputs": {"images": ["137", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "260": {"inputs": {"dilation": 20, "mask": ["236", 1]}, "class_type": "ImpactDilateMask", "_meta": {"title": "Dilate Mask"}}, "266": {"inputs": {"masks": ["233", 0]}, "class_type": "Convert Masks to Images", "_meta": {"title": "Convert Masks to Images"}}, "267": {"inputs": {"images": ["266", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "274": {"inputs": {"threshold": 100, "mask": ["234", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "297": {"inputs": {"styles": "fooocus_styles", "positive": ["207", 0], "negative": ["311", 0], "select_styles": "<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,mre-spontaneous-picture,<PERSON><PERSON><PERSON><PERSON>"}, "class_type": "easy stylesSelector", "_meta": {"title": "Styles Selector"}}, "300": {"inputs": {"text": "face, body, person, human, skin, eyes, nose, mouth, ears, torso, arms, hands, legs, feet, messy background, cluttered background, harsh lighting, blurry, distorted, artifacts, noise, low quality, bad composition, curls, curly hair, tight curls, cartoonish, anime-style, unrealistic, wrinkles, skin texture, pores, blemishes, imperfections, aging, fine lines, creases, folds, skin details,long wavy hair, medium-length hair, natural waves, soft curls, middle part, voluminous hair, layered hair, shiny hair, smooth hair texture, healthy hair, silky hair, natural highlights, romantic hairstyle, casual hairstyle, elegant hair, feminine hair, lazy waves, soft lighting, hair strands details, flowing hair, natural hair movement, realistic hair texture, highly detailed, ultra-realistic, 8k resolution, deformed, bad anatomy, disfigured, poorly drawn face, mutated, extra limb, ugly, poorly drawn hands, missing limb, floating limbs, disconnected limbs, disconnected head, malformed hands, long neck, mutated hands and fingers, bad hands, missing fingers, cropped, worst quality, low quality, mutation, poorly drawn, huge calf, bad hands, fused hand, missing hand, disappearing arms, disappearing thigh, disappearing calf, disappearing legs, missing fingers, fused fingers, abnormal eye proportion, Abnormal hands, abnormal legs, abnormal feet, abnormal fingers, drawing, painting, crayon, sketch, graphite, impressionist, noisy, blurry, soft, deformed, ugly, anime, cartoon, graphic, text, painting, crayon, graphite, abstract, glitch, anime, cartoon, graphic, (blur, blurry, bokeh), text, painting, crayon, graphite, abstract, glitch, deformed, mutated, ugly, disfigured, overthinked. low quality, low resolution, (worst quality, low quality, normal quality, lowres, low details, oversaturated, undersaturated, overexposed, underexposed, grayscale, bw, bad photo, bad photography, bad art:1.4), (watermark, signature, text font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2), (blur, blurry, grainy), morbid, ugly, asymmetrical, mutated malformed, mutilated, poorly lit, bad shadow, draft, cropped, out of frame, cut off, censored, jpeg artifacts, out of focus, glitch, duplicate, (airbrushed, cartoon, anime, semi-realistic, cgi, render, blender, digital art, manga, amateur:1.3), (3D ,3D Game, 3D Game Scene, 3D Character:1.1), (bad hands, bad anatomy, bad body, bad face, bad teeth, bad arms, bad legs, deformities:1.3)", "anything": ["297", 1]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "301": {"inputs": {"text": "cinematic still long hair, medium-length hair, wavy hair, layered hair, voluminous hair, middle part, natural black hair, warm brown hair, soft brown highlights, golden highlights, chestnut brown hair, shiny hair, smooth hair texture, healthy hair, silky hair, natural highlights, elegant hairstyle, trendy hairstyle, romantic hairstyle, feminine hair, glamorous hair, soft lighting, hair strands details, flowing hair, natural hair movement, realistic hair texture, highly detailed, ultra-realistic, 8k resolution . emotional, harmonious, vignette, 4k epic detailed, shot on kodak, 35mm photo, sharp focus, high budget, cinemascope, moody, epic, gorgeous, film grain, grainy, spontaneous picture of , taken by talented amateur. best quality, high resolution. magical moment, natural look. simple but good looking", "anything": ["297", 0]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "311": {"inputs": {"string": "face, body, person, human, skin, eyes, nose, mouth, ears, torso, arms, hands, legs, feet, messy background, cluttered background, harsh lighting, blurry, distorted, artifacts, noise, low quality, bad composition, curls, curly hair, tight curls, cartoonish, anime-style, unrealistic, wrinkles, skin texture, pores, blemishes, imperfections, aging, fine lines, creases, folds, skin details,long wavy hair, medium-length hair, natural waves, soft curls, middle part, voluminous hair, layered hair, shiny hair, smooth hair texture, healthy hair, silky hair, natural highlights, romantic hairstyle, casual hairstyle, elegant hair, feminine hair, lazy waves, soft lighting, hair strands details, flowing hair, natural hair movement, realistic hair texture, highly detailed, ultra-realistic, 8k resolution"}, "class_type": "String Literal", "_meta": {"title": "String Literal"}}, "315": {"inputs": {"image": "example.png", "channel": "alpha", "upload": "image"}, "class_type": "LoadImageMask", "_meta": {"title": "加载图像（作为遮罩）"}}, "316": {"inputs": {"smoothness": 12, "mask": ["315", 0]}, "class_type": "SmoothMask", "_meta": {"title": "Smooth Mask ♾️Mixlab"}}, "317": {"inputs": {"mask": ["316", 0]}, "class_type": "MaskToImage", "_meta": {"title": "遮罩转换为图像"}}, "318": {"inputs": {"images": ["317", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}}}