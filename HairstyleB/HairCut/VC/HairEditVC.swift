//
//  HairEditVC.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import UIKit
import SnapKit
import UMCommon
import SVProgressHUD

class HairEditVC: UIViewController {
    
    // MARK: - UI Components
    private let imageView = UIImageView()
    private let compareButton = UIButton(type: .custom)
    
    private let bottomView = UIView()
    private let cancelButton = UIButton(type: .custom)
    private let confirmButton = UIButton(type: .custom)
    
    private let categoryStackView = UIStackView()
    private let categoryIndicatorView = UIView()
    
    private let hairFunctionCollectionView: UICollectionView
    
    // MARK: - Properties
    var sourceImage: UIImage?

    // 回调闭包，用于传递处理后的图片给上级页面
    var onImageProcessed: ((UIImage) -> Void)?

    private var originalImage: UIImage?
    private var currentImage: UIImage?
    private var hairFunctions: [HairEditFunction] = []
    private var selectedFunctionIndex = 0 // 默认选中原图
    
    // MARK: - Initialization
    init() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        
        hairFunctionCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        
        super.init(nibName: nil, bundle: nil)
        
        setupHairFunctions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
        loadDefaultImage()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: false)
    }
    
    // MARK: - Setup Methods
    private func setupHairFunctions() {
        hairFunctions = HairEditManager.shared.getAllHairEditFunctions()
    }
    
    private func setupUI() {
        view.backgroundColor = UIColor.hex(string: "#F9F9F9")
        
        // Setup image view
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        view.addSubview(imageView)
        
        // Setup compare button
        compareButton.setImage(UIImage(named: "美容对比"), for: .normal)
        compareButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        compareButton.layer.cornerRadius = 25
        view.addSubview(compareButton)
        
        // Setup bottom view
        bottomView.backgroundColor = .white
        bottomView.layer.cornerRadius = 16
        bottomView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.addSubview(bottomView)
        
        setupBottomButtons()
        setupCategoryView()
        setupCollectionView()
    }
    
    private func setupBottomButtons() {
        // Cancel button
        cancelButton.setImage(UIImage(named: "美容返回"), for: .normal)
        bottomView.addSubview(cancelButton)

        // Confirm button
        confirmButton.setImage(UIImage(named: "美容确认"), for: .normal)
        bottomView.addSubview(confirmButton)
    }
    
    private func setupCategoryView() {
        // 发型编辑只有一个分类
        categoryStackView.axis = .horizontal
        categoryStackView.distribution = .fillEqually
        categoryStackView.spacing = 0
        bottomView.addSubview(categoryStackView)
        
        // Category indicator
        categoryIndicatorView.backgroundColor = UIColor.hex(string: "#FFEC53")
        categoryIndicatorView.layer.cornerRadius = 4
        bottomView.addSubview(categoryIndicatorView)
        
        // Create category button
        let button = UIButton(type: .custom)
        button.setTitle("发型编辑".localized, for: .normal)
        button.setTitleColor(UIColor.hex(string: "#333333"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.isSelected = true
        
        categoryStackView.addArrangedSubview(button)
    }
    
    private func setupCollectionView() {
        hairFunctionCollectionView.backgroundColor = .clear
        hairFunctionCollectionView.showsHorizontalScrollIndicator = false
        hairFunctionCollectionView.delegate = self
        hairFunctionCollectionView.dataSource = self
        
        // Register cell
        hairFunctionCollectionView.register(HairFunctionCell.self, forCellWithReuseIdentifier: "HairFunctionCell")
        
        bottomView.addSubview(hairFunctionCollectionView)
    }
    
    private func setupConstraints() {
        // Image view constraints
        imageView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomView.snp.top)
        }
        
        // Compare button constraints
        compareButton.snp.makeConstraints { make in
            make.bottom.equalTo(bottomView.snp.top).offset(-20)
            make.right.equalToSuperview().offset(-20)
            make.width.height.equalTo(50)
        }
        
        // Bottom view constraints
        bottomView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(200)
        }
        
        // Cancel and confirm button constraints
        cancelButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.width.height.equalTo(30)
        }
        
        confirmButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20)
            make.bottom.equalToSuperview().offset(-20)
            make.width.height.equalTo(30)
        }
        
        // Category stack view constraints
        categoryStackView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
            make.height.equalTo(30)
            make.left.equalTo(cancelButton.snp.right).offset(20)
            make.right.equalTo(confirmButton.snp.left).offset(-20)
        }
        
        // Category indicator constraints
        if let firstButton = categoryStackView.arrangedSubviews.first as? UIButton {
            categoryIndicatorView.snp.makeConstraints { make in
                make.bottom.equalTo(firstButton.titleLabel!.snp.bottom)
                make.height.equalTo(2)
                make.width.equalTo(firstButton.titleLabel!.snp.width)
                make.centerX.equalTo(firstButton.titleLabel!.snp.centerX)
            }
        }
        
        // Collection view constraints
        hairFunctionCollectionView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.top.equalToSuperview().offset(20)
            make.bottom.equalTo(categoryStackView.snp.top).offset(-20)
        }
    }
    
    private func setupActions() {
        cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        confirmButton.addTarget(self, action: #selector(confirmButtonTapped), for: .touchUpInside)
        
        let longPressGesture = UILongPressGestureRecognizer(target: self, action: #selector(compareButtonPressed(_:)))
        compareButton.addGestureRecognizer(longPressGesture)
    }
    
    private func loadDefaultImage() {
        if let sourceImage = sourceImage {
            originalImage = sourceImage
            currentImage = sourceImage
            imageView.image = sourceImage
        }
    }
    
    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        MobClick.event("Portrait_beauty_care", attributes: ["Hairstyle_editor": "点击按钮错"])
        navigationController?.popViewController(animated: true)
    }

    @objc private func confirmButtonTapped() {
        MobClick.event("Portrait_beauty_care", attributes: ["Hairstyle_editor": "点击按钮对"])

        // 确认编辑，传递处理后的图片给上级页面
        if let processedImage = currentImage {
            onImageProcessed?(processedImage)
        }
        navigationController?.popViewController(animated: true)
    }
    
    @objc private func compareButtonPressed(_ gesture: UILongPressGestureRecognizer) {
        switch gesture.state {
        case .began:
            MobClick.event("Portrait_beauty_care", attributes: ["Hairstyle_editor": "对比"])
            // 显示原图
            imageView.image = originalImage
        case .ended, .cancelled:
            // 显示当前处理后的图
            imageView.image = currentImage
        default:
            break
        }
    }
    
    private func applyHairEditEffect(functionId: String) {
        guard let originalImage = originalImage else { return }

        if functionId == "original" {
            // 原图功能，直接显示原图
            MobClick.event("Portrait_beauty_care", attributes: ["Hairstyle_editor": "重置"])
            currentImage = originalImage
            imageView.image = originalImage
        } else {
            // 显示加载指示器
            SVProgressHUD.show(withStatus: "正在处理...".localized)

            // 应用发型编辑效果
            HairEditManager.shared.applyHairEditEffect(functionId, to: originalImage) { [weak self] result in
                DispatchQueue.main.async {
                    SVProgressHUD.dismiss()

                    switch result {
                    case .success(let processedImage):
                        self?.currentImage = processedImage
                        self?.imageView.image = processedImage
                        print("✅ 发型编辑成功: \(functionId)")
                    case .failure(let error):
                        print("❌ 发型编辑失败: \(error.localizedDescription)")
                        // 失败时显示原图
                        self?.currentImage = originalImage
                        self?.imageView.image = originalImage

                        // 检查是否为次数不足错误
                        if let nsError = error as? NSError, nsError.domain == "HairEditUsageLimit" {
                            // 次数不足时不显示错误HUD，订阅页面已经弹出
                            print("💡 次数不足，订阅页面已弹出")
                        } else {
                            // 其他错误才显示错误提示
                            SVProgressHUD.showError(withStatus: "处理失败，请重试".localized)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - UICollectionViewDataSource
extension HairEditVC: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return hairFunctions.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "HairFunctionCell", for: indexPath) as! HairFunctionCell

        let hairFunction = hairFunctions[indexPath.item]
        let isSelected = indexPath.item == selectedFunctionIndex

        cell.configure(with: hairFunction, isSelected: isSelected)

        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension HairEditVC: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 更新选中状态
        selectedFunctionIndex = indexPath.item

        // 刷新collection view
        collectionView.reloadData()

        // 应用发型编辑效果
        let selectedFunction = hairFunctions[indexPath.item]

        // 添加功能选择埋点
        MobClick.event("Portrait_beauty_care", attributes: ["Hairstyle_editor": selectedFunction.name])

        applyHairEditEffect(functionId: selectedFunction.id)

        print("🎨 选择发型功能: \(selectedFunction.name)")
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension HairEditVC: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let hairFunction = hairFunctions[indexPath.item]

        if hairFunction.isOriginal {
            // 原图按钮：46*95
            return CGSize(width: 46, height: 95)
        } else {
            // 其他功能按钮：80*95
            return CGSize(width: 80, height: 95)
        }
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 10
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 10
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
}
