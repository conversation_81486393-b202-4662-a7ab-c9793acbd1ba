//
//  SettingVC.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/7/29.
//

import Foundation
import UIKit
import SnapKit

class SettingVC: UIViewController  {
    
    private let settingView = SettingView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.title = "setting".localized
        self.settingView.delegate = self
        self.view.addSubview(self.settingView)
        self.settingView.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
    }
    
    deinit {
        printLog(message: "deinit")
    }
}

typealias SettingVCAction = SettingVC 
extension SettingVCAction: SettingViewDelgate{
    func changeIconImage(imageString: String) {
        if UIApplication.shared.supportsAlternateIcons {
            UIApplication.shared.setAlternateIconName(imageString) { error in
                if let error = error {
                    printLog(message: "Error changing icon: \(error.localizedDescription)")
                } else {
                    printLog(message: "Icon:\(imageString) changed successfully")
                }
            }
        } else {
            printLog(message: "Alternate icons are not supported")
        }
    }
    
    func aboutUsClickAction() {
        let aboutUsVC = AboutUsVC()
        aboutUsVC.hidesBottomBarWhenPushed = true
        self.navigationController?.pushViewController(aboutUsVC, animated: false)
    }
    
    func termsOfUseClickAction() {
        self.openUrl(urlType: .termsOfUse)
    }
    
    func privacyAgreementClickAction() {
        self.openUrl(urlType: .privacyAgreement)
    }
    
    private func openUrl(urlType: UrlType) {
        var urlString = ""
        switch urlType {
        case .termsOfUse:
            urlString = HttpConst.termOfUseLink
        case .privacyAgreement:
            urlString = HttpConst.privacyAgreementLink
        }
        
        guard let url = URL(string: urlString) else {
            printLog(message: "打开url时为空")
            return
        }
        if UIApplication.shared.canOpenURL(url) {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(url)
            }
        } else {
            printLog(message: "无法打开url:\(url.absoluteString)")
        }
    }
}
