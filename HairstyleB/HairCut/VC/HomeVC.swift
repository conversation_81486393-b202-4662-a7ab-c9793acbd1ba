//
//  HomeVC.swift
//  HairCut
//
//  Created by Bigger on 2024/7/29.
//

import Foundation
import UIKit
import BUAdSDK

class HomeVC: UIViewController  {

    private let homeView = HomeView()
    private let pickImageTool = PickImageTool()
    private var bannerAdView = BUNativeExpressBannerView()
    private var nailCameraDelegate: NailCameraDelegate? // 持有美甲相机代理
    
    override func viewDidLoad() {
        super.viewDidLoad()

        self.initView()
        self.testRequest()
    }
    
    func initView () {
        self.homeView.delegate = self
        self.view.addSubview(self.homeView)

        // 检查是否应该显示banner广告
        if shouldShowBannerAd() {
            // 显示banner广告
            safeConstraints(self.homeView) { make in
                make.top.left.right.width.equalToSuperview()
                make.bottom.equalTo(-AdsCNSetting.bannerHeight)
            }

            // 初始化广告视图
            let size = CGSize(width:self.view.frame.width, height: AdsCNSetting.bannerHeight)
            self.bannerAdView = BUNativeExpressBannerView(slotID: AdsCNSetting.bannerId, rootViewController: self, adSize: size)
            self.view.addSubview(self.bannerAdView)
            safeConstraints(self.bannerAdView) { make in
                make.top.equalTo(self.homeView.snp.bottom)
                make.left.right.equalTo(self.homeView)
                make.height.equalTo(AdsCNSetting.bannerHeight)
            }
            self.bannerAdView.delegate = self
            self.bannerAdView.loadAdData()
        } else {
            // 不显示banner广告，homeView占据全屏
            safeConstraints(self.homeView) { make in
                make.left.top.right.bottom.equalToSuperview()
            }
        }
    }

    /// 增加app启动次数计数（每次app启动只计数一次）
    private func incrementAppLaunchCountIfNeeded() {
        let lastCountedDate = UserDefaults.standard.object(forKey: "lastAppLaunchCountDate") as? Date
        let today = Date()
        let calendar = Calendar.current

        // 检查是否是同一天或者从未计数过
        var shouldCount = false
        if let lastDate = lastCountedDate {
            // 如果不是同一天，则计数
            if !calendar.isDate(lastDate, inSameDayAs: today) {
                shouldCount = true
            }
        } else {
            // 从未计数过，计数
            shouldCount = true
        }

        if shouldCount {
            let currentCount = UserDefaults.standard.integer(forKey: "startWithADTimes")
            let newCount = currentCount + 1
            UserDefaults.standard.set(newCount, forKey: "startWithADTimes")
            UserDefaults.standard.set(today, forKey: "lastAppLaunchCountDate")
            print("📱 App启动次数: \(newCount)")
        }
    }

    /// 判断是否应该显示banner广告
    private func shouldShowBannerAd() -> Bool {
        // 1. 检查用户是否已订阅
        if APPMakeStoreIAPManager.featureVip() {
            print("🚫 用户已订阅，不显示banner广告")
            return false
        }

        // 2. 检查启动次数是否达到第三次
        let launchCount = UserDefaults.standard.integer(forKey: "startWithADTimes")
        if launchCount < 3 {
            print("🚫 启动次数不足3次(\(launchCount))，不显示banner广告")
            return false
        }

        print("✅ 满足条件，显示banner广告 (启动次数: \(launchCount), 未订阅)")
        return true
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.isNavigationBarHidden = true

        // 每次页面出现时重新检查banner显示条件
        if !shouldShowBannerAd() && self.bannerAdView.superview != nil {
            // 如果不应该显示banner且广告视图存在，则移除广告视图
            self.closeBannerView()
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 增加app启动次数计数（只在app启动时计数一次）
        incrementAppLaunchCountIfNeeded()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.navigationController?.isNavigationBarHidden = false
    }
    
    deinit {
        printLog(message: "deinit")
    }
    
    private func testRequest() {
        let isFirstUseInternet = UserDefaultsTool.readTemporaryString(key: UserDefaultsConst.firstInternetRequest) == nil
        if isFirstUseInternet {
            var config = HttpConfig()
            config.url = "www.baidu.com"
            config.method = .post
            _ = HttpTool.shared.request(config: config)
            UserDefaultsTool.saveTemporaryString(key: UserDefaultsConst.firstInternetRequest, value: "true")
        }
    }
}

// MARK: - 点击事件
typealias HomeVCAction = HomeVC
extension HomeVCAction: HomeViewDelegate {
    func aiStylistClickAction() {
        // 显示AI造型师引导弹窗
        showAIStylistGuidePopup()
        MobClick.event("HOME", attributes: ["source": "AI造型师"])
    }

    /// 显示AI造型师引导弹窗
    private func showAIStylistGuidePopup() {
        let guidePopup = AIStylistGuidePopupView()

        // 换发型按钮回调
        guidePopup.onHairStyleButtonTapped = { [weak self] in
            let aiHairCutVC = AIHairCutVC()
            aiHairCutVC.hidesBottomBarWhenPushed = true
            self?.navigationController?.pushViewController(aiHairCutVC, animated: false)
            MobClick.event("HOME", attributes: ["source": "AI造型师_换发型"])
        }

        // 美甲按钮回调 - 直接进入拍照页面
        guidePopup.onNailArtButtonTapped = { [weak self] in
            self?.presentNailCameraViewController()
            MobClick.event("HOME", attributes: ["source": "AI造型师_美甲"])
        }

        // 显示弹窗
        guidePopup.show()
    }

    
    func portraitBeautyClickAction() {
        MobClick.event("HOME", attributes: ["source": "人像美容"])
        self.showPortraitBeautyActionSheet()
    }
    
    func faceTextClickAction() {
        MobClick.event("HOME", attributes: ["source": "脸型测试"])
        self.showActionSheet()
    }
    
    func showActionSheet() {
        
        let actionSheet = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        if UIDevice.current.userInterfaceIdiom == .pad {
            actionSheet.popoverPresentationController?.sourceView = self.homeView
            actionSheet.popoverPresentationController?.sourceRect = CGRectMake(self.homeView.frame.size .width / 2, self.homeView.frame.size.height / 2, 1, 1)
            actionSheet.popoverPresentationController?.permittedArrowDirections = []
        }
        
        actionSheet.addAction(UIAlertAction(title: "camera".localized, style: .default, handler: {[weak self] action in
            self?.pickImageTool.getImageWithCamera().then {[weak self] result in
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_camera_permissions".localized)
                    return
                }
                
                guard let image = result.images.first else {
                    printLog(message: "选择相机，找不到图片")
                    return
                }
                
                let faceShapeTestVC = FaceShapeTestVC()
                faceShapeTestVC.sourceImage = image
                faceShapeTestVC.hidesBottomBarWhenPushed = true
                self?.navigationController?.pushViewController(faceShapeTestVC, animated: false)
            }
        }))
        actionSheet.addAction(UIAlertAction(title: "photo_library".localized, style: .default, handler: { [weak self] action in
            self?.pickImageTool.getImageWithAlbum(maxNum: 1).then {[weak self] result in
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_alum_permissions".localized)
                    return
                }
                
                guard let image = result.images.first else {
                    printLog(message: "选择相册，找不到图片")
                    return
                }
                
                let faceShapeTestVC = FaceShapeTestVC()
                faceShapeTestVC.sourceImage = image
                faceShapeTestVC.hidesBottomBarWhenPushed = true
                self?.navigationController?.pushViewController(faceShapeTestVC, animated: false)
            }
        }))
        
        actionSheet.addAction(UIAlertAction(title: "cancel".localized, style: .cancel, handler: nil))
        present(actionSheet, animated: true, completion: nil)
    }

    func showPortraitBeautyActionSheet() {
        let actionSheet = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        if UIDevice.current.userInterfaceIdiom == .pad {
            actionSheet.popoverPresentationController?.sourceView = self.homeView
            actionSheet.popoverPresentationController?.sourceRect = CGRectMake(self.homeView.frame.size.width / 2, self.homeView.frame.size.height / 2, 1, 1)
            actionSheet.popoverPresentationController?.permittedArrowDirections = []
        }

        actionSheet.addAction(UIAlertAction(title: "camera".localized, style: .default, handler: {[weak self] action in
            self?.pickImageTool.getImageWithCamera().then {[weak self] result in
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_camera_permissions".localized)
                    return
                }

                guard let image = result.images.first else {
                    printLog(message: "选择相机，找不到图片")
                    return
                }

                let portraitBeautyVC = PortraitBeautyVC()
                portraitBeautyVC.sourceImage = image
                portraitBeautyVC.hidesBottomBarWhenPushed = true
                self?.navigationController?.pushViewController(portraitBeautyVC, animated: false)
            }
        }))

        actionSheet.addAction(UIAlertAction(title: "photo_library".localized, style: .default, handler: { [weak self] action in
            self?.pickImageTool.getImageWithAlbum(maxNum: 1).then {[weak self] result in
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_alum_permissions".localized)
                    return
                }

                guard let image = result.images.first else {
                    printLog(message: "选择相册，找不到图片")
                    return
                }

                let portraitBeautyVC = PortraitBeautyVC()
                portraitBeautyVC.sourceImage = image
                portraitBeautyVC.hidesBottomBarWhenPushed = true
                self?.navigationController?.pushViewController(portraitBeautyVC, animated: false)
            }
        }))

        actionSheet.addAction(UIAlertAction(title: "cancel".localized, style: .cancel, handler: nil))
        present(actionSheet, animated: true, completion: nil)
    }
    
    
    func popularHairCutDataAction(type: AIHairModel) {
        print("🔍 HomeVC: 点击模型 - name: \(type.name), type: \(type.type), ID: \(type.ID)")

        let aiHairCutVC = AIHairCutVC()
        MobClick.event("HOME", attributes: ["source": "热门模板"])
        aiHairCutVC.hidesBottomBarWhenPushed = true
        aiHairCutVC.hairModel = type

        // 根据type决定显示哪个页面：
        // type=1: 发型，显示发型页面 (isShowPopularPage = true)
        // type=2: 造型，显示造型页面 (isShowPopularPage = false)
        // type=3: 美甲，不进入AI换发页面
        aiHairCutVC.isShowPopularPage = (type.type == 1)

        print("🔍 HomeVC: 设置 isShowPopularPage = \(type.type == 1) (type=\(type.type))")

        self.navigationController?.pushViewController(aiHairCutVC, animated: false)
    }

    func bannerClickAction(index: Int) {
        // 根据banner索引处理不同的点击事件
        switch index {
        case 0:
            // 第一个banner (home_banner) - 跳转到AI造型师
            MobClick.event("HOME", attributes: ["source": "banner_ai_stylist"])
            aiStylistClickAction()
        case 1:
            // 第二个banner (AI美甲banner) - 弹出拍照VC
            MobClick.event("HOME", attributes: ["source": "banner_ai_nail"])
            presentNailCameraViewController()
        default:
            break
        }
    }

    private func presentNailCameraViewController() {
        let nailCameraVC = NailCameraViewController()
        nailCameraVC.delegate = self
        nailCameraVC.modalPresentationStyle = .fullScreen
        present(nailCameraVC, animated: true, completion: nil)
    }

    func nailTypeClickAction(nailId: String) {
        // 美甲类型点击事件，直接弹出相机页面，并传递nailId
        print("🎯 美甲类型点击，nailId: \(nailId)")
        MobClick.event("HOME", attributes: ["source": "nail_type_collection", "nail_id": nailId])
        let nailCameraVC = NailCameraViewController()

        // 创建并持有delegate
        nailCameraDelegate = NailCameraDelegate(parentVC: self, preselectedNailId: nailId)
        nailCameraVC.delegate = nailCameraDelegate
        nailCameraVC.modalPresentationStyle = .fullScreen
        present(nailCameraVC, animated: true, completion: nil)
    }
}

// MARK: - NailCameraViewControllerDelegate
extension HomeVC: NailCameraViewControllerDelegate {
    func nailCameraDidSelectImage(_ image: UIImage) {
        // 用户选择了图片，跳转到美甲处理页面
        print("用户选择了图片，准备跳转到美甲页面")

        // 创建美甲处理页面并push
        let nailProcessVC = NailProcessViewController(userImage: image)
        nailProcessVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(nailProcessVC, animated: true)
    }

    func nailCameraDidCancel() {
        // 用户取消了拍照
        print("用户取消了拍照")
    }
}

// MARK: - 带预选美甲ID的相机代理
class NailCameraDelegate: NSObject, NailCameraViewControllerDelegate {
    weak var parentVC: HomeVC?
    let preselectedNailId: String

    init(parentVC: HomeVC, preselectedNailId: String) {
        self.parentVC = parentVC
        self.preselectedNailId = preselectedNailId
    }

    func nailCameraDidSelectImage(_ image: UIImage) {
        // 用户选择了图片，跳转到美甲处理页面并传入预选的美甲ID
        print("📸 NailCameraDelegate: 用户选择了图片，准备跳转到美甲页面，预选美甲ID: \(preselectedNailId)")

        guard let parentVC = parentVC else {
            print("❌ NailCameraDelegate: parentVC 为 nil")
            return
        }

        guard let navController = parentVC.navigationController else {
            print("❌ NailCameraDelegate: navigationController 为 nil")
            return
        }

        // 创建美甲处理页面并传入预选ID
        let nailProcessVC = NailProcessViewController(userImage: image, preselectedNailId: preselectedNailId)
        nailProcessVC.hidesBottomBarWhenPushed = true

        print("✅ NailCameraDelegate: 准备push到美甲处理页面")
        navController.pushViewController(nailProcessVC, animated: true)

        // 清理delegate引用
//        parentVC.nailCameraDelegate = nil
    }

    func nailCameraDidCancel() {
        // 用户取消了拍照
        print("用户取消了拍照")

        // 清理delegate引用
//        parentVC?.nailCameraDelegate = nil
    }
}

typealias HomeVCAD = HomeVC
extension HomeVCAD: BUNativeExpressBannerViewDelegate {
    func nativeExpressBannerAdViewDidLoad(_ bannerAdView: BUNativeExpressBannerView) {
        printLog(message:"Banner广告加载成功")
    }
    
    func nativeExpressBannerAdViewRenderFail(_ bannerAdView: BUNativeExpressBannerView, error: (any Error)?) {
        printLog(message:"Banner广告渲染失败，\(String(describing: error))")
        self.closeBannerView()
    }
    
    func nativeExpressBannerAdView(_ bannerAdView: BUNativeExpressBannerView, didLoadFailWithError error: (any Error)?) {
        printLog(message:"Banner广告加载失败，\(String(describing: error))")
        self.closeBannerView()
    }
    
    func nativeExpressBannerAdView(_ bannerAdView: BUNativeExpressBannerView, dislikeWithReason filterwords: [BUDislikeWords]?) {
        printLog(message:"Banner广告关闭")
        self.closeBannerView()
    }
    
    func closeBannerView () {
        DispatchQueue.main.async {
            self.bannerAdView.removeFromSuperview()
            safeRemakeConstraints(self.homeView) { make in
                make.left.top.width.height.equalToSuperview()
            }
        }
    }
}
