//
//  WelcomeVC.swift
//  HairCut
//
//  Created by Bigger on 2024/7/29.
//

import Foundation
import UIKit

class WelcomeVC: UIViewController, WelcomeViewDelegate, MKStoreKitDelegate {
    
    let welcomeView = WelcomeView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.view.addSubview(self.welcomeView)
        self.welcomeView.delegate = self
        self.welcomeView.snp.makeConstraints { make in
            make.left.right.width.height.equalToSuperview()
        }
        
        // 设置为IAP代理
        APPMakeStoreIAPManager.sharedManager.delegate = self
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.isNavigationBarHidden = true
    }
    
    deinit {
        printLog(message: "deint")
    }
    
    // MARK: - WelcomeViewDelegate
    
    func paymentGestureClick() {
        printLog(message: "WelcomeVC-paymentGestureClick")
        // SubscribeView现在会自己处理点击动作
    }
    
    func termOfUseClcikAction() {
        printLog(message: "WelcomeVC-termOfUseClcikAction")
        // 可以在这里打开使用条款网页
        if let url = URL(string: HttpConst.termOfUseLink) {
            UIApplication.shared.open(url)
        }
    }
    
    func privacyAgreementClickAction() {
        printLog(message: "WelcomeVC-privacyAgreementClickAction")
        // 可以在这里打开隐私政策网页
        if let url = URL(string: HttpConst.privacyAgreementLink) {
            UIApplication.shared.open(url)
        }
    }
    
    func rePurchaseClickAction() {
        printLog(message: "WelcomeVC-rePurchaseClickAction")
        // SubscribeView现在会自己处理恢复购买
    }
    
    func homeVCAction() {
        // 标记首次使用并进入首页，现在SubscribeView也会直接调用这个逻辑
        UserDefaultsTool.saveTemporaryString(key: UserDefaultsConst.firstUse, value: "true")
        AppLoad.resetTabbarVC()
    }
    
    // MARK: - MKStoreKitDelegate
    
    func productCPurchased(_ productIdentifier: String) {
        // 支付成功，转到主页
        DispatchQueue.main.async {
            self.homeVCAction()
        }
    }
    
    func failed() {
        // 支付失败，不做处理，用户可以重试
    }
    
    func buySuccessBack() {
        // 处理购买成功回调
        DispatchQueue.main.async {
            self.homeVCAction()
        }
    }
    
    func buycancelBack() {
        // 处理购买取消回调
    }
}
