//
//  AIHairCutVC.swift
//  HairCut
//
//  Created by Bigger on 2024/8/1.
//

import Foundation
import UIKit
import SnapKit
import SwiftyJSON
import Promises
import UMCommon
import Starscream
import AVFoundation
import Photos
import SDWebImage

class AIHairCutVC: UIViewController {
    private var aiHairCutView: AIHairCutView?
    private var loadingView: UIView? = nil
    private var activityIndicator: UIActivityIndicatorView? = nil
    private var navigationRightButton: UIButton? = nil

    // 次数显示相关
    private var usageCountLabel: UILabel!
    private var addUsageButton: UIButton!

    private let pickimageTool = PickImageTool()

    private var taskId: String? = nil
    private var missionId: String? = nil
    public var isShowPopularPage = true  // 默认显示发型模版页面
    public var hairModel: AIHairModel? = nil
    //判断意外断开socket，区分结束断开以及主动断开，以及逻辑上的断开
    private var isSocketConnecting = false
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setLeftNavigationBar()
        self.aiHairCutView = AIHairCutView(isShowPopularPage: self.isShowPopularPage)
        self.aiHairCutView?.delegate = self
        self.aiHairCutView?.isShowHairPage = self.isShowPopularPage  // 设置为true以显示发型模版页面
        self.aiHairCutView?.hairCutChooseView.isShowPopularPage = self.isShowPopularPage  // 设置为true以选中发型模版标签
        self.aiHairCutView?.hairModel = self.hairModel
        self.view.addSubview(self.aiHairCutView ?? AIHairCutView(isShowPopularPage: self.isShowPopularPage))
        self.aiHairCutView?.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
        self.setRightNavigationBarButton(buttonString: "change_picture".localized)
        self.navigationRightButton?.isHidden = true

        // 设置次数显示UI
        setupUsageCountUI()
        updateUsageCountDisplay()

        NotificationCenter.default.addObserver(self, selector: #selector(didEnterBackground), name: UIApplication.didEnterBackgroundNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(willEnterForeground), name: UIApplication.willEnterForegroundNotification, object: nil)

        // 监听次数和订阅状态更新通知
        NotificationCenter.default.addObserver(self, selector: #selector(handleUsageCountUpdate), name: .usageCountDidUpdate, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleSubscriptionStatusUpdate), name: .subscriptionStatusDidUpdate, object: nil)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.navigationBar.isTranslucent = false
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.navigationController?.navigationBar.isTranslucent = true
        self.activeDisConnectSocket()
    }
    
    @objc func didEnterBackground() {
        printLog(message: "did enter background")
    }
    
    @objc func willEnterForeground() {
        let isAvailableInterNet = HttpTool.shared.isAvailable
        if isAvailableInterNet == false && self.isSocketConnecting == true {
            self.activeDisConnectSocket()
            AppLoad.showActionAlert(message: "change_hairstyle_fail".localized)
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        printLog(message: "deinit")
    }
    
    // Add a method to open the VIP controller with hairstyle information
    private func openVipViewController() {
        let vipVC = VIPViewController()
        vipVC.from = "AI换发"
        if let hairModelName = self.hairModel?.name {
            vipVC.style = hairModelName
        }
        if UIDevice.current.userInterfaceIdiom == .pad {
            vipVC.modalPresentationStyle = .formSheet
        }
        self.present(vipVC, animated: true, completion: nil)
    }
}

typealias AiHairCutViewAction = AIHairCutVC
extension AiHairCutViewAction: AIHairCutViewDelegate {
    
    
    
    func generateResultImageAction(data: AIHairModel, requestjson:[String: Any]) {
        self.hairModel = data
        self.showLoadingView()
        
        // 注释原有的上传流程，使用火山引擎方案
        /*
        // 先上传原始图片
        let config = HttpConfig(method: .post, url: HttpConst.hairCutUploadImage, params: ["img": self.hairModel!.origin_image!])
        HttpTool.shared.uploadImage(config: config).then {[weak self] result -> Promise<Any> in
            guard let self = self else {
                return Promise<Any>(NSError(domain: "SelfError", code: -1, userInfo: nil))
            }
            let json = result.json
            let originalImageName = json["name"].stringValue

            guard !originalImageName.isEmpty else {
                self.hideLoadingView()
                printLog(message: "上传原始图片失败")
                AppLoad.showActionAlert(message: "image_upload_fail".localized)
                return Promise<Any>(NSError(domain: "UploadError", code: -1, userInfo: nil))
            }
        */

        // 使用火山引擎异步图生图方案
        guard let originalImage = self.hairModel?.origin_image else {
            self.hideLoadingView()
            AppLoad.showActionAlert(message: "image_upload_fail".localized)
            return
        }

        // 构建提示词
        let prompt = self.hairModel?.prompt ?? ""

        // 调用统一的异步图生图API（自动管理次数）
        VolcanoEngineAPI.processAsyncImageGeneration(
            image: originalImage,
            prompt: prompt
        ) { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingView()

                switch result {
                case .success(let processedImage):
                    // 生成成功，更新次数显示
                    self?.updateUsageCountDisplay()
                    // 跳转到结果页面
                    self?.navigateToResult(with: processedImage)
                case .failure(let error):
                    printLog(message: "AI发型生成失败: \(error.localizedDescription)")

                    // 添加生成失败埋点
                    if let hairModel = self?.hairModel {
                        let maskType = hairModel.mask_image != nil ? "手动mask" : "自动mask"

                        if let nsError = error as? NSError, nsError.domain == "VolcanoUsageLimit" {
                            // 次数不足的特殊埋点
                            MobClick.event("Intelligent_hair_replacement", attributes: ["generation_failed": "\(maskType)&\(hairModel.name)&生成失败 (次数不足)"])
                        } else {
                            // 其他失败原因
                            MobClick.event("Intelligent_hair_replacement", attributes: ["generation_failed": "\(maskType)&\(hairModel.name)&生成失败 (processAsyncImageGeneration)"])
                        }
                    }

                    // 如果是次数不足，不显示错误提示（因为已经弹出了购买弹窗）
                    if let nsError = error as? NSError, nsError.domain == "VolcanoUsageLimit" {
                        // 次数不足时不显示额外的错误提示
                    } else {
                        AppLoad.showActionAlert(message: "image_upload_fail".localized)
                    }
                }
            }
        }
        /*
            // 检查是否有mask图片需要上传
            var maskImageName: String?
            let hasMask = self.hairModel!.mask_image != nil

            // 定义一个函数，根据条件上传mask图片
            let uploadMaskIfNeeded = { () -> Promise<String?> in
                if hasMask, let maskImg = self.hairModel!.mask_image {
                    // 上传mask图片
                    printLog(message: "开始上传mask图片")
                    let maskConfig = HttpConfig(method: .post, url: HttpConst.hairCutUploadImage, params: ["img": maskImg])

                    return HttpTool.shared.uploadImage(config: maskConfig).then { maskResult -> String? in
                        let maskJson = maskResult.json
                        let maskName = maskJson["name"].stringValue
                        printLog(message: "mask图片上传成功，name: \(maskName)")
                        return maskName
                    }
                } else {
                    return Promise<String?>(nil as String?)
                }
            }

            // 获取正向和负向提示词
            let negativePrompt = self.hairModel?.NegativePrompt ?? ""
            let positivePrompt = self.hairModel?.prompt ?? ""

            // 上传mask图片(如果有)，然后继续处理
            return uploadMaskIfNeeded().then { maskName -> Promise<Data> in
                maskImageName = maskName

                // 如果有mask，使用mask专用的JSON模板
                if UserDefaults.standard.bool(forKey: "isAreaSelectionEnabled") {
                    // 使用mask专用的JSON模板URL
                    let maskJsonUrl = "https://faxingceshi.oss-cn-guangzhou.aliyuncs.com/haircut_hair_request_mask.json"

                    return AIHairModel.fectchQuestJson(url: maskJsonUrl)
                } else {
                    // 使用原来的JSON请求数据
                    return Promise<Data>(try! JSONSerialization.data(withJSONObject: requestjson))
                }
            }.then { jsonData -> Promise<Any> in
                // 解析JSON
                guard var json = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any] else {
                    self.hideLoadingView()
                    printLog(message: "JSON解析失败")
                    AppLoad.showActionAlert(message: "image_upload_fail".localized)
                    return Promise<Any>(NSError(domain: "JSONError", code: -1, userInfo: nil))
                }

                // 根据是否有mask选择不同的JSON处理方式
                var modifiedJSON: [String: Any]?
                if hasMask && maskImageName != nil {
                    // 使用带mask的处理方式
                    printLog(message: "使用mask模式处理JSON")
                    modifiedJSON = modifyJSONWithMask(
                        json: json,
                        image: self.hairModel!.origin_image!,
                        name: originalImageName,
                        maskName: maskImageName!,
                        positivePrompt: positivePrompt,
                        negativePrompt: negativePrompt
                    )
                } else {
                    // 使用标准模式处理JSON
                    printLog(message: "使用标准模式处理JSON")
                    modifiedJSON = modifyJSON(
                        json: json,
                        image: self.hairModel!.origin_image!,
                        name: originalImageName,
                        positivePrompt: positivePrompt,
                        negativePrompt: negativePrompt
                    )
                }

                guard let finalJSON = modifiedJSON else {
                    self.hideLoadingView()
                    printLog(message: "修改JSON失败")
                    AppLoad.showActionAlert(message: "image_upload_fail".localized)
                    return Promise<Any>(NSError(domain: "JSONModifyError", code: -1, userInfo: nil))
                }

                // 将修改后的 JSON 转换为 Data并发送请求
                printLog(message: "处理后的JSON: \(finalJSON)")

                // 创建请求配置
                var startConfig = HttpConfig(method: .post, url: HttpConst.hairCutStartQueen)
                startConfig.params = finalJSON

                // 发送生成请求
                return HttpTool.shared.requestWithOuth(config: startConfig).then { result -> Any in
                    print(result.json)
                    if let clientId = finalJSON["client_id"] as? String {
                        let webSocketConfig = WebSocketConifg(url: "\(HttpConst.hairCutStatus)?clientId=\(clientId)")
                        printLog(message: "webSocketConfig---> \(HttpConst.hairCutStatus)?clientId=\(clientId)")
                        WebSocketTool.shared.delegate = self
                        WebSocketTool.shared.connectSocket(config: webSocketConfig)
                        return true
                    } else {
                        self.hideLoadingView()
                        AppLoad.showActionAlert(message: "image_upload_fail".localized)
                        return false
                    }
                }
            }
        }.catch { error in
            self.hideLoadingView()
            printLog(message: "处理失败，error:\(error)")
            AppLoad.showActionAlert(message: "image_upload_fail".localized)
        }
        */
    }

    // MARK: - 跳转到结果页面
    private func navigateToResult(with processedImage: UIImage) {
        // 将处理后的图片转换为Data
        guard let imageData = processedImage.jpegData(compressionQuality: 0.8) else {
            AppLoad.showActionAlert(message: "image_upload_fail".localized)
            return
        }

        // 初始化结果页控制器
        let aiHairResultVC = AIHairResultVC()
        aiHairResultVC.imageData = imageData
        aiHairResultVC.hairModel = self.hairModel

        // 添加生成成功埋点
        if let hairModel = self.hairModel {
            MobClick.event("Intelligent_hair_replacement", attributes: ["successfully_generated": "火山引擎&\(hairModel.name)&生成成功"])
        }

        // 执行导航跳转
        self.navigationController?.pushViewController(aiHairResultVC, animated: false)
    }

    //选择相机/相册获取图片
    func uploadImageAction() {
        
        let actionSheet = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        if UIDevice.current.userInterfaceIdiom == .pad {
            actionSheet.popoverPresentationController?.sourceView = self.aiHairCutView
            actionSheet.popoverPresentationController?.sourceRect = CGRectMake(self.view.frame.size.width / 2, self.view.frame.size.height / 2, 1, 1)
            actionSheet.popoverPresentationController?.permittedArrowDirections = []
        }
        
        actionSheet.addAction(UIAlertAction(title: "camera".localized, style: .default, handler: {[weak self] action in
            self?.pickimageTool.getImageWithCamera().then({ result in
                printLog(message: "获取相机图片：\(result)")
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_camera_permissions".localized)
                    self?.aiHairCutView?.checkGenerateEnable()
                    return
                }
                self?.handleGetImageAction(images: result.images)
            })
        }))
        
        actionSheet.addAction(UIAlertAction(title: "photo_library".localized, style: .default, handler: { [weak self] action in
            self?.pickimageTool.getImageWithAlbum(maxNum: 1).then { result in
                printLog(message: "获取相册图片：\(result)")
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_alum_permissions".localized)
                    self?.aiHairCutView?.checkGenerateEnable()
                    return
                }
                self?.handleGetImageAction(images: result.images)
            }
        }))
        actionSheet.addAction(UIAlertAction(title: "cancel".localized, style: .cancel, handler: nil))
        present(actionSheet, animated: true, completion: nil)
    }
    
    private func handleGetImageAction(images: [UIImage]) {
        guard let image = images.first else {
            self.aiHairCutView?.checkGenerateEnable()
            return
        }
        if self.navigationRightButton?.isHidden == true {
            self.navigationRightButton?.isHidden = false
        }
        self.aiHairCutView?.addImageAction(image: image)
        self.aiHairCutView?.checkGenerateEnable()
    }
    
    //加载等待动画
    private func showLoadingView() {
        let loadingView = UIView(frame: UIScreen.main.bounds)
        self.loadingView = loadingView
        loadingView.backgroundColor = UIColor.hex(string: "#F9F9F9").withAlphaComponent(0.8)
        self.view.addSubview(loadingView)
        let activityIndicator =  UIActivityIndicatorView(frame: CGRect(x: 0, y: 0, width: 20, height: 20))
        self.activityIndicator = activityIndicator
        
        activityIndicator.center = CGPoint(x: self.view.frame.width / 2, y: (self.view.frame.height - (self.navigationController?.navigationBar.frame.height ?? 0) - UIApplication.shared.statusBarFrame.height) / 2)
        loadingView.addSubview(activityIndicator)
        activityIndicator.startAnimating()
        self.navigationRightButton?.isEnabled = false
        self.navigationRightButton?.backgroundColor = UIColor.hex(string: "#FFEC53").alpha(0.5)
        let tips = UILabel(frame: CGRect(x: 20, y: self.activityIndicator!.frame.size.height + self.activityIndicator!.frame.origin.y + 10, width: self.loadingView!.frame.size.width - 40, height: 50))
        tips.text = "loading_tips".localized
        tips.textColor = UIColor.hex(string: "#333333")
        tips.numberOfLines = 0
        tips.textAlignment = .center
        self.loadingView?.addSubview(tips)
    }
    //取消等待动画
    private func hideLoadingView() {
        self.activityIndicator?.stopAnimating()
        self.loadingView?.removeFromSuperview()
        self.navigationRightButton?.isEnabled = true
        self.navigationRightButton?.backgroundColor = UIColor.hex(string: "#FFEC53")
    }
}

typealias AIHairCutVCRightNavigationBar = AIHairCutVC
extension AIHairCutVCRightNavigationBar {
    private func setRightNavigationBarButton(
        buttonString: String,
        buttonBackgroundColor: UIColor = UIColor.hex(string: "#FFEC53"),
        buttonTitleColor: UIColor = UIColor.hex(string: "#333333"),
        buttonFont: UIFont = UIFont.systemFont(ofSize: 13),
        buttonRect: CGRect = CGRect(x: 0, y: 0, width: LanguageTool.currentLanguage() == .english || LanguageTool.currentLanguage() == .vietnamese ? 120 : 70, height: 26),
        buttonCornerRadius: CGFloat = 13)
    {
        let button = UIButton(type: .custom)
        button.layer.cornerRadius = buttonCornerRadius
        button.frame = buttonRect
        button.setTitle(buttonString, for: .normal)
        button.setTitleColor(buttonTitleColor, for: .normal)
        button.backgroundColor = buttonBackgroundColor
        button.titleLabel?.font = buttonFont
        button.addTarget(self, action: #selector(buttonClick), for: .touchUpInside)
        self.navigationRightButton = button
        self.navigationItem.rightBarButtonItem = UIBarButtonItem(customView: button)
    }
    
    @objc func buttonClick() {
        MobClick.event("Intelligent_hair_replacement", attributes: ["source": "更换图片"])
        self.uploadImageAction()
    }
}

typealias AIHairCutVCWebSocket = AIHairCutVC
extension AIHairCutVCWebSocket: WebSocketToolProtocol {
    ///  主动断开连接并清空数据
    private func activeDisConnectSocket() {
        self.taskId = nil
        self.missionId = nil
        self.isSocketConnecting = false
        self.hideLoadingView()
        WebSocketTool.shared.disConnect()
    }
    
    func webSocketDidConnect(websocket: WebSocketTool) {
        //        guard let taskId = self.taskId else {
        //            self.activeDisConnectSocket()
        //            return
        //        }
        self.isSocketConnecting = true
        //        websocket.sendData(input: taskId)
        //        websocket.sendPing()
        
        WebSocketTool.shared.initHeartBeat(message: "")
    }
    
    func webSocketDidDisconnect(websocket: WebSocketTool, errorString: String, errorCode: UInt16) {
        //成功结束和异常通讯，服务器都会主动断开
        printLog(message: "服务器主动断开socket连接")
        if self.isSocketConnecting == true {
            self.activeDisConnectSocket()
            AppLoad.showActionAlert(message: "change_hairstyle_fail".localized)
        }
    }
    
    func webSocketViabilityChanged(websocket: WebSocketTool, viabilityValue: Bool) {
        printLog(message: "socket网络状态发生变化：\(viabilityValue)")
        if viabilityValue == false && self.isSocketConnecting == true {
            self.activeDisConnectSocket()
            AppLoad.showActionAlert(message: "change_hairstyle_fail".localized)
        }
    }
    
    func webSocketGetError(websocket: WebSocketTool, error: (any Error)?) {
        self.activeDisConnectSocket()
        self.isSocketConnecting = false
        AppLoad.showActionAlert(message: "change_hairstyle_fail".localized)
    }
    
    func webSocketDidReceiveMessage(websocket: WebSocketTool, text: String) {
        let json = JSON(parseJSON: text)
        
        let type = json["type"].stringValue
        
        
        if type == "execution_success" {
            self.isSocketConnecting = false
            var httpConfig = HttpConfig()
            
            
            guard let prompt_id = json["data"]["prompt_id"].string else
            {
                self.activeDisConnectSocket()
                AppLoad.showActionAlert(message: "rebuilad_fail".localized)
                self.hideLoadingView()
                if let hairModel = self.hairModel {
                    let maskType = hairModel.mask_image != nil ? "手动mask" : "自动mask"
                    MobClick.event("Intelligent_hair_replacement", attributes: ["generation_failed": "\(maskType)&\(hairModel.name)&生成失败 (发送了请求, 但没进入到结果页)"])
                }
                return
            }
            httpConfig.url = HttpConst.hairCutGetResult + "\(prompt_id)"
            httpConfig.method = .get
            
            
            HttpTool.shared.request(config: httpConfig).then{[weak self] result in
                
                
                // 解析 JSON
                if let json = result.json.dictionary,
                   let taskData = json[prompt_id]?.dictionary,
                   let outputs = taskData["outputs"]?.dictionary,
                   let node157 = outputs["157"]?.dictionary,
                   let images = node157["images"]?.array,
                   let firstImage = images.first?.dictionary,
                   let filename = firstImage["filename"]?.string
                {
                    
                    // 构建新的 URL
                    let baseURL = "https://\(HttpConst.superBaseWeb)/view?filename="
                    let type = "&type=temp"
                    let newURL = baseURL + filename + type
                    
                    // 打印新 URL（调试用）
                    printLog(message: "生成的图片 URL: \(newURL)")
                    
                    // 下载图片数据
                    if let imageURL = URL(string: newURL) {
                        // 异步加载图片数据
                        DispatchQueue.global(qos:  .userInitiated).async { [weak self] in
                            // 后台线程执行IO操作
                            let imageData = try? Data(contentsOf: imageURL)
                            
                            // 切回主线程更新UI
                            DispatchQueue.main.async  { [weak self] in
                                guard let self = self, let imageData = imageData else {
                                    // 可在此处添加错误处理
                                    print("图片数据加载失败")
                                    self?.hideLoadingView()
                                    
                                    // 添加生成失败埋点
                                    if let self = self, let hairModel = self.hairModel {
                                        let maskType = hairModel.mask_image != nil ? "手动mask" : "自动mask"
                                        MobClick.event("Intelligent_hair_replacement", attributes: ["generation_failed": "\(maskType)&\(hairModel.name)&生成失败 (图片数据加载失败)"])
                                    }
                                    return
                                }
                                
                                // 添加生成成功埋点
                                if let hairModel = self.hairModel {
                                    let maskType = hairModel.mask_image != nil ? "手动mask" : "自动mask"
                                    MobClick.event("Intelligent_hair_replacement", attributes: ["successfully_generated": "\(maskType)&\(hairModel.name)&生成成功"])
                                }
                                
                                // 初始化结果页控制器""
                                let aiHairResultVC = AIHairResultVC()
                                aiHairResultVC.imageData  = imageData
                                aiHairResultVC.hairModel  = self.hairModel
                                self.hideLoadingView()

                                // 执行导航跳转
                                self.navigationController?.pushViewController(aiHairResultVC, animated: false)
                            }
                        }
                    } else {
                        // URL创建失败的埋点
                        if let self = self, let hairModel = self.hairModel {
                            let maskType = hairModel.mask_image != nil ? "手动mask" : "自动mask"
                            MobClick.event("Intelligent_hair_replacement", attributes: ["generation_failed": "\(maskType)&\(hairModel.name)&生成失败 (URL创建失败)"])
                        }
                        self?.hideLoadingView()
                    }
                } else {
                    // JSON解析失败的埋点
                    if let self = self, let hairModel = self.hairModel {
                        let maskType = hairModel.mask_image != nil ? "手动mask" : "自动mask"
                        MobClick.event("Intelligent_hair_replacement", attributes: ["generation_failed": "\(maskType)&\(hairModel.name)&生成失败 (JSON解析失败)"])
                    }
                    self?.hideLoadingView()
                    AppLoad.showActionAlert(message: "change_hairstyle_fail".localized)
                }
            }.catch { error in
                self.hideLoadingView()
                printLog(message: "获取图片结果失败,error:\(error)")
                // 添加生成失败埋点
                if let hairModel = self.hairModel {
                    let maskType = hairModel.mask_image != nil ? "手动mask" : "自动mask"
                    MobClick.event("Intelligent_hair_replacement", attributes: ["generation_failed": "\(maskType)&\(hairModel.name)&生成失败 (获取图片结果失败)"])
                }
                AppLoad.showActionAlert(message: "change_hairstyle_fail".localized)
            }
            
        } else if type == "execution_error" {
            self.hideLoadingView()
            self.activeDisConnectSocket()
            // 添加生成失败埋点
            if let hairModel = self.hairModel {
                let maskType = hairModel.mask_image != nil ? "手动mask" : "自动mask"
                MobClick.event("Intelligent_hair_replacement", attributes: ["generation_failed": "\(maskType)&\(hairModel.name)&生成失败 (execution_error)"])
            }
            AppLoad.showActionAlert(message: "change_hairstyle_fail".localized)
            //            printLog(message: "接口错误,message:\(message),msg:\(msg)")
        }
    }
}

// MARK: - 次数显示相关
extension AIHairCutVC {

    /// 设置次数显示UI
    private func setupUsageCountUI() {
        // 创建次数显示标签
        usageCountLabel = UILabel()
        usageCountLabel.font = UIFont.systemFont(ofSize: 14)
        usageCountLabel.textColor = UIColor(valueRGB: 0x999999)
        usageCountLabel.text = local("剩余次数: ") + "0"

        // 创建添加次数按钮
        addUsageButton = UIButton(type: .custom)
        addUsageButton.setImage(UIImage(named: "添加次数按钮"), for: .normal)
        addUsageButton.addTarget(self, action: #selector(addUsageButtonTapped), for: .touchUpInside)

        // 添加到视图
        view.addSubview(usageCountLabel)
        view.addSubview(addUsageButton)

        // 设置约束 - 保持在左上角
        usageCountLabel.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(8)
        }

        addUsageButton.snp.makeConstraints { make in
            make.left.equalTo(usageCountLabel.snp.right).offset(8)
            make.centerY.equalTo(usageCountLabel)
            make.width.height.equalTo(24)
        }
    }

    /// 更新次数显示
    private func updateUsageCountDisplay() {
        let remaining = VolcanoUsageManager.shared.getRemainingCount()
        usageCountLabel.text = local("剩余次数: ") + "\(remaining)"
    }

    /// 添加次数按钮点击
    @objc private func addUsageButtonTapped() {
        VolcanoEngineAPI.showUsagePurchasePopup {
            // 次数更新通过通知自动处理，无需手动调用
        }
    }

    /// 处理次数更新通知
    @objc private func handleUsageCountUpdate() {
        DispatchQueue.main.async {
            self.updateUsageCountDisplay()
        }
    }

    /// 处理订阅状态更新通知
    @objc private func handleSubscriptionStatusUpdate() {
        DispatchQueue.main.async {
            self.updateUsageCountDisplay()
        }
    }
}

