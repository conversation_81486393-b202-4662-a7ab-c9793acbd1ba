//
//  HairStyleVC.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/11/14.
//

import Foundation
import UIKit
import SwiftyJSON

class HairStyleVC: UIViewController  {
    private let hairStyleView = HairStyleMainListView()
    private let pickImageTool = PickImageTool()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.initView()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.setNavigationBarHidden(true, animated: animated)
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.navigationController?.setNavigationBarHidden(false, animated: animated)
    }
    
    private func initView() {
        self.view.addSubview(hairStyleView);
        self.hairStyleView.delegate = self
        self.hairStyleView.imageArr = self.buildHairData() ?? [HaitStyleHairData]()
        self.hairStyleView.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
        self.hairStyleView.refreshData()
    }
    
    private func buildHairData() -> [HaitStyleHairData]? {
        guard let fileUrl = Bundle.main.url(forResource: "FaceTestResultImageMatch", withExtension: "json") else {
            return nil
        }
        var result = [HaitStyleHairData]()
        do {
            let data = try Data(contentsOf: fileUrl)
            let jsonData = try JSON(data: data)
            guard let hairStyles = jsonData["HairStyle"].array else {
                return nil
            }
            
            for hairStyleJson in hairStyles {
                if let style = hairStyleJson["style"].string,
                   let small = hairStyleJson["small"].string,
                   let big = hairStyleJson["big"].string {
                    let smallUrl = "\(HttpConst.faceTestResultDomain)\(small)"
                    let bigUrl = "\(HttpConst.faceTestResultDomain)\(big)"
                    let isMale = style == "0"
                    let data = HaitStyleHairData(smallImage: smallUrl, bigImage: bigUrl, isMale: isMale)
                    result.append(data)
                }
            }
        } catch {
            printLog(message: "getOneImageFormJson解析JSON失败: \(error)")
            return nil
        }
            
        return result
    }
}

typealias HairStyleVCHairStyleMainListViewDelegate = HairStyleVC
extension HairStyleVCHairStyleMainListViewDelegate: HairStyleMainListViewDelegate {
    func selectHair(hairUrlString: String, selectIndex: Int) {
        self.showActionSheet(hairUrlString, selectIndex: selectIndex)
    }
    
    func showActionSheet(_ hairUrlString: String, selectIndex: Int) {
        
        let actionSheet = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        if UIDevice.current.userInterfaceIdiom == .pad {
            actionSheet.popoverPresentationController?.sourceView = self.hairStyleView
            actionSheet.popoverPresentationController?.sourceRect = CGRectMake(self.hairStyleView.frame.size .width / 2, self.hairStyleView.frame.size.height / 2, 1, 1)
            actionSheet.popoverPresentationController?.permittedArrowDirections = []
        }
        
        actionSheet.addAction(UIAlertAction(title: "camera".localized, style: .default, handler: {[weak self] action in
            self?.pickImageTool.getImageWithCamera().then {[weak self] result in
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_camera_permissions".localized)
                    return
                }
                
                guard let image = result.images.first else {
                    printLog(message: "选择相机，找不到图片")
                    return
                }
                
                let hairStyleEditVC = HairStyleEditVC()
                hairStyleEditVC.personImage = image
                hairStyleEditVC.originHairImageUrl = hairUrlString
                hairStyleEditVC.imageArr = self?.hairStyleView.imageArr ?? [HaitStyleHairData]()
                hairStyleEditVC.isMale = self?.hairStyleView.isMale ?? false
                if self?.hairStyleView.isMale == true {
                    hairStyleEditVC.maleSelectedIndex = selectIndex
                } else {
                    hairStyleEditVC.femaleSelectedIndex = selectIndex
                }
                hairStyleEditVC.hidesBottomBarWhenPushed = true
                self?.navigationController?.pushViewController(hairStyleEditVC, animated: false)
            }
        }))
        actionSheet.addAction(UIAlertAction(title: "photo_library".localized, style: .default, handler: { [weak self] action in
            self?.pickImageTool.getImageWithAlbum(maxNum: 1).then {[weak self] result in
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_alum_permissions".localized)
                    return
                }
                
                guard let image = result.images.first else {
                    printLog(message: "选择相册，找不到图片")
                    return
                }
                
                let hairStyleEditVC = HairStyleEditVC()
                hairStyleEditVC.personImage = image
                hairStyleEditVC.originHairImageUrl = hairUrlString
                hairStyleEditVC.imageArr = self?.hairStyleView.imageArr ?? [HaitStyleHairData]()
                hairStyleEditVC.isMale = self?.hairStyleView.isMale ?? false
                if self?.hairStyleView.isMale == true {
                    hairStyleEditVC.maleSelectedIndex = selectIndex
                } else {
                    hairStyleEditVC.femaleSelectedIndex = selectIndex
                }
                hairStyleEditVC.hidesBottomBarWhenPushed = true
                self?.navigationController?.pushViewController(hairStyleEditVC, animated: false)
            }
        }))
        
        actionSheet.addAction(UIAlertAction(title: "cancel".localized, style: .cancel, handler: nil))
        present(actionSheet, animated: true, completion: nil)
    }
}
