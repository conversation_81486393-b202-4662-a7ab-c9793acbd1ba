import UIKit
import SnapKit

protocol CategoryViewControllerDelegate: AnyObject {
    func didSelectModel(_ model: AIHairModel)
}

class CategoryViewController: UIViewController {
    
    private var collectionView: UICollectionView!
    public var models: [AIHairModel] = []
    weak var delegate: CategoryViewControllerDelegate?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCollectionView()
    }
    
    private func setupCollectionView() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical

        // 正确的间距计算：左边距16 + 右边距16 + 中间间距5 = 37
        let width = (UIScreen.main.bounds.width - 16 - 16 - 5) / 2
        let height = 218 * (UIScreen.main.bounds.width / 375.0)  // 按照375宽度的比例缩放
        layout.itemSize = CGSize(width: width, height: height)

        // 设置间距
        layout.minimumLineSpacing = 6  // 行间距
        layout.minimumInteritemSpacing = 5  // 列间距（中间间距）
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)  // 左右边距
        
        collectionView = UICollectionView(frame: view.bounds, collectionViewLayout: layout)
        collectionView.showsVerticalScrollIndicator = false
        collectionView.backgroundColor = UIColor.white
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(HotHairCutCollectionViewCell.self, forCellWithReuseIdentifier: HotHairCutCollectionViewCell.identifier)
        
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
}

extension CategoryViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return models.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HotHairCutCollectionViewCell.identifier, for: indexPath) as? HotHairCutCollectionViewCell else {
            return UICollectionViewCell()
        }
        
        cell.imageString = models[indexPath.row].image
        if isChinese {
            cell.titleString = models[indexPath.row].name
        } else {
            cell.titleString = models[indexPath.row].name_en
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        delegate?.didSelectModel(models[indexPath.row])
    }
}
