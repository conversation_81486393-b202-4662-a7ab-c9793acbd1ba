//
//  STbaseVC.swift
//  SearchT
//
//  Created by fs0011 on 2024/7/3.
//

import Foundation
import UIKit

class BaseVC: UIViewController,MyAdManagerDelegate {
    
    
    var adManager: MyAdManager?
   
    
    
    var topBarView: UIView?
    var titleLabel: UILabel?
    var isPresent: Bool = false
    var backButton : UIButton!
    @objc func injected()  {
          #if DEBUG

          self.viewDidLoad()

          #endif
       }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // 其他初始化代码
    }
    
    func initTopBarView() {
        if topBarView == nil {
            topBarView = UIView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: UIApplication.shared.statusBarFrame.height + 44))
            topBarView?.backgroundColor = .white
            if let topBarView = topBarView {
                view.addSubview(topBarView)
            }
        }
    }
    
    func initWhiteBackButton() {
        let backButton = UIButton(frame: CGRect(x: 0, y: UIApplication.shared.statusBarFrame.height, width: 60, height: 44))
        backButton.setImage(UIImage(named: "白色返回"), for: .normal)
        backButton.addTarget(self, action: #selector(backButtonAction), for: .touchUpInside)
        view.addSubview(backButton)
    }
    
    func initBlackBackButton() {
        backButton = UIButton(frame: CGRect(x: 0, y: UIApplication.shared.statusBarFrame.height, width: 60, height: 44))
        backButton.setImage(UIImage(named: "黑色返回"), for: .normal)
        backButton.addTarget(self, action: #selector(backButtonAction), for: .touchUpInside)
        view.addSubview(backButton)
    }
    
    @objc func backButtonAction() {
        if isPresent {
            dismiss(animated: true, completion: nil)
        } else {
            navigationController?.popViewController(animated: true)
        }
    }
    
    func initTitleLabel() {
        if titleLabel == nil {
            titleLabel = UILabel(frame: CGRect(x: UIScreen.main.bounds.width * 0.5 - 100, y: UIApplication.shared.statusBarFrame.height, width: 200, height: 44))
            titleLabel?.font = UIFont.boldSystemFont(ofSize: 17.0)
            titleLabel?.textColor = .white
            titleLabel?.textAlignment = .center
            if let titleLabel = titleLabel {
                view.addSubview(titleLabel)
            }
        }
    }
    
    // MARK: - Rotation Handling
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return .portrait
    }
    
    override var shouldAutorotate: Bool {
        return false
    }
    
    override var preferredInterfaceOrientationForPresentation: UIInterfaceOrientation {
        return .portrait
    }
    
    func showFullscreenVideoAd() {
            let adTimes = UserDefaults.standard.integer(forKey: "startWithADTimes")
            let adCount = UpdateManager.shared.AD_count
            let isVip = APPMakeStoreIAPManager.featureVip()

            if adTimes >= adCount && !isVip {
                if self.adManager == nil {
                    self.adManager = MyAdManager()
                    self.adManager?.delegate = self
                }
                self.adManager?.showFullscreenVideoAd(with: self)
            }
        }

        func showBannerView(frame: CGRect, size: CGSize) {
            let adTimes = UserDefaults.standard.integer(forKey: "startWithADTimes")
            let adCount = UpdateManager.shared.AD_count
            let isVip = APPMakeStoreIAPManager.featureVip()

            if adTimes >= adCount && !isVip {
                if self.adManager == nil {
                    self.adManager = MyAdManager()
                    self.adManager?.delegate = self
                }
                self.adManager?.showBannerAd(with: self, frame: frame, size: size)
            }
        }

    
}

extension UIViewController {
    func backIndex(animated: Bool = true) {
            var viewControllerToDismiss: UIViewController? = self
            
            // 递归找到最上层的 presentingViewController
            while let presentingVC = viewControllerToDismiss?.presentingViewController {
                viewControllerToDismiss = presentingVC
            }
            
            // 现在 viewControllerToDismiss 是最上层的 presentingViewController
            viewControllerToDismiss?.dismiss(animated: animated, completion: nil)
        }
}
