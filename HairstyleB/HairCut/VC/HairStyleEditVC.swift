//
//  HairStyleEditVC.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/11/15.
//

import Foundation
import UIKit
import SnapKit
import Promises
import SDWebImage

class HairStyleEditVC: UIViewController  {
    public var originHairImageUrl: String? = nil
    public var femaleSelectedIndex: Int? = nil
    public var maleSelectedIndex: Int? = nil
    public var personImage: UIImage? = nil {
        willSet {
            if newValue == nil {
                return
            }
            if self.personImage == nil {
                self.personImageView.image = newValue
            }
            self.refreshView(image: newValue)
        }
    }
    
    private let navHeight = UIScreen.main.bounds.height >= 812 ? 44.0 : 20.0
    private let tarHeight = UIScreen.main.bounds.height >= 812 ? 34.0 : 0.0
    
    private let pickImageTool = PickImageTool()
    public var imageArr = [HaitStyleHairData]()
    private var maleImageArr = [HaitStyleHairData]()
    private var femaleImageArr = [HaitStyleHairData]()
    public var isMale = false
    private var heightConstraint: Constraint?
    private var isAddedChooseHeight = false;
    private var token: String? = nil
    private var faceData: FaceTestData?
    
    private var loadingView: UIView? = nil
    private var activityIndicator: UIActivityIndicatorView? = nil
    
    private var buttonView: HairStyleNavRightActionView? = nil
    
    private let editAreaView = UIView()
    private let personImageView = UIImageView()
    private let maskView = UIView()
    private let hairView = UIImageView()
    private let hairChooseStyleView = HairStyleEditHairChooseView()
    private let hairPreViewImageView = UIImageView()
    private let hairColorChooseView = HairStyleEditColorChooseView()
    private let rightAreaView = HairStyleEditRightAreaView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setRightNavigationBarButton()
        self.navigationController?.navigationBar.isTranslucent = false
        self.initData()
        self.initView()
        self.addHair()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.isNavigationBarHidden = true
    }
        
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        self.navigationController?.isNavigationBarHidden = false
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        let cornerRadius: CGFloat = 16.0
        let path = UIBezierPath(roundedRect: self.hairChooseStyleView.bounds,byRoundingCorners: [.topLeft, .topRight],cornerRadii: CGSize(width: cornerRadius, height: cornerRadius))
        let maskLayer = CAShapeLayer()
        maskLayer.path = path.cgPath
        self.hairChooseStyleView.layer.mask = maskLayer
        
        // 设置边框
        let borderLayer = CAShapeLayer()
        borderLayer.path = path.cgPath
        borderLayer.strokeColor = UIColor(valueRGB: 0x000000, 0.25).cgColor
        borderLayer.fillColor = UIColor.clear.cgColor
        borderLayer.lineWidth = 0.5
        borderLayer.frame = self.hairChooseStyleView.bounds

        // 确保不重复添加边框图层
        if let oldBorderLayer = self.hairChooseStyleView.layer.sublayers?.first(where: { $0.name == "borderLayer" }) {
            oldBorderLayer.removeFromSuperlayer()
        }
        borderLayer.name = "borderLayer"
        self.hairChooseStyleView.layer.addSublayer(borderLayer)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.hairChooseStyleView.scrollToRow()
    }
    
// MARK: - 视图相关
    private func initData() {
        self.maleImageArr.removeAll()
        self.femaleImageArr.removeAll()
        self.imageArr.forEach { data in
            if data.isMale == true {
                self.maleImageArr.append(data)
            } else {
                self.femaleImageArr.append(data)
            }
        }
    }
    
    private func initView() {
        self.view.addSubview(self.editAreaView)
        self.editAreaView.snp.makeConstraints { make in
            make.top.equalTo(self.buttonView?.snp.bottom ?? 0).offset(9)
            make.bottom.equalTo(self.view.safeAreaLayoutGuide.snp.bottom)
            make.left.right.equalToSuperview()
        }
        self.editAreaView.layoutIfNeeded()
        self.editAreaView.addSubview(self.personImageView)
        
        self.view.addSubview(self.maskView)
        self.maskView.backgroundColor = UIColor(valueRGB: 0x000000, 0.3)
        self.maskView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(self.editAreaView.snp.top)
        }
        self.maskView.isHidden = true
        let maskTap = UITapGestureRecognizer()
        maskTap.addTarget(self, action: #selector(maskTapAction))
        self.maskView.addGestureRecognizer(maskTap)
        
        self.rightAreaView.layer.borderColor = UIColor(valueRGB: 0x000000, 0.25).cgColor
        self.rightAreaView.layer.borderWidth = 1
        self.view.addSubview(self.rightAreaView)
        self.rightAreaView.delegate = self
        let plus = UIDevice.current.userInterfaceIdiom == .pad ? 1.5 : 1
        self.rightAreaView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16 * plus)
            make.top.equalTo(self.editAreaView.snp.top).offset(105)
            make.width.equalTo(36 * plus)
            make.height.equalTo(194 * plus)
        }
                    
        //发型颜色选择视图
        self.hairColorChooseView.backgroundColor = UIColor(valueRGB: 0xFFFFFF)
        self.hairColorChooseView.delegate = self
        self.view.addSubview(self.hairColorChooseView)
        self.hairColorChooseView.snp.makeConstraints { make in
            make.width.equalTo(HDColorPickerConst.colorPickerWidth * plus)
            make.height.equalTo(HDColorPickerConst.colorPickerHeight * plus)
            make.top.equalTo(self.editAreaView.snp.top).offset(163 * plus)
            if UIDevice.current.userInterfaceIdiom == .pad {
                make.centerX.equalToSuperview()
            } else {
                make.right.equalTo(self.rightAreaView.snp.left).offset(-26)
            }
        }
        self.hairColorChooseView.isHidden = true
        
        //预览头像视图
        self.hairPreViewImageView.layer.cornerRadius = 8
        self.hairPreViewImageView.backgroundColor = UIColor(valueRGB: 0xF7F7F7)
        self.view.addSubview(self.hairPreViewImageView)
        self.hairPreViewImageView.snp.makeConstraints { make in
            make.width.equalTo(105 * plus)
            make.height.equalTo(118 * plus)
            make.centerX.equalTo(self.hairColorChooseView)
            make.top.equalTo(self.editAreaView.snp.top).offset(30 * plus)
        }
        self.hairPreViewImageView.isHidden = true
        
        //发型选择视图
        self.view.addSubview(self.hairChooseStyleView)
        self.hairChooseStyleView.maleImageArr = self.maleImageArr
        self.hairChooseStyleView.femalImageArr = self.femaleImageArr
        self.hairChooseStyleView.isMale = self.isMale
        self.hairChooseStyleView.delegate = self
        self.hairChooseStyleView.maleSelectedIndex = self.maleSelectedIndex
        self.hairChooseStyleView.femaleSelcetedIndex = self.femaleSelectedIndex
        self.hairChooseStyleView.clipsToBounds = true
        let height = self.getChooseViewHeight()
        self.hairChooseStyleView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            heightConstraint = make.height.equalTo(height).constraint
        }
        self.hairChooseStyleView.refreshData()
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        hairChooseStyleView.addGestureRecognizer(panGesture)
    }
    
    @objc func maskTapAction() {
        self.maskView.isHidden = true
        self.hairColorChooseView.isHidden = true
        self.hairPreViewImageView.isHidden = true
        self.rightAreaView.isColor = false
        self.hairChooseStyleView.isHidden = false
    }
    
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if self.hairPreViewImageView.isHidden == false {
            return
        }
        let translation = gesture.translation(in: gesture.view?.superview)
        if gesture.state == .changed {
            if translation.y > 0 && self.isAddedChooseHeight == false {
                return
            } else if translation.y < 0 && self.isAddedChooseHeight == true {
                return
            }
            self.scrollAnimation()
        }
    }
    
    private func getChooseViewHeight(_ isAdd: Bool = false) -> CGFloat {
        if isAdd {
            return self.view.bounds.height - navHeight - tarHeight - 105.0 - 35
        }
        
        if UIScreen.main.bounds.height < 812 {
            return 210
        }
        
        return 240
    }
    
    private func addHair() {
        self.showLoadingView()
        self.downLoadBigHairImage(self.originHairImageUrl).then { image in
            return self.getToken()
        }.then { token in
            return self.getFaceTextMessage()
        }.then { result in
            return self.buildFaceData(result: result)
        }.then { result in
            self.faceData = result
            self.loadHairAction()
            self.hideLoadingView()
        }.catch { error in
            self.hideLoadingView()
            printLog(message: "发型库添加发型失败:\(error)")
            if let err = error as? HttpError,err.statusMesg == "解析百度接口数据失败" {
                AppLoad.showActionAlert(message: "no_face".localized)
                return
            }
            
            self.showActionAlert(message: "hairstyle_fail".localized)
        }
    }
    
    private func refreshView(image: UIImage?) {
        guard let pImage = image else {
            return
        }
        DispatchQueue.main.async {
            self.view.layoutIfNeeded()
            self.personImageView.frame = CGRectMake(0, 0, self.editAreaView.frame.width, self.editAreaView.frame.width * (pImage.size.height / pImage.size.width))
        }
    }
    
    private func showLoadingView() {
        DispatchQueue.main.async {
            let loadingView = UIView(frame: self.view.bounds)
            self.loadingView = loadingView
            loadingView.backgroundColor = UIColor(valueRGB: 0xF9F9F9).withAlphaComponent(0.8)
            self.view.addSubview(loadingView)
            let activityIndicator =  UIActivityIndicatorView(frame: CGRect(x: 0, y: 0, width: 20, height: 20))
            self.activityIndicator = activityIndicator
            activityIndicator.center = CGPoint(x: self.view.frame.width / 2, y: (self.view.frame.height - (self.navigationController?.navigationBar.frame.height ?? 0) - UIApplication.shared.statusBarFrame.height) / 2)
            loadingView.addSubview(activityIndicator)
            activityIndicator.startAnimating()
            self.buttonView?.isEnable = false
            let tips = UILabel(frame: CGRect(x: 20, y: self.activityIndicator!.frame.size.height + self.activityIndicator!.frame.origin.y + 10, width: self.loadingView!.frame.size.width - 40, height: 50))
            tips.text = "loading_tips".localized
            tips.textColor = UIColor(valueRGB: 0x333333)
            tips.numberOfLines = 0
            tips.textAlignment = .center
            self.loadingView?.addSubview(tips)
        }
    }
    
    private func hideLoadingView() {
        DispatchQueue.main.async {
            self.activityIndicator?.stopAnimating()
            self.loadingView?.removeFromSuperview()
            self.buttonView?.isEnable = true
        }
    }
    
    private func showActionAlert(title: String? = nil, message:String) {
        let actionSheet = UIAlertController(title: title, message: message, preferredStyle: .alert)
        actionSheet.popoverPresentationController?.sourceView = self.view
        let action = UIAlertAction(title: "confirm".localized, style: .default, handler: nil)
        actionSheet.addAction(action)
        let retry = UIAlertAction(title: "retry".localized, style: .default) { [weak self]action in
            self?.addHair()
        }
        actionSheet.addAction(retry)
        self.present(actionSheet, animated: true, completion: nil)
    }
}

// MARK: - 导航栏右上角自定义操作
typealias HairStyleEditVCRightNavigationBar = HairStyleEditVC
extension HairStyleEditVCRightNavigationBar: HairStyleNavRightActionViewDelegate {
    private func setRightNavigationBarButton() {
        let background = UIView()
        background.backgroundColor = UIColor(valueRGB: 0xFFFFFF)
        self.view.addSubview(background)
        background.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
            make.top.equalTo(navHeight)
        }
        let buttonView = HairStyleNavRightActionView()
        buttonView.frame = CGRect(x: 0, y: 0, width: 140, height: 26)
        buttonView.delegate = self
        self.buttonView = buttonView
        background.addSubview(buttonView)
        buttonView.snp.makeConstraints({ make in
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-9)
            make.width.equalTo(140)
            make.height.equalTo(26)
        })
        
        let back = UIButton()
        back.setImage(UIImage(named: "left_arrow"), for: .normal)
        back.addTarget(self, action: #selector(backAction), for: .touchUpInside)
        background.addSubview(back)
        back.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.centerY.equalTo(buttonView)
            make.height.equalTo(44)
            make.width.equalTo(60)
        }
    }
    
    @objc func backAction() {
        self.navigationController?.popViewController(animated: true)
    }
    
    func saveImageAction() {
        MobClick.event("Hairstyle_Library", attributes: ["source": "保存"])
        guard let image = self.getResultImage(superView: self.editAreaView, targerView: self.personImageView)else {
            AppLoad.showActionAlert(message: "save_fail".localized)
            return
        }
        self.pickImageTool.saveImageToAlbum(image) { success in
            AppLoad.showActionAlert(message: success == true ? "save_success".localized : "save_fail".localized)
        }
        
    }
    
    private func getResultImage(superView: UIView, targerView: UIView) -> UIImage? {
        superView.layoutIfNeeded()
        targerView.layoutIfNeeded()
        let region = targerView.frame
        let renderer = UIGraphicsImageRenderer(size: region.size)
        let image = renderer.image { context in
            context.cgContext.saveGState()
            context.cgContext.translateBy(x: -region.origin.x, y: -region.origin.y)
            superView.layer.render(in: context.cgContext)
            context.cgContext.restoreGState()
        }

        return image
    }
    
    func changeImageAction() {
        let actionSheet = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        if UIDevice.current.userInterfaceIdiom == .pad {
            actionSheet.popoverPresentationController?.sourceView = self.view
            actionSheet.popoverPresentationController?.sourceRect = CGRectMake(self.view.frame.size .width / 2, self.view.frame.size.height / 2, 1, 1)
            actionSheet.popoverPresentationController?.permittedArrowDirections = []
        }
        
        actionSheet.addAction(UIAlertAction(title: "camera".localized, style: .default, handler: {[weak self] action in
            self?.pickImageTool.getImageWithCamera().then {[weak self] result in
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_camera_permissions".localized)
                    return
                }
                
                guard let image = result.images.first else {
                    printLog(message: "选择相机，找不到图片")
                    return
                }
                
                self?.changePersonImage(image)
            }
        }))
        actionSheet.addAction(UIAlertAction(title: "photo_library".localized, style: .default, handler: { [weak self] action in
            self?.pickImageTool.getImageWithAlbum(maxNum: 1).then {[weak self] result in
                let status = result.authorizeStatus
                if status != .authorized {
                    AppLoad.showActionAlert(message: "tip_alum_permissions".localized)
                    return
                }
                
                guard let image = result.images.first else {
                    printLog(message: "选择相册，找不到图片")
                    return
                }
                
                self?.changePersonImage(image)
            }
        }))
        
        actionSheet.addAction(UIAlertAction(title: "cancel".localized, style: .cancel, handler: nil))
        present(actionSheet, animated: true, completion: nil)
    }
    
    private func changePersonImage(_ image: UIImage) {
        self.showLoadingView()
        self.personImage = image
        self.getToken().then { token in
            return self.getFaceTextMessage()
        }.then { result in
            return self.buildFaceData(result: result)
        }.then { result in
            self.faceData = result
            self.loadHairAction()
            self.hideLoadingView()
        }.catch { error in
            self.hideLoadingView()
            printLog(message: "发型库添加发型失败:\(error)")
            if let err = error as? HttpError,err.statusMesg == "解析百度接口数据失败" {
                AppLoad.showActionAlert(message: "no_face".localized)
                return
            }
            self.showActionAlert(message: "hairstyle_fail".localized)
        }
    }
}

// MARK: - 百度接口
typealias HairStyleEditVCBaiDu = HairStyleEditVC
extension HairStyleEditVCBaiDu {
    private func downLoadBigHairImage(_ url: String?)-> Promise<UIImage> {
        return Promise<UIImage> {[weak self] (resolve, reject) in
            guard let urlString = url, let imageURL = URL(string: urlString) else {
                var error = HttpError()
                error.statusMesg = "url为空或者转化URL失败"
                reject(error)
                return
            }

            SDWebImageManager.shared.loadImage(with: imageURL,
                                               options: .highPriority,
                                               progress: { (receivedSize, expectedSize, _) in
                let progress = Double(receivedSize) / Double(expectedSize)
                printLog(message: "下载进度: \(progress * 100)%")
            }) { [weak self](image, data, error, cacheType, finished, imageURL) in
                if let image = image, finished {
                    // 图片下载完成或从缓存中读取
                    printLog(message: "图片获取成功: \(image)")
                    self?.hairPreViewImageView.image = image
                    HDEditManager.shared.originImage = image
                    HDEditManager.shared.hairImage = image
                    resolve(image)
                } else {
                    // 下载失败
                    printLog(message: "图片获取失败: \(error?.localizedDescription ?? "未知错误")")
                    guard let sError = error else {
                        var cError = HttpError()
                        cError.statusMesg = "图片下载失败"
                        reject(cError)
                        return
                    }
                    reject(sError)
                }
            }
        }
    }
    
    private func getToken() -> Promise<String> {
        return Promise<String> {[weak self] (resolve, reject) in
            var config = HttpConfig()
            config.url = HttpConst.badiduToken + "?grant_type=client_credentials&client_id=\(BaiduTokenConst.apiKey)&client_secret=\(BaiduTokenConst.secrectKey)"
            config.method = .post
            config.params = ["grant_type": BaiduTokenConst.grantType,"client_id": BaiduTokenConst.apiKey, "client_secret": BaiduTokenConst.secrectKey]
            HttpTool.shared.request(config: config).then { result in
                let token = result.json["access_token"].stringValue
                self?.token = token
                resolve(token)
            }.catch { error in
                reject(error)
            }
        }
    }
    
    private func getFaceTextMessage() -> Promise<[String: Any]> {
        return Promise<[String: Any]> {[weak self] (resolve, reject) in
            var config = HttpConfig()
            //上传限制10mb,像素小于1920x1080
            guard let token = self?.token, let image = self?.personImage else {
                var error = HttpError()
                error.statusMesg = "token未空或者base64不存在"
                reject(error)
                return
            }
            
            var imageBase64: String?
            let fixSize = ImageTool().getFixSize(image.size, maxSize: 800 * 800)
            if image.size.width > fixSize.width || image.size.height > fixSize.height {
                let newImage = image.createImage(fixSize)
                imageBase64 = newImage?.base64EncodedString()
                //重新赋值压缩后的图片
                self?.personImage = newImage
            } else {
                imageBase64 = image.base64EncodedString()
            }
            
            config.url = HttpConst.faceTestV3 + "?access_token=\(token)"
            config.method = .post
            config.params = ["image": imageBase64 ?? "","image_type": "BASE64", "face_field": "age,expression,gender,face_shape,glasses,beauty,landmark150"]
            //目前只做第一个人脸识别，接口支持多个
            HttpTool.shared.request(config: config).then { result in
                guard let result = result.json["result"].dictionaryObject, let faceList = result["face_list"] as? [[String: Any]], let targetResult = faceList.first else {
                    resolve([String: Any]())
                    return
                }
                resolve(targetResult)
            }.catch { error in
                reject(error)
            }
        }
    }
    
    private func buildFaceData(result: [String: Any]) -> Promise<FaceTestData> {
        return Promise<FaceTestData> { (resolve, reject) in
            guard let genderDictionary = result["gender"] as? [String: Any],
                  let gender = genderDictionary["type"] as? String,
                  let age = result["age"] as? Int64,
                  let beauty = result["beauty"] as? Double,
                  let expressionDictionary = result["expression"] as? [String: Any],
                  let expression = expressionDictionary["type"] as? String,
                  let glassessDictionary = result["glasses"] as? [String: Any],
                  let glassess = glassessDictionary["type"] as? String,
                  let faceShapeDictionary = result["face_shape"] as? [String: Any],
                  let faceShape = faceShapeDictionary["type"] as? String,
                  let landmark150 = result["landmark150"] as? [String: Any],
                  let eyeLeftEyeBallCenter = landmark150["eye_left_eyeball_center"] as? [String: Any],
                  let eyeLeftEyeBallX = eyeLeftEyeBallCenter["x"] as? Double,
                  let eyeLeftEyeBallY = eyeLeftEyeBallCenter["y"] as? Double,
                  let eyeRightEyeBallCenter = landmark150["eye_right_eyeball_center"] as? [String: Any],
                  let eyeRightEyeBallX = eyeRightEyeBallCenter["x"] as? Double,
                  let eyeRightEyeBallY = eyeRightEyeBallCenter["y"] as? Double,
                  let location = result["location"] as? [String: Any],
                  let rotation = location["rotation"] as? Int64
            else {
                var error = HttpError()
                error.statusMesg = "解析百度接口数据失败"
                reject(error)
                return
            }
            
            let leftEye = CGPoint(x: eyeLeftEyeBallX, y: eyeLeftEyeBallY)
            let rightEye = CGPoint(x: eyeRightEyeBallX, y: eyeRightEyeBallY)
            let data = FaceTestData(gender: FaceTestGender(rawValue: gender) ?? .none, age: Int(age), glassess: FaceTestGlasses(rawValue: glassess) ?? .none, faceshape: FaceShape(rawValue: faceShape) ?? .noChoose, beauty: Int(beauty), expression: FaceTestExpression(rawValue: expression) ?? .none, letfEyeCenter: rightEye, rightEyeCenter: leftEye, rotation: rotation)
            resolve(data)
        }
    }
    
    private func loadHairAction() {
        guard let data = self.faceData, let pImage = self.personImage else {
            return
        }
        DispatchQueue.main.async {
            self.hairView.transform = .identity
            
            let imageWidth = self.personImageView.frame.width
            let imageHeight = self.personImageView.frame.height
            let pLeftEye = data.letfEyeCenter
            let pRightEye = data.rightEyeCenter
            let pRotation = data.rotation
            var leftEyeCenter = CGPointMake(pLeftEye.x * imageWidth / pImage.size.width , pLeftEye.y * imageHeight / pImage.size.height)
            var rightEyeCenter = CGPointMake(pRightEye.x * imageWidth / pImage.size.width, pRightEye.y * imageHeight / pImage.size.height)
            
            leftEyeCenter = self.editAreaView.convert(leftEyeCenter, from: self.personImageView)
            rightEyeCenter = self.editAreaView.convert(rightEyeCenter, from: self.personImageView)

            let x = ((leftEyeCenter.x + rightEyeCenter.x) / 2.0 )
            let y = ((leftEyeCenter.y + rightEyeCenter.y) / 2.0 )
            
            if (abs(leftEyeCenter.x - rightEyeCenter.x) == 0) {
                self.hairView.frame = CGRectMake(x, y, imageWidth / 3.0, imageWidth / 3.0 * FaceTestConst.hairHeight / FaceTestConst.hairWidth)
                self.editAreaView.addSubview(self.hairView)
                self.hairView.center = CGPointMake(x, y)
            } else {
                let width = fabs(leftEyeCenter.x - rightEyeCenter.x) / (FaceTestConst.eyeDistance / FaceTestConst.hairWidth)
                let height = fabs(leftEyeCenter.x - rightEyeCenter.x) / (FaceTestConst.eyeDistance / FaceTestConst.hairWidth) * FaceTestConst.hairHeight / FaceTestConst.hairWidth
                self.editAreaView.addSubview(self.hairView)
                self.hairView.frame = CGRectMake(x, y, width, height)
                let centerX = (leftEyeCenter.x + rightEyeCenter.x) / 2
                let centerY1 = (leftEyeCenter.y + rightEyeCenter.y) / 2.0
                let centerY2 = (fabs(leftEyeCenter.x - rightEyeCenter.x) / (FaceTestConst.eyeDistance / FaceTestConst.hairWidth) * FaceTestConst.hairHeight / FaceTestConst.hairWidth) * (0.5 - (FaceTestConst.eyeProportionY / FaceTestConst.hairHeight))
                let centerY = centerY1 + centerY2
                
                let hairCenter = CGPointMake(centerX, centerY)
                self.hairView.center = hairCenter
            }
            
            if pRotation != 0 {
                let eyeCenter = CGPoint(x: ((leftEyeCenter.x + rightEyeCenter.x) / 2.0), y: ((leftEyeCenter.y + rightEyeCenter.y) / 2.0))

                let eyeProportionX = 837.0
                let op = CGPointMake(self.hairView.frame.size.width * eyeProportionX / FaceTestConst.hairWidth, self.hairView.frame.size.height * eyeProportionX / FaceTestConst.hairHeight)
                let angle = Double(pRotation) * Double.pi / 180
                self.hairView.transform = CGAffineTransformMakeRotation(angle)
                let point = self.editAreaView.layer.convert(op, from: self.hairView.layer)
                self.hairView.transform = CGAffineTransformTranslate(self.hairView.transform, eyeCenter.x - point.x, eyeCenter.y - point.y)
            }

            self.personImageView.image = self.personImage
            self.hairView.image = HDEditManager.shared.hairImage
            self.addHairViewGesture()
            self.rightAreaView.isRecover = false
        }
    }
}

// MARK: - 发型手势
typealias HairStyleEditVCUIGestureRecognizerDelegate = HairStyleEditVC
extension HairStyleEditVCUIGestureRecognizerDelegate: UIGestureRecognizerDelegate {
    private func addHairViewGesture() {
        //添加手势前先删除已有手势，不删除会造成叠加效果
        if let gestures = self.hairView.gestureRecognizers {
            for gesture in gestures {
                self.hairView.removeGestureRecognizer(gesture)
            }
        }
        //移动手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(panGesture(_ :)))
        panGesture.delegate = self
        panGesture.maximumNumberOfTouches = 2
        self.hairView.isUserInteractionEnabled = true
        self.hairView.addGestureRecognizer(panGesture)
        //缩放手势
        let pinGesture = UIPinchGestureRecognizer(target: self, action: #selector(pinGesture(_ :)))
        pinGesture.delegate = self
        self.hairView.addGestureRecognizer(pinGesture)
        //旋转手势
        let rotationGesture = UIRotationGestureRecognizer(target: self, action: #selector(rotationGesture(_ :)))
        rotationGesture.delegate = self
        self.hairView.addGestureRecognizer(rotationGesture)
    }
    
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
    
    @objc func rotationGesture(_ gesture: UIRotationGestureRecognizer) {
        if gesture.state == .began || gesture.state == .changed {
            self.hairView.transform = CGAffineTransformRotate(self.hairView.transform, gesture.rotation)
            gesture.rotation = 0
            self.rightAreaView.isRecover = true
        }
    }
    
    @objc func panGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: self.hairView.superview)
        let center = CGPoint(x: self.hairView.center.x + translation.x,
                             y: self.hairView.center.y + translation.y)
        self.hairView.center = center
        gesture.setTranslation(.zero, in: self.hairView.superview)
        self.rightAreaView.isRecover = true
    }
    
    @objc func pinGesture(_ gesture: UIPinchGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        let pinchCenter = gesture.location(in: view)
        
        if gesture.state == .began || gesture.state == .changed {
            //根据缩放因子scale去计算出transform
            let scale = gesture.scale
            let transform = view.transform.scaledBy(x: scale, y: scale)
            view.transform = transform
            //因为锚点位置变化会导致位移,计算相对于视图中心的x/y偏移量，重新赋值中心点
            let newCenter = CGPoint(x: view.center.x + (pinchCenter.x - view.bounds.size.width / 2) * (scale - 1),y: view.center.y + (pinchCenter.y - view.bounds.size.height / 2) * (scale - 1))
            view.center = newCenter
            gesture.scale = 1.0
            self.rightAreaView.isRecover = true
        }
    }
}

// MARK: - 右侧三按钮操作框
typealias HairStyleEditVCHairStyleEditRightAreaViewDelegate = HairStyleEditVC
extension HairStyleEditVCHairStyleEditRightAreaViewDelegate: HairStyleEditRightAreaViewDelegate {
    func hideHairAction(isHide: Bool) {
        self.hairView.isHidden = isHide
        if isHide == true {
            self.rightAreaView.isColor = false
            self.maskView.isHidden = true
            self.hairPreViewImageView.isHidden = true
            self.hairColorChooseView.isHidden = true
        }
    }
    
    func colorAction(isColor: Bool) {
        self.hairColorChooseView.isHidden.toggle()
        self.hairPreViewImageView.isHidden.toggle()
        self.maskView.isHidden.toggle()
        if isColor == true {
            if self.isAddedChooseHeight == true {
                self.scrollAnimation()
            }
            
            self.hairColorChooseView.refreshColor()
            self.rightAreaView.isHideImage = false
            self.hairView.isHidden = false
        } else {
            self.hairChooseStyleView.isHidden = false
        }
    }
    
    func recoverAction() {
        self.maskView.isHidden = true
        self.hairColorChooseView.isHidden = true
        self.hairPreViewImageView.isHidden = true
        self.rightAreaView.isHideImage = false
        self.rightAreaView.isColor = false
        self.rightAreaView.isRecover = false
        self.hairView.isHidden = false
        self.hairView.image = HDEditManager.shared.originImage
        HDEditManager.shared.recoverHairImageAction()
        self.loadHairAction()
    }
}

// MARK: - 底部发型选择框
typealias HairStyleEditVCHairStyleEditHairChooseViewDelegate = HairStyleEditVC
extension HairStyleEditVCHairStyleEditHairChooseViewDelegate: HairStyleEditHairChooseViewDelegate {
    func selectHair(hairUrlString: String, selectIndex: Int) {
        //修改发型，发色清除
        self.hairView.sd_setImage(with: URL(string: hairUrlString)) { [weak self]image, error, cacgeType, url in
            if error != nil {
                printLog(message: "修改发型出现ERROR:\(String(describing: error))")
                return
            }
            self?.hairView.image = image
            HDEditManager.shared.originImage = image
            if self?.hairPreViewImageView.isHidden == true {
                HDEditManager.shared.recoverHairImageAction()
                self?.loadHairAction()
            } else {
                self?.hairColorChooseView.refreshColor()
                self?.rightAreaView.isRecover = true
            }
            
            self?.rightAreaView.isHideImage = false
            self?.hairView.isHidden = false
            self?.hairChooseStyleView.refreshData()
        }
    }
    
    func changeGender() {
        //同步修改vc值，更新发型
        self.isMale = self.hairChooseStyleView.isMale
        self.maleSelectedIndex = self.hairChooseStyleView.maleSelectedIndex
        self.femaleSelectedIndex = self.hairChooseStyleView.femaleSelcetedIndex
        guard let maleSelectedIndex = self.maleSelectedIndex,
              let femaleSelectedIndex = self.femaleSelectedIndex,
                maleSelectedIndex >= 0 && maleSelectedIndex < self.maleImageArr.count - 1,
              femaleSelectedIndex >= 0 && femaleSelectedIndex < self.femaleImageArr.count - 1
        else {
            return
        }
        
        let hairData = isMale == true ? self.maleImageArr[maleSelectedIndex] : self.femaleImageArr[femaleSelectedIndex]
        guard let hairUrl = hairData.bigImage else {
            return
        }
        
        self.hairView.sd_setImage(with: URL(string: hairUrl)) { [weak self]image, error, cacgeType, url in
            if error != nil {
                printLog(message: "转换性别出现error:\(String(describing: error))")
                return
            }
            HDEditManager.shared.changeHairAction(newOriginHair: image)
            let newImage = HDEditManager.shared.hairImage
            self?.hairPreViewImageView.image = newImage
            self?.hairView.image = newImage
            //刷新底部发型选择库选中以及跳转到已选择的对应行
            self?.hairChooseStyleView.refreshData()
            self?.hairChooseStyleView.scrollToRow()
        }
    }
    
    func scrollAction() {
        if self.hairPreViewImageView.isHidden == false {
            return
        }
        self.scrollAnimation()
    }
    
    func scrollAnimation(animation: Bool = true) {
        self.isAddedChooseHeight.toggle() // 切换状态
        let finalHeight = self.getChooseViewHeight(self.isAddedChooseHeight)
        
        if animation == false {
            heightConstraint?.update(offset: finalHeight)
            return
        }
        
        if self.isAddedChooseHeight {
            heightConstraint?.update(offset: finalHeight)
            UIView.animate(withDuration: 0.2, delay: 0, options: [.curveLinear]) {
                self.view.layoutIfNeeded()
            }
        } else {
            UIView.animateKeyframes(withDuration: 0.3, delay: 0, options: [], animations: {
                UIView.addKeyframe(withRelativeStartTime: 0.0, relativeDuration: 0.3) {
                    self.heightConstraint?.update(offset: finalHeight + 100)
                    self.view.layoutIfNeeded()
                }
                UIView.addKeyframe(withRelativeStartTime: 0.05, relativeDuration: 0.3) {
                    self.heightConstraint?.update(offset: finalHeight + 80)
                    self.view.layoutIfNeeded()
                }
                UIView.addKeyframe(withRelativeStartTime: 0.1, relativeDuration: 0.3) {
                    self.heightConstraint?.update(offset: finalHeight + 60)
                    self.view.layoutIfNeeded()
                }
                UIView.addKeyframe(withRelativeStartTime: 0.2, relativeDuration: 0.3) {
                    self.heightConstraint?.update(offset: finalHeight + 40)
                    self.view.layoutIfNeeded()
                }
                UIView.addKeyframe(withRelativeStartTime: 0.25, relativeDuration: 0.3) {
                    self.heightConstraint?.update(offset: finalHeight + 20)
                    self.view.layoutIfNeeded()
                }
                UIView.addKeyframe(withRelativeStartTime: 0.3, relativeDuration: 0.3) {
                    self.heightConstraint?.update(offset: finalHeight)
                    self.view.layoutIfNeeded()
                }
            })
        }
    }
}

// MARK: - 颜色选择框
typealias HairStyleEditVCHairStyleEditColorChooseViewDelegate = HairStyleEditVC
extension HairStyleEditVCHairStyleEditColorChooseViewDelegate: HairStyleEditColorChooseViewDelegate {
    //更新选择颜色，改变发色
    func updateHairColorAction(color: UIColor) {
        HDEditManager.shared.changeHairColorAction(newColor: color)
        let newImage = HDEditManager.shared.hairImage
        self.hairPreViewImageView.image = newImage
        self.hairView.image = newImage
        self.rightAreaView.isRecover = true
    }
    
    //点击添加自定义颜色
    func addHairColorAction(color: UIColor) {
        let colorString = color.toHexString()
        HDEditManager.shared.addColorHexString(colorString)
        self.hairColorChooseView.refreshCollectionView()
    }
}
