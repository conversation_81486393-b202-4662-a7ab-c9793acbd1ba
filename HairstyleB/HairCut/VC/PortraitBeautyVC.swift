//
//  PortraitBeautyVC.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import UIKit
import SnapKit
import UMCommon

class PortraitBeautyVC: UIViewController {
    
    // MARK: - UI Components
    private let topBar = UIView()
    private let backButton = UIButton(type: .custom)
    private let saveButton = UIButton(type: .custom)
    
    private let imageView = UIImageView()
    
    private let bottomBar = UIView()
    private let skinButton = UIButton(type: .custom)
    private let faceShapeButton = UIButton(type: .custom)
    private let hairEditButton = UIButton(type: .custom)
    
    // MARK: - Properties
    var sourceImage: UIImage? // 用户选择的原始图片，永远不变
    private var currentImage: UIImage? // 当前显示的图片（可能是处理后的）
    
    // 用户保存次数统计
    private let portraitBeautySaveCountKey = "PortraitBeautySaveCount"
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
        loadDefaultImage()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: false)
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = UIColor.hex(string: "#F9F9F9")
        
        // Setup top bar
        topBar.backgroundColor = .white
        view.addSubview(topBar)
        
        // Setup back button
        backButton.setImage(UIImage(named: "返回"), for: .normal)
        topBar.addSubview(backButton)
        
        // Setup save button
        saveButton.setTitle(local("保存"), for: .normal)
        saveButton.setTitleColor(.black, for: .normal)
        saveButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        saveButton.backgroundColor = UIColor.hex(string: "#FFEC53")
        saveButton.layer.cornerRadius = 15
        topBar.addSubview(saveButton)
        
        // Setup image view
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        view.addSubview(imageView)
        
        // Setup bottom bar
        bottomBar.backgroundColor = .white
        bottomBar.layer.cornerRadius = 12
        bottomBar.layer.shadowColor = UIColor.black.cgColor
        bottomBar.layer.shadowOffset = CGSize(width: 0, height: -2)
        bottomBar.layer.shadowOpacity = 0.1
        bottomBar.layer.shadowRadius = 8
        view.addSubview(bottomBar)
        
        // Setup bottom buttons
        setupBottomButtons()
    }
    
    private func setupBottomButtons() {
        // 美肤按钮
        skinButton.setImage(UIImage(named: "美肤"), for: .normal)
        skinButton.setTitle(local("美肤"), for: .normal)
        skinButton.setTitleColor(UIColor.hex(string: "#333333"), for: .normal)
        skinButton.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        skinButton.setImagePosition(with: .top, spacing: 4)
        bottomBar.addSubview(skinButton)
        
        // 面部重塑按钮
        faceShapeButton.setImage(UIImage(named: "面部重塑"), for: .normal)
        faceShapeButton.setTitle(local("面部重塑"), for: .normal)
        faceShapeButton.setTitleColor(UIColor.hex(string: "#333333"), for: .normal)
        faceShapeButton.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        faceShapeButton.setImagePosition(with: .top, spacing: 4)
        bottomBar.addSubview(faceShapeButton)
        
        // 发型编辑按钮
        hairEditButton.setImage(UIImage(named: "发型编辑"), for: .normal)
        hairEditButton.setTitle(local("发型编辑"), for: .normal)
        hairEditButton.setTitleColor(UIColor.hex(string: "#333333"), for: .normal)
        hairEditButton.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        hairEditButton.setImagePosition(with: .top, spacing: 4)
        bottomBar.addSubview(hairEditButton)
    }
    
    private func setupConstraints() {
        // Top bar constraints
        topBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        // Back button constraints
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // Save button constraints
        saveButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(30)
        }
        
        // Bottom bar constraints
        bottomBar.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.height.equalTo(87)
        }
        
        // Image view constraints
        imageView.snp.makeConstraints { make in
            make.top.equalTo(topBar.snp.bottom).offset(20)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.bottom.equalTo(bottomBar.snp.top).offset(-20)
        }
        
        // Bottom buttons constraints
        let buttonWidth = (UIScreen.main.bounds.width - 32 - 40) / 3 // 减去左右边距和按钮间距
        
        skinButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
            make.width.equalTo(buttonWidth)
            make.height.equalTo(60)
        }
        
        faceShapeButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(buttonWidth)
            make.height.equalTo(60)
        }
        
        hairEditButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
            make.width.equalTo(buttonWidth)
            make.height.equalTo(60)
        }
    }
    
    private func setupActions() {
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        saveButton.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        skinButton.addTarget(self, action: #selector(skinButtonTapped), for: .touchUpInside)
        faceShapeButton.addTarget(self, action: #selector(faceShapeButtonTapped), for: .touchUpInside)
        hairEditButton.addTarget(self, action: #selector(hairEditButtonTapped), for: .touchUpInside)
    }
    
    private func loadDefaultImage() {
        // 优先使用传入的sourceImage，否则使用默认图片
        if let sourceImage = sourceImage {
            currentImage = sourceImage
            imageView.image = sourceImage
        } else if let defaultImage = UIImage(named: "welcome_picture_1") {
            currentImage = defaultImage
            imageView.image = defaultImage
        }
    }
    
    // MARK: - Actions
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
    
    @objc private func saveButtonTapped() {
        guard let image = currentImage else {
            showAlert(message: local("没有可保存的图片"))
            return
        }
        MobClick.event("Portrait_beauty_care", attributes: ["Source": "保存"])
        // 检查是否为VIP用户
        if !APPMakeStoreIAPManager.featureVip() {
            // 非VIP用户，检查保存次数
            let currentCount = UserDefaults.standard.integer(forKey: portraitBeautySaveCountKey)
            if currentCount >= 2 {
                // 已经保存过2次，弹出订阅页面
                MobClick.event("Subscribe", attributes: ["Source": "人像美容"])
                showSubscriptionPage()
                return
            }
        }
        
        // 保存图片到相册
        UIImageWriteToSavedPhotosAlbum(image, self, #selector(image(_:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    @objc private func skinButtonTapped() {
        MobClick.event("Portrait_beauty_care", attributes: ["Source": "美肤"])
        print("美肤按钮被点击")

        // 显示加载指示器
        SVProgressHUD.show(withStatus: local("正在加载美颜资源..."))

        // 在后台线程预加载资源
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            // 预初始化美颜SDK
            PTMFilterHelper.setupSDK()

            // 回到主线程创建和推送页面
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()

                let beautyEditVC = BeautyEditVC(mode: .skinBeauty)
                beautyEditVC.sourceImage = self?.currentImage ?? self?.sourceImage // 传递当前显示的图片
                beautyEditVC.hidesBottomBarWhenPushed = true

                // 设置回调，接收处理后的图片
                beautyEditVC.onImageProcessed = { [weak self] processedImage in
                    self?.updateImage(processedImage)
                }

                self?.navigationController?.pushViewController(beautyEditVC, animated: true)
            }
        }
    }

    @objc private func faceShapeButtonTapped() {
        MobClick.event("Portrait_beauty_care", attributes: ["Source": "面部重塑"])
        print("面部重塑按钮被点击")

        // 显示加载指示器
        SVProgressHUD.show(withStatus: local("正在加载美颜资源..."))

        // 在后台线程预加载资源
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            // 预初始化美颜SDK
            PTMFilterHelper.setupSDK()

            // 回到主线程创建和推送页面
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()

                let beautyEditVC = BeautyEditVC(mode: .faceShape)
                beautyEditVC.sourceImage = self?.currentImage ?? self?.sourceImage // 传递当前显示的图片
                beautyEditVC.hidesBottomBarWhenPushed = true

                // 设置回调，接收处理后的图片
                beautyEditVC.onImageProcessed = { [weak self] processedImage in
                    self?.updateImage(processedImage)
                }

                self?.navigationController?.pushViewController(beautyEditVC, animated: true)
            }
        }
    }
    
    @objc private func hairEditButtonTapped() {
        MobClick.event("Portrait_beauty_care", attributes: ["Source": "发型编辑"])
        print("发型编辑按钮被点击")

        // 显示加载指示器
        SVProgressHUD.show(withStatus: local("正在加载发型编辑..."))

        // 在后台线程预加载资源
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            // 这里可以预加载发型编辑相关的资源

            // 回到主线程创建和推送页面
            DispatchQueue.main.async {
                SVProgressHUD.dismiss()

                let hairEditVC = HairEditVC()
                hairEditVC.sourceImage = self?.currentImage ?? self?.sourceImage // 传递当前显示的图片
                hairEditVC.hidesBottomBarWhenPushed = true

                // 设置回调，接收处理后的图片
                hairEditVC.onImageProcessed = { [weak self] processedImage in
                    self?.updateImage(processedImage)
                }

                self?.navigationController?.pushViewController(hairEditVC, animated: true)
            }
        }
    }
    
    @objc private func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            showAlert(message: local("保存失败: ") + error.localizedDescription)
        } else {
            showAlert(message: local("保存成功"))
            
            // 保存成功后，增加保存次数（仅非VIP用户）
            if !APPMakeStoreIAPManager.featureVip() {
                let currentCount = UserDefaults.standard.integer(forKey: portraitBeautySaveCountKey)
                UserDefaults.standard.set(currentCount + 1, forKey: portraitBeautySaveCountKey)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func showAlert(message: String) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: local("确定"), style: .default))
        present(alert, animated: true)
    }
    
    private func showSubscriptionPage() {
        let vipVC = VIPViewController()
        vipVC.from = "人像美容"
        vipVC.modalPresentationStyle = .fullScreen
        present(vipVC, animated: true)
    }

    /// 更新显示的图片
    func updateImage(_ image: UIImage?) {
        currentImage = image
        imageView.image = image
    }
}
