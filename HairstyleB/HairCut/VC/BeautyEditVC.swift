//
//  BeautyEditVC.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import UIKit
import SnapKit
import UMCommon

enum BeautyEditMode {
    case skinBeauty    // 美肤模式
    case faceShape     // 面部重塑模式
}

class BeautyEditVC: UIViewController {

    // MARK: - UI Components
    private let imageView = UIImageView()
    private let compareButton = UIButton(type: .custom)

    private let bottomView = UIView()
    private let slider = UISlider()

    private let cancelButton = UIButton(type: .custom)
    private let confirmButton = UIButton(type: .custom)
    private let resetButton = UIButton(type: .custom)

    private let categoryStackView = UIStackView()
    private let categoryIndicatorView = UIView()

    private let beautyCollectionView: UICollectionView

    // MARK: - Properties
    var sourceImage: UIImage?
    var editMode: BeautyEditMode = .skinBeauty

    // 回调闭包，用于传递处理后的图片给上级页面
    var onImageProcessed: ((UIImage) -> Void)?

    private var originalImage: UIImage?
    private var currentImage: UIImage?
    private var beautyConfig: BeautyConfig?

    private var currentCategory = "skinBeauty"
    private var currentParameterIndex = 0
    private var categories: [String] = []
    private var categoryTitles: [String] = []
    private var currentParameters: [BeautyParameter] = []

    // 当前实时参数值（不保存到本地）
    private var currentParameterValues: [String: Double] = [:]
    
    // MARK: - Initialization
    init(mode: BeautyEditMode) {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        
        beautyCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        
        super.init(nibName: nil, bundle: nil)
        
        self.editMode = mode
        setupCategories()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()

        // 显示加载指示器
        let loadingIndicator = UIActivityIndicatorView(style: .large)
        loadingIndicator.center = view.center
        loadingIndicator.startAnimating()
        loadingIndicator.hidesWhenStopped = true
        view.addSubview(loadingIndicator)

        // 先设置基本UI，让用户看到界面
        setupUI()
        setupConstraints()
        setupActions()

        // 如果有源图片，先显示原图
        if let sourceImage = sourceImage {
            originalImage = sourceImage
            currentImage = sourceImage
            imageView.image = sourceImage
        }

        // 在后台线程加载美颜资源和配置
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            // 加载美颜配置
            self?.loadBeautyConfig()

            // 回到主线程完成初始化
            DispatchQueue.main.async {
                self?.initializeBeautySDK()
                loadingIndicator.stopAnimating()
            }
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: false)
    }
    
    // MARK: - Setup Methods
    private func setupCategories() {
        switch editMode {
        case .skinBeauty:
            categories = ["skinBeauty"]
            categoryTitles = [local("美肤")]
        case .faceShape:
            categories = ["faceShape", "eyeShape", "noseShape", "mouthShape", "eyebrowShape"]
            categoryTitles = [local("脸型"), local("眼型"), local("鼻子"), local("嘴巴"), local("眉毛")]
        }
        currentCategory = categories.first ?? "skinBeauty"
        currentParameterIndex = 0 // 默认选中第一个功能参数
    }
    
    private func setupUI() {
        view.backgroundColor = UIColor.hex(string: "#F9F9F9")
        
        // Setup image view
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        view.addSubview(imageView)
        
        // Setup compare button
        if let compareImage = UIImage(named: "美容对比") {
            compareButton.setImage(compareImage, for: .normal)
        } else {
            compareButton.setImage(UIImage(named: "拖动对比"), for: .normal)
        }
        compareButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        compareButton.layer.cornerRadius = 20
        view.addSubview(compareButton)
        
        // Setup bottom view
        bottomView.backgroundColor = .white
        bottomView.layer.cornerRadius = 12
        bottomView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.addSubview(bottomView)
        
        // Setup slider
        slider.minimumValue = 0
        slider.maximumValue = 1
        slider.value = 0
        slider.minimumTrackTintColor = UIColor.hex(string: "#FFEC53")
        slider.maximumTrackTintColor = UIColor.hex(string: "#E5E5E5")
        slider.thumbTintColor = UIColor.hex(string: "#FFEC53")
        bottomView.addSubview(slider)
        
        // Setup buttons
        setupBottomButtons()
        setupCategoryView()
        setupCollectionView()
    }
    
    private func setupBottomButtons() {
        // Cancel button - 使用美容返回图片
        if let cancelImage = UIImage(named: "美容返回") {
            cancelButton.setImage(cancelImage, for: .normal)
        } else {
            cancelButton.setImage(UIImage(named: "close_button"), for: .normal)
        }
        cancelButton.backgroundColor = .clear
        bottomView.addSubview(cancelButton)

        // Confirm button - 使用美容确认图片
        if let confirmImage = UIImage(named: "美容确认") {
            confirmButton.setImage(confirmImage, for: .normal)
        } else {
            confirmButton.setImage(UIImage(named: "faceshape_color_selected"), for: .normal)
        }
        confirmButton.backgroundColor = .clear
        bottomView.addSubview(confirmButton)
    }
    
    private func setupCategoryView() {
        // 所有模式都显示分类选择，美肤模式只有一个分类
        categoryStackView.axis = .horizontal
        categoryStackView.distribution = .fillEqually
        categoryStackView.spacing = 0
        bottomView.addSubview(categoryStackView)

        // Category indicator
        categoryIndicatorView.backgroundColor = UIColor.hex(string: "#FFEC53")
        categoryIndicatorView.layer.cornerRadius = 4
        bottomView.addSubview(categoryIndicatorView)

        // Create category buttons
        for (index, title) in categoryTitles.enumerated() {
            let button = UIButton(type: .custom)
            button.setTitle(title, for: .normal)
            button.setTitleColor(UIColor.hex(string: "#999999"), for: .normal)
            button.setTitleColor(UIColor.hex(string: "#333333"), for: .selected)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
            button.tag = index
            button.addTarget(self, action: #selector(categoryButtonTapped(_:)), for: .touchUpInside)

            if index == 0 {
                button.isSelected = true
                button.setTitleColor(UIColor.hex(string: "#333333"), for: .normal)
            }

            categoryStackView.addArrangedSubview(button)
        }
    }
    
    private func setupCollectionView() {
        beautyCollectionView.backgroundColor = .clear
        beautyCollectionView.showsHorizontalScrollIndicator = false
        beautyCollectionView.delegate = self
        beautyCollectionView.dataSource = self
        beautyCollectionView.register(UICollectionViewCell.self, forCellWithReuseIdentifier: "BeautyParameterCell")
        bottomView.addSubview(beautyCollectionView)
    }
    
    private func setupConstraints() {
        // Image view constraints
        imageView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomView.snp.top)
        }
        
        // Compare button constraints
        compareButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20)
            make.bottom.equalTo(bottomView.snp.top).offset(-20)
            make.width.height.equalTo(40)
        }
        
        // Bottom view constraints
        bottomView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(200)
        }
        
        // Slider constraints
        slider.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(30)
        }
        
        // Collection view constraints
        beautyCollectionView.snp.makeConstraints { make in
            make.top.equalTo(slider.snp.bottom).offset(20)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(80)
        }

        // Bottom buttons constraints
        cancelButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.width.height.equalTo(30)
        }

        confirmButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20)
            make.bottom.equalToSuperview().offset(-20)
            make.width.height.equalTo(30)
        }

        // Category stack view constraints - 在取消和确认按钮中间，所有模式都显示
        categoryStackView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
            make.height.equalTo(30)
            make.left.equalTo(cancelButton.snp.right).offset(20)
            make.right.equalTo(confirmButton.snp.left).offset(-20)
        }

        // Category indicator constraints - 与第一个按钮的label对齐
        if let firstButton = categoryStackView.arrangedSubviews.first as? UIButton {
            categoryIndicatorView.snp.makeConstraints { make in
                make.bottom.equalTo(firstButton.titleLabel!.snp.bottom)
                make.height.equalTo(2)
                make.width.equalTo(firstButton.titleLabel!.snp.width)
                make.centerX.equalTo(firstButton.titleLabel!.snp.centerX)
            }
        }
    }
    
    private func setupActions() {
        slider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)
        
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(compareButtonPressed(_:)))
        compareButton.addGestureRecognizer(longPress)
        
        cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        confirmButton.addTarget(self, action: #selector(confirmButtonTapped), for: .touchUpInside)
    }

    private func loadBeautyConfig() {
        print("🔧 开始加载美颜配置...")

        beautyConfig = BeautyConfigManager.shared.loadConfig()

        print("✅ 美颜配置加载成功")
        print("   美肤参数: \(beautyConfig?.skinBeauty.count ?? 0) 个")
        print("   脸型参数: \(beautyConfig?.faceShape.count ?? 0) 个")

        // 检查配置是否有效
        guard beautyConfig != nil else {
            print("❌ 美颜配置为空，创建默认配置")
            beautyConfig = BeautyConfig(skinBeauty: [], faceShape: [], eyeShape: [], noseShape: [], mouthShape: [], eyebrowShape: [])
            return
        }

        updateCurrentParameters()
        initializeDefaultParameters()
    }

    /// 初始化默认参数值
    private func initializeDefaultParameters() {
        guard let config = beautyConfig else { return }

        // 重置所有参数为默认值
        currentParameterValues.removeAll()

        let allParameters = config.skinBeauty + config.faceShape + config.eyeShape +
                           config.noseShape + config.mouthShape + config.eyebrowShape

        for parameter in allParameters {
            currentParameterValues[parameter.name] = parameter.defaultValue
        }

        print("🔄 初始化 \(allParameters.count) 个参数为默认值")
    }

    private func updateCurrentParameters() {
        guard let config = beautyConfig else {
            print("❌ updateCurrentParameters: beautyConfig为空")
            return
        }

        print("🔄 更新当前参数，分类: \(currentCategory)")

        switch currentCategory {
        case "skinBeauty":
            currentParameters = config.skinBeauty
        case "faceShape":
            currentParameters = config.faceShape
        case "eyeShape":
            currentParameters = config.eyeShape
        case "noseShape":
            currentParameters = config.noseShape
        case "mouthShape":
            currentParameters = config.mouthShape
        case "eyebrowShape":
            currentParameters = config.eyebrowShape
        default:
            currentParameters = config.skinBeauty
        }

        print("   当前分类参数数量: \(currentParameters.count)")

        // 确保currentParameterIndex在有效范围内
        if currentParameterIndex >= currentParameters.count {
            currentParameterIndex = 0
        }

        DispatchQueue.main.async {
            self.beautyCollectionView.reloadData()
            
            // 更新slider值 - 优先使用临时参数值
            if !self.currentParameters.isEmpty {
                let parameter = self.currentParameters[self.currentParameterIndex]
                self.slider.minimumValue = Float(parameter.minValue)
                self.slider.maximumValue = Float(parameter.maxValue)

                // 检查是否有临时参数值
                if let tempValue = BeautyFilterManager.shared.getTemporaryParameter(parameter.name) {
                    self.slider.value = Float(tempValue)
                    print("   使用临时参数值: \(parameter.name) = \(tempValue)")
                } else {
                    self.slider.value = Float(parameter.currentValue)
                    print("   使用配置参数值: \(parameter.name) = \(parameter.currentValue)")
                }
            } else {
                print("⚠️ 当前分类没有参数")
            }
        }
    }

    private func initializeBeautySDK() {
        // 确保有图片
        if let sourceImage = sourceImage {
            originalImage = sourceImage
            currentImage = sourceImage
            imageView.image = sourceImage

            // 初始化美颜SDK
            PTMFilterHelper.setupSDK()

            // 应用默认参数并生成初始效果
            applyInitialBeautyEffect()
        } else {
            print("⚠️ 没有源图片，无法初始化美颜SDK")
        }
    }

    /// 应用初始美颜效果（使用默认参数）
    private func applyInitialBeautyEffect() {
        guard let originalImage = originalImage else { return }

        // 显示加载指示器
        let loadingIndicator = UIActivityIndicatorView(style: .medium)
        loadingIndicator.center = imageView.center
        loadingIndicator.startAnimating()
        view.addSubview(loadingIndicator)

        // 在后台线程处理图片，避免阻塞UI
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            // 应用所有默认参数到美颜SDK
            self.applyCurrentParametersToSDK()

            // 使用默认参数处理图片
            let processedImage = PTMFilterHelper.processImage(withCurrentBeautySettings: originalImage)

            // 回到主线程更新UI
            DispatchQueue.main.async {
                self.currentImage = processedImage ?? originalImage
                self.imageView.image = self.currentImage

                // 移除加载指示器
                loadingIndicator.stopAnimating()
                loadingIndicator.removeFromSuperview()

                print("✅ 已应用初始美颜效果（默认参数）")
            }
        }
    }

    /// 将当前参数值应用到美颜SDK
    private func applyCurrentParametersToSDK() {
        for (parameterName, value) in currentParameterValues {
            PTMFilterHelper.applyParameter(toFUBeauty: parameterName, value: value)
        }
    }

    // MARK: - Actions
    @objc private func sliderValueChanged(_ slider: UISlider) {
        guard currentParameterIndex < currentParameters.count,
              !currentParameters.isEmpty else {
            print("⚠️ 参数索引越界或参数数组为空")
            return
        }

        let parameter = currentParameters[currentParameterIndex]
        let newValue = Double(slider.value)

        // 更新当前参数值（不保存到本地）
        currentParameterValues[parameter.name] = newValue

        // 实时应用美颜效果
        applyBeautyEffect()

        print("🎛️ 调整参数 \(parameter.name) = \(newValue)")
    }

    @objc private func compareButtonPressed(_ gesture: UILongPressGestureRecognizer) {
        switch gesture.state {
        case .began:
            // 添加对比按钮埋点
            if editMode == .skinBeauty {
                MobClick.event("Portrait_beauty_care", attributes: ["Beauty_care": "对比"])
            } else {
                MobClick.event("Portrait_beauty_care", attributes: ["Facial_reshaping": "对比"])
            }
            // 显示原图
            imageView.image = originalImage
        case .ended, .cancelled:
            // 显示当前处理后的图
            imageView.image = currentImage
        default:
            break
        }
    }

    @objc private func categoryButtonTapped(_ sender: UIButton) {
        let index = sender.tag
        guard index < categories.count else { return }

        // 更新选中状态
        for (i, button) in categoryStackView.arrangedSubviews.enumerated() {
            if let btn = button as? UIButton {
                btn.isSelected = (i == index)
                btn.setTitleColor(i == index ? UIColor.hex(string: "#333333") : UIColor.hex(string: "#999999"), for: .normal)
            }
        }

        // 更新指示器位置 - 与选中label的底部对齐，宽度与label一致
        let selectedButton = categoryStackView.arrangedSubviews[index] as! UIButton

        DispatchQueue.main.async {
            self.categoryIndicatorView.snp.remakeConstraints { make in
                make.bottom.equalTo(selectedButton.titleLabel!.snp.bottom)
                make.height.equalTo(2)
                make.width.equalTo(selectedButton.titleLabel!.snp.width)
                make.centerX.equalTo(selectedButton.titleLabel!.snp.centerX)
            }

            UIView.animate(withDuration: 0.3) {
                self.view.layoutIfNeeded()
            }
        }

        // 更新当前分类
        currentCategory = categories[index]
        currentParameterIndex = 0 // 默认选中第一个功能参数（除重置外）
        updateCurrentParameters()
    }

    @objc private func cancelButtonTapped() {
        // 添加取消按钮埋点
        if editMode == .skinBeauty {
            MobClick.event("Portrait_beauty_care", attributes: ["Beauty_care": "点击按钮错"])
        } else {
            MobClick.event("Portrait_beauty_care", attributes: ["Facial_reshaping": "点击按钮错"])
        }

        // 取消编辑，不传递处理后的图片
        navigationController?.popViewController(animated: true)
    }

    @objc private func confirmButtonTapped() {
        // 显示确认弹窗
        let alert = UIAlertController(
            title: local("确认保存"),
            message: local("保存后将无法撤回修改，是否继续？"),
            preferredStyle: .alert
        )

        // 取消按钮
        let cancelAction = UIAlertAction(title: local("取消"), style: .cancel, handler: nil)
        alert.addAction(cancelAction)

        // 确认按钮
        let confirmAction = UIAlertAction(title: local("确认"), style: .default) { [weak self] _ in
            // 添加确认按钮埋点
            if self?.editMode == .skinBeauty {
                MobClick.event("Portrait_beauty_care", attributes: ["Beauty_care": "点击按钮对"])
            } else {
                MobClick.event("Portrait_beauty_care", attributes: ["Facial_reshaping": "点击按钮对"])
            }

            // 确认编辑，传递处理后的图片给上级页面
            if let processedImage = self?.currentImage {
                self?.onImageProcessed?(processedImage)
            }
            self?.navigationController?.popViewController(animated: true)
        }
        alert.addAction(confirmAction)

        present(alert, animated: true)
    }

    @objc private func resetButtonTapped() {
        // 添加重置按钮埋点
        if editMode == .skinBeauty {
            MobClick.event("Portrait_beauty_care", attributes: ["Beauty_care": "重置"])
        } else {
            MobClick.event("Portrait_beauty_care", attributes: ["Facial_reshaping": "重置"])
        }

        // 重置所有参数为默认值
        initializeDefaultParameters()

        // 更新slider值 - 如果当前有选中的参数
        DispatchQueue.main.async {
            if !self.currentParameters.isEmpty && self.currentParameterIndex < self.currentParameters.count {
                let parameter = self.currentParameters[self.currentParameterIndex]
                self.slider.value = Float(parameter.defaultValue)
            }
        }

        // 应用效果
        applyBeautyEffect()

        print("🔄 重置所有参数为默认值")
    }

    // MARK: - Helper Methods
    private func updateBeautyConfig() {
        guard var config = beautyConfig else { return }

        switch currentCategory {
        case "skinBeauty":
            config.skinBeauty = currentParameters
        case "faceShape":
            config.faceShape = currentParameters
        case "eyeShape":
            config.eyeShape = currentParameters
        case "noseShape":
            config.noseShape = currentParameters
        case "mouthShape":
            config.mouthShape = currentParameters
        case "eyebrowShape":
            config.eyebrowShape = currentParameters
        default:
            break
        }

        beautyConfig = config
    }

    private func applyBeautyEffect() {
        guard let originalImage = originalImage else { return }

        // 在后台线程处理图片，避免阻塞UI
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            // 应用当前参数值到美颜SDK
            self.applyCurrentParametersToSDK()

            // 使用当前参数处理图片
            let processedImage = PTMFilterHelper.processImage(withCurrentBeautySettings: originalImage)

            // 回到主线程更新UI
            DispatchQueue.main.async {
                self.currentImage = processedImage ?? originalImage
                self.imageView.image = self.currentImage
            }
        }
    }

    private func applyAllBeautyParameters() {
        guard let config = beautyConfig else { return }

        // 在后台线程应用所有参数，避免阻塞UI
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            // 收集所有参数
            var parameters: [[String: Any]] = []

            // 添加各个分类的参数
            for parameter in config.skinBeauty {
                parameters.append(["name": parameter.name, "value": parameter.currentValue])
            }
            for parameter in config.faceShape {
                parameters.append(["name": parameter.name, "value": parameter.currentValue])
            }
            for parameter in config.eyeShape {
                parameters.append(["name": parameter.name, "value": parameter.currentValue])
            }
            for parameter in config.noseShape {
                parameters.append(["name": parameter.name, "value": parameter.currentValue])
            }
            for parameter in config.mouthShape {
                parameters.append(["name": parameter.name, "value": parameter.currentValue])
            }
            for parameter in config.eyebrowShape {
                parameters.append(["name": parameter.name, "value": parameter.currentValue])
            }

            // 使用批量更新方法一次性应用所有参数
            PTMFilterHelper.batchUpdateBeautyParameters(parameters)

            // 回到主线程应用美颜效果
            DispatchQueue.main.async {
                self.applyBeautyEffect()
            }
        }
    }
}

// MARK: - UICollectionViewDataSource
extension BeautyEditVC: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        // 重置按钮 + 功能参数
        return currentParameters.count + 1
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "BeautyParameterCell", for: indexPath)

        // 清除之前的子视图
        cell.subviews.forEach { $0.removeFromSuperview() }

        if indexPath.item == 0 {
            // 第一个cell是重置按钮
            setupResetCell(cell)
        } else {
            // 功能参数cell
            let parameterIndex = indexPath.item - 1
            guard parameterIndex < currentParameters.count else {
                print("⚠️ Collection view 参数索引越界: \(parameterIndex) >= \(currentParameters.count)")
                return cell
            }

            let parameter = currentParameters[parameterIndex]
            let isSelected = (indexPath.item - 1) == currentParameterIndex
            setupParameterCell(cell, parameter: parameter, isSelected: isSelected)
        }

        return cell
    }

    private func setupResetCell(_ cell: UICollectionViewCell) {
        // 清理之前的子视图，避免重复添加
        cell.subviews.forEach { $0.removeFromSuperview() }

        // 重置按钮样式
        cell.backgroundColor = .white
        cell.layer.cornerRadius = 8
        cell.layer.borderWidth = 0

        // 添加重置图片
        let imageView = UIImageView()
        if let resetImage = UIImage(named: "美容重置") {
            imageView.image = resetImage
        }
        imageView.contentMode = .scaleAspectFit
        cell.addSubview(imageView)

        // 添加重置文字
        let label = UILabel()
        label.text = "重置".localized
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor.hex(string: "#999999")
        cell.addSubview(label)

        // 设置约束 - 图片在上，文字在下
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(32)
        }

        label.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(4)
            make.bottom.equalToSuperview().offset(-8)
        }
    }

    private func setupParameterCell(_ cell: UICollectionViewCell, parameter: BeautyParameter, isSelected: Bool) {
        // 清理之前的子视图，避免重复添加
        cell.subviews.forEach { $0.removeFromSuperview() }

        // 设置cell样式
        cell.backgroundColor = isSelected ? UIColor.hex(string: "#FFF8C5") : .white
        cell.layer.cornerRadius = 8

        // 不使用边框，只通过背景颜色区分选中状态
        cell.layer.borderWidth = 0
        cell.layer.borderColor = UIColor.clear.cgColor

        // 添加图片
        let imageView = UIImageView()
        // 图片名称仍然使用中文名，因为图片资源是以中文命名的
        let imageName = isSelected ? "\(parameter.cnname) 1" : parameter.cnname
        imageView.image = UIImage(named: imageName)
        imageView.contentMode = .scaleAspectFit
        cell.addSubview(imageView)

        // 添加标题标签
        let label = UILabel()
        // 根据当前语言环境选择显示中文或英文名称
        let currentLanguage = LanguageTool.currentLanguage()
        label.text = currentLanguage == .chineseSimplified ? parameter.cnname : parameter.enname
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 11) // 稍微缩小字体
        label.textColor = isSelected ? UIColor.hex(string: "#333333") : UIColor.hex(string: "#999999")
        label.numberOfLines = 2 // 允许最多2行
        label.adjustsFontSizeToFitWidth = true // 自动调整字体大小
        label.minimumScaleFactor = 0.8 // 最小缩放比例
        cell.addSubview(label)

        // 设置约束
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(32)
        }

        label.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(4)
            make.bottom.equalToSuperview().offset(-8)
        }
    }
}

// MARK: - UICollectionViewDelegate
extension BeautyEditVC: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.item == 0 {
            // 点击重置按钮
            resetButtonTapped()
            return
        }

        // 功能参数选择
        currentParameterIndex = indexPath.item - 1

        guard currentParameterIndex < currentParameters.count else { return }

        let parameter = currentParameters[currentParameterIndex]

        // 添加功能选择埋点
        if editMode == .skinBeauty {
            MobClick.event("Portrait_beauty_care", attributes: ["Beauty_care": parameter.cnname])
        } else {
            // 面部重塑需要包含分类信息
            MobClick.event("Portrait_beauty_care", attributes: ["Facial_reshaping": "\(currentCategory)-\(parameter.cnname)"])
        }

        // 更新slider值和刷新collection view
        DispatchQueue.main.async {
            self.slider.minimumValue = Float(parameter.minValue)
            self.slider.maximumValue = Float(parameter.maxValue)

            // 使用当前参数值（如果有修改过的值则使用修改值，否则使用默认值）
            if let currentValue = self.currentParameterValues[parameter.name] {
                self.slider.value = Float(currentValue)
            } else {
                self.slider.value = Float(parameter.defaultValue)
            }

            collectionView.reloadData()
        }
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension BeautyEditVC: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 70, height: 80) // 增加宽度从60到70
    }
}
