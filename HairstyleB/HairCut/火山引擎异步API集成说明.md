# 火山引擎异步API集成说明

## 🔄 异步处理流程

根据火山引擎SeedEdit 3.0文档，API采用异步处理模式：

### 1. 提交任务 (CVSync2AsyncSubmitTask)
```
用户图片 + 美甲提示词 → 提交任务 → 获取task_id
```

### 2. 轮询查询 (CVSync2AsyncGetResult)
```
task_id → 查询状态 → in_queue/generating/done → 获取结果图片
```

## 🔧 技术实现

### API配置更新
```swift
// VolcanoEngineAPI.swift
private static let action = "CVSync2AsyncSubmitTask"  // 提交任务
private static let queryAction = "CVSync2AsyncGetResult"  // 查询结果
```

### 请求参数
```swift
// 提交任务参数
{
    "req_key": "seededit_v3.0",
    "binary_data_base64": ["base64编码的图片"],
    "prompt": "美甲样式描述",
    "scale": 0.5,        // 文本影响程度 [0-1]
    "seed": -1           // 随机种子，-1为随机
}

// 查询结果参数
{
    "req_key": "seededit_v3.0",
    "task_id": "任务ID",
    "req_json": "{\"return_url\":false}"
}
```

### 响应结构
```swift
// 提交任务响应
struct SubmitTaskResponse {
    let code: Int           // 10000为成功
    let data: TaskData?     // 包含task_id
    let message: String
}

// 查询结果响应
struct QueryResultResponse {
    let code: Int
    let data: ResultData?   // 包含状态和结果图片
    let message: String
}
```

## 📱 用户体验优化

### 进度状态显示
```swift
// 阶段1：提交任务
SVProgressHUD.show(withStatus: "正在提交任务...")

// 阶段2：任务处理中
SVProgressHUD.show(withStatus: "任务已提交，正在处理...")

// 阶段3：AI生成中
SVProgressHUD.show(withStatus: "AI正在生成美甲效果...")

// 阶段4：完成
SVProgressHUD.showSuccess(withStatus: "美甲处理完成！")
```

### 任务状态处理
```swift
switch taskStatus {
case "in_queue":     // 任务排队中
case "generating":   // 任务处理中
case "done":         // 任务完成
case "not_found":    // 任务未找到
case "expired":      // 任务已过期
}
```

## 🔄 轮询机制

### 轮询配置
- **轮询间隔**: 2秒
- **最大尝试次数**: 30次（总计60秒）
- **超时处理**: 显示"处理超时"错误

### 轮询逻辑
```swift
private static func pollTaskResult(
    taskId: String,
    completion: @escaping (Result<UIImage, Error>) -> Void,
    maxAttempts: Int = 30,
    currentAttempt: Int = 0
) {
    // 检查超时
    if currentAttempt >= maxAttempts {
        completion(.failure(TimeoutError))
        return
    }
    
    // 查询任务状态
    queryTaskResult(taskId: taskId) { result in
        switch taskStatus {
        case "done":
            // 解析结果图片
        case "in_queue", "generating":
            // 继续轮询
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                pollTaskResult(taskId: taskId, ...)
            }
        default:
            // 错误处理
        }
    }
}
```

## 🎯 API限制和建议

### 图片要求
- **格式**: JPEG、PNG（建议JPEG）
- **大小**: 最大5MB
- **分辨率**: 最大4096×4096
- **比例**: 长边与短边比例在3以内

### 提示词建议
- **长度**: ≤120字符
- **内容**: 使用自然语言，单指令效果更好
- **示例**:
  - "添加粉色渐变美甲效果"
  - "把指甲改成法式美甲"
  - "添加闪亮的钻石装饰"

### 性能参数
- **scale**: 文本影响程度，0.5为默认值
- **seed**: 随机种子，-1为随机生成

## 🧪 测试方法

### 1. 完整异步流程测试
```swift
// 在任何视图控制器中调用
self.testAsyncNailProcess()
```

### 2. API连接测试
```swift
// 测试火山引擎异步API
VolcanoEngineAsyncTest.testAPIConnection()
```

### 3. 任务提交测试
```swift
// 测试任务提交功能
VolcanoEngineAsyncTest.testTaskSubmission()
```

## ⚠️ 错误处理

### 常见错误码
- **10000**: 成功
- **50411**: 输入图片审核未通过
- **50511**: 输出图片审核未通过
- **50412**: 输入文本审核未通过
- **50429**: QPS超限
- **50430**: 并发超限
- **50500**: 内部错误

### 错误处理策略
```swift
private func getErrorMessage(from error: Error) -> String {
    let errorDescription = error.localizedDescription
    
    if errorDescription.contains("TimeoutError") {
        return "处理超时，请重试"
    } else if errorDescription.contains("ImageError") {
        return "图片格式错误"
    } else if errorDescription.contains("APIError") {
        return "服务暂时不可用"
    } else {
        return "处理失败，请重试"
    }
}
```

## 📊 性能监控

### 关键指标
- 任务提交成功率
- 平均处理时间
- 轮询次数分布
- 错误类型统计

### 埋点建议
```swift
// 任务提交
MobClick.event("NAIL_TASK_SUBMIT", attributes: ["prompt_length": prompt.count])

// 任务完成
MobClick.event("NAIL_TASK_COMPLETE", attributes: [
    "processing_time": processingTime,
    "poll_count": pollCount
])

// 任务失败
MobClick.event("NAIL_TASK_FAILED", attributes: ["error_type": errorType])
```

## 🚀 部署注意事项

### 1. 网络配置
- 确保应用有网络访问权限
- 配置合适的请求超时时间
- 处理网络异常情况

### 2. 用户体验
- 提供清晰的进度反馈
- 允许用户取消长时间处理
- 合理的错误提示信息

### 3. 资源管理
- 及时释放图片内存
- 避免重复提交相同任务
- 实现合理的重试机制

## 🔮 未来优化

### 1. 缓存机制
- 缓存处理结果
- 避免重复处理相同图片

### 2. 批量处理
- 支持多张图片批量处理
- 队列管理和优先级

### 3. 实时预览
- WebSocket连接获取实时进度
- 中间结果预览

---

🎉 **火山引擎异步API已完全集成，支持SeedEdit 3.0的完整异步处理流程！**
