//
//  AppDelegate.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/7/29.
//

import UIKit
import BUAdSDK
import Alamofire
import <PERSON><PERSON><PERSON><PERSON>
@main
class AppDelegate: UIResponder, UIApplicationDelegate {
    var window: UIWindow?
    private var isInitAD = false
    private var reachabilityManager = NetworkReachabilityManager()
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.
        window = UIWindow()
        window?.frame = UIScreen.main.bounds
        
        AppLoad.initAllClass()
        AppLoad.resetLaunchVC()
        
        window?.makeKeyAndVisible()
        initADconfiguration()
        startMonitoring()
        
        // Setup 3D Touch Quick Actions
        setupQuickActions()
        
        UMConfigure.setEncryptEnabled(false)
        UMConfigure.setLogEnabled(false)

        let config = UMAPMConfig.default()

        config.crashAndBlockMonitorEnable = true
        config.memMonitorEnable = true
        config.oomMonitorEnable = true
        config.launchMonitorEnable = true
        config.networkEnable = true
        config.pageMonitorEnable = true

        UMCrashConfigure.setAPMConfig(config)

        UMConfigure.initWithAppkey("64f57b298efadc41dcd3c724", channel: "App Store")

        // Uncomment if using this feature:
        // MobClick.setScenarioType(E_UM_NORMAL)

        MobClick.setAutoPageEnabled(true)
        BaiduAPIConfig.configure(apiKey: "Vpf9rJq1dtSxrVJdhqw5GU1E", secretKey: "Yfq88pcsWRuOcYAjr5YbQ1DOgpZwdK2F")
        
        return true
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        _ = AppLoad.idfa()
    }
    
    func initADconfiguration() {
        let configuration = BUAdSDKConfiguration()
        configuration.appID = AdsCNSetting.appId // 配置 App ID
        //设置可以跟其他音乐软件共存声音，默认allowModifyAudioSessionSetting为false，会打断其他播放
        configuration.allowModifyAudioSessionSetting = true
        configuration.audioSessionSetType = .mix
        BUAdSDKManager.start(asyncCompletionHandler: { isSuccess, error in
            if isSuccess {
                printLog(message: "广告 SDK 初始化成功")
                self.isInitAD = true
            } else if let error = error {
                printLog(message: "广告 SDK 初始化失败: \(error.localizedDescription)")
                self.isInitAD = false
            }
        })
    }
    
    func startMonitoring() {
        reachabilityManager?.startListening { status in
            switch status {
            case .notReachable:
                printLog(message: "Network is not reachable")
            case .reachable(.ethernetOrWiFi):
                printLog(message: "Network is reachable via WiFi or Ethernet")
                if !self.isInitAD {
                    self.initADconfiguration()
                }
            case .reachable(.cellular):
                printLog(message: "Network is reachable via cellular")
                if !self.isInitAD {
                    self.initADconfiguration()
                }
            case .unknown:
                printLog(message: "Network status is unknown")
            }
        }
    }

    // MARK: - 3D Touch Quick Actions
    
    func setupQuickActions() {
        // Static quick actions (appear on app install)
        let discountItem = UIApplicationShortcutItem(
            type: "com.hairstyle.quickAction.discount",
            localizedTitle: "限时特惠".localized,
            localizedSubtitle: "三折月度会员".localized,
            icon: UIApplicationShortcutIcon(templateImageName: "皇冠"),
            userInfo: nil
        )
        
        UIApplication.shared.shortcutItems = [discountItem]
    }
    
    func application(_ application: UIApplication, performActionFor shortcutItem: UIApplicationShortcutItem, completionHandler: @escaping (Bool) -> Void) {
        // Handle the shortcut item
        handleShortcutItem(shortcutItem)
        completionHandler(true)
    }
    
    private func handleShortcutItem(_ shortcutItem: UIApplicationShortcutItem) {
        switch shortcutItem.type {
        case "com.hairstyle.quickAction.discount":
            openVIPViewController()
        default:
            break
        }
    }
    
    private func openVIPViewController() {
        // Find the root view controller
        guard let rootViewController = window?.rootViewController else { return }
        
        // Dismiss any presented view controllers
        if let presentedVC = rootViewController.presentedViewController {
            presentedVC.dismiss(animated: true) {
                self.navigateToVIPVC(from: rootViewController)
            }
        } else {
            navigateToVIPVC(from: rootViewController)
        }
    }
    
    private func navigateToVIPVC(from viewController: UIViewController) {
        // Create VIPViewController programmatically
        let vipVC = VIPViewController()
        vipVC.from = "限时特惠" // Set the source of navigation
        
        // Present the VIP view controller
        if let navigationController = viewController as? UINavigationController {
            navigationController.pushViewController(vipVC, animated: true)
        } else {
            let navController = UINavigationController(rootViewController: vipVC)
            navController.modalPresentationStyle = .fullScreen
            viewController.present(navController, animated: true, completion: nil)
        }
    }
}

