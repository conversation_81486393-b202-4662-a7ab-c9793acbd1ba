# 美颜编辑功能说明

## 功能概述

新的美颜编辑功能提供了统一的美肤和面部重塑编辑界面，支持实时预览、参数调节和对比查看等功能。

## 页面设计

### UI布局
- **无顶部导航栏**：全屏显示图片
- **白色底部工具栏**：包含所有控制元素
- **对比按钮**：右下角悬浮，长按显示原图
- **滑块控制**：实时调节当前选中参数
- **功能按钮区域**：可滑动的CollectionView显示所有参数
- **分类选择区域**：底部中间，支持多分类切换
- **操作按钮**：取消、重置、确认

### 颜色规范
- **选中背景色**：#FFF8C5
- **选中文字色**：#333333
- **未选中背景色**：白色
- **未选中文字色**：#999999
- **主题色**：#FFEC53（滑块、指示器）
- **分类指示器**：#FFEC53，高度8px，圆角4px

## 功能特性

### 1. 双模式支持
- **美肤模式**：只有一个"美肤"分类
- **面部重塑模式**：包含脸型、眼型、鼻子、嘴巴、眉毛五个分类

### 2. 实时预览
- 滑块调节时立即更新图片效果
- 参数值与JSON配置实时同步
- 切换分类时自动更新滑块值

### 3. 对比功能
- 长按对比按钮显示原图
- 松手恢复当前处理效果
- 直观查看美颜前后差异

### 4. 参数管理
- 自动保存用户调节的参数
- 重置功能恢复所有参数到默认值
- 取消操作恢复到进入页面时的状态

## 技术实现

### 1. 页面结构
```swift
class BeautyEditVC: UIViewController {
    enum BeautyEditMode {
        case skinBeauty    // 美肤模式
        case faceShape     // 面部重塑模式
    }
}
```

### 2. 分类配置
```swift
// 美肤模式
categories = ["skinBeauty"]
categoryTitles = ["美肤"]

// 面部重塑模式
categories = ["faceShape", "eyeShape", "noseShape", "mouthShape", "eyebrowShape"]
categoryTitles = ["脸型", "眼型", "鼻子", "嘴巴", "眉毛"]
```

### 3. 参数同步
```swift
// 滑块值变化时
@objc private func sliderValueChanged(_ slider: UISlider) {
    let newValue = Double(slider.value)
    currentParameters[currentParameterIndex].currentValue = newValue
    PTMFilterHelper.updateBeautyParameter(parameter.name, value: newValue)
    applyBeautyEffect()
}
```

### 4. 图片处理
```swift
private func applyBeautyEffect() {
    DispatchQueue.global(qos: .userInitiated).async { [weak self] in
        let processedImage = PTMFilterHelper.processImage(withBeauty: originalImage)
        DispatchQueue.main.async {
            self?.currentImage = processedImage ?? originalImage
            self?.imageView.image = self?.currentImage
        }
    }
}
```

## 使用流程

### 1. 从人像美容页面进入
```swift
// 美肤模式
@objc private func skinButtonTapped() {
    let beautyEditVC = BeautyEditVC(mode: .skinBeauty)
    beautyEditVC.sourceImage = currentImage
    beautyEditVC.hidesBottomBarWhenPushed = true
    navigationController?.pushViewController(beautyEditVC, animated: true)
}

// 面部重塑模式
@objc private func faceShapeButtonTapped() {
    let beautyEditVC = BeautyEditVC(mode: .faceShape)
    beautyEditVC.sourceImage = currentImage
    beautyEditVC.hidesBottomBarWhenPushed = true
    navigationController?.pushViewController(beautyEditVC, animated: true)
}
```

### 2. 参数调节
1. 选择分类（面部重塑模式）
2. 点击功能按钮选择具体参数
3. 使用滑块调节参数值
4. 实时查看效果变化
5. 长按对比按钮查看原图

### 3. 操作完成
- **确认**：保存参数，返回上一页并更新图片
- **取消**：恢复原始参数，返回上一页
- **重置**：所有参数恢复默认值

## 自定义配置

### 1. 添加新的美颜参数
在 `beauty.json` 中添加新参数：
```json
{
  "cnname": "新参数",
  "enname": "new_parameter", 
  "name": "new_param",
  "type": 1,
  "currentValue": 0.0,
  "defaultValue": 0.0,
  "minValue": 0.0,
  "maxValue": 1.0
}
```

### 2. 添加新的分类
修改 `BeautyEditVC` 中的分类配置：
```swift
case .newCategory:
    categories = ["newCategory"]
    categoryTitles = ["新分类"]
```

### 3. 自定义按钮图片
按钮图片命名规则：
- **默认状态**：参数中文名（如"磨皮"）
- **选中状态**：参数中文名 + " 1"（如"磨皮 1"）

## 注意事项

### 1. 性能优化
- 图片处理在后台线程进行
- 参数调节使用防抖机制
- 及时释放内存资源

### 2. 用户体验
- 流畅的动画过渡
- 直观的视觉反馈
- 简洁的操作界面

### 3. 数据安全
- 自动备份进入时的参数状态
- 取消操作完全恢复原始状态
- 参数持久化存储

## 扩展功能

### 1. 批量操作
- 一键美颜：应用预设的美颜组合
- 参数预设：保存和加载用户自定义预设

### 2. 高级功能
- 局部美颜：只对脸部区域应用效果
- 智能推荐：根据人脸特征推荐参数

### 3. 社交分享
- 美颜前后对比图
- 参数配置分享
- 效果展示动画

这个统一的美颜编辑界面提供了完整的美肤和面部重塑功能，支持实时预览和灵活的参数调节，为用户提供专业的美颜体验。
