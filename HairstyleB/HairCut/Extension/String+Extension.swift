//
//  String+localized.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/7/29.
//

import Foundation

extension String {
    /// 获取本地语言
    var localized: String {
        return NSLocalizedString(self, comment: "comment")
    }
    
    // 截取字符串
    /// - Parameters:
    ///   - index: 启始位置
    ///   - offset: 数量
    /// - Returns: String
    func subStr(index: Int, offset: Int) -> String? {
        let endIndex = index + offset - 1
        guard offset != 0, endIndex < count else {
            return nil
        }

        let startIndexTemp = self.index(self.startIndex, offsetBy: index)
        let endIndexTemp = self.index(self.startIndex, offsetBy: endIndex)
        return String(self[startIndexTemp ... endIndexTemp])
    }

    /// 将字符串从 index 位置截取到尾
    /// - Parameter index: 启始位置
    func subStrFromIndex(_ index: Int) -> String? {
        guard index < count else {
            return nil
        }
        return String(dropFirst(Int(index)))
    }

    /// 将字符串截取到 index 的位置
    /// - Parameter index: 截止位置
    func subStrToIndex(_ index: Int) -> String? {
        guard index < count else {
            return nil
        }
        return String(dropLast(Int(count - Int(index) - 1)))
    }
    
    ///字符串base64处理
    func encodeBase64() -> String {
        let data = self.data(using: String.Encoding.utf8)
        guard let base64String = data?.base64EncodedString(options: NSData.Base64EncodingOptions.init(rawValue: 0)) else { return "" }
        return base64String
    }
    
    ///base64数据转化为string
    func decodeBase64() -> String {
        let decodeData = NSData(base64Encoded: self, options: NSData.Base64DecodingOptions.init(rawValue: 0))
        guard let decodeString = NSString(data: decodeData! as Data, encoding: String.Encoding.utf8.rawValue) as String? else { return ""}
        return decodeString
    }
    
    ///JsonString转化为string
    func jsonStringToDictionary() -> [String : Any] {
        //jsonstring转字典
        let data = self.data(using: String.Encoding.utf8)
        guard let dictionary = try? JSONSerialization.jsonObject(with: data!,options: JSONSerialization.ReadingOptions.mutableContainers) as? [String : Any] else {
            return [String : Any]()
        }
        return dictionary
    }
}

extension String {
    var unicodeStr:String {
        let tempStr1 = self.replacingOccurrences(of: "\\u", with: "\\U")
        let tempStr2 = tempStr1.replacingOccurrences(of: "\"", with: "\\\"")
        let tempStr3 = "\"".appending(tempStr2).appending("\"")
        let tempData = tempStr3.data(using: String.Encoding.utf8)
        var returnStr:String = ""
        do {
            returnStr = try PropertyListSerialization.propertyList(from: tempData!, options: [.mutableContainers], format: nil) as! String
        } catch {
            printLog(message: error)
        }
        return returnStr.replacingOccurrences(of: "\\r\\n", with: "\n")
    }
}
