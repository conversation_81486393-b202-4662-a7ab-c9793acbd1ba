//
//  UIColor+Hex.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/7/30.
//

import Foundation
import UIKit

extension UIColor {
    convenience init(valueRGB: UInt, _ alphaValue: CGFloat = 1.0) {
        self.init(
            red: CGFloat((valueRGB & 0xFF0000) >> 16) / 255.0,
            green: CGFloat((valueRGB & 0x00FF00) >> 8) / 255.0,
            blue: CGFloat(valueRGB & 0x0000FF) / 255.0,
            alpha: CGFloat(alphaValue)
        )
    }
}

typealias UIColorHexString = UIColor
extension UIColorHexString {
    private convenience init(hex string: String) {
            var hex = string.hasPrefix("#")
                ? String(string.dropFirst())
                : string
            guard hex.count == 3 || hex.count == 6
                else {
                    self.init(white: 1.0, alpha: 0.0)
                    return
            }
            if hex.count == 3 {
                for (index, char) in hex.enumerated() {
                    hex.insert(char, at: hex.index(hex.startIndex, offsetBy: index * 2))
                }
            }
            
            guard let intCode = Int(hex, radix: 16) else {
                self.init(white: 1.0, alpha: 0.0)
                return
            }
            
            self.init(red: CGFloat((intCode >> 16) & 0xFF) / 255.0, green: CGFloat((intCode >> 8) & 0xFF) / 255.0, blue: CGFloat((intCode) & 0xFF) / 255.0, alpha: 1.0)
        }
    
    public static func hex(string: String, _ dartString: String? = nil) -> UIColor {
        if #available(iOS 13.0, *), let dark = dartString {
            let color = UIColor.init { (state: UITraitCollection) -> UIColor in
                if state.userInterfaceStyle == .dark {
                    return UIColor(hex: dark)
                }
                return UIColor(hex: string)
            }
            return color
        }
        return UIColor(hex: string)
    }
 
    public func alpha(_ value: CGFloat) -> UIColor {
        return withAlphaComponent(value)
    }
    
    public func toHexString(includeAlpha: Bool = false) -> String? {
            var red: CGFloat = 0
            var green: CGFloat = 0
            var blue: CGFloat = 0
            var alpha: CGFloat = 0
            
            // 使用 UIColor 的 getRed 方法提取 RGBA 分量
            guard self.getRed(&red, green: &green, blue: &blue, alpha: &alpha) else {
                return nil // 转换失败，返回 nil
            }
            
            if includeAlpha {
                return String(format: "#%02X%02X%02X%02X",
                              Int(red * 255),
                              Int(green * 255),
                              Int(blue * 255),
                              Int(alpha * 255))
            } else {
                return String(format: "#%02X%02X%02X",
                              Int(red * 255),
                              Int(green * 255),
                              Int(blue * 255))
            }
        }
}
