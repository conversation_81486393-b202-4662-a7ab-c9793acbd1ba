//
//  Array+Extension.swift
//  HairCut
//
//  Created by Clarence on 2024/8/4.
//

import Foundation

import Foundation

public extension Array where Element: Equatable {
    
    /// 去除数组重复元素
    /// - Returns: 去除数组重复元素后的数组
    func removeDuplicate() -> Array {
       return self.enumerated().filter { (index,value) -> Bool in
            return self.firstIndex(of: value) == index
        }.map { (_, value) in
            value
        }
    }
}
