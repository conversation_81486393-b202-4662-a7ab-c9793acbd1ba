//
//  UIViewController+Extension.swift
//  HairCut
//
//  Created by <PERSON>ger on 2024/8/2.
//

import Foundation
import UIKit



extension UIViewController {
    public func setLeftNavigationBar(_ imageName: String = "left_arrow") {
        self.navigationController?.navigationBar.barTintColor = UIColor.white
        
        let leftArrowImage = UIImage(named: imageName)
        let backButton = UIButton(type: .custom)
        backButton.setImage(leftArrowImage, for: .normal)
        backButton.frame = CGRect(x: 0, y: 0, width: 24, height: 24)
        backButton.addTarget(self, action: #selector(backToPreviousViewController), for: .touchUpInside)
        self.navigationItem.leftBarButtonItem = UIBarButtonItem(customView: backButton)
    }
    
    @objc func backToPreviousViewController() {
        self.navigationController?.popViewController(animated: false)
    }
}
