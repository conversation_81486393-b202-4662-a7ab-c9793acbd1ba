//
//  Dictionary+Extension.swift
//  HairCut
//
//  Created by <PERSON> on 2024/8/4.
//

import Foundation
import SwiftyJSON

extension Dictionary {
    /// 根据 keys 和 value 创建
    ///
    /// - Parameters:
    ///   - keys: key 数组
    ///   - values: value数组
    public init?(keys: [Key], values: [Value]) {
        guard keys.count == values.count else { return nil }
        self.init()
        for (index, key) in keys.enumerated() { self[key] = values[index] }
    }
    
    /// 组合 Dictionary
    ///
    /// - Parameter other: 需要添加的 Dictionary
    public mutating func merge(_ other: [Key: Value]) {
        for (key, value) in other { self[key] = value }
    }
    
    
    /// 转化为json字符串
    /// - Returns: String?
    public func toJsonString() -> String? {
        guard let jsonData = try? JSONSerialization.data(withJSONObject: self, options: .prettyPrinted) else {
            return nil
        }
        return String(data: jsonData, encoding: .utf8)
    }
}
