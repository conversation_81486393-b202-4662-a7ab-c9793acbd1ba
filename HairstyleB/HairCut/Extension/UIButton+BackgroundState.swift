//
//  UIButton+BackgroundState.swift
//  HairCut
//
//  Created by fs0011 on 2025/3/19.
//

import UIKit
 
// MARK: - 运行时关联对象键
private struct AssociatedKeys {
    static var normalBackgroundColor = "normalBackgroundColorKey"
    static var selectedBackgroundColor = "selectedBackgroundColorKey"
}
 
extension UIButton {
    // MARK: - 状态背景色配置
    /// 设置状态背景色（带颜色缓存）
    /// - Parameters:
    ///   - color: 目标颜色
    ///   - state: 对应状态
    func setBackgroundColor(_ color: UIColor?, for state: UIControl.State) {
        guard let color = color else { return }
        
        // 创建纯色图片并缓存
        let colorImage = UIImage.fromColor(color,  size: self.bounds.size)
        self.setBackgroundImage(colorImage,  for: state)
        
        // 动态绑定颜色对象
        switch state {
        case .normal:
            objc_setAssociatedObject(self, &AssociatedKeys.normalBackgroundColor,  color, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        case .selected:
            objc_setAssociatedObject(self, &AssociatedKeys.selectedBackgroundColor,  color, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        default: break
        }
        
        // 添加状态监听
        self.addTarget(self,  action: #selector(updateBackgroundColor), for: .touchUpInside)
    }
    
    // MARK: - 颜色状态更新
    @objc private func updateBackgroundColor() {
        let targetColor = self.isSelected  ?
        objc_getAssociatedObject(self, &AssociatedKeys.selectedBackgroundColor)  as? UIColor :
        objc_getAssociatedObject(self, &AssociatedKeys.normalBackgroundColor)  as? UIColor
        
        if let color = targetColor {
            self.setBackgroundImage(UIImage.fromColor(color,  size: self.bounds.size),  for: .normal)
        }
    }
}
 
// MARK: - 颜色转图片扩展
extension UIImage {
    static func fromColor(_ color: UIColor, size: CGSize) -> UIImage {
        let rect = CGRect(origin: .zero, size: size)
        UIGraphicsBeginImageContext(rect.size)
        let context = UIGraphicsGetCurrentContext()
        context?.setFillColor(color.cgColor)
        context?.fill(rect)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image ?? UIImage()
    }
}
