//
//  UIView+gradient.swift
//  HairCut
//
//  Created by Bigger on 2024/7/30.
//

import Foundation
import UIKit

extension UIView {
    func setGradientBackground(colors: [UIColor], resizeBound: CGRect) {
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = resizeBound
        gradientLayer.colors = colors.map{ $0.cgColor }
        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1.0)
        
        layer.insertSublayer(gradientLayer, at: 0)
    }
}
