//
//  Print+Extension.swift
//  HairCut
//
//  Created by Bigger on 2024/8/1.
//

import Foundation

private let dateFormat = DateFormatter()
func printLog<T>(message: T, file: String = #file, method: String = #function, line: Int = #line) {
    #if DEBUG
    dateFormat.dateFormat = "HH:mm:ss SSS"
    print("[\((file as NSString).lastPathComponent) \(dateFormat.string(from: Date()))]: \(message)")
    #endif
}
