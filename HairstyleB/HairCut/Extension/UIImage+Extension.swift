//
//  UIImage+Extension.swift
//  HairCut
//
//  Created by <PERSON><PERSON> on 2024/8/5.
//

import Foundation
import UIKit
extension UIImage {
    func maskWith(mask: UIImage?) -> UIImage? {
        let imageView = UIImageView(image: self)
        let maskView = UIImageView(image: mask)
        maskView.frame = imageView.bounds
        imageView.mask = maskView
        UIGraphicsBeginImageContextWithOptions(imageView.bounds.size, false, self.scale)
        imageView.layer.render(in: UIGraphicsGetCurrentContext()!)
        let result = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return result
    }
    
    func base64EncodedString() -> String? {
        guard let imageData = self.jpegData(compressionQuality: 1) ?? self.pngData() else { return nil }
        return imageData.base64EncodedString(options: [])
    }
    
    func compressImage(maxKB: Int) -> UIImage? {
        var compressionQuality = 1.0
        var imageData = self.jpegData(compressionQuality: compressionQuality)
        while imageData?.count ?? 0 > maxKB && compressionQuality > 0 {
            compressionQuality -= 0.05
            imageData = self.jpegData(compressionQuality: compressionQuality)
        }
        
        guard let compressData = imageData else {
            return nil
        }
        return UIImage(data: compressData)
    }
    
    func resizeImage(_ size: CGSize) -> UIImage? {
        let renderer = UIGraphicsImageRenderer(size: size)
        let scaledImage = renderer.image { (context) in
            self.draw(in: CGRect(origin: .zero, size: size))
        }
        return scaledImage
    }
    
    func createImage(_ size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContext(size)
        
        guard let context = UIGraphicsGetCurrentContext() else {
            return nil
        }
        context.setFillColor(UIColor.clear.cgColor)
        let bgRect = CGRect(origin: .zero, size: size)
        context.fill(bgRect)
        
        let rect = CGRect(origin: .zero, size: size)
        self.draw(in: rect)
        
        let resultImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return resultImage
    }
}

