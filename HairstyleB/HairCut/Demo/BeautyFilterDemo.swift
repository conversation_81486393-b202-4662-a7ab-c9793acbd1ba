//
//  BeautyFilterDemo.swift
//  HairCut
//
//  Created by AI Assistant on 2024/12/15.
//

import Foundation
import UIKit

/// 美颜滤镜使用示例
class BeautyFilterDemo {
    
    /// 示例：应用基础美颜效果
    static func applyBasicBeauty(to image: UIImage) -> UIImage? {
        // 1. 初始化SDK（通常在应用启动时调用一次）
        PTMFilterHelper.setupSDK()
        
        // 2. 设置一些基础美颜参数
        _ = PTMFilterHelper.updateBeautyParameter("blur_level", value: 3.0)      // 磨皮
        _ = PTMFilterHelper.updateBeautyParameter("color_level", value: 0.3)     // 美白
        _ = PTMFilterHelper.updateBeautyParameter("red_level", value: 0.2)       // 红润
        _ = PTMFilterHelper.updateBeautyParameter("eye_bright", value: 0.4)      // 亮眼
        
        // 3. 应用美颜效果
        return PTMFilterHelper.processImageWithBeauty(image)
    }
    
    /// 示例：应用脸型调整
    static func applyFaceShapeAdjustment(to image: UIImage) -> UIImage? {
        // 设置脸型参数
        _ = PTMFilterHelper.updateBeautyParameter("cheek_v", value: 0.3)         // v脸
        _ = PTMFilterHelper.updateBeautyParameter("cheek_thinning", value: 0.2)  // 瘦脸
        _ = PTMFilterHelper.updateBeautyParameter("eye_enlarging", value: 0.3)   // 大眼
        
        // 应用效果
        return PTMFilterHelper.processImageWithBeauty(image)
    }
    
    /// 示例：自定义美颜强度
    static func applyCustomBeauty(to image: UIImage, skinLevel: Double, shapeLevel: Double) -> UIImage? {
        // 美肤效果
        _ = PTMFilterHelper.updateBeautyParameter("blur_level", value: skinLevel * 6.0)
        _ = PTMFilterHelper.updateBeautyParameter("color_level", value: skinLevel * 0.5)
        _ = PTMFilterHelper.updateBeautyParameter("red_level", value: skinLevel * 0.3)
        
        // 美型效果
        _ = PTMFilterHelper.updateBeautyParameter("cheek_v", value: shapeLevel * 0.4)
        _ = PTMFilterHelper.updateBeautyParameter("eye_enlarging", value: shapeLevel * 0.3)
        
        return PTMFilterHelper.processImageWithBeauty(image)
    }
    
    /// 示例：重置到默认效果
    static func resetToDefault() {
        PTMFilterHelper.resetToDefault()
        print("已重置所有美颜参数到默认值")
    }
    
    /// 示例：获取当前参数值
    static func getCurrentBeautySettings() {
        let blurLevel = PTMFilterHelper.getCurrentValue("blur_level")
        let colorLevel = PTMFilterHelper.getCurrentValue("color_level")
        let vFaceLevel = PTMFilterHelper.getCurrentValue("cheek_v")
        
        print("当前美颜设置:")
        print("磨皮: \(blurLevel)")
        print("美白: \(colorLevel)")
        print("V脸: \(vFaceLevel)")
    }
    
    /// 示例：批量设置参数
    static func setBatchParameters() {
        let parameters: [(String, Double)] = [
            ("blur_level", 4.0),           // 磨皮
            ("color_level", 0.3),          // 美白
            ("red_level", 0.2),            // 红润
            ("eye_bright", 0.4),           // 亮眼
            ("tooth_whiten", 0.3),         // 美牙
            ("cheek_v", 0.2),              // v脸
            ("cheek_thinning", 0.1),       // 瘦脸
            ("eye_enlarging", 0.3),        // 大眼
            ("intensity_nose", 0.2),       // 瘦鼻
            ("intensity_smile", 0.1)       // 微笑
        ]
        
        for (name, value) in parameters {
            let success = PTMFilterHelper.updateBeautyParameter(name, value: value)
            if success {
                print("✅ 设置 \(name) = \(value)")
            } else {
                print("❌ 设置 \(name) 失败")
            }
        }
    }
}

// MARK: - 使用示例扩展
extension BeautyFilterDemo {
    
    /// 完整的美颜处理流程示例
    static func completeBeautyProcess(originalImage: UIImage, completion: @escaping (UIImage?) -> Void) {
        // FUBeauty 操作必须在主线程中执行
        // 1. 初始化SDK
        PTMFilterHelper.setupSDK()

        // 2. 设置美颜参数
        setBatchParameters()

        // 3. 处理图片
        let processedImage = PTMFilterHelper.processImageWithBeauty(originalImage)

        // 4. 返回结果
        completion(processedImage)
    }
    
    /// 渐进式美颜效果（从无到有的动画效果）
    static func progressiveBeauty(originalImage: UIImage, steps: Int = 10, completion: @escaping ([UIImage]) -> Void) {
        // FUBeauty 操作必须在主线程中执行
        var results: [UIImage] = []

        PTMFilterHelper.setupSDK()

        for i in 0...steps {
            let progress = Double(i) / Double(steps)

            // 根据进度设置参数
            _ = PTMFilterHelper.updateBeautyParameter("blur_level", value: progress * 4.0)
            _ = PTMFilterHelper.updateBeautyParameter("color_level", value: progress * 0.3)
            _ = PTMFilterHelper.updateBeautyParameter("cheek_v", value: progress * 0.2)

            if let processedImage = PTMFilterHelper.processImageWithBeauty(originalImage) {
                results.append(processedImage)
            }
        }

        completion(results)
    }
}
