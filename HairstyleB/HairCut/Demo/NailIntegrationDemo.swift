//
//  NailIntegrationDemo.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import UIKit
import SVProgressHUD

/// 美甲功能集成演示
class NailIntegrationDemo {
    
    /// 演示如何在现有视图控制器中添加美甲功能
    /// - Parameter parentViewController: 父视图控制器
    static func showNailStylePicker(from parentViewController: UIViewController) {
        let nailVC = NailStyleViewController()
        let navController = UINavigationController(rootViewController: nailVC)
        
        // 添加关闭按钮
        nailVC.navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: nailVC,
            action: #selector(dismissViewController)
        )
        
        parentViewController.present(navController, animated: true)
    }
    
    /// 演示如何获取美甲数据并处理
    static func demonstrateNailDataUsage() {
        print("🎨 开始演示美甲数据使用...")
        
        // 方式1: 直接使用NailModel获取数据
        NailModel.fetchNailData { result in
            switch result {
            case .success(let models):
                print("✅ 获取到 \(models.count) 个美甲样式")
                
                // 示例：筛选女性美甲样式
                let femaleNails = models.filter { $0.sex == 0 }
                print("👩 女性美甲样式: \(femaleNails.count) 个")
                
                // 示例：筛选VIP美甲样式
                let vipNails = models.filter { $0.isVip }
                print("💎 VIP美甲样式: \(vipNails.count) 个")
                
                // 示例：打印前3个样式的信息
                for (index, nail) in models.prefix(3).enumerated() {
                    print("\n美甲样式 \(index + 1):")
                    print("  名称: \(nail.name)")
                    print("  提示词: \(nail.prompt)")
                    print("  是否VIP: \(nail.isVip)")
                }
                
            case .failure(let error):
                print("❌ 获取美甲数据失败: \(error.localizedDescription)")
            }
        }
        
        // 方式2: 使用数据管理器
        let manager = NailDataManager.shared
        manager.loadNailData { result in
            switch result {
            case .success(let models):
                print("\n📊 数据管理器演示:")
                print("总数量: \(manager.getAllModels().count)")
                print("女性样式: \(manager.getModelsBySex(0).count)")
                print("男性样式: \(manager.getModelsBySex(1).count)")
                print("VIP样式: \(manager.getVipModels().count)")
                print("免费样式: \(manager.getFreeModels().count)")
                
                // 搜索演示
                let searchResults = manager.searchModels(keyword: "渐变")
                print("搜索'渐变': \(searchResults.count) 个结果")
                
            case .failure(let error):
                print("❌ 数据管理器加载失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 演示如何将美甲样式应用到图片处理
    /// - Parameters:
    ///   - nailModel: 选中的美甲模型
    ///   - userImage: 用户上传的手部图片
    static func demonstrateNailProcessing(nailModel: NailModel, userImage: UIImage) {
        print("🖼️ 开始美甲处理演示...")
        
        // 设置用户图片到模型
        var processingModel = nailModel
        processingModel.origin_image = userImage
        
        // 获取处理参数
        let parameters = processingModel.toDictionary()
        
        print("处理参数:")
        for (key, value) in parameters {
            if key == "img" {
                print("  \(key): [UIImage对象]")
            } else {
                print("  \(key): \(value)")
            }
        }
        
        // 这里可以调用实际的AI处理接口
        // 例如：AIProcessingService.processNail(parameters: parameters) { result in ... }
        
        print("✅ 美甲处理参数准备完成，可以发送到AI服务")
    }
    
    /// 演示如何在现有的相机界面中集成美甲功能
    /// - Parameter cameraViewController: 相机视图控制器
    static func integrateWithCamera(cameraViewController: UIViewController) {
        print("📸 集成相机功能演示...")
        
        // 创建美甲按钮
        let nailButton = UIButton(type: .custom)
        nailButton.setTitle("美甲", for: .normal)
        nailButton.setTitleColor(.white, for: .normal)
        nailButton.backgroundColor = .systemPink
        nailButton.layer.cornerRadius = 20
        nailButton.addTarget(self, action: #selector(nailButtonTapped), for: .touchUpInside)
        
        cameraViewController.view.addSubview(nailButton)
        
        // 设置约束（这里使用SnapKit）
        nailButton.snp.makeConstraints { make in
            make.bottom.equalTo(cameraViewController.view.safeAreaLayoutGuide).offset(-100)
            make.right.equalToSuperview().offset(-20)
            make.width.height.equalTo(40)
        }
    }
    
    @objc private static func nailButtonTapped() {
        print("美甲按钮被点击")
        // 这里可以打开美甲样式选择器
    }
    
    /// 演示数据缓存和离线使用
    static func demonstrateCaching() {
        print("💾 缓存演示...")
        
        // 使用SDWebImage的缓存功能预加载美甲图片
        NailModel.fetchNailData { result in
            switch result {
            case .success(let models):
                print("开始预加载 \(models.count) 个美甲图片...")
                
                let group = DispatchGroup()
                var successCount = 0
                var failCount = 0
                
                for model in models {
                    if let url = URL(string: model.image) {
                        group.enter()
                        SDWebImageManager.shared.loadImage(
                            with: url,
                            options: .highPriority,
                            progress: nil
                        ) { image, data, error, cacheType, finished, imageURL in
                            if finished {
                                if error == nil {
                                    successCount += 1
                                } else {
                                    failCount += 1
                                }
                                group.leave()
                            }
                        }
                    }
                }
                
                group.notify(queue: .main) {
                    print("✅ 预加载完成: 成功 \(successCount) 个，失败 \(failCount) 个")
                }
                
            case .failure(let error):
                print("❌ 获取数据失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 运行所有演示
    static func runAllDemos() {
        print("🚀 开始运行美甲功能集成演示...")
        print("=" * 60)
        
        // 1. 数据使用演示
        demonstrateNailDataUsage()
        
        // 2. 延迟执行缓存演示
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            demonstrateCaching()
        }
        
        // 3. 延迟执行测试套件
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            NailModelTest.runAllTests()
        }
    }
}

// MARK: - UIViewController扩展，添加美甲功能
extension UIViewController {
    
    /// 便捷方法：显示美甲样式选择器
    func showNailStylePicker() {
        NailIntegrationDemo.showNailStylePicker(from: self)
    }
    
    /// 便捷方法：处理美甲样式选择结果
    /// - Parameters:
    ///   - nailModel: 选中的美甲模型
    ///   - userImage: 用户图片
    func handleNailStyleSelection(nailModel: NailModel, userImage: UIImage) {
        SVProgressHUD.show(withStatus: "处理中...")
        
        // 这里可以调用实际的美甲处理逻辑
        NailIntegrationDemo.demonstrateNailProcessing(nailModel: nailModel, userImage: userImage)
        
        // 模拟处理时间
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            SVProgressHUD.dismiss()
            // 显示处理结果
        }
    }
}

// MARK: - 扩展NailStyleViewController，添加关闭方法
extension NailStyleViewController {
    @objc func dismissViewController() {
        dismiss(animated: true)
    }
}
