//
//  NailProcessDemoViewController.swift
//  HairCut
//
//  Created by fs0011 on 2025/7/14.
//

import UIKit
import SnapKit
import SVProgressHUD

// MARK: - UIColor Extension for Demo
extension UIColor {
    convenience init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            alpha: Double(a) / 255
        )
    }
}

/// 美甲处理演示视图控制器
class NailProcessDemoViewController: UIViewController {
    
    // MARK: - UI Elements
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "美甲处理演示"
        label.font = UIFont.boldSystemFont(ofSize: 24)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "选择一张手部照片开始美甲处理"
        label.font = UIFont.systemFont(ofSize: 16)
        label.textAlignment = .center
        label.textColor = .gray
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var selectImageButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("选择图片", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.backgroundColor = .systemBlue
        btn.layer.cornerRadius = 25
        btn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        btn.addTarget(self, action: #selector(selectImageButtonTapped), for: .touchUpInside)
        return btn
    }()
    
    private lazy var takePhotoButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("拍照", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.backgroundColor = .systemGreen
        btn.layer.cornerRadius = 25
        btn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        btn.addTarget(self, action: #selector(takePhotoButtonTapped), for: .touchUpInside)
        return btn
    }()
    
    private lazy var testAPIButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("测试API连接", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.backgroundColor = .systemOrange
        btn.layer.cornerRadius = 25
        btn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        btn.addTarget(self, action: #selector(testAPIButtonTapped), for: .touchUpInside)
        return btn
    }()
    
    private lazy var quickProcessButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("快速处理（演示图片）", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.backgroundColor = .systemPink
        btn.layer.cornerRadius = 25
        btn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        btn.addTarget(self, action: #selector(quickProcessButtonTapped), for: .touchUpInside)
        return btn
    }()

    private lazy var testUIButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("测试新UI界面", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.backgroundColor = UIColor(hex: "#FFEC53")
        btn.layer.cornerRadius = 25
        btn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        btn.addTarget(self, action: #selector(testUIButtonTapped), for: .touchUpInside)
        return btn
    }()
    
    private lazy var stackView: UIStackView = {
        let sv = UIStackView()
        sv.axis = .vertical
        sv.spacing = 20
        sv.alignment = .fill
        sv.distribution = .fillEqually
        return sv
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = .white
        title = "美甲演示"
        
        // 添加导航栏按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "关于",
            style: .plain,
            target: self,
            action: #selector(aboutButtonTapped)
        )
        
        // 添加子视图
        view.addSubview(titleLabel)
        view.addSubview(descriptionLabel)
        view.addSubview(stackView)
        
        // 添加按钮到堆栈视图
        stackView.addArrangedSubview(selectImageButton)
        stackView.addArrangedSubview(takePhotoButton)
        stackView.addArrangedSubview(testAPIButton)
        stackView.addArrangedSubview(quickProcessButton)
        stackView.addArrangedSubview(testUIButton)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        safeConstraints(titleLabel) { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(50)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(30)
        }

        safeConstraints(descriptionLabel) { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(20)
        }

        safeConstraints(stackView) { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(40)
            make.height.equalTo(320) // 5个按钮 * 50高度 + 4个间距 * 20
        }

        // 设置按钮高度
        [selectImageButton, takePhotoButton, testAPIButton, quickProcessButton, testUIButton].forEach { button in
            safeConstraints(button) { make in
                make.height.equalTo(50)
            }
        }
    }
    
    // MARK: - Actions
    @objc private func selectImageButtonTapped() {
        let imagePicker = UIImagePickerController()
        imagePicker.delegate = self
        imagePicker.sourceType = .photoLibrary
        imagePicker.allowsEditing = true
        present(imagePicker, animated: true)
    }
    
    @objc private func takePhotoButtonTapped() {
        if UIImagePickerController.isSourceTypeAvailable(.camera) {
            let imagePicker = UIImagePickerController()
            imagePicker.delegate = self
            imagePicker.sourceType = .camera
            imagePicker.allowsEditing = true
            present(imagePicker, animated: true)
        } else {
            // 如果没有相机，使用自定义相机界面
            let nailCameraVC = NailCameraViewController()
            nailCameraVC.delegate = self
            let navController = UINavigationController(rootViewController: nailCameraVC)
            navController.modalPresentationStyle = .fullScreen
            present(navController, animated: true)
        }
    }
    
    @objc private func testAPIButtonTapped() {
        TestNailAPI.runTestsFromViewController(self)
    }
    
    @objc private func quickProcessButtonTapped() {
        // 使用一张演示图片进行快速处理
        if let demoImage = UIImage(named: "demo_hand") ?? createDemoImage() {
            quickNailProcess(with: demoImage)
        } else {
            SVProgressHUD.showError(withStatus: "无法创建演示图片")
        }
    }

    @objc private func testUIButtonTapped() {
        // 测试新的美甲详情页面UI
        NailProcessUITest.testNailProcessUI(from: self)
    }
    
    @objc private func aboutButtonTapped() {
        let alert = UIAlertController(
            title: "关于美甲处理",
            message: """
            这是一个美甲处理演示应用，集成了：
            
            • 火山引擎图生图API
            • 美甲样式数据管理
            • 图片预处理和后处理
            • 用户友好的操作界面
            
            选择或拍摄一张手部照片，然后选择喜欢的美甲样式进行处理。
            """,
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    // MARK: - Helper Methods
    private func createDemoImage() -> UIImage? {
        // 创建一个简单的演示图片
        let size = CGSize(width: 300, height: 400)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        
        // 绘制背景
        UIColor.lightGray.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        
        // 绘制手的轮廓（简化版）
        UIColor.systemPink.setFill()
        let handRect = CGRect(x: 50, y: 50, width: 200, height: 300)
        UIBezierPath(ovalIn: handRect).fill()
        
        // 添加文字
        let text = "演示图片"
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.boldSystemFont(ofSize: 20),
            .foregroundColor: UIColor.white
        ]
        let textSize = text.size(withAttributes: attributes)
        let textRect = CGRect(
            x: (size.width - textSize.width) / 2,
            y: (size.height - textSize.height) / 2,
            width: textSize.width,
            height: textSize.height
        )
        text.draw(in: textRect, withAttributes: attributes)
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return image
    }
    
    private func processSelectedImage(_ image: UIImage) {
        let alert = UIAlertController(
            title: "选择处理方式",
            message: "您想如何处理这张图片？",
            preferredStyle: .actionSheet
        )
        
        alert.addAction(UIAlertAction(title: "完整美甲处理", style: .default) { [weak self] _ in
            self?.startNailProcess(with: image)
        })
        
        alert.addAction(UIAlertAction(title: "快速处理", style: .default) { [weak self] _ in
            self?.quickNailProcess(with: image)
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // 为iPad设置popover
        if let popover = alert.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }
        
        present(alert, animated: true)
    }
}

// MARK: - UIImagePickerControllerDelegate
extension NailProcessDemoViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true) { [weak self] in
            if let image = info[.editedImage] as? UIImage ?? info[.originalImage] as? UIImage {
                self?.processSelectedImage(image)
            }
        }
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}

// MARK: - NailCameraViewControllerDelegate
extension NailProcessDemoViewController: NailCameraViewControllerDelegate {
    func nailCameraDidSelectImage(_ image: UIImage) {
        processSelectedImage(image)
    }
    
    func nailCameraDidCancel() {
        // 相机取消，不需要特殊处理
    }
}
