// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		79349D732CEACE3F00ED9853 /* HDColorPickerHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D722CEACE3F00ED9853 /* HDColorPickerHelpers.swift */; };
		79349D752CEAD24800ED9853 /* HDColorPickerLayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D742CEAD24800ED9853 /* HDColorPickerLayer.swift */; };
		79349D772CEAD42800ED9853 /* HDPointView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D762CEAD42800ED9853 /* HDPointView.swift */; };
		79349D792CEAD4A600ED9853 /* HDColorPickerBarLayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D782CEAD4A600ED9853 /* HDColorPickerBarLayer.swift */; };
		79349D7B2CEAD5CB00ED9853 /* HDColorPickerBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D7A2CEAD5CB00ED9853 /* HDColorPickerBarView.swift */; };
		79349D7D2CEAD6C300ED9853 /* HDColorPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D7C2CEAD6C300ED9853 /* HDColorPickerView.swift */; };
		79349D7F2CEADE2100ED9853 /* HDColorPalette.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D7E2CEADE2100ED9853 /* HDColorPalette.swift */; };
		79349D812CEADEA900ED9853 /* HDColorCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D802CEADEA900ED9853 /* HDColorCell.swift */; };
		79349D842CEADF5400ED9853 /* HDEditManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D832CEADF5400ED9853 /* HDEditManager.swift */; };
		79349D882CEAF10500ED9853 /* HairStyleEditColorCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79349D872CEAF10500ED9853 /* HairStyleEditColorCell.swift */; };
		793B25232C58814D005492DF /* HomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 793B25222C58814D005492DF /* HomeView.swift */; };
		793B25252C589440005492DF /* UIColor+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 793B25242C589440005492DF /* UIColor+Extension.swift */; };
		793B25272C58BDA3005492DF /* SettingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 793B25262C58BDA3005492DF /* SettingView.swift */; };
		793B25292C58C4F8005492DF /* WelcomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 793B25282C58C4F8005492DF /* WelcomeView.swift */; };
		793B252B2C58CC20005492DF /* UIView+gradient.swift in Sources */ = {isa = PBXBuildFile; fileRef = 793B252A2C58CC20005492DF /* UIView+gradient.swift */; };
		793B252D2C58E714005492DF /* SubscribeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 793B252C2C58E714005492DF /* SubscribeView.swift */; };
		79578DF52C61B2DF00387CD5 /* 灰色渐变卷发.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 79578DEF2C61B2DF00387CD5 /* 灰色渐变卷发.jpg */; };
		79578DF62C61B2DF00387CD5 /* 洛丽塔发型.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 79578DF02C61B2DF00387CD5 /* 洛丽塔发型.jpg */; };
		79578DF72C61B2DF00387CD5 /* 纹理烫.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 79578DF12C61B2DF00387CD5 /* 纹理烫.jpg */; };
		79578DF82C61B2DF00387CD5 /* 挑染编织发.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 79578DF22C61B2DF00387CD5 /* 挑染编织发.jpg */; };
		79578DF92C61B2DF00387CD5 /* 羊毛卷.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 79578DF32C61B2DF00387CD5 /* 羊毛卷.jpg */; };
		79578DFA2C61B2DF00387CD5 /* 爆炸头.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 79578DF42C61B2DF00387CD5 /* 爆炸头.jpg */; };
		79578DFC2C61B55800387CD5 /* PopularHairCutData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79578DFB2C61B55800387CD5 /* PopularHairCutData.swift */; };
		79578DFE2C6207E700387CD5 /* FaceTestData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79578DFD2C6207E700387CD5 /* FaceTestData.swift */; };
		796A43202C6C57AC00E724C5 /* LanguageTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 796A431F2C6C57AC00E724C5 /* LanguageTool.swift */; };
		796D03482CE6DC1200046500 /* HairStyleMainListHairCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 796D03472CE6DC1200046500 /* HairStyleMainListHairCell.swift */; };
		796D034A2CE6E35C00046500 /* HairStyleEditVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 796D03492CE6E35C00046500 /* HairStyleEditVC.swift */; };
		796D034F2CE6E55E00046500 /* HairStyleEditHairChooseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 796D034E2CE6E55E00046500 /* HairStyleEditHairChooseView.swift */; };
		796D03512CE6E59B00046500 /* HairStyleEditRightAreaView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 796D03502CE6E59B00046500 /* HairStyleEditRightAreaView.swift */; };
		796D03532CE6E5C300046500 /* HairStyleEditColorChooseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 796D03522CE6E5C300046500 /* HairStyleEditColorChooseView.swift */; };
		796D03552CE6EEB000046500 /* HairStyleNavRightActionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 796D03542CE6EEB000046500 /* HairStyleNavRightActionView.swift */; };
		796D03572CE72B7400046500 /* HairStyleEditHairChooseCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 796D03562CE72B7400046500 /* HairStyleEditHairChooseCell.swift */; };
		796D03592CE747B500046500 /* HaitStyleHairData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 796D03582CE747B500046500 /* HaitStyleHairData.swift */; };
		7971E0BC2C578042002381F1 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7971E0BB2C578042002381F1 /* AppDelegate.swift */; };
		7971E0C52C578044002381F1 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 7971E0C42C578044002381F1 /* Assets.xcassets */; };
		7971E0C82C578044002381F1 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = 7971E0C72C578044002381F1 /* Base */; };
		7971E0D42C57853E002381F1 /* AppLoad.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7971E0D32C57853E002381F1 /* AppLoad.swift */; };
		7971E0D82C578646002381F1 /* WelcomeVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7971E0D72C578646002381F1 /* WelcomeVC.swift */; };
		7971E0DD2C57A35E002381F1 /* TabbarVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7971E0DC2C57A35E002381F1 /* TabbarVC.swift */; };
		7971E0DF2C57A3D3002381F1 /* SettingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7971E0DE2C57A3D3002381F1 /* SettingVC.swift */; };
		7971E0E12C57A436002381F1 /* HomeVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7971E0E02C57A436002381F1 /* HomeVC.swift */; };
		7971E0E62C57A91D002381F1 /* String+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7971E0E52C57A91D002381F1 /* String+Extension.swift */; };
		7971E0EA2C57A97D002381F1 /* HttpTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7971E0E92C57A97D002381F1 /* HttpTool.swift */; };
		7971E0EC2C57A9F9002381F1 /* Const.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7971E0EB2C57A9F9002381F1 /* Const.swift */; };
		797215132CE5CAC5001B82BE /* HairStyleVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 797215122CE5CAC5001B82BE /* HairStyleVC.swift */; };
		797215162CE5E337001B82BE /* HairStyleMainListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 797215152CE5E337001B82BE /* HairStyleMainListView.swift */; };
		797491802C6DEE4300F5D89C /* icon4.png in Resources */ = {isa = PBXBuildFile; fileRef = 7974917E2C6DEE4300F5D89C /* icon4.png */; };
		797491812C6DEE4300F5D89C /* icon5.png in Resources */ = {isa = PBXBuildFile; fileRef = 7974917F2C6DEE4300F5D89C /* icon5.png */; };
		798E3CE82C65A1B000DE4093 /* FaceTestCellData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 798E3CE72C65A1B000DE4093 /* FaceTestCellData.swift */; };
		798F145E2C5C68F80029A34D /* AIHairResultVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 798F145D2C5C68F80029A34D /* AIHairResultVC.swift */; };
		798F14602C5C69A00029A34D /* AIHairCutResultView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 798F145F2C5C69A00029A34D /* AIHairCutResultView.swift */; };
		798F14622C5C6F5B0029A34D /* UIViewController+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 798F14612C5C6F5A0029A34D /* UIViewController+Extension.swift */; };
		798F14642C5C7AA80029A34D /* FaceShapeTestView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 798F14632C5C7AA80029A34D /* FaceShapeTestView.swift */; };
		798F14662C5C90A30029A34D /* FaceShapeTestCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 798F14652C5C90A30029A34D /* FaceShapeTestCell.swift */; };
		798F146A2C5CBCEA0029A34D /* AIHairCutUploadButtonView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 798F14692C5CBCEA0029A34D /* AIHairCutUploadButtonView.swift */; };
		798F146C2C5CBD150029A34D /* HairCutChooseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 798F146B2C5CBD150029A34D /* HairCutChooseView.swift */; };
		798F146E2C5CBD490029A34D /* HairCutAreaView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 798F146D2C5CBD490029A34D /* HairCutAreaView.swift */; };
		799686D12CED728A00971950 /* HairCutMalloc.c in Sources */ = {isa = PBXBuildFile; fileRef = 799686D02CED728A00971950 /* HairCutMalloc.c */; };
		799686D42CED7BDB00971950 /* HDImageTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 799686D32CED7BDB00971950 /* HDImageTool.m */; };
		799B6E262C6B346400FC2015 /* UserDefaultsTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 799B6E252C6B346400FC2015 /* UserDefaultsTool.swift */; };
		79AD56FF2C5A490A00A06A2F /* HotHairCutCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79AD56FE2C5A490A00A06A2F /* HotHairCutCollectionViewCell.swift */; };
		79AD57012C5A4E6700A06A2F /* AboutUsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79AD57002C5A4E6700A06A2F /* AboutUsVC.swift */; };
		79AD57032C5A4E7700A06A2F /* AboutUsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79AD57022C5A4E7700A06A2F /* AboutUsView.swift */; };
		79C007412C59CD55006521B7 /* Enum.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C007402C59CD55006521B7 /* Enum.swift */; };
		79C007452C5A33CF006521B7 /* LaunchScreenVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C007442C5A33CF006521B7 /* LaunchScreenVC.swift */; };
		79C007472C5A33E5006521B7 /* LaunchScreenView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C007462C5A33E5006521B7 /* LaunchScreenView.swift */; };
		79C733BD2C5B6743002FBCE7 /* SettingIconViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C733BC2C5B6743002FBCE7 /* SettingIconViewCell.swift */; };
		79C733C42C5B7029002FBCE7 /* Print+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C733C32C5B7029002FBCE7 /* Print+Extension.swift */; };
		79C733C62C5B71DB002FBCE7 /* AIHairCutVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C733C52C5B71DB002FBCE7 /* AIHairCutVC.swift */; };
		79C733C82C5B7202002FBCE7 /* FaceShapeTestVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C733C72C5B7202002FBCE7 /* FaceShapeTestVC.swift */; };
		79C733CA2C5B736A002FBCE7 /* AIHairCutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C733C92C5B736A002FBCE7 /* AIHairCutView.swift */; };
		79C733CD2C5B9AEC002FBCE7 /* HairCutImageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C733CC2C5B9AEC002FBCE7 /* HairCutImageCell.swift */; };
		79C733CF2C5B9B78002FBCE7 /* HairCutTextCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79C733CE2C5B9B78002FBCE7 /* HairCutTextCell.swift */; };
		79D8642C2CB4E020009DEB91 /* GoodRepulationTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79D8642B2CB4E020009DEB91 /* GoodRepulationTool.swift */; };
		79EEBBE52C60A4F100905018 /* UIImage+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79EEBBE42C60A4F100905018 /* UIImage+Extension.swift */; };
		79EEBBE72C60ADA100905018 /* AIHairCutData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79EEBBE62C60ADA100905018 /* AIHairCutData.swift */; };
		79EF1AA82C6620C500B2D555 /* ImageTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79EF1AA72C6620C500B2D555 /* ImageTool.swift */; };
		79EF1AAE2C6628F400B2D555 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 79EF1AAC2C6628F400B2D555 /* Localizable.strings */; };
		79F8FC3B2C634C9F0054A742 /* FaceTestResultImageMatch.json in Resources */ = {isa = PBXBuildFile; fileRef = 79F8FC3A2C634C9F0054A742 /* FaceTestResultImageMatch.json */; };
		79F8FC3F2C6360C30054A742 /* FaceTestResult.json in Resources */ = {isa = PBXBuildFile; fileRef = 79F8FC3E2C6360C30054A742 /* FaceTestResult.json */; };
		A5171D922E25E92700C9233F /* PTMFilterHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = A5171D912E25E92700C9233F /* PTMFilterHelper.m */; };
		A5171D972E25EF3E00C9233F /* beauty.json in Resources */ = {isa = PBXBuildFile; fileRef = A5171D962E25EF3E00C9233F /* beauty.json */; };
		A5171D992E25F03500C9233F /* BeautyParameter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5171D982E25F03400C9233F /* BeautyParameter.swift */; };
		A5171D9B2E25F42B00C9233F /* PortraitBeautyVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5171D9A2E25F42B00C9233F /* PortraitBeautyVC.swift */; };
		A5171D9F2E26030100C9233F /* BeautyEditVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5171D9E2E26030100C9233F /* BeautyEditVC.swift */; };
		A5171DA12E26034B00C9233F /* BeautyParameterCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5171DA02E26034B00C9233F /* BeautyParameterCell.swift */; };
		A5171DA32E26057900C9233F /* BeautyFilterManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5171DA22E26057900C9233F /* BeautyFilterManager.swift */; };
		A5171F7E2E262F5E00C9233F /* FURenderKit快速集成文档.md in Resources */ = {isa = PBXBuildFile; fileRef = A5171F7D2E262F5E00C9233F /* FURenderKit快速集成文档.md */; };
		A5556B682DCF393D00009F7E /* PTMPaintView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5556B672DCF393D00009F7E /* PTMPaintView.swift */; };
		A5556B692DCF393D00009F7E /* BrushPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5556B652DCF393D00009F7E /* BrushPreviewView.swift */; };
		A5556B6A2DCF393D00009F7E /* AreaSelectViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5556B642DCF393D00009F7E /* AreaSelectViewController.swift */; };
		A5556B6B2DCF393D00009F7E /* DrawView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5556B662DCF393D00009F7E /* DrawView.swift */; };
		A596577E2D3A13B1003DFD2E /* RSA.m in Sources */ = {isa = PBXBuildFile; fileRef = A596577A2D3A13B1003DFD2E /* RSA.m */; };
		A596577F2D3A13B1003DFD2E /* UIImage+Base64.m in Sources */ = {isa = PBXBuildFile; fileRef = A596577C2D3A13B1003DFD2E /* UIImage+Base64.m */; };
		A59659C32D3A47F2003DFD2E /* AIHairJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = A59659C22D3A47F2003DFD2E /* AIHairJSON.swift */; };
		A59659C52D3A483C003DFD2E /* input.json in Resources */ = {isa = PBXBuildFile; fileRef = A59659C42D3A483C003DFD2E /* input.json */; };
		A5B678582D8BA5AC009F5A53 /* MyAdManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5B678572D8BA5AC009F5A53 /* MyAdManager.swift */; };
		A5BC4A9A2DD47CC50048F92E /* TagBasedHairModelManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5BC4A992DD47CC50048F92E /* TagBasedHairModelManager.swift */; };
		A5BC4A9D2DD47CF80048F92E /* CategoryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5BC4A9C2DD47CF80048F92E /* CategoryViewController.swift */; };
		A5BC4A9E2DD47CF80048F92E /* CategoryListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5BC4A9B2DD47CF80048F92E /* CategoryListViewController.swift */; };
		A5CE732A2D8A6AF2009C61F6 /* AIHairCutSettingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE73292D8A6AF2009C61F6 /* AIHairCutSettingView.swift */; };
		A5CE74142D8A6C27009C61F6 /* ZYEExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE74112D8A6C27009C61F6 /* ZYEExtension.swift */; };
		A5CE74152D8A6C27009C61F6 /* ZYETapTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE74122D8A6C27009C61F6 /* ZYETapTool.swift */; };
		A5CE74162D8A6C27009C61F6 /* UIDefault.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE740E2D8A6C27009C61F6 /* UIDefault.swift */; };
		A5CE74172D8A6C27009C61F6 /* PTSWSwftObj.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE740D2D8A6C27009C61F6 /* PTSWSwftObj.swift */; };
		A5CE74182D8A6C27009C61F6 /* ZYEButtonPositionAndSpace.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE74102D8A6C27009C61F6 /* ZYEButtonPositionAndSpace.swift */; };
		A5CE74192D8A6C27009C61F6 /* ZyeButtonEx.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE740F2D8A6C27009C61F6 /* ZyeButtonEx.swift */; };
		A5CE741A2D8A6C27009C61F6 /* PTSWheader.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE740C2D8A6C27009C61F6 /* PTSWheader.swift */; };
		A5CE741B2D8A6C27009C61F6 /* CreatUI.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE740B2D8A6C27009C61F6 /* CreatUI.swift */; };
		A5CE741D2D8A715D009C61F6 /* UIButton+BackgroundState.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE741C2D8A715D009C61F6 /* UIButton+BackgroundState.swift */; };
		A5CE75032D8A7C89009C61F6 /* PopAnimationTool.m in Sources */ = {isa = PBXBuildFile; fileRef = A5CE74FF2D8A7C89009C61F6 /* PopAnimationTool.m */; };
		A5CE75042D8A7C89009C61F6 /* PopView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5CE75012D8A7C89009C61F6 /* PopView.m */; };
		A5CE75062D8A9B75009C61F6 /* AIHairCutTipView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5CE75052D8A9B75009C61F6 /* AIHairCutTipView.swift */; };
		A5CE75092D8AA61D009C61F6 /* PTMImageSliderView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5CE75082D8AA61D009C61F6 /* PTMImageSliderView.m */; };
		A5CE750C2D8AA68C009C61F6 /* UILabel+createLabels.m in Sources */ = {isa = PBXBuildFile; fileRef = A5CE750B2D8AA68C009C61F6 /* UILabel+createLabels.m */; };
		A5CE75102D8AA6DB009C61F6 /* ZYELabel.m in Sources */ = {isa = PBXBuildFile; fileRef = A5CE750F2D8AA6DB009C61F6 /* ZYELabel.m */; };
		A5D2306A2D811EA8007D1911 /* AIHairModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5D230692D811EA8007D1911 /* AIHairModel.swift */; };
		A5DE73BF2E45CC1500E9FEE5 /* DrdpbEP7X5wvNgc-VOPHrp.xccrashpoint in Resources */ = {isa = PBXBuildFile; fileRef = A5DE73BE2E45CC1500E9FEE5 /* DrdpbEP7X5wvNgc-VOPHrp.xccrashpoint */; };
		A5DE73C02E45CC1500E9FEE5 /* Dd-ygnFcfsvUANOm8EOQpA.xccrashpoint in Resources */ = {isa = PBXBuildFile; fileRef = A5DE73BB2E45CC1500E9FEE5 /* Dd-ygnFcfsvUANOm8EOQpA.xccrashpoint */; };
		A5DE73C12E45CC1500E9FEE5 /* Cf8e0w1GBWNMkjzjyfdC8J.xccrashpoint in Resources */ = {isa = PBXBuildFile; fileRef = A5DE73BA2E45CC1500E9FEE5 /* Cf8e0w1GBWNMkjzjyfdC8J.xccrashpoint */; };
		A5DE73C22E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B.xccrashpoint in Resources */ = {isa = PBXBuildFile; fileRef = A5DE73BC2E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B.xccrashpoint */; };
		A5DE73C32E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B_副本.xccrashpoint in Resources */ = {isa = PBXBuildFile; fileRef = A5DE73BD2E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B_副本.xccrashpoint */; };
		A5DE73C42E45CC1500E9FEE5 /* BKpHzAc8Ri8t05shY3RgyH.xccrashpoint in Resources */ = {isa = PBXBuildFile; fileRef = A5DE73B92E45CC1500E9FEE5 /* BKpHzAc8Ri8t05shY3RgyH.xccrashpoint */; };
		A5DE73C62E45D06E00E9FEE5 /* ThreadSafeSnapKit.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5DE73C52E45D06E00E9FEE5 /* ThreadSafeSnapKit.swift */; };
		A5E495C62DD6DED3007814CC /* DiscountPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5E495C52DD6DED2007814CC /* DiscountPopupView.swift */; };
		A5E61D3F2E29EF6A009EDBC4 /* HairEditVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5E61D3E2E29EF6A009EDBC4 /* HairEditVC.swift */; };
		A5E61D412E29EFC9009EDBC4 /* HairEditModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5E61D402E29EFC9009EDBC4 /* HairEditModel.swift */; };
		A5E61D432E29EFE7009EDBC4 /* HairFunctionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5E61D422E29EFE7009EDBC4 /* HairFunctionCell.swift */; };
		A5EBA9E62E2DE93E0049EBD5 /* VolcanoUsageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5EBA9E52E2DE93E0049EBD5 /* VolcanoUsageManager.swift */; };
		A5EBA9E92E2DE96E0049EBD5 /* AIStylistGuidePopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5EBA9E72E2DE96E0049EBD5 /* AIStylistGuidePopupView.swift */; };
		A5EBA9EB2E2DE98F0049EBD5 /* HairEditUsageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5EBA9EA2E2DE98F0049EBD5 /* HairEditUsageManager.swift */; };
		A5EBA9ED2E2DE9AB0049EBD5 /* UsagePurchasePopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5EBA9EC2E2DE9AB0049EBD5 /* UsagePurchasePopupView.swift */; };
		A5EBA9EF2E2E23A50049EBD5 /* DeveloperApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5EBA9EE2E2E23A50049EBD5 /* DeveloperApp.swift */; };
		A5EBA9F12E2E23CD0049EBD5 /* DeveloperAppCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5EBA9F02E2E23CD0049EBD5 /* DeveloperAppCell.swift */; };
		A5F49A0A2E24A9720057B28C /* HXPhotoPicker.bundle in Resources */ = {isa = PBXBuildFile; fileRef = A5F499C12E24A9720057B28C /* HXPhotoPicker.bundle */; };
		A5F49A0B2E24A9720057B28C /* HXVideoEditViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499722E24A9720057B28C /* HXVideoEditViewController.m */; };
		A5F49A0C2E24A9720057B28C /* NSTimer+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4994D2E24A9720057B28C /* NSTimer+HXExtension.m */; };
		A5F49A0D2E24A9720057B28C /* HXPhotoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499BB2E24A9720057B28C /* HXPhotoModel.m */; };
		A5F49A0E2E24A9720057B28C /* UIView+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4995D2E24A9720057B28C /* UIView+HXExtension.m */; };
		A5F49A0F2E24A9720057B28C /* HX_PhotoEditBottomView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4997F2E24A9720057B28C /* HX_PhotoEditBottomView.m */; };
		A5F49A102E24A9720057B28C /* UIImage+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499572E24A9720057B28C /* UIImage+HXExtension.m */; };
		A5F49A112E24A9720057B28C /* PHAsset+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4994F2E24A9720057B28C /* PHAsset+HXExtension.m */; };
		A5F49A122E24A9720057B28C /* HXCollectionView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499D52E24A9720057B28C /* HXCollectionView.m */; };
		A5F49A132E24A9720057B28C /* HXCameraBottomView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499D12E24A9720057B28C /* HXCameraBottomView.m */; };
		A5F49A142E24A9720057B28C /* HXAlbumlistView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499CF2E24A9720057B28C /* HXAlbumlistView.m */; };
		A5F49A152E24A9720057B28C /* HXCircleProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499D32E24A9720057B28C /* HXCircleProgressView.m */; };
		A5F49A162E24A9720057B28C /* HXPhotoEditMosaicView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4999D2E24A9720057B28C /* HXPhotoEditMosaicView.m */; };
		A5F49A172E24A9720057B28C /* HXPhotoPreviewImageViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499E52E24A9720057B28C /* HXPhotoPreviewImageViewCell.m */; };
		A5F49A182E24A9720057B28C /* NSString+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4994B2E24A9720057B28C /* NSString+HXExtension.m */; };
		A5F49A192E24A9720057B28C /* HXPhotoEditSplashView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499A32E24A9720057B28C /* HXPhotoEditSplashView.m */; };
		A5F49A1A2E24A9720057B28C /* HXAlbumListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499622E24A9720057B28C /* HXAlbumListViewController.m */; };
		A5F49A1B2E24A9720057B28C /* HXPhotoEditImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499992E24A9720057B28C /* HXPhotoEditImageView.m */; };
		A5F49A1C2E24A9720057B28C /* HXPhotoTools.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F49A062E24A9720057B28C /* HXPhotoTools.m */; };
		A5F49A1D2E24A9720057B28C /* HXPhotoEditChartletPreviewView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499872E24A9720057B28C /* HXPhotoEditChartletPreviewView.m */; };
		A5F49A1E2E24A9720057B28C /* HXPhotoEditClippingToolBar.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499892E24A9720057B28C /* HXPhotoEditClippingToolBar.m */; };
		A5F49A1F2E24A9720057B28C /* HXPhotoEditStickerItem.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499A52E24A9720057B28C /* HXPhotoEditStickerItem.m */; };
		A5F49A202E24A9720057B28C /* HXCustomCameraViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499662E24A9720057B28C /* HXCustomCameraViewController.m */; };
		A5F49A212E24A9720057B28C /* HXPhotoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F49A032E24A9720057B28C /* HXPhotoManager.m */; };
		A5F49A222E24A9720057B28C /* HXPhotoEditStickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499AD2E24A9720057B28C /* HXPhotoEditStickerView.m */; };
		A5F49A232E24A9720057B28C /* UIButton+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499512E24A9720057B28C /* UIButton+HXExtension.m */; };
		A5F49A242E24A9720057B28C /* HXPhotoPreviewBottomView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499E32E24A9720057B28C /* HXPhotoPreviewBottomView.m */; };
		A5F49A252E24A9720057B28C /* HXPhotoLimitView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499E12E24A9720057B28C /* HXPhotoLimitView.m */; };
		A5F49A262E24A9720057B28C /* HXPhotoCommon.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499FE2E24A9720057B28C /* HXPhotoCommon.m */; };
		A5F49A272E24A9720057B28C /* HXCustomCameraController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499642E24A9720057B28C /* HXCustomCameraController.m */; };
		A5F49A282E24A9720057B28C /* HXPhotoEditDrawView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4998B2E24A9720057B28C /* HXPhotoEditDrawView.m */; };
		A5F49A292E24A9720057B28C /* HXPhotoClippingView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499812E24A9720057B28C /* HXPhotoClippingView.m */; };
		A5F49A2A2E24A9720057B28C /* HXPhotoEditStickerTrashView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499AB2E24A9720057B28C /* HXPhotoEditStickerTrashView.m */; };
		A5F49A2B2E24A9720057B28C /* NSBundle+HXPhotoPicker.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499472E24A9720057B28C /* NSBundle+HXPhotoPicker.m */; };
		A5F49A2C2E24A9720057B28C /* HXPhotoViewPresentTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499CA2E24A9720057B28C /* HXPhotoViewPresentTransition.m */; };
		A5F49A2D2E24A9720057B28C /* HXPreviewVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499F92E24A9720057B28C /* HXPreviewVideoView.m */; };
		A5F49A2E2E24A9720057B28C /* HXPhotoEditResizeControl.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4999F2E24A9720057B28C /* HXPhotoEditResizeControl.m */; };
		A5F49A2F2E24A9720057B28C /* HXAssetManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499FC2E24A9720057B28C /* HXAssetManager.m */; };
		A5F49A302E24A9720057B28C /* HXPhotoEditChartletContentViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499832E24A9720057B28C /* HXPhotoEditChartletContentViewCell.m */; };
		A5F49A312E24A9720057B28C /* HXCustomCollectionReusableView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499D72E24A9720057B28C /* HXCustomCollectionReusableView.m */; };
		A5F49A322E24A9720057B28C /* HXPhotoPreviewViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4996E2E24A9720057B28C /* HXPhotoPreviewViewController.m */; };
		A5F49A332E24A9720057B28C /* HXPhotoViewTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499CC2E24A9720057B28C /* HXPhotoViewTransition.m */; };
		A5F49A342E24A9720057B28C /* UIFont+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499552E24A9720057B28C /* UIFont+HXExtension.m */; };
		A5F49A352E24A9720057B28C /* HXPhotoPersentInteractiveTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499C82E24A9720057B28C /* HXPhotoPersentInteractiveTransition.m */; };
		A5F49A362E24A9720057B28C /* HXPhotoView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499EF2E24A9720057B28C /* HXPhotoView.m */; };
		A5F49A372E24A9720057B28C /* HXPhotoEditingView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4999B2E24A9720057B28C /* HXPhotoEditingView.m */; };
		A5F49A382E24A9720057B28C /* HXPhotoEditStickerItemContentView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499A72E24A9720057B28C /* HXPhotoEditStickerItemContentView.m */; };
		A5F49A392E24A9720057B28C /* HXPhotoConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F49A002E24A9720057B28C /* HXPhotoConfiguration.m */; };
		A5F49A3A2E24A9720057B28C /* HXPhotoEditSplashMaskLayer.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499A12E24A9720057B28C /* HXPhotoEditSplashMaskLayer.m */; };
		A5F49A3B2E24A9720057B28C /* HXCustomNavigationController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499682E24A9720057B28C /* HXCustomNavigationController.m */; };
		A5F49A3C2E24A9720057B28C /* HXPhotoEditChartletModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4997A2E24A9720057B28C /* HXPhotoEditChartletModel.m */; };
		A5F49A3D2E24A9720057B28C /* HXPhotoEditGraffitiColorModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4997C2E24A9720057B28C /* HXPhotoEditGraffitiColorModel.m */; };
		A5F49A3E2E24A9720057B28C /* UIColor+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499532E24A9720057B28C /* UIColor+HXExtension.m */; };
		A5F49A3F2E24A9720057B28C /* HXAlbumModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499B72E24A9720057B28C /* HXAlbumModel.m */; };
		A5F49A402E24A9720057B28C /* NSArray+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499452E24A9720057B28C /* NSArray+HXExtension.m */; };
		A5F49A412E24A9720057B28C /* HXPhotoEditGridLayer.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499932E24A9720057B28C /* HXPhotoEditGridLayer.m */; };
		A5F49A422E24A9720057B28C /* HX_PhotoEditViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499752E24A9720057B28C /* HX_PhotoEditViewController.m */; };
		A5F49A432E24A9720057B28C /* HXMECancelBlock.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499782E24A9720057B28C /* HXMECancelBlock.m */; };
		A5F49A442E24A9720057B28C /* HXPhotoViewFlowLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499F12E24A9720057B28C /* HXPhotoViewFlowLayout.m */; };
		A5F49A452E24A9720057B28C /* HXPhotoEditGraffitiColorSizeView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4998D2E24A9720057B28C /* HXPhotoEditGraffitiColorSizeView.m */; };
		A5F49A462E24A9720057B28C /* HXCustomAssetModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499B92E24A9720057B28C /* HXCustomAssetModel.m */; };
		A5F49A472E24A9720057B28C /* HXPreviewLivePhotoView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499F72E24A9720057B28C /* HXPreviewLivePhotoView.m */; };
		A5F49A482E24A9720057B28C /* HXPhotoPreviewViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499EB2E24A9720057B28C /* HXPhotoPreviewViewCell.m */; };
		A5F49A492E24A9720057B28C /* HXPhotoEditGridView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499972E24A9720057B28C /* HXPhotoEditGridView.m */; };
		A5F49A4A2E24A9720057B28C /* HXFullScreenCameraPlayView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499DB2E24A9720057B28C /* HXFullScreenCameraPlayView.m */; };
		A5F49A4B2E24A9720057B28C /* NSDate+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499492E24A9720057B28C /* NSDate+HXExtension.m */; };
		A5F49A4C2E24A9720057B28C /* HXPhotoEditConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499B42E24A9720057B28C /* HXPhotoEditConfiguration.m */; };
		A5F49A4D2E24A9720057B28C /* HXPhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499702E24A9720057B28C /* HXPhotoViewController.m */; };
		A5F49A4E2E24A9720057B28C /* HXPhotoEditViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4996C2E24A9720057B28C /* HXPhotoEditViewController.m */; };
		A5F49A4F2E24A9720057B28C /* HXPreviewImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499F52E24A9720057B28C /* HXPreviewImageView.m */; };
		A5F49A502E24A9720057B28C /* HXPhotoPreviewLivePhotoCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499E72E24A9720057B28C /* HXPhotoPreviewLivePhotoCell.m */; };
		A5F49A512E24A9720057B28C /* HXPhotoEditChartletListView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499852E24A9720057B28C /* HXPhotoEditChartletListView.m */; };
		A5F49A522E24A9720057B28C /* HXPhotoInteractiveTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499C62E24A9720057B28C /* HXPhotoInteractiveTransition.m */; };
		A5F49A532E24A9720057B28C /* HXPhotoBottomSelectView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499DD2E24A9720057B28C /* HXPhotoBottomSelectView.m */; };
		A5F49A542E24A9720057B28C /* UIViewController+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4995F2E24A9720057B28C /* UIViewController+HXExtension.m */; };
		A5F49A552E24A9720057B28C /* HXPhotoEditGraffitiColorView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4998F2E24A9720057B28C /* HXPhotoEditGraffitiColorView.m */; };
		A5F49A562E24A9720057B28C /* HXPhotoCustomNavigationBar.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499DF2E24A9720057B28C /* HXPhotoCustomNavigationBar.m */; };
		A5F49A572E24A9720057B28C /* HXPreviewContentView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499F32E24A9720057B28C /* HXPreviewContentView.m */; };
		A5F49A582E24A9720057B28C /* HXPhotoSubViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499ED2E24A9720057B28C /* HXPhotoSubViewCell.m */; };
		A5F49A592E24A9720057B28C /* UIImageView+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499592E24A9720057B28C /* UIImageView+HXExtension.m */; };
		A5F49A5A2E24A9720057B28C /* HXPhotoEditGridMaskLayer.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499952E24A9720057B28C /* HXPhotoEditGridMaskLayer.m */; };
		A5F49A5B2E24A9720057B28C /* HXPhotoEditGraffitiColorViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499912E24A9720057B28C /* HXPhotoEditGraffitiColorViewCell.m */; };
		A5F49A5C2E24A9720057B28C /* HXPhotoEditStickerItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499A92E24A9720057B28C /* HXPhotoEditStickerItemView.m */; };
		A5F49A5D2E24A9720057B28C /* UILabel+HXExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4995B2E24A9720057B28C /* UILabel+HXExtension.m */; };
		A5F49A5E2E24A9720057B28C /* HXPhotoEdit.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499B22E24A9720057B28C /* HXPhotoEdit.m */; };
		A5F49A5F2E24A9720057B28C /* HXPhotoEditTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499AF2E24A9720057B28C /* HXPhotoEditTextView.m */; };
		A5F49A602E24A9720057B28C /* HXPhotoEditTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499C42E24A9720057B28C /* HXPhotoEditTransition.m */; };
		A5F49A612E24A9720057B28C /* HXPhotoPreviewVideoViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499E92E24A9720057B28C /* HXPhotoPreviewVideoViewCell.m */; };
		A5F49A622E24A9720057B28C /* HXCustomPreviewView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499D92E24A9720057B28C /* HXCustomPreviewView.m */; };
		A5F49A632E24A9720057B28C /* HXPhoto3DTouchViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F4996A2E24A9720057B28C /* HXPhoto3DTouchViewController.m */; };
		A5F49A642E24A9720057B28C /* HXPickerResult.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F499BF2E24A9720057B28C /* HXPickerResult.m */; };
		A5F49A672E24A99D0057B28C /* PTMPhotoPickerHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = A5F49A662E24A99D0057B28C /* PTMPhotoPickerHelper.m */; };
		A5F49A6A2E24ACA90057B28C /* NailCameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5F49A682E24ACA90057B28C /* NailCameraViewController.swift */; };
		A5F49A6E2E24BCB10057B28C /* NailStyleViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5F49A6C2E24BCB10057B28C /* NailStyleViewController.swift */; };
		A5F49A6F2E24BCB10057B28C /* NailProcessViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5F49A6B2E24BCB10057B28C /* NailProcessViewController.swift */; };
		A5F49A712E24BCE70057B28C /* NailModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5F49A702E24BCE70057B28C /* NailModel.swift */; };
		A5F49A732E24BD0C0057B28C /* VolcanoEngineAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5F49A722E24BD0C0057B28C /* VolcanoEngineAPI.swift */; };
		A5FBC2182DD1942E0015329D /* APPStoreIAPObserver.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5FBC2162DD1942E0015329D /* APPStoreIAPObserver.swift */; };
		A5FBC2192DD1942E0015329D /* APPMakeStoreIAPManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5FBC2152DD1942E0015329D /* APPMakeStoreIAPManager.swift */; };
		A5FBC45B2DD195460015329D /* VipViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5FBC45A2DD195460015329D /* VipViewController.swift */; };
		A5FBC45D2DD196B20015329D /* STbaseVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5FBC45C2DD196B20015329D /* STbaseVC.swift */; };
		A5FBC4602DD196E70015329D /* UpdateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5FBC45E2DD196E70015329D /* UpdateManager.swift */; };
		B5841743B2E57E154C5C9424 /* Pods_HairCut.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F0316C360453D1D95C8A6E35 /* Pods_HairCut.framework */; };
		C4992D332C5DC65500241D59 /* FaceShapeResultCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4992D322C5DC65500241D59 /* FaceShapeResultCell.swift */; };
		C4992D352C5DC9AB00241D59 /* FaceShapeTestResultView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4992D342C5DC9AB00241D59 /* FaceShapeTestResultView.swift */; };
		C4992D372C5DCA3400241D59 /* FaceShapeSliderResultView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4992D362C5DCA3400241D59 /* FaceShapeSliderResultView.swift */; };
		C4B1537D2C67D5F700E7420C /* icon3.png in Resources */ = {isa = PBXBuildFile; fileRef = C4B1537A2C67D5F700E7420C /* icon3.png */; };
		C4B1537E2C67D5F700E7420C /* icon2.png in Resources */ = {isa = PBXBuildFile; fileRef = C4B1537B2C67D5F700E7420C /* icon2.png */; };
		C4B1537F2C67D5F700E7420C /* icon1.png in Resources */ = {isa = PBXBuildFile; fileRef = C4B1537C2C67D5F700E7420C /* icon1.png */; };
		C4C734EF2C5F138000354F24 /* Dictionary+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4C734EE2C5F138000354F24 /* Dictionary+Extension.swift */; };
		C4C734F12C5F146200354F24 /* Array+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4C734F02C5F146200354F24 /* Array+Extension.swift */; };
		C4C734F32C5F15F700354F24 /* WebSocketTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4C734F22C5F15F700354F24 /* WebSocketTool.swift */; };
		C4C734F52C5F383B00354F24 /* PickImageTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4C734F42C5F383B00354F24 /* PickImageTool.swift */; };
		C4DA01582C5FBE160058B6F7 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = C4DA015A2C5FBE160058B6F7 /* InfoPlist.strings */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1FF9CED795EAA10E681585C5 /* Pods-HairCut.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HairCut.release.xcconfig"; path = "Target Support Files/Pods-HairCut/Pods-HairCut.release.xcconfig"; sourceTree = "<group>"; };
		4D0E33C4E63B6D2D24F68AA6 /* Pods-HairCut.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HairCut.debug.xcconfig"; path = "Target Support Files/Pods-HairCut/Pods-HairCut.debug.xcconfig"; sourceTree = "<group>"; };
		79349D722CEACE3F00ED9853 /* HDColorPickerHelpers.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HDColorPickerHelpers.swift; sourceTree = "<group>"; };
		79349D742CEAD24800ED9853 /* HDColorPickerLayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HDColorPickerLayer.swift; sourceTree = "<group>"; };
		79349D762CEAD42800ED9853 /* HDPointView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HDPointView.swift; sourceTree = "<group>"; };
		79349D782CEAD4A600ED9853 /* HDColorPickerBarLayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HDColorPickerBarLayer.swift; sourceTree = "<group>"; };
		79349D7A2CEAD5CB00ED9853 /* HDColorPickerBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HDColorPickerBarView.swift; sourceTree = "<group>"; };
		79349D7C2CEAD6C300ED9853 /* HDColorPickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HDColorPickerView.swift; sourceTree = "<group>"; };
		79349D7E2CEADE2100ED9853 /* HDColorPalette.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HDColorPalette.swift; sourceTree = "<group>"; };
		79349D802CEADEA900ED9853 /* HDColorCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HDColorCell.swift; sourceTree = "<group>"; };
		79349D832CEADF5400ED9853 /* HDEditManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HDEditManager.swift; sourceTree = "<group>"; };
		79349D872CEAF10500ED9853 /* HairStyleEditColorCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleEditColorCell.swift; sourceTree = "<group>"; };
		793B25222C58814D005492DF /* HomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeView.swift; sourceTree = "<group>"; };
		793B25242C589440005492DF /* UIColor+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIColor+Extension.swift"; sourceTree = "<group>"; };
		793B25262C58BDA3005492DF /* SettingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingView.swift; sourceTree = "<group>"; };
		793B25282C58C4F8005492DF /* WelcomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WelcomeView.swift; sourceTree = "<group>"; };
		793B252A2C58CC20005492DF /* UIView+gradient.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIView+gradient.swift"; sourceTree = "<group>"; };
		793B252C2C58E714005492DF /* SubscribeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SubscribeView.swift; sourceTree = "<group>"; };
		794B89022C6B3DF3000CE954 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		79578DEF2C61B2DF00387CD5 /* 灰色渐变卷发.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = "灰色渐变卷发.jpg"; sourceTree = "<group>"; };
		79578DF02C61B2DF00387CD5 /* 洛丽塔发型.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = "洛丽塔发型.jpg"; sourceTree = "<group>"; };
		79578DF12C61B2DF00387CD5 /* 纹理烫.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = "纹理烫.jpg"; sourceTree = "<group>"; };
		79578DF22C61B2DF00387CD5 /* 挑染编织发.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = "挑染编织发.jpg"; sourceTree = "<group>"; };
		79578DF32C61B2DF00387CD5 /* 羊毛卷.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = "羊毛卷.jpg"; sourceTree = "<group>"; };
		79578DF42C61B2DF00387CD5 /* 爆炸头.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = "爆炸头.jpg"; sourceTree = "<group>"; };
		79578DFB2C61B55800387CD5 /* PopularHairCutData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PopularHairCutData.swift; sourceTree = "<group>"; };
		79578DFD2C6207E700387CD5 /* FaceTestData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FaceTestData.swift; sourceTree = "<group>"; };
		796A431F2C6C57AC00E724C5 /* LanguageTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LanguageTool.swift; sourceTree = "<group>"; };
		796D03472CE6DC1200046500 /* HairStyleMainListHairCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleMainListHairCell.swift; sourceTree = "<group>"; };
		796D03492CE6E35C00046500 /* HairStyleEditVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleEditVC.swift; sourceTree = "<group>"; };
		796D034E2CE6E55E00046500 /* HairStyleEditHairChooseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleEditHairChooseView.swift; sourceTree = "<group>"; };
		796D03502CE6E59B00046500 /* HairStyleEditRightAreaView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleEditRightAreaView.swift; sourceTree = "<group>"; };
		796D03522CE6E5C300046500 /* HairStyleEditColorChooseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleEditColorChooseView.swift; sourceTree = "<group>"; };
		796D03542CE6EEB000046500 /* HairStyleNavRightActionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleNavRightActionView.swift; sourceTree = "<group>"; };
		796D03562CE72B7400046500 /* HairStyleEditHairChooseCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleEditHairChooseCell.swift; sourceTree = "<group>"; };
		796D03582CE747B500046500 /* HaitStyleHairData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HaitStyleHairData.swift; sourceTree = "<group>"; };
		7971E0B82C578042002381F1 /* 发型测试.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "发型测试.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		7971E0BB2C578042002381F1 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7971E0C42C578044002381F1 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		7971E0C72C578044002381F1 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		7971E0C92C578044002381F1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		7971E0D32C57853E002381F1 /* AppLoad.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppLoad.swift; sourceTree = "<group>"; };
		7971E0D72C578646002381F1 /* WelcomeVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WelcomeVC.swift; sourceTree = "<group>"; };
		7971E0DC2C57A35E002381F1 /* TabbarVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabbarVC.swift; sourceTree = "<group>"; };
		7971E0DE2C57A3D3002381F1 /* SettingVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingVC.swift; sourceTree = "<group>"; };
		7971E0E02C57A436002381F1 /* HomeVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeVC.swift; sourceTree = "<group>"; };
		7971E0E52C57A91D002381F1 /* String+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "String+Extension.swift"; sourceTree = "<group>"; };
		7971E0E92C57A97D002381F1 /* HttpTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HttpTool.swift; sourceTree = "<group>"; };
		7971E0EB2C57A9F9002381F1 /* Const.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Const.swift; sourceTree = "<group>"; };
		797215122CE5CAC5001B82BE /* HairStyleVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleVC.swift; sourceTree = "<group>"; };
		797215152CE5E337001B82BE /* HairStyleMainListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairStyleMainListView.swift; sourceTree = "<group>"; };
		7974917E2C6DEE4300F5D89C /* icon4.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon4.png; sourceTree = "<group>"; };
		7974917F2C6DEE4300F5D89C /* icon5.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon5.png; sourceTree = "<group>"; };
		797F80D82D23872000EEBDD8 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F80D92D23872000EEBDD8 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F80DA2D23872000EEBDD8 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/Localizable.strings"; sourceTree = "<group>"; };
		797F80DB2D2387B200EEBDD8 /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F80DC2D2387B200EEBDD8 /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F80DD2D2387B200EEBDD8 /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F80DF2D23CFDB00EEBDD8 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F80E02D23CFDB00EEBDD8 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F80E12D23CFDB00EEBDD8 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F80E22D23D8A100EEBDD8 /* en-GB */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-GB"; path = "en-GB.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F80E32D23D8A100EEBDD8 /* en-GB */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-GB"; path = "en-GB.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F80E42D23D8A100EEBDD8 /* en-GB */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-GB"; path = "en-GB.lproj/Localizable.strings"; sourceTree = "<group>"; };
		797F80E52D23D91500EEBDD8 /* en-CA */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-CA"; path = "en-CA.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F80E62D23D91500EEBDD8 /* en-CA */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-CA"; path = "en-CA.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F80E72D23D91500EEBDD8 /* en-CA */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-CA"; path = "en-CA.lproj/Localizable.strings"; sourceTree = "<group>"; };
		797F80E82D23D9CE00EEBDD8 /* en-US */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-US"; path = "en-US.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F80E92D23D9CE00EEBDD8 /* en-US */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-US"; path = "en-US.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F80EA2D23D9CE00EEBDD8 /* en-US */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-US"; path = "en-US.lproj/Localizable.strings"; sourceTree = "<group>"; };
		797F80EB2D23DA9800EEBDD8 /* en-AU */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-AU"; path = "en-AU.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F80EC2D23DA9800EEBDD8 /* en-AU */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-AU"; path = "en-AU.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F80ED2D23DA9800EEBDD8 /* en-AU */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "en-AU"; path = "en-AU.lproj/Localizable.strings"; sourceTree = "<group>"; };
		797F80EE2D23DB9F00EEBDD8 /* it */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = it; path = it.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F80EF2D23DB9F00EEBDD8 /* it */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = it; path = it.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F80F02D23DB9F00EEBDD8 /* it */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = it; path = it.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F80F12D23DC3F00EEBDD8 /* hu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hu; path = hu.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F80F22D23DC3F00EEBDD8 /* hu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hu; path = hu.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F80F32D23DC3F00EEBDD8 /* hu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hu; path = hu.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F80F42D23DC9E00EEBDD8 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F80F52D23DC9E00EEBDD8 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F80F62D23DC9E00EEBDD8 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F80F72D23DD1F00EEBDD8 /* da */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = da; path = da.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F80F82D23DD1F00EEBDD8 /* da */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = da; path = da.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F80F92D23DD1F00EEBDD8 /* da */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = da; path = da.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F80FA2D23DD7800EEBDD8 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F80FB2D23DD7800EEBDD8 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F80FC2D23DD7800EEBDD8 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F80FD2D23DDC600EEBDD8 /* ru */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ru; path = ru.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F80FE2D23DDC600EEBDD8 /* ru */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ru; path = ru.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F80FF2D23DDC600EEBDD8 /* ru */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ru; path = ru.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F81002D23DDFF00EEBDD8 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F81012D23DDFF00EEBDD8 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F81022D23DDFF00EEBDD8 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F81032D23DE9400EEBDD8 /* fr-CA */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "fr-CA"; path = "fr-CA.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F81042D23DE9400EEBDD8 /* fr-CA */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "fr-CA"; path = "fr-CA.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F81052D23DE9400EEBDD8 /* fr-CA */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "fr-CA"; path = "fr-CA.lproj/Localizable.strings"; sourceTree = "<group>"; };
		797F81062D23DEE700EEBDD8 /* ms */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ms; path = ms.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F81072D23DEE700EEBDD8 /* ms */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ms; path = ms.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F81082D23DEE700EEBDD8 /* ms */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ms; path = ms.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F81092D23DF3900EEBDD8 /* pt-BR */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-BR"; path = "pt-BR.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F810A2D23DF3A00EEBDD8 /* pt-BR */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-BR"; path = "pt-BR.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F810B2D23DF3A00EEBDD8 /* pt-BR */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-BR"; path = "pt-BR.lproj/Localizable.strings"; sourceTree = "<group>"; };
		797F810C2D23DF9B00EEBDD8 /* pt-PT */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-PT"; path = "pt-PT.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F810D2D23DF9B00EEBDD8 /* pt-PT */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-PT"; path = "pt-PT.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F810E2D23DF9B00EEBDD8 /* pt-PT */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-PT"; path = "pt-PT.lproj/Localizable.strings"; sourceTree = "<group>"; };
		797F810F2D23E00100EEBDD8 /* th */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = th; path = th.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		797F81102D23E00100EEBDD8 /* th */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = th; path = th.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		797F81112D23E00100EEBDD8 /* th */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = th; path = th.lproj/Localizable.strings; sourceTree = "<group>"; };
		797F81122D23E07F00EEBDD8 /* es-MX */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "es-MX"; path = "es-MX.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F81132D23E07F00EEBDD8 /* es-MX */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "es-MX"; path = "es-MX.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F81142D23E07F00EEBDD8 /* es-MX */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "es-MX"; path = "es-MX.lproj/Localizable.strings"; sourceTree = "<group>"; };
		797F81152D23E0E400EEBDD8 /* es-ES */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "es-ES"; path = "es-ES.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		797F81162D23E0E400EEBDD8 /* es-ES */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "es-ES"; path = "es-ES.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		797F81172D23E0E400EEBDD8 /* es-ES */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "es-ES"; path = "es-ES.lproj/Localizable.strings"; sourceTree = "<group>"; };
		798E3CE72C65A1B000DE4093 /* FaceTestCellData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FaceTestCellData.swift; sourceTree = "<group>"; };
		798F145D2C5C68F80029A34D /* AIHairResultVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairResultVC.swift; sourceTree = "<group>"; };
		798F145F2C5C69A00029A34D /* AIHairCutResultView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairCutResultView.swift; sourceTree = "<group>"; };
		798F14612C5C6F5A0029A34D /* UIViewController+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIViewController+Extension.swift"; sourceTree = "<group>"; };
		798F14632C5C7AA80029A34D /* FaceShapeTestView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FaceShapeTestView.swift; sourceTree = "<group>"; };
		798F14652C5C90A30029A34D /* FaceShapeTestCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FaceShapeTestCell.swift; sourceTree = "<group>"; };
		798F14692C5CBCEA0029A34D /* AIHairCutUploadButtonView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairCutUploadButtonView.swift; sourceTree = "<group>"; };
		798F146B2C5CBD150029A34D /* HairCutChooseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairCutChooseView.swift; sourceTree = "<group>"; };
		798F146D2C5CBD490029A34D /* HairCutAreaView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairCutAreaView.swift; sourceTree = "<group>"; };
		799686CE2CED728A00971950 /* HairCut-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "HairCut-Bridging-Header.h"; sourceTree = "<group>"; };
		799686CF2CED728A00971950 /* HairCutMalloc.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HairCutMalloc.h; sourceTree = "<group>"; };
		799686D02CED728A00971950 /* HairCutMalloc.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = HairCutMalloc.c; sourceTree = "<group>"; };
		799686D22CED7BDB00971950 /* HDImageTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HDImageTool.h; sourceTree = "<group>"; };
		799686D32CED7BDB00971950 /* HDImageTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HDImageTool.m; sourceTree = "<group>"; };
		799B6E252C6B346400FC2015 /* UserDefaultsTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserDefaultsTool.swift; sourceTree = "<group>"; };
		79AD56FE2C5A490A00A06A2F /* HotHairCutCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HotHairCutCollectionViewCell.swift; sourceTree = "<group>"; };
		79AD57002C5A4E6700A06A2F /* AboutUsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutUsVC.swift; sourceTree = "<group>"; };
		79AD57022C5A4E7700A06A2F /* AboutUsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutUsView.swift; sourceTree = "<group>"; };
		79C007402C59CD55006521B7 /* Enum.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Enum.swift; sourceTree = "<group>"; };
		79C007442C5A33CF006521B7 /* LaunchScreenVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaunchScreenVC.swift; sourceTree = "<group>"; };
		79C007462C5A33E5006521B7 /* LaunchScreenView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaunchScreenView.swift; sourceTree = "<group>"; };
		79C733BC2C5B6743002FBCE7 /* SettingIconViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingIconViewCell.swift; sourceTree = "<group>"; };
		79C733C32C5B7029002FBCE7 /* Print+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Print+Extension.swift"; sourceTree = "<group>"; };
		79C733C52C5B71DB002FBCE7 /* AIHairCutVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairCutVC.swift; sourceTree = "<group>"; };
		79C733C72C5B7202002FBCE7 /* FaceShapeTestVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FaceShapeTestVC.swift; sourceTree = "<group>"; };
		79C733C92C5B736A002FBCE7 /* AIHairCutView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairCutView.swift; sourceTree = "<group>"; };
		79C733CC2C5B9AEC002FBCE7 /* HairCutImageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairCutImageCell.swift; sourceTree = "<group>"; };
		79C733CE2C5B9B78002FBCE7 /* HairCutTextCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairCutTextCell.swift; sourceTree = "<group>"; };
		79D8642B2CB4E020009DEB91 /* GoodRepulationTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoodRepulationTool.swift; sourceTree = "<group>"; };
		79EEBBE42C60A4F100905018 /* UIImage+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIImage+Extension.swift"; sourceTree = "<group>"; };
		79EEBBE62C60ADA100905018 /* AIHairCutData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairCutData.swift; sourceTree = "<group>"; };
		79EF1AA72C6620C500B2D555 /* ImageTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageTool.swift; sourceTree = "<group>"; };
		79EF1AAA2C66279300B2D555 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		79EF1AAB2C66279300B2D555 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		79EF1AAD2C6628F400B2D555 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		79EF1AAF2C6628F800B2D555 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		79F8FC3A2C634C9F0054A742 /* FaceTestResultImageMatch.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = FaceTestResultImageMatch.json; sourceTree = "<group>"; };
		79F8FC3E2C6360C30054A742 /* FaceTestResult.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = FaceTestResult.json; sourceTree = "<group>"; };
		A5171D8F2E25E88100C9233F /* authpack.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = authpack.h; sourceTree = "<group>"; };
		A5171D902E25E92700C9233F /* PTMFilterHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PTMFilterHelper.h; sourceTree = "<group>"; };
		A5171D912E25E92700C9233F /* PTMFilterHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PTMFilterHelper.m; sourceTree = "<group>"; };
		A5171D962E25EF3E00C9233F /* beauty.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = beauty.json; sourceTree = "<group>"; };
		A5171D982E25F03400C9233F /* BeautyParameter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BeautyParameter.swift; sourceTree = "<group>"; };
		A5171D9A2E25F42B00C9233F /* PortraitBeautyVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PortraitBeautyVC.swift; sourceTree = "<group>"; };
		A5171D9E2E26030100C9233F /* BeautyEditVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BeautyEditVC.swift; sourceTree = "<group>"; };
		A5171DA02E26034B00C9233F /* BeautyParameterCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BeautyParameterCell.swift; sourceTree = "<group>"; };
		A5171DA22E26057900C9233F /* BeautyFilterManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BeautyFilterManager.swift; sourceTree = "<group>"; };
		A5171F7D2E262F5E00C9233F /* FURenderKit快速集成文档.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "FURenderKit快速集成文档.md"; sourceTree = "<group>"; };
		A5556B642DCF393D00009F7E /* AreaSelectViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AreaSelectViewController.swift; sourceTree = "<group>"; };
		A5556B652DCF393D00009F7E /* BrushPreviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BrushPreviewView.swift; sourceTree = "<group>"; };
		A5556B662DCF393D00009F7E /* DrawView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DrawView.swift; sourceTree = "<group>"; };
		A5556B672DCF393D00009F7E /* PTMPaintView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PTMPaintView.swift; sourceTree = "<group>"; };
		A59657792D3A13B1003DFD2E /* RSA.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RSA.h; sourceTree = "<group>"; };
		A596577A2D3A13B1003DFD2E /* RSA.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RSA.m; sourceTree = "<group>"; };
		A596577B2D3A13B1003DFD2E /* UIImage+Base64.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Base64.h"; sourceTree = "<group>"; };
		A596577C2D3A13B1003DFD2E /* UIImage+Base64.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Base64.m"; sourceTree = "<group>"; };
		A59659C22D3A47F2003DFD2E /* AIHairJSON.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairJSON.swift; sourceTree = "<group>"; };
		A59659C42D3A483C003DFD2E /* input.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = input.json; sourceTree = "<group>"; };
		A5B678572D8BA5AC009F5A53 /* MyAdManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyAdManager.swift; sourceTree = "<group>"; };
		A5BC4A992DD47CC50048F92E /* TagBasedHairModelManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagBasedHairModelManager.swift; sourceTree = "<group>"; };
		A5BC4A9B2DD47CF80048F92E /* CategoryListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoryListViewController.swift; sourceTree = "<group>"; };
		A5BC4A9C2DD47CF80048F92E /* CategoryViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoryViewController.swift; sourceTree = "<group>"; };
		A5CE73292D8A6AF2009C61F6 /* AIHairCutSettingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairCutSettingView.swift; sourceTree = "<group>"; };
		A5CE740B2D8A6C27009C61F6 /* CreatUI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreatUI.swift; sourceTree = "<group>"; };
		A5CE740C2D8A6C27009C61F6 /* PTSWheader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PTSWheader.swift; sourceTree = "<group>"; };
		A5CE740D2D8A6C27009C61F6 /* PTSWSwftObj.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PTSWSwftObj.swift; sourceTree = "<group>"; };
		A5CE740E2D8A6C27009C61F6 /* UIDefault.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIDefault.swift; sourceTree = "<group>"; };
		A5CE740F2D8A6C27009C61F6 /* ZyeButtonEx.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZyeButtonEx.swift; sourceTree = "<group>"; };
		A5CE74102D8A6C27009C61F6 /* ZYEButtonPositionAndSpace.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZYEButtonPositionAndSpace.swift; sourceTree = "<group>"; };
		A5CE74112D8A6C27009C61F6 /* ZYEExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZYEExtension.swift; sourceTree = "<group>"; };
		A5CE74122D8A6C27009C61F6 /* ZYETapTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZYETapTool.swift; sourceTree = "<group>"; };
		A5CE741C2D8A715D009C61F6 /* UIButton+BackgroundState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIButton+BackgroundState.swift"; sourceTree = "<group>"; };
		A5CE74FE2D8A7C89009C61F6 /* PopAnimationTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PopAnimationTool.h; sourceTree = "<group>"; };
		A5CE74FF2D8A7C89009C61F6 /* PopAnimationTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PopAnimationTool.m; sourceTree = "<group>"; };
		A5CE75002D8A7C89009C61F6 /* PopView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PopView.h; sourceTree = "<group>"; };
		A5CE75012D8A7C89009C61F6 /* PopView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PopView.m; sourceTree = "<group>"; };
		A5CE75052D8A9B75009C61F6 /* AIHairCutTipView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairCutTipView.swift; sourceTree = "<group>"; };
		A5CE75072D8AA61D009C61F6 /* PTMImageSliderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PTMImageSliderView.h; sourceTree = "<group>"; };
		A5CE75082D8AA61D009C61F6 /* PTMImageSliderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PTMImageSliderView.m; sourceTree = "<group>"; };
		A5CE750A2D8AA68C009C61F6 /* UILabel+createLabels.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UILabel+createLabels.h"; sourceTree = "<group>"; };
		A5CE750B2D8AA68C009C61F6 /* UILabel+createLabels.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UILabel+createLabels.m"; sourceTree = "<group>"; };
		A5CE750E2D8AA6DB009C61F6 /* ZYELabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZYELabel.h; sourceTree = "<group>"; };
		A5CE750F2D8AA6DB009C61F6 /* ZYELabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZYELabel.m; sourceTree = "<group>"; };
		A5D230692D811EA8007D1911 /* AIHairModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIHairModel.swift; sourceTree = "<group>"; };
		A5DE73B92E45CC1500E9FEE5 /* BKpHzAc8Ri8t05shY3RgyH.xccrashpoint */ = {isa = PBXFileReference; lastKnownFileType = wrapper; path = BKpHzAc8Ri8t05shY3RgyH.xccrashpoint; sourceTree = "<group>"; };
		A5DE73BA2E45CC1500E9FEE5 /* Cf8e0w1GBWNMkjzjyfdC8J.xccrashpoint */ = {isa = PBXFileReference; lastKnownFileType = wrapper; path = Cf8e0w1GBWNMkjzjyfdC8J.xccrashpoint; sourceTree = "<group>"; };
		A5DE73BB2E45CC1500E9FEE5 /* Dd-ygnFcfsvUANOm8EOQpA.xccrashpoint */ = {isa = PBXFileReference; lastKnownFileType = wrapper; path = "Dd-ygnFcfsvUANOm8EOQpA.xccrashpoint"; sourceTree = "<group>"; };
		A5DE73BC2E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B.xccrashpoint */ = {isa = PBXFileReference; lastKnownFileType = wrapper; path = DF3Jm2JaGsfRyv7x4bDP3B.xccrashpoint; sourceTree = "<group>"; };
		A5DE73BD2E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B_副本.xccrashpoint */ = {isa = PBXFileReference; lastKnownFileType = wrapper; path = "DF3Jm2JaGsfRyv7x4bDP3B_副本.xccrashpoint"; sourceTree = "<group>"; };
		A5DE73BE2E45CC1500E9FEE5 /* DrdpbEP7X5wvNgc-VOPHrp.xccrashpoint */ = {isa = PBXFileReference; lastKnownFileType = wrapper; path = "DrdpbEP7X5wvNgc-VOPHrp.xccrashpoint"; sourceTree = "<group>"; };
		A5DE73C52E45D06E00E9FEE5 /* ThreadSafeSnapKit.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThreadSafeSnapKit.swift; sourceTree = "<group>"; };
		A5E495C52DD6DED2007814CC /* DiscountPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DiscountPopupView.swift; sourceTree = "<group>"; };
		A5E61D3E2E29EF6A009EDBC4 /* HairEditVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairEditVC.swift; sourceTree = "<group>"; };
		A5E61D402E29EFC9009EDBC4 /* HairEditModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairEditModel.swift; sourceTree = "<group>"; };
		A5E61D422E29EFE7009EDBC4 /* HairFunctionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairFunctionCell.swift; sourceTree = "<group>"; };
		A5EBA9E52E2DE93E0049EBD5 /* VolcanoUsageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VolcanoUsageManager.swift; sourceTree = "<group>"; };
		A5EBA9E72E2DE96E0049EBD5 /* AIStylistGuidePopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIStylistGuidePopupView.swift; sourceTree = "<group>"; };
		A5EBA9EA2E2DE98F0049EBD5 /* HairEditUsageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HairEditUsageManager.swift; sourceTree = "<group>"; };
		A5EBA9EC2E2DE9AB0049EBD5 /* UsagePurchasePopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UsagePurchasePopupView.swift; sourceTree = "<group>"; };
		A5EBA9EE2E2E23A50049EBD5 /* DeveloperApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeveloperApp.swift; sourceTree = "<group>"; };
		A5EBA9F02E2E23CD0049EBD5 /* DeveloperAppCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeveloperAppCell.swift; sourceTree = "<group>"; };
		A5F499442E24A9720057B28C /* NSArray+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+HXExtension.h"; sourceTree = "<group>"; };
		A5F499452E24A9720057B28C /* NSArray+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSArray+HXExtension.m"; sourceTree = "<group>"; };
		A5F499462E24A9720057B28C /* NSBundle+HXPhotoPicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSBundle+HXPhotoPicker.h"; sourceTree = "<group>"; };
		A5F499472E24A9720057B28C /* NSBundle+HXPhotoPicker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSBundle+HXPhotoPicker.m"; sourceTree = "<group>"; };
		A5F499482E24A9720057B28C /* NSDate+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSDate+HXExtension.h"; sourceTree = "<group>"; };
		A5F499492E24A9720057B28C /* NSDate+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSDate+HXExtension.m"; sourceTree = "<group>"; };
		A5F4994A2E24A9720057B28C /* NSString+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+HXExtension.h"; sourceTree = "<group>"; };
		A5F4994B2E24A9720057B28C /* NSString+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+HXExtension.m"; sourceTree = "<group>"; };
		A5F4994C2E24A9720057B28C /* NSTimer+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSTimer+HXExtension.h"; sourceTree = "<group>"; };
		A5F4994D2E24A9720057B28C /* NSTimer+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSTimer+HXExtension.m"; sourceTree = "<group>"; };
		A5F4994E2E24A9720057B28C /* PHAsset+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PHAsset+HXExtension.h"; sourceTree = "<group>"; };
		A5F4994F2E24A9720057B28C /* PHAsset+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PHAsset+HXExtension.m"; sourceTree = "<group>"; };
		A5F499502E24A9720057B28C /* UIButton+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIButton+HXExtension.h"; sourceTree = "<group>"; };
		A5F499512E24A9720057B28C /* UIButton+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIButton+HXExtension.m"; sourceTree = "<group>"; };
		A5F499522E24A9720057B28C /* UIColor+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+HXExtension.h"; sourceTree = "<group>"; };
		A5F499532E24A9720057B28C /* UIColor+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+HXExtension.m"; sourceTree = "<group>"; };
		A5F499542E24A9720057B28C /* UIFont+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIFont+HXExtension.h"; sourceTree = "<group>"; };
		A5F499552E24A9720057B28C /* UIFont+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIFont+HXExtension.m"; sourceTree = "<group>"; };
		A5F499562E24A9720057B28C /* UIImage+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+HXExtension.h"; sourceTree = "<group>"; };
		A5F499572E24A9720057B28C /* UIImage+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+HXExtension.m"; sourceTree = "<group>"; };
		A5F499582E24A9720057B28C /* UIImageView+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+HXExtension.h"; sourceTree = "<group>"; };
		A5F499592E24A9720057B28C /* UIImageView+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+HXExtension.m"; sourceTree = "<group>"; };
		A5F4995A2E24A9720057B28C /* UILabel+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UILabel+HXExtension.h"; sourceTree = "<group>"; };
		A5F4995B2E24A9720057B28C /* UILabel+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UILabel+HXExtension.m"; sourceTree = "<group>"; };
		A5F4995C2E24A9720057B28C /* UIView+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+HXExtension.h"; sourceTree = "<group>"; };
		A5F4995D2E24A9720057B28C /* UIView+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+HXExtension.m"; sourceTree = "<group>"; };
		A5F4995E2E24A9720057B28C /* UIViewController+HXExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIViewController+HXExtension.h"; sourceTree = "<group>"; };
		A5F4995F2E24A9720057B28C /* UIViewController+HXExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+HXExtension.m"; sourceTree = "<group>"; };
		A5F499612E24A9720057B28C /* HXAlbumListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXAlbumListViewController.h; sourceTree = "<group>"; };
		A5F499622E24A9720057B28C /* HXAlbumListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXAlbumListViewController.m; sourceTree = "<group>"; };
		A5F499632E24A9720057B28C /* HXCustomCameraController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXCustomCameraController.h; sourceTree = "<group>"; };
		A5F499642E24A9720057B28C /* HXCustomCameraController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXCustomCameraController.m; sourceTree = "<group>"; };
		A5F499652E24A9720057B28C /* HXCustomCameraViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXCustomCameraViewController.h; sourceTree = "<group>"; };
		A5F499662E24A9720057B28C /* HXCustomCameraViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXCustomCameraViewController.m; sourceTree = "<group>"; };
		A5F499672E24A9720057B28C /* HXCustomNavigationController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXCustomNavigationController.h; sourceTree = "<group>"; };
		A5F499682E24A9720057B28C /* HXCustomNavigationController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXCustomNavigationController.m; sourceTree = "<group>"; };
		A5F499692E24A9720057B28C /* HXPhoto3DTouchViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhoto3DTouchViewController.h; sourceTree = "<group>"; };
		A5F4996A2E24A9720057B28C /* HXPhoto3DTouchViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhoto3DTouchViewController.m; sourceTree = "<group>"; };
		A5F4996B2E24A9720057B28C /* HXPhotoEditViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditViewController.h; sourceTree = "<group>"; };
		A5F4996C2E24A9720057B28C /* HXPhotoEditViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditViewController.m; sourceTree = "<group>"; };
		A5F4996D2E24A9720057B28C /* HXPhotoPreviewViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoPreviewViewController.h; sourceTree = "<group>"; };
		A5F4996E2E24A9720057B28C /* HXPhotoPreviewViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoPreviewViewController.m; sourceTree = "<group>"; };
		A5F4996F2E24A9720057B28C /* HXPhotoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoViewController.h; sourceTree = "<group>"; };
		A5F499702E24A9720057B28C /* HXPhotoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoViewController.m; sourceTree = "<group>"; };
		A5F499712E24A9720057B28C /* HXVideoEditViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXVideoEditViewController.h; sourceTree = "<group>"; };
		A5F499722E24A9720057B28C /* HXVideoEditViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXVideoEditViewController.m; sourceTree = "<group>"; };
		A5F499742E24A9720057B28C /* HX_PhotoEditViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HX_PhotoEditViewController.h; sourceTree = "<group>"; };
		A5F499752E24A9720057B28C /* HX_PhotoEditViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HX_PhotoEditViewController.m; sourceTree = "<group>"; };
		A5F499772E24A9720057B28C /* HXMECancelBlock.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXMECancelBlock.h; sourceTree = "<group>"; };
		A5F499782E24A9720057B28C /* HXMECancelBlock.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXMECancelBlock.m; sourceTree = "<group>"; };
		A5F499792E24A9720057B28C /* HXPhotoEditChartletModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditChartletModel.h; sourceTree = "<group>"; };
		A5F4997A2E24A9720057B28C /* HXPhotoEditChartletModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditChartletModel.m; sourceTree = "<group>"; };
		A5F4997B2E24A9720057B28C /* HXPhotoEditGraffitiColorModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditGraffitiColorModel.h; sourceTree = "<group>"; };
		A5F4997C2E24A9720057B28C /* HXPhotoEditGraffitiColorModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditGraffitiColorModel.m; sourceTree = "<group>"; };
		A5F4997E2E24A9720057B28C /* HX_PhotoEditBottomView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HX_PhotoEditBottomView.h; sourceTree = "<group>"; };
		A5F4997F2E24A9720057B28C /* HX_PhotoEditBottomView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HX_PhotoEditBottomView.m; sourceTree = "<group>"; };
		A5F499802E24A9720057B28C /* HXPhotoClippingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoClippingView.h; sourceTree = "<group>"; };
		A5F499812E24A9720057B28C /* HXPhotoClippingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoClippingView.m; sourceTree = "<group>"; };
		A5F499822E24A9720057B28C /* HXPhotoEditChartletContentViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditChartletContentViewCell.h; sourceTree = "<group>"; };
		A5F499832E24A9720057B28C /* HXPhotoEditChartletContentViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditChartletContentViewCell.m; sourceTree = "<group>"; };
		A5F499842E24A9720057B28C /* HXPhotoEditChartletListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditChartletListView.h; sourceTree = "<group>"; };
		A5F499852E24A9720057B28C /* HXPhotoEditChartletListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditChartletListView.m; sourceTree = "<group>"; };
		A5F499862E24A9720057B28C /* HXPhotoEditChartletPreviewView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditChartletPreviewView.h; sourceTree = "<group>"; };
		A5F499872E24A9720057B28C /* HXPhotoEditChartletPreviewView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditChartletPreviewView.m; sourceTree = "<group>"; };
		A5F499882E24A9720057B28C /* HXPhotoEditClippingToolBar.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditClippingToolBar.h; sourceTree = "<group>"; };
		A5F499892E24A9720057B28C /* HXPhotoEditClippingToolBar.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditClippingToolBar.m; sourceTree = "<group>"; };
		A5F4998A2E24A9720057B28C /* HXPhotoEditDrawView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditDrawView.h; sourceTree = "<group>"; };
		A5F4998B2E24A9720057B28C /* HXPhotoEditDrawView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditDrawView.m; sourceTree = "<group>"; };
		A5F4998C2E24A9720057B28C /* HXPhotoEditGraffitiColorSizeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditGraffitiColorSizeView.h; sourceTree = "<group>"; };
		A5F4998D2E24A9720057B28C /* HXPhotoEditGraffitiColorSizeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditGraffitiColorSizeView.m; sourceTree = "<group>"; };
		A5F4998E2E24A9720057B28C /* HXPhotoEditGraffitiColorView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditGraffitiColorView.h; sourceTree = "<group>"; };
		A5F4998F2E24A9720057B28C /* HXPhotoEditGraffitiColorView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditGraffitiColorView.m; sourceTree = "<group>"; };
		A5F499902E24A9720057B28C /* HXPhotoEditGraffitiColorViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditGraffitiColorViewCell.h; sourceTree = "<group>"; };
		A5F499912E24A9720057B28C /* HXPhotoEditGraffitiColorViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditGraffitiColorViewCell.m; sourceTree = "<group>"; };
		A5F499922E24A9720057B28C /* HXPhotoEditGridLayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditGridLayer.h; sourceTree = "<group>"; };
		A5F499932E24A9720057B28C /* HXPhotoEditGridLayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditGridLayer.m; sourceTree = "<group>"; };
		A5F499942E24A9720057B28C /* HXPhotoEditGridMaskLayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditGridMaskLayer.h; sourceTree = "<group>"; };
		A5F499952E24A9720057B28C /* HXPhotoEditGridMaskLayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditGridMaskLayer.m; sourceTree = "<group>"; };
		A5F499962E24A9720057B28C /* HXPhotoEditGridView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditGridView.h; sourceTree = "<group>"; };
		A5F499972E24A9720057B28C /* HXPhotoEditGridView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditGridView.m; sourceTree = "<group>"; };
		A5F499982E24A9720057B28C /* HXPhotoEditImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditImageView.h; sourceTree = "<group>"; };
		A5F499992E24A9720057B28C /* HXPhotoEditImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditImageView.m; sourceTree = "<group>"; };
		A5F4999A2E24A9720057B28C /* HXPhotoEditingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditingView.h; sourceTree = "<group>"; };
		A5F4999B2E24A9720057B28C /* HXPhotoEditingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditingView.m; sourceTree = "<group>"; };
		A5F4999C2E24A9720057B28C /* HXPhotoEditMosaicView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditMosaicView.h; sourceTree = "<group>"; };
		A5F4999D2E24A9720057B28C /* HXPhotoEditMosaicView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditMosaicView.m; sourceTree = "<group>"; };
		A5F4999E2E24A9720057B28C /* HXPhotoEditResizeControl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditResizeControl.h; sourceTree = "<group>"; };
		A5F4999F2E24A9720057B28C /* HXPhotoEditResizeControl.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditResizeControl.m; sourceTree = "<group>"; };
		A5F499A02E24A9720057B28C /* HXPhotoEditSplashMaskLayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditSplashMaskLayer.h; sourceTree = "<group>"; };
		A5F499A12E24A9720057B28C /* HXPhotoEditSplashMaskLayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditSplashMaskLayer.m; sourceTree = "<group>"; };
		A5F499A22E24A9720057B28C /* HXPhotoEditSplashView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditSplashView.h; sourceTree = "<group>"; };
		A5F499A32E24A9720057B28C /* HXPhotoEditSplashView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditSplashView.m; sourceTree = "<group>"; };
		A5F499A42E24A9720057B28C /* HXPhotoEditStickerItem.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditStickerItem.h; sourceTree = "<group>"; };
		A5F499A52E24A9720057B28C /* HXPhotoEditStickerItem.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditStickerItem.m; sourceTree = "<group>"; };
		A5F499A62E24A9720057B28C /* HXPhotoEditStickerItemContentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditStickerItemContentView.h; sourceTree = "<group>"; };
		A5F499A72E24A9720057B28C /* HXPhotoEditStickerItemContentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditStickerItemContentView.m; sourceTree = "<group>"; };
		A5F499A82E24A9720057B28C /* HXPhotoEditStickerItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditStickerItemView.h; sourceTree = "<group>"; };
		A5F499A92E24A9720057B28C /* HXPhotoEditStickerItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditStickerItemView.m; sourceTree = "<group>"; };
		A5F499AA2E24A9720057B28C /* HXPhotoEditStickerTrashView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditStickerTrashView.h; sourceTree = "<group>"; };
		A5F499AB2E24A9720057B28C /* HXPhotoEditStickerTrashView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditStickerTrashView.m; sourceTree = "<group>"; };
		A5F499AC2E24A9720057B28C /* HXPhotoEditStickerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditStickerView.h; sourceTree = "<group>"; };
		A5F499AD2E24A9720057B28C /* HXPhotoEditStickerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditStickerView.m; sourceTree = "<group>"; };
		A5F499AE2E24A9720057B28C /* HXPhotoEditTextView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditTextView.h; sourceTree = "<group>"; };
		A5F499AF2E24A9720057B28C /* HXPhotoEditTextView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditTextView.m; sourceTree = "<group>"; };
		A5F499B12E24A9720057B28C /* HXPhotoEdit.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEdit.h; sourceTree = "<group>"; };
		A5F499B22E24A9720057B28C /* HXPhotoEdit.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEdit.m; sourceTree = "<group>"; };
		A5F499B32E24A9720057B28C /* HXPhotoEditConfiguration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditConfiguration.h; sourceTree = "<group>"; };
		A5F499B42E24A9720057B28C /* HXPhotoEditConfiguration.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditConfiguration.m; sourceTree = "<group>"; };
		A5F499B62E24A9720057B28C /* HXAlbumModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXAlbumModel.h; sourceTree = "<group>"; };
		A5F499B72E24A9720057B28C /* HXAlbumModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXAlbumModel.m; sourceTree = "<group>"; };
		A5F499B82E24A9720057B28C /* HXCustomAssetModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXCustomAssetModel.h; sourceTree = "<group>"; };
		A5F499B92E24A9720057B28C /* HXCustomAssetModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXCustomAssetModel.m; sourceTree = "<group>"; };
		A5F499BA2E24A9720057B28C /* HXPhotoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoModel.h; sourceTree = "<group>"; };
		A5F499BB2E24A9720057B28C /* HXPhotoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoModel.m; sourceTree = "<group>"; };
		A5F499BC2E24A9720057B28C /* HXPhotoViewCellCustomProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoViewCellCustomProtocol.h; sourceTree = "<group>"; };
		A5F499BD2E24A9720057B28C /* HXPhotoViewProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoViewProtocol.h; sourceTree = "<group>"; };
		A5F499BE2E24A9720057B28C /* HXPickerResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPickerResult.h; sourceTree = "<group>"; };
		A5F499BF2E24A9720057B28C /* HXPickerResult.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPickerResult.m; sourceTree = "<group>"; };
		A5F499C12E24A9720057B28C /* HXPhotoPicker.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = HXPhotoPicker.bundle; sourceTree = "<group>"; };
		A5F499C32E24A9720057B28C /* HXPhotoEditTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoEditTransition.h; sourceTree = "<group>"; };
		A5F499C42E24A9720057B28C /* HXPhotoEditTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoEditTransition.m; sourceTree = "<group>"; };
		A5F499C52E24A9720057B28C /* HXPhotoInteractiveTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoInteractiveTransition.h; sourceTree = "<group>"; };
		A5F499C62E24A9720057B28C /* HXPhotoInteractiveTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoInteractiveTransition.m; sourceTree = "<group>"; };
		A5F499C72E24A9720057B28C /* HXPhotoPersentInteractiveTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoPersentInteractiveTransition.h; sourceTree = "<group>"; };
		A5F499C82E24A9720057B28C /* HXPhotoPersentInteractiveTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoPersentInteractiveTransition.m; sourceTree = "<group>"; };
		A5F499C92E24A9720057B28C /* HXPhotoViewPresentTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoViewPresentTransition.h; sourceTree = "<group>"; };
		A5F499CA2E24A9720057B28C /* HXPhotoViewPresentTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoViewPresentTransition.m; sourceTree = "<group>"; };
		A5F499CB2E24A9720057B28C /* HXPhotoViewTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoViewTransition.h; sourceTree = "<group>"; };
		A5F499CC2E24A9720057B28C /* HXPhotoViewTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoViewTransition.m; sourceTree = "<group>"; };
		A5F499CE2E24A9720057B28C /* HXAlbumlistView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXAlbumlistView.h; sourceTree = "<group>"; };
		A5F499CF2E24A9720057B28C /* HXAlbumlistView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXAlbumlistView.m; sourceTree = "<group>"; };
		A5F499D02E24A9720057B28C /* HXCameraBottomView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXCameraBottomView.h; sourceTree = "<group>"; };
		A5F499D12E24A9720057B28C /* HXCameraBottomView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXCameraBottomView.m; sourceTree = "<group>"; };
		A5F499D22E24A9720057B28C /* HXCircleProgressView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXCircleProgressView.h; sourceTree = "<group>"; };
		A5F499D32E24A9720057B28C /* HXCircleProgressView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXCircleProgressView.m; sourceTree = "<group>"; };
		A5F499D42E24A9720057B28C /* HXCollectionView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXCollectionView.h; sourceTree = "<group>"; };
		A5F499D52E24A9720057B28C /* HXCollectionView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXCollectionView.m; sourceTree = "<group>"; };
		A5F499D62E24A9720057B28C /* HXCustomCollectionReusableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXCustomCollectionReusableView.h; sourceTree = "<group>"; };
		A5F499D72E24A9720057B28C /* HXCustomCollectionReusableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXCustomCollectionReusableView.m; sourceTree = "<group>"; };
		A5F499D82E24A9720057B28C /* HXCustomPreviewView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXCustomPreviewView.h; sourceTree = "<group>"; };
		A5F499D92E24A9720057B28C /* HXCustomPreviewView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXCustomPreviewView.m; sourceTree = "<group>"; };
		A5F499DA2E24A9720057B28C /* HXFullScreenCameraPlayView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXFullScreenCameraPlayView.h; sourceTree = "<group>"; };
		A5F499DB2E24A9720057B28C /* HXFullScreenCameraPlayView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXFullScreenCameraPlayView.m; sourceTree = "<group>"; };
		A5F499DC2E24A9720057B28C /* HXPhotoBottomSelectView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoBottomSelectView.h; sourceTree = "<group>"; };
		A5F499DD2E24A9720057B28C /* HXPhotoBottomSelectView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoBottomSelectView.m; sourceTree = "<group>"; };
		A5F499DE2E24A9720057B28C /* HXPhotoCustomNavigationBar.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoCustomNavigationBar.h; sourceTree = "<group>"; };
		A5F499DF2E24A9720057B28C /* HXPhotoCustomNavigationBar.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoCustomNavigationBar.m; sourceTree = "<group>"; };
		A5F499E02E24A9720057B28C /* HXPhotoLimitView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoLimitView.h; sourceTree = "<group>"; };
		A5F499E12E24A9720057B28C /* HXPhotoLimitView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoLimitView.m; sourceTree = "<group>"; };
		A5F499E22E24A9720057B28C /* HXPhotoPreviewBottomView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoPreviewBottomView.h; sourceTree = "<group>"; };
		A5F499E32E24A9720057B28C /* HXPhotoPreviewBottomView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoPreviewBottomView.m; sourceTree = "<group>"; };
		A5F499E42E24A9720057B28C /* HXPhotoPreviewImageViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoPreviewImageViewCell.h; sourceTree = "<group>"; };
		A5F499E52E24A9720057B28C /* HXPhotoPreviewImageViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoPreviewImageViewCell.m; sourceTree = "<group>"; };
		A5F499E62E24A9720057B28C /* HXPhotoPreviewLivePhotoCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoPreviewLivePhotoCell.h; sourceTree = "<group>"; };
		A5F499E72E24A9720057B28C /* HXPhotoPreviewLivePhotoCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoPreviewLivePhotoCell.m; sourceTree = "<group>"; };
		A5F499E82E24A9720057B28C /* HXPhotoPreviewVideoViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoPreviewVideoViewCell.h; sourceTree = "<group>"; };
		A5F499E92E24A9720057B28C /* HXPhotoPreviewVideoViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoPreviewVideoViewCell.m; sourceTree = "<group>"; };
		A5F499EA2E24A9720057B28C /* HXPhotoPreviewViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoPreviewViewCell.h; sourceTree = "<group>"; };
		A5F499EB2E24A9720057B28C /* HXPhotoPreviewViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoPreviewViewCell.m; sourceTree = "<group>"; };
		A5F499EC2E24A9720057B28C /* HXPhotoSubViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoSubViewCell.h; sourceTree = "<group>"; };
		A5F499ED2E24A9720057B28C /* HXPhotoSubViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoSubViewCell.m; sourceTree = "<group>"; };
		A5F499EE2E24A9720057B28C /* HXPhotoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoView.h; sourceTree = "<group>"; };
		A5F499EF2E24A9720057B28C /* HXPhotoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoView.m; sourceTree = "<group>"; };
		A5F499F02E24A9720057B28C /* HXPhotoViewFlowLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoViewFlowLayout.h; sourceTree = "<group>"; };
		A5F499F12E24A9720057B28C /* HXPhotoViewFlowLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoViewFlowLayout.m; sourceTree = "<group>"; };
		A5F499F22E24A9720057B28C /* HXPreviewContentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPreviewContentView.h; sourceTree = "<group>"; };
		A5F499F32E24A9720057B28C /* HXPreviewContentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPreviewContentView.m; sourceTree = "<group>"; };
		A5F499F42E24A9720057B28C /* HXPreviewImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPreviewImageView.h; sourceTree = "<group>"; };
		A5F499F52E24A9720057B28C /* HXPreviewImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPreviewImageView.m; sourceTree = "<group>"; };
		A5F499F62E24A9720057B28C /* HXPreviewLivePhotoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPreviewLivePhotoView.h; sourceTree = "<group>"; };
		A5F499F72E24A9720057B28C /* HXPreviewLivePhotoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPreviewLivePhotoView.m; sourceTree = "<group>"; };
		A5F499F82E24A9720057B28C /* HXPreviewVideoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPreviewVideoView.h; sourceTree = "<group>"; };
		A5F499F92E24A9720057B28C /* HXPreviewVideoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPreviewVideoView.m; sourceTree = "<group>"; };
		A5F499FB2E24A9720057B28C /* HXAssetManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXAssetManager.h; sourceTree = "<group>"; };
		A5F499FC2E24A9720057B28C /* HXAssetManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXAssetManager.m; sourceTree = "<group>"; };
		A5F499FD2E24A9720057B28C /* HXPhotoCommon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoCommon.h; sourceTree = "<group>"; };
		A5F499FE2E24A9720057B28C /* HXPhotoCommon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoCommon.m; sourceTree = "<group>"; };
		A5F499FF2E24A9720057B28C /* HXPhotoConfiguration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoConfiguration.h; sourceTree = "<group>"; };
		A5F49A002E24A9720057B28C /* HXPhotoConfiguration.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoConfiguration.m; sourceTree = "<group>"; };
		A5F49A012E24A9720057B28C /* HXPhotoDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoDefine.h; sourceTree = "<group>"; };
		A5F49A022E24A9720057B28C /* HXPhotoManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoManager.h; sourceTree = "<group>"; };
		A5F49A032E24A9720057B28C /* HXPhotoManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoManager.m; sourceTree = "<group>"; };
		A5F49A042E24A9720057B28C /* HXPhotoPicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoPicker.h; sourceTree = "<group>"; };
		A5F49A052E24A9720057B28C /* HXPhotoTools.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoTools.h; sourceTree = "<group>"; };
		A5F49A062E24A9720057B28C /* HXPhotoTools.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HXPhotoTools.m; sourceTree = "<group>"; };
		A5F49A072E24A9720057B28C /* HXPhotoTypes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HXPhotoTypes.h; sourceTree = "<group>"; };
		A5F49A652E24A99D0057B28C /* PTMPhotoPickerHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PTMPhotoPickerHelper.h; sourceTree = "<group>"; };
		A5F49A662E24A99D0057B28C /* PTMPhotoPickerHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PTMPhotoPickerHelper.m; sourceTree = "<group>"; };
		A5F49A682E24ACA90057B28C /* NailCameraViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NailCameraViewController.swift; sourceTree = "<group>"; };
		A5F49A6B2E24BCB10057B28C /* NailProcessViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NailProcessViewController.swift; sourceTree = "<group>"; };
		A5F49A6C2E24BCB10057B28C /* NailStyleViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NailStyleViewController.swift; sourceTree = "<group>"; };
		A5F49A702E24BCE70057B28C /* NailModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NailModel.swift; sourceTree = "<group>"; };
		A5F49A722E24BD0C0057B28C /* VolcanoEngineAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VolcanoEngineAPI.swift; sourceTree = "<group>"; };
		A5FBC2152DD1942E0015329D /* APPMakeStoreIAPManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APPMakeStoreIAPManager.swift; sourceTree = "<group>"; };
		A5FBC2162DD1942E0015329D /* APPStoreIAPObserver.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APPStoreIAPObserver.swift; sourceTree = "<group>"; };
		A5FBC45A2DD195460015329D /* VipViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VipViewController.swift; sourceTree = "<group>"; };
		A5FBC45C2DD196B20015329D /* STbaseVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = STbaseVC.swift; sourceTree = "<group>"; };
		A5FBC45E2DD196E70015329D /* UpdateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpdateManager.swift; sourceTree = "<group>"; };
		C4992D322C5DC65500241D59 /* FaceShapeResultCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FaceShapeResultCell.swift; sourceTree = "<group>"; };
		C4992D342C5DC9AB00241D59 /* FaceShapeTestResultView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FaceShapeTestResultView.swift; sourceTree = "<group>"; };
		C4992D362C5DCA3400241D59 /* FaceShapeSliderResultView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FaceShapeSliderResultView.swift; sourceTree = "<group>"; };
		C4B1537A2C67D5F700E7420C /* icon3.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon3.png; sourceTree = "<group>"; };
		C4B1537B2C67D5F700E7420C /* icon2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon2.png; sourceTree = "<group>"; };
		C4B1537C2C67D5F700E7420C /* icon1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon1.png; sourceTree = "<group>"; };
		C4C734EE2C5F138000354F24 /* Dictionary+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Dictionary+Extension.swift"; sourceTree = "<group>"; };
		C4C734F02C5F146200354F24 /* Array+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Array+Extension.swift"; sourceTree = "<group>"; };
		C4C734F22C5F15F700354F24 /* WebSocketTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebSocketTool.swift; sourceTree = "<group>"; };
		C4C734F42C5F383B00354F24 /* PickImageTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PickImageTool.swift; sourceTree = "<group>"; };
		C4DA01592C5FBE160058B6F7 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		F0316C360453D1D95C8A6E35 /* Pods_HairCut.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_HairCut.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7971E0B52C578042002381F1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5841743B2E57E154C5C9424 /* Pods_HairCut.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		79349D712CEACE1700ED9853 /* 颜色选择器 */ = {
			isa = PBXGroup;
			children = (
				79349D722CEACE3F00ED9853 /* HDColorPickerHelpers.swift */,
				79349D742CEAD24800ED9853 /* HDColorPickerLayer.swift */,
				79349D762CEAD42800ED9853 /* HDPointView.swift */,
				79349D782CEAD4A600ED9853 /* HDColorPickerBarLayer.swift */,
				79349D7A2CEAD5CB00ED9853 /* HDColorPickerBarView.swift */,
				79349D7C2CEAD6C300ED9853 /* HDColorPickerView.swift */,
				79349D7E2CEADE2100ED9853 /* HDColorPalette.swift */,
				79349D802CEADEA900ED9853 /* HDColorCell.swift */,
				79349D822CEADF3600ED9853 /* 颜色管理单例 */,
			);
			path = "颜色选择器";
			sourceTree = "<group>";
		};
		79349D822CEADF3600ED9853 /* 颜色管理单例 */ = {
			isa = PBXGroup;
			children = (
				79349D832CEADF5400ED9853 /* HDEditManager.swift */,
				799686CF2CED728A00971950 /* HairCutMalloc.h */,
				799686D02CED728A00971950 /* HairCutMalloc.c */,
				799686D22CED7BDB00971950 /* HDImageTool.h */,
				799686D32CED7BDB00971950 /* HDImageTool.m */,
			);
			path = "颜色管理单例";
			sourceTree = "<group>";
		};
		79578DEE2C61B14A00387CD5 /* Image */ = {
			isa = PBXGroup;
			children = (
				C4B153772C67D51F00E7420C /* iconImage */,
				79578DF42C61B2DF00387CD5 /* 爆炸头.jpg */,
				79578DEF2C61B2DF00387CD5 /* 灰色渐变卷发.jpg */,
				79578DF02C61B2DF00387CD5 /* 洛丽塔发型.jpg */,
				79578DF22C61B2DF00387CD5 /* 挑染编织发.jpg */,
				79578DF12C61B2DF00387CD5 /* 纹理烫.jpg */,
				79578DF32C61B2DF00387CD5 /* 羊毛卷.jpg */,
			);
			path = Image;
			sourceTree = "<group>";
		};
		796D034B2CE6E52E00046500 /* 编辑页面 */ = {
			isa = PBXGroup;
			children = (
				796D034E2CE6E55E00046500 /* HairStyleEditHairChooseView.swift */,
				796D03502CE6E59B00046500 /* HairStyleEditRightAreaView.swift */,
				796D03522CE6E5C300046500 /* HairStyleEditColorChooseView.swift */,
				796D03542CE6EEB000046500 /* HairStyleNavRightActionView.swift */,
				796D03562CE72B7400046500 /* HairStyleEditHairChooseCell.swift */,
				79349D872CEAF10500ED9853 /* HairStyleEditColorCell.swift */,
				79349D712CEACE1700ED9853 /* 颜色选择器 */,
			);
			path = "编辑页面";
			sourceTree = "<group>";
		};
		7971E0AF2C578042002381F1 = {
			isa = PBXGroup;
			children = (
				7971E0BA2C578042002381F1 /* HairCut */,
				7971E0B92C578042002381F1 /* Products */,
				D44D6A90A7BAAFF45F3BCB28 /* Pods */,
				9AD2CFD85569E228AF0236D1 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		7971E0B92C578042002381F1 /* Products */ = {
			isa = PBXGroup;
			children = (
				7971E0B82C578042002381F1 /* 发型测试.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7971E0BA2C578042002381F1 /* HairCut */ = {
			isa = PBXGroup;
			children = (
				A5DE73B82E45CC0A00E9FEE5 /* crash */,
				799686CE2CED728A00971950 /* HairCut-Bridging-Header.h */,
				7971E0BB2C578042002381F1 /* AppDelegate.swift */,
				7971E0C42C578044002381F1 /* Assets.xcassets */,
				7971E0C62C578044002381F1 /* LaunchScreen.storyboard */,
				7971E0C92C578044002381F1 /* Info.plist */,
				79C733CB2C5B75CA002FBCE7 /* Resource */,
				7971E0E42C57A7EC002381F1 /* Extension */,
				7971E0D92C5790E1002381F1 /* Const */,
				7971E0D12C5781FE002381F1 /* Tool */,
				7971E0D22C5783DC002381F1 /* View */,
				7971E0D02C5781E8002381F1 /* VC */,
				7971E0CF2C5781DB002381F1 /* Model */,
				A5171F7D2E262F5E00C9233F /* FURenderKit快速集成文档.md */,
			);
			path = HairCut;
			sourceTree = "<group>";
		};
		7971E0CF2C5781DB002381F1 /* Model */ = {
			isa = PBXGroup;
			children = (
				A5EBA9EE2E2E23A50049EBD5 /* DeveloperApp.swift */,
				A5E61D402E29EFC9009EDBC4 /* HairEditModel.swift */,
				A5171D982E25F03400C9233F /* BeautyParameter.swift */,
				A5BC4A992DD47CC50048F92E /* TagBasedHairModelManager.swift */,
				79EEBBE62C60ADA100905018 /* AIHairCutData.swift */,
				A5D230692D811EA8007D1911 /* AIHairModel.swift */,
				798E3CE72C65A1B000DE4093 /* FaceTestCellData.swift */,
				79578DFD2C6207E700387CD5 /* FaceTestData.swift */,
				A5F49A702E24BCE70057B28C /* NailModel.swift */,
				796D03582CE747B500046500 /* HaitStyleHairData.swift */,
				79578DFB2C61B55800387CD5 /* PopularHairCutData.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		7971E0D02C5781E8002381F1 /* VC */ = {
			isa = PBXGroup;
			children = (
				A5E61D3E2E29EF6A009EDBC4 /* HairEditVC.swift */,
				A5FBC45C2DD196B20015329D /* STbaseVC.swift */,
				A59659C02D3A4532003DFD2E /* resultVc */,
				79C007442C5A33CF006521B7 /* LaunchScreenVC.swift */,
				7971E0D72C578646002381F1 /* WelcomeVC.swift */,
				7971E0DC2C57A35E002381F1 /* TabbarVC.swift */,
				7971E0E02C57A436002381F1 /* HomeVC.swift */,
				797215122CE5CAC5001B82BE /* HairStyleVC.swift */,
				796D03492CE6E35C00046500 /* HairStyleEditVC.swift */,
				7971E0DE2C57A3D3002381F1 /* SettingVC.swift */,
				79C733C52C5B71DB002FBCE7 /* AIHairCutVC.swift */,
				79C733C72C5B7202002FBCE7 /* FaceShapeTestVC.swift */,
				79AD57002C5A4E6700A06A2F /* AboutUsVC.swift */,
				A5BC4A9B2DD47CF80048F92E /* CategoryListViewController.swift */,
				A5BC4A9C2DD47CF80048F92E /* CategoryViewController.swift */,
				A5171D9A2E25F42B00C9233F /* PortraitBeautyVC.swift */,
				A5171D9E2E26030100C9233F /* BeautyEditVC.swift */,
			);
			path = VC;
			sourceTree = "<group>";
		};
		7971E0D12C5781FE002381F1 /* Tool */ = {
			isa = PBXGroup;
			children = (
				A5EBA9EA2E2DE98F0049EBD5 /* HairEditUsageManager.swift */,
				A5EBA9E52E2DE93E0049EBD5 /* VolcanoUsageManager.swift */,
				A5171DA22E26057900C9233F /* BeautyFilterManager.swift */,
				A5171D932E25E92A00C9233F /* beauteFace */,
				A5FBC45F2DD196E70015329D /* update */,
				A5FBC2172DD1942E0015329D /* vip */,
				A5B678572D8BA5AC009F5A53 /* MyAdManager.swift */,
				A5CE750D2D8AA6BC009C61F6 /* SliderView */,
				A5CE75022D8A7C89009C61F6 /* PopView */,
				A5CE74132D8A6C27009C61F6 /* header */,
				A596577D2D3A13B1003DFD2E /* network */,
				7971E0D32C57853E002381F1 /* AppLoad.swift */,
				799B6E252C6B346400FC2015 /* UserDefaultsTool.swift */,
				7971E0E92C57A97D002381F1 /* HttpTool.swift */,
				C4C734F22C5F15F700354F24 /* WebSocketTool.swift */,
				C4C734F42C5F383B00354F24 /* PickImageTool.swift */,
				79EF1AA72C6620C500B2D555 /* ImageTool.swift */,
				796A431F2C6C57AC00E724C5 /* LanguageTool.swift */,
				79D8642B2CB4E020009DEB91 /* GoodRepulationTool.swift */,
				A5F49A092E24A9720057B28C /* picker */,
				A5F49A722E24BD0C0057B28C /* VolcanoEngineAPI.swift */,
				A5DE73C52E45D06E00E9FEE5 /* ThreadSafeSnapKit.swift */,
			);
			path = Tool;
			sourceTree = "<group>";
		};
		7971E0D22C5783DC002381F1 /* View */ = {
			isa = PBXGroup;
			children = (
				A5EBA9EC2E2DE9AB0049EBD5 /* UsagePurchasePopupView.swift */,
				A5EBA9E82E2DE96E0049EBD5 /* 引导弹窗 */,
				A51E59442E2A258E0033C783 /* 美容 */,
				A5F49A6D2E24BCB10057B28C /* 美甲 */,
				A5F49A692E24ACA90057B28C /* 拍照 */,
				797215142CE5E2B6001B82BE /* 发型库 */,
				79C733C22C5B6EEF002FBCE7 /* 脸型测试 */,
				79C733C12C5B6EDD002FBCE7 /* AI换发 */,
				79C733C02C5B6777002FBCE7 /* 欢迎页 */,
				79C733BF2C5B6762002FBCE7 /* 设置页 */,
				79C733BE2C5B6756002FBCE7 /* 首页 */,
			);
			path = View;
			sourceTree = "<group>";
		};
		7971E0D92C5790E1002381F1 /* Const */ = {
			isa = PBXGroup;
			children = (
				7971E0EB2C57A9F9002381F1 /* Const.swift */,
				79C007402C59CD55006521B7 /* Enum.swift */,
			);
			path = Const;
			sourceTree = "<group>";
		};
		7971E0E42C57A7EC002381F1 /* Extension */ = {
			isa = PBXGroup;
			children = (
				7971E0E52C57A91D002381F1 /* String+Extension.swift */,
				793B25242C589440005492DF /* UIColor+Extension.swift */,
				793B252A2C58CC20005492DF /* UIView+gradient.swift */,
				79C733C32C5B7029002FBCE7 /* Print+Extension.swift */,
				798F14612C5C6F5A0029A34D /* UIViewController+Extension.swift */,
				C4C734EE2C5F138000354F24 /* Dictionary+Extension.swift */,
				C4C734F02C5F146200354F24 /* Array+Extension.swift */,
				79EEBBE42C60A4F100905018 /* UIImage+Extension.swift */,
				A5CE741C2D8A715D009C61F6 /* UIButton+BackgroundState.swift */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		797215142CE5E2B6001B82BE /* 发型库 */ = {
			isa = PBXGroup;
			children = (
				797215152CE5E337001B82BE /* HairStyleMainListView.swift */,
				796D03472CE6DC1200046500 /* HairStyleMainListHairCell.swift */,
				796D034B2CE6E52E00046500 /* 编辑页面 */,
			);
			path = "发型库";
			sourceTree = "<group>";
		};
		798F146F2C5CC9BC0029A34D /* 发型选择 */ = {
			isa = PBXGroup;
			children = (
				798F146B2C5CBD150029A34D /* HairCutChooseView.swift */,
				798F146D2C5CBD490029A34D /* HairCutAreaView.swift */,
				79C733CC2C5B9AEC002FBCE7 /* HairCutImageCell.swift */,
				79C733CE2C5B9B78002FBCE7 /* HairCutTextCell.swift */,
			);
			path = "发型选择";
			sourceTree = "<group>";
		};
		79C733BE2C5B6756002FBCE7 /* 首页 */ = {
			isa = PBXGroup;
			children = (
				79AD56FE2C5A490A00A06A2F /* HotHairCutCollectionViewCell.swift */,
				793B25222C58814D005492DF /* HomeView.swift */,
			);
			path = "首页";
			sourceTree = "<group>";
		};
		79C733BF2C5B6762002FBCE7 /* 设置页 */ = {
			isa = PBXGroup;
			children = (
				A5EBA9F02E2E23CD0049EBD5 /* DeveloperAppCell.swift */,
				793B25262C58BDA3005492DF /* SettingView.swift */,
				79C733BC2C5B6743002FBCE7 /* SettingIconViewCell.swift */,
				79AD57022C5A4E7700A06A2F /* AboutUsView.swift */,
			);
			path = "设置页";
			sourceTree = "<group>";
		};
		79C733C02C5B6777002FBCE7 /* 欢迎页 */ = {
			isa = PBXGroup;
			children = (
				79C007462C5A33E5006521B7 /* LaunchScreenView.swift */,
				793B25282C58C4F8005492DF /* WelcomeView.swift */,
				793B252C2C58E714005492DF /* SubscribeView.swift */,
			);
			path = "欢迎页";
			sourceTree = "<group>";
		};
		79C733C12C5B6EDD002FBCE7 /* AI换发 */ = {
			isa = PBXGroup;
			children = (
				A5556B642DCF393D00009F7E /* AreaSelectViewController.swift */,
				A5556B652DCF393D00009F7E /* BrushPreviewView.swift */,
				A5556B662DCF393D00009F7E /* DrawView.swift */,
				A5556B672DCF393D00009F7E /* PTMPaintView.swift */,
				79C733C92C5B736A002FBCE7 /* AIHairCutView.swift */,
				A5CE75052D8A9B75009C61F6 /* AIHairCutTipView.swift */,
				A5CE73292D8A6AF2009C61F6 /* AIHairCutSettingView.swift */,
				798F14692C5CBCEA0029A34D /* AIHairCutUploadButtonView.swift */,
				798F145F2C5C69A00029A34D /* AIHairCutResultView.swift */,
				798F146F2C5CC9BC0029A34D /* 发型选择 */,
			);
			path = "AI换发";
			sourceTree = "<group>";
		};
		79C733C22C5B6EEF002FBCE7 /* 脸型测试 */ = {
			isa = PBXGroup;
			children = (
				798F14632C5C7AA80029A34D /* FaceShapeTestView.swift */,
				C4992D342C5DC9AB00241D59 /* FaceShapeTestResultView.swift */,
				C4992D362C5DCA3400241D59 /* FaceShapeSliderResultView.swift */,
				798F14652C5C90A30029A34D /* FaceShapeTestCell.swift */,
				C4992D322C5DC65500241D59 /* FaceShapeResultCell.swift */,
			);
			path = "脸型测试";
			sourceTree = "<group>";
		};
		79C733CB2C5B75CA002FBCE7 /* Resource */ = {
			isa = PBXGroup;
			children = (
				A5171D8F2E25E88100C9233F /* authpack.h */,
				79F8FC392C634C910054A742 /* Json */,
				79578DEE2C61B14A00387CD5 /* Image */,
				C4DA01552C5FBDE70058B6F7 /* InfoPlist */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		79F8FC392C634C910054A742 /* Json */ = {
			isa = PBXGroup;
			children = (
				79F8FC3E2C6360C30054A742 /* FaceTestResult.json */,
				79F8FC3A2C634C9F0054A742 /* FaceTestResultImageMatch.json */,
				A5171D962E25EF3E00C9233F /* beauty.json */,
			);
			path = Json;
			sourceTree = "<group>";
		};
		9AD2CFD85569E228AF0236D1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F0316C360453D1D95C8A6E35 /* Pods_HairCut.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A5171D932E25E92A00C9233F /* beauteFace */ = {
			isa = PBXGroup;
			children = (
				A5171D902E25E92700C9233F /* PTMFilterHelper.h */,
				A5171D912E25E92700C9233F /* PTMFilterHelper.m */,
			);
			path = beauteFace;
			sourceTree = "<group>";
		};
		A51E59442E2A258E0033C783 /* 美容 */ = {
			isa = PBXGroup;
			children = (
				A5E61D422E29EFE7009EDBC4 /* HairFunctionCell.swift */,
				A5171DA02E26034B00C9233F /* BeautyParameterCell.swift */,
			);
			path = "美容";
			sourceTree = "<group>";
		};
		A596577D2D3A13B1003DFD2E /* network */ = {
			isa = PBXGroup;
			children = (
				A59657792D3A13B1003DFD2E /* RSA.h */,
				A596577A2D3A13B1003DFD2E /* RSA.m */,
				A596577B2D3A13B1003DFD2E /* UIImage+Base64.h */,
				A596577C2D3A13B1003DFD2E /* UIImage+Base64.m */,
			);
			path = network;
			sourceTree = "<group>";
		};
		A59659C02D3A4532003DFD2E /* resultVc */ = {
			isa = PBXGroup;
			children = (
				A59659C42D3A483C003DFD2E /* input.json */,
				798F145D2C5C68F80029A34D /* AIHairResultVC.swift */,
				A59659C22D3A47F2003DFD2E /* AIHairJSON.swift */,
			);
			path = resultVc;
			sourceTree = "<group>";
		};
		A5CE74132D8A6C27009C61F6 /* header */ = {
			isa = PBXGroup;
			children = (
				A5CE740B2D8A6C27009C61F6 /* CreatUI.swift */,
				A5CE740C2D8A6C27009C61F6 /* PTSWheader.swift */,
				A5CE740D2D8A6C27009C61F6 /* PTSWSwftObj.swift */,
				A5CE740E2D8A6C27009C61F6 /* UIDefault.swift */,
				A5CE740F2D8A6C27009C61F6 /* ZyeButtonEx.swift */,
				A5CE74102D8A6C27009C61F6 /* ZYEButtonPositionAndSpace.swift */,
				A5CE74112D8A6C27009C61F6 /* ZYEExtension.swift */,
				A5CE74122D8A6C27009C61F6 /* ZYETapTool.swift */,
			);
			path = header;
			sourceTree = "<group>";
		};
		A5CE75022D8A7C89009C61F6 /* PopView */ = {
			isa = PBXGroup;
			children = (
				A5CE74FE2D8A7C89009C61F6 /* PopAnimationTool.h */,
				A5CE74FF2D8A7C89009C61F6 /* PopAnimationTool.m */,
				A5CE75002D8A7C89009C61F6 /* PopView.h */,
				A5CE75012D8A7C89009C61F6 /* PopView.m */,
			);
			path = PopView;
			sourceTree = "<group>";
		};
		A5CE750D2D8AA6BC009C61F6 /* SliderView */ = {
			isa = PBXGroup;
			children = (
				A5CE750E2D8AA6DB009C61F6 /* ZYELabel.h */,
				A5CE750F2D8AA6DB009C61F6 /* ZYELabel.m */,
				A5CE750A2D8AA68C009C61F6 /* UILabel+createLabels.h */,
				A5CE750B2D8AA68C009C61F6 /* UILabel+createLabels.m */,
				A5CE75072D8AA61D009C61F6 /* PTMImageSliderView.h */,
				A5CE75082D8AA61D009C61F6 /* PTMImageSliderView.m */,
			);
			path = SliderView;
			sourceTree = "<group>";
		};
		A5DE73B82E45CC0A00E9FEE5 /* crash */ = {
			isa = PBXGroup;
			children = (
				A5DE73B92E45CC1500E9FEE5 /* BKpHzAc8Ri8t05shY3RgyH.xccrashpoint */,
				A5DE73BA2E45CC1500E9FEE5 /* Cf8e0w1GBWNMkjzjyfdC8J.xccrashpoint */,
				A5DE73BB2E45CC1500E9FEE5 /* Dd-ygnFcfsvUANOm8EOQpA.xccrashpoint */,
				A5DE73BC2E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B.xccrashpoint */,
				A5DE73BD2E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B_副本.xccrashpoint */,
				A5DE73BE2E45CC1500E9FEE5 /* DrdpbEP7X5wvNgc-VOPHrp.xccrashpoint */,
			);
			path = crash;
			sourceTree = "<group>";
		};
		A5EBA9E82E2DE96E0049EBD5 /* 引导弹窗 */ = {
			isa = PBXGroup;
			children = (
				A5EBA9E72E2DE96E0049EBD5 /* AIStylistGuidePopupView.swift */,
			);
			path = "引导弹窗";
			sourceTree = "<group>";
		};
		A5F499602E24A9720057B28C /* Category */ = {
			isa = PBXGroup;
			children = (
				A5F499442E24A9720057B28C /* NSArray+HXExtension.h */,
				A5F499452E24A9720057B28C /* NSArray+HXExtension.m */,
				A5F499462E24A9720057B28C /* NSBundle+HXPhotoPicker.h */,
				A5F499472E24A9720057B28C /* NSBundle+HXPhotoPicker.m */,
				A5F499482E24A9720057B28C /* NSDate+HXExtension.h */,
				A5F499492E24A9720057B28C /* NSDate+HXExtension.m */,
				A5F4994A2E24A9720057B28C /* NSString+HXExtension.h */,
				A5F4994B2E24A9720057B28C /* NSString+HXExtension.m */,
				A5F4994C2E24A9720057B28C /* NSTimer+HXExtension.h */,
				A5F4994D2E24A9720057B28C /* NSTimer+HXExtension.m */,
				A5F4994E2E24A9720057B28C /* PHAsset+HXExtension.h */,
				A5F4994F2E24A9720057B28C /* PHAsset+HXExtension.m */,
				A5F499502E24A9720057B28C /* UIButton+HXExtension.h */,
				A5F499512E24A9720057B28C /* UIButton+HXExtension.m */,
				A5F499522E24A9720057B28C /* UIColor+HXExtension.h */,
				A5F499532E24A9720057B28C /* UIColor+HXExtension.m */,
				A5F499542E24A9720057B28C /* UIFont+HXExtension.h */,
				A5F499552E24A9720057B28C /* UIFont+HXExtension.m */,
				A5F499562E24A9720057B28C /* UIImage+HXExtension.h */,
				A5F499572E24A9720057B28C /* UIImage+HXExtension.m */,
				A5F499582E24A9720057B28C /* UIImageView+HXExtension.h */,
				A5F499592E24A9720057B28C /* UIImageView+HXExtension.m */,
				A5F4995A2E24A9720057B28C /* UILabel+HXExtension.h */,
				A5F4995B2E24A9720057B28C /* UILabel+HXExtension.m */,
				A5F4995C2E24A9720057B28C /* UIView+HXExtension.h */,
				A5F4995D2E24A9720057B28C /* UIView+HXExtension.m */,
				A5F4995E2E24A9720057B28C /* UIViewController+HXExtension.h */,
				A5F4995F2E24A9720057B28C /* UIViewController+HXExtension.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		A5F499732E24A9720057B28C /* Controller */ = {
			isa = PBXGroup;
			children = (
				A5F499612E24A9720057B28C /* HXAlbumListViewController.h */,
				A5F499622E24A9720057B28C /* HXAlbumListViewController.m */,
				A5F499632E24A9720057B28C /* HXCustomCameraController.h */,
				A5F499642E24A9720057B28C /* HXCustomCameraController.m */,
				A5F499652E24A9720057B28C /* HXCustomCameraViewController.h */,
				A5F499662E24A9720057B28C /* HXCustomCameraViewController.m */,
				A5F499672E24A9720057B28C /* HXCustomNavigationController.h */,
				A5F499682E24A9720057B28C /* HXCustomNavigationController.m */,
				A5F499692E24A9720057B28C /* HXPhoto3DTouchViewController.h */,
				A5F4996A2E24A9720057B28C /* HXPhoto3DTouchViewController.m */,
				A5F4996B2E24A9720057B28C /* HXPhotoEditViewController.h */,
				A5F4996C2E24A9720057B28C /* HXPhotoEditViewController.m */,
				A5F4996D2E24A9720057B28C /* HXPhotoPreviewViewController.h */,
				A5F4996E2E24A9720057B28C /* HXPhotoPreviewViewController.m */,
				A5F4996F2E24A9720057B28C /* HXPhotoViewController.h */,
				A5F499702E24A9720057B28C /* HXPhotoViewController.m */,
				A5F499712E24A9720057B28C /* HXVideoEditViewController.h */,
				A5F499722E24A9720057B28C /* HXVideoEditViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		A5F499762E24A9720057B28C /* Controller */ = {
			isa = PBXGroup;
			children = (
				A5F499742E24A9720057B28C /* HX_PhotoEditViewController.h */,
				A5F499752E24A9720057B28C /* HX_PhotoEditViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		A5F4997D2E24A9720057B28C /* Model */ = {
			isa = PBXGroup;
			children = (
				A5F499772E24A9720057B28C /* HXMECancelBlock.h */,
				A5F499782E24A9720057B28C /* HXMECancelBlock.m */,
				A5F499792E24A9720057B28C /* HXPhotoEditChartletModel.h */,
				A5F4997A2E24A9720057B28C /* HXPhotoEditChartletModel.m */,
				A5F4997B2E24A9720057B28C /* HXPhotoEditGraffitiColorModel.h */,
				A5F4997C2E24A9720057B28C /* HXPhotoEditGraffitiColorModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		A5F499B02E24A9720057B28C /* View */ = {
			isa = PBXGroup;
			children = (
				A5F4997E2E24A9720057B28C /* HX_PhotoEditBottomView.h */,
				A5F4997F2E24A9720057B28C /* HX_PhotoEditBottomView.m */,
				A5F499802E24A9720057B28C /* HXPhotoClippingView.h */,
				A5F499812E24A9720057B28C /* HXPhotoClippingView.m */,
				A5F499822E24A9720057B28C /* HXPhotoEditChartletContentViewCell.h */,
				A5F499832E24A9720057B28C /* HXPhotoEditChartletContentViewCell.m */,
				A5F499842E24A9720057B28C /* HXPhotoEditChartletListView.h */,
				A5F499852E24A9720057B28C /* HXPhotoEditChartletListView.m */,
				A5F499862E24A9720057B28C /* HXPhotoEditChartletPreviewView.h */,
				A5F499872E24A9720057B28C /* HXPhotoEditChartletPreviewView.m */,
				A5F499882E24A9720057B28C /* HXPhotoEditClippingToolBar.h */,
				A5F499892E24A9720057B28C /* HXPhotoEditClippingToolBar.m */,
				A5F4998A2E24A9720057B28C /* HXPhotoEditDrawView.h */,
				A5F4998B2E24A9720057B28C /* HXPhotoEditDrawView.m */,
				A5F4998C2E24A9720057B28C /* HXPhotoEditGraffitiColorSizeView.h */,
				A5F4998D2E24A9720057B28C /* HXPhotoEditGraffitiColorSizeView.m */,
				A5F4998E2E24A9720057B28C /* HXPhotoEditGraffitiColorView.h */,
				A5F4998F2E24A9720057B28C /* HXPhotoEditGraffitiColorView.m */,
				A5F499902E24A9720057B28C /* HXPhotoEditGraffitiColorViewCell.h */,
				A5F499912E24A9720057B28C /* HXPhotoEditGraffitiColorViewCell.m */,
				A5F499922E24A9720057B28C /* HXPhotoEditGridLayer.h */,
				A5F499932E24A9720057B28C /* HXPhotoEditGridLayer.m */,
				A5F499942E24A9720057B28C /* HXPhotoEditGridMaskLayer.h */,
				A5F499952E24A9720057B28C /* HXPhotoEditGridMaskLayer.m */,
				A5F499962E24A9720057B28C /* HXPhotoEditGridView.h */,
				A5F499972E24A9720057B28C /* HXPhotoEditGridView.m */,
				A5F499982E24A9720057B28C /* HXPhotoEditImageView.h */,
				A5F499992E24A9720057B28C /* HXPhotoEditImageView.m */,
				A5F4999A2E24A9720057B28C /* HXPhotoEditingView.h */,
				A5F4999B2E24A9720057B28C /* HXPhotoEditingView.m */,
				A5F4999C2E24A9720057B28C /* HXPhotoEditMosaicView.h */,
				A5F4999D2E24A9720057B28C /* HXPhotoEditMosaicView.m */,
				A5F4999E2E24A9720057B28C /* HXPhotoEditResizeControl.h */,
				A5F4999F2E24A9720057B28C /* HXPhotoEditResizeControl.m */,
				A5F499A02E24A9720057B28C /* HXPhotoEditSplashMaskLayer.h */,
				A5F499A12E24A9720057B28C /* HXPhotoEditSplashMaskLayer.m */,
				A5F499A22E24A9720057B28C /* HXPhotoEditSplashView.h */,
				A5F499A32E24A9720057B28C /* HXPhotoEditSplashView.m */,
				A5F499A42E24A9720057B28C /* HXPhotoEditStickerItem.h */,
				A5F499A52E24A9720057B28C /* HXPhotoEditStickerItem.m */,
				A5F499A62E24A9720057B28C /* HXPhotoEditStickerItemContentView.h */,
				A5F499A72E24A9720057B28C /* HXPhotoEditStickerItemContentView.m */,
				A5F499A82E24A9720057B28C /* HXPhotoEditStickerItemView.h */,
				A5F499A92E24A9720057B28C /* HXPhotoEditStickerItemView.m */,
				A5F499AA2E24A9720057B28C /* HXPhotoEditStickerTrashView.h */,
				A5F499AB2E24A9720057B28C /* HXPhotoEditStickerTrashView.m */,
				A5F499AC2E24A9720057B28C /* HXPhotoEditStickerView.h */,
				A5F499AD2E24A9720057B28C /* HXPhotoEditStickerView.m */,
				A5F499AE2E24A9720057B28C /* HXPhotoEditTextView.h */,
				A5F499AF2E24A9720057B28C /* HXPhotoEditTextView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		A5F499B52E24A9720057B28C /* HXPhotoEdit */ = {
			isa = PBXGroup;
			children = (
				A5F499762E24A9720057B28C /* Controller */,
				A5F4997D2E24A9720057B28C /* Model */,
				A5F499B02E24A9720057B28C /* View */,
				A5F499B12E24A9720057B28C /* HXPhotoEdit.h */,
				A5F499B22E24A9720057B28C /* HXPhotoEdit.m */,
				A5F499B32E24A9720057B28C /* HXPhotoEditConfiguration.h */,
				A5F499B42E24A9720057B28C /* HXPhotoEditConfiguration.m */,
			);
			path = HXPhotoEdit;
			sourceTree = "<group>";
		};
		A5F499C02E24A9720057B28C /* Model */ = {
			isa = PBXGroup;
			children = (
				A5F499B62E24A9720057B28C /* HXAlbumModel.h */,
				A5F499B72E24A9720057B28C /* HXAlbumModel.m */,
				A5F499B82E24A9720057B28C /* HXCustomAssetModel.h */,
				A5F499B92E24A9720057B28C /* HXCustomAssetModel.m */,
				A5F499BA2E24A9720057B28C /* HXPhotoModel.h */,
				A5F499BB2E24A9720057B28C /* HXPhotoModel.m */,
				A5F499BC2E24A9720057B28C /* HXPhotoViewCellCustomProtocol.h */,
				A5F499BD2E24A9720057B28C /* HXPhotoViewProtocol.h */,
				A5F499BE2E24A9720057B28C /* HXPickerResult.h */,
				A5F499BF2E24A9720057B28C /* HXPickerResult.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		A5F499C22E24A9720057B28C /* Resources */ = {
			isa = PBXGroup;
			children = (
				A5F499C12E24A9720057B28C /* HXPhotoPicker.bundle */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		A5F499CD2E24A9720057B28C /* TransitionAnimation */ = {
			isa = PBXGroup;
			children = (
				A5F499C32E24A9720057B28C /* HXPhotoEditTransition.h */,
				A5F499C42E24A9720057B28C /* HXPhotoEditTransition.m */,
				A5F499C52E24A9720057B28C /* HXPhotoInteractiveTransition.h */,
				A5F499C62E24A9720057B28C /* HXPhotoInteractiveTransition.m */,
				A5F499C72E24A9720057B28C /* HXPhotoPersentInteractiveTransition.h */,
				A5F499C82E24A9720057B28C /* HXPhotoPersentInteractiveTransition.m */,
				A5F499C92E24A9720057B28C /* HXPhotoViewPresentTransition.h */,
				A5F499CA2E24A9720057B28C /* HXPhotoViewPresentTransition.m */,
				A5F499CB2E24A9720057B28C /* HXPhotoViewTransition.h */,
				A5F499CC2E24A9720057B28C /* HXPhotoViewTransition.m */,
			);
			path = TransitionAnimation;
			sourceTree = "<group>";
		};
		A5F499FA2E24A9720057B28C /* View */ = {
			isa = PBXGroup;
			children = (
				A5F499CE2E24A9720057B28C /* HXAlbumlistView.h */,
				A5F499CF2E24A9720057B28C /* HXAlbumlistView.m */,
				A5F499D02E24A9720057B28C /* HXCameraBottomView.h */,
				A5F499D12E24A9720057B28C /* HXCameraBottomView.m */,
				A5F499D22E24A9720057B28C /* HXCircleProgressView.h */,
				A5F499D32E24A9720057B28C /* HXCircleProgressView.m */,
				A5F499D42E24A9720057B28C /* HXCollectionView.h */,
				A5F499D52E24A9720057B28C /* HXCollectionView.m */,
				A5F499D62E24A9720057B28C /* HXCustomCollectionReusableView.h */,
				A5F499D72E24A9720057B28C /* HXCustomCollectionReusableView.m */,
				A5F499D82E24A9720057B28C /* HXCustomPreviewView.h */,
				A5F499D92E24A9720057B28C /* HXCustomPreviewView.m */,
				A5F499DA2E24A9720057B28C /* HXFullScreenCameraPlayView.h */,
				A5F499DB2E24A9720057B28C /* HXFullScreenCameraPlayView.m */,
				A5F499DC2E24A9720057B28C /* HXPhotoBottomSelectView.h */,
				A5F499DD2E24A9720057B28C /* HXPhotoBottomSelectView.m */,
				A5F499DE2E24A9720057B28C /* HXPhotoCustomNavigationBar.h */,
				A5F499DF2E24A9720057B28C /* HXPhotoCustomNavigationBar.m */,
				A5F499E02E24A9720057B28C /* HXPhotoLimitView.h */,
				A5F499E12E24A9720057B28C /* HXPhotoLimitView.m */,
				A5F499E22E24A9720057B28C /* HXPhotoPreviewBottomView.h */,
				A5F499E32E24A9720057B28C /* HXPhotoPreviewBottomView.m */,
				A5F499E42E24A9720057B28C /* HXPhotoPreviewImageViewCell.h */,
				A5F499E52E24A9720057B28C /* HXPhotoPreviewImageViewCell.m */,
				A5F499E62E24A9720057B28C /* HXPhotoPreviewLivePhotoCell.h */,
				A5F499E72E24A9720057B28C /* HXPhotoPreviewLivePhotoCell.m */,
				A5F499E82E24A9720057B28C /* HXPhotoPreviewVideoViewCell.h */,
				A5F499E92E24A9720057B28C /* HXPhotoPreviewVideoViewCell.m */,
				A5F499EA2E24A9720057B28C /* HXPhotoPreviewViewCell.h */,
				A5F499EB2E24A9720057B28C /* HXPhotoPreviewViewCell.m */,
				A5F499EC2E24A9720057B28C /* HXPhotoSubViewCell.h */,
				A5F499ED2E24A9720057B28C /* HXPhotoSubViewCell.m */,
				A5F499EE2E24A9720057B28C /* HXPhotoView.h */,
				A5F499EF2E24A9720057B28C /* HXPhotoView.m */,
				A5F499F02E24A9720057B28C /* HXPhotoViewFlowLayout.h */,
				A5F499F12E24A9720057B28C /* HXPhotoViewFlowLayout.m */,
				A5F499F22E24A9720057B28C /* HXPreviewContentView.h */,
				A5F499F32E24A9720057B28C /* HXPreviewContentView.m */,
				A5F499F42E24A9720057B28C /* HXPreviewImageView.h */,
				A5F499F52E24A9720057B28C /* HXPreviewImageView.m */,
				A5F499F62E24A9720057B28C /* HXPreviewLivePhotoView.h */,
				A5F499F72E24A9720057B28C /* HXPreviewLivePhotoView.m */,
				A5F499F82E24A9720057B28C /* HXPreviewVideoView.h */,
				A5F499F92E24A9720057B28C /* HXPreviewVideoView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		A5F49A082E24A9720057B28C /* HXPhotoPicker */ = {
			isa = PBXGroup;
			children = (
				A5F499602E24A9720057B28C /* Category */,
				A5F499732E24A9720057B28C /* Controller */,
				A5F499B52E24A9720057B28C /* HXPhotoEdit */,
				A5F499C02E24A9720057B28C /* Model */,
				A5F499C22E24A9720057B28C /* Resources */,
				A5F499CD2E24A9720057B28C /* TransitionAnimation */,
				A5F499FA2E24A9720057B28C /* View */,
				A5F499FB2E24A9720057B28C /* HXAssetManager.h */,
				A5F499FC2E24A9720057B28C /* HXAssetManager.m */,
				A5F499FD2E24A9720057B28C /* HXPhotoCommon.h */,
				A5F499FE2E24A9720057B28C /* HXPhotoCommon.m */,
				A5F499FF2E24A9720057B28C /* HXPhotoConfiguration.h */,
				A5F49A002E24A9720057B28C /* HXPhotoConfiguration.m */,
				A5F49A012E24A9720057B28C /* HXPhotoDefine.h */,
				A5F49A022E24A9720057B28C /* HXPhotoManager.h */,
				A5F49A032E24A9720057B28C /* HXPhotoManager.m */,
				A5F49A042E24A9720057B28C /* HXPhotoPicker.h */,
				A5F49A052E24A9720057B28C /* HXPhotoTools.h */,
				A5F49A062E24A9720057B28C /* HXPhotoTools.m */,
				A5F49A072E24A9720057B28C /* HXPhotoTypes.h */,
			);
			path = HXPhotoPicker;
			sourceTree = "<group>";
		};
		A5F49A092E24A9720057B28C /* picker */ = {
			isa = PBXGroup;
			children = (
				A5F49A082E24A9720057B28C /* HXPhotoPicker */,
				A5F49A652E24A99D0057B28C /* PTMPhotoPickerHelper.h */,
				A5F49A662E24A99D0057B28C /* PTMPhotoPickerHelper.m */,
			);
			path = picker;
			sourceTree = "<group>";
		};
		A5F49A692E24ACA90057B28C /* 拍照 */ = {
			isa = PBXGroup;
			children = (
				A5F49A682E24ACA90057B28C /* NailCameraViewController.swift */,
			);
			path = "拍照";
			sourceTree = "<group>";
		};
		A5F49A6D2E24BCB10057B28C /* 美甲 */ = {
			isa = PBXGroup;
			children = (
				A5F49A6B2E24BCB10057B28C /* NailProcessViewController.swift */,
				A5F49A6C2E24BCB10057B28C /* NailStyleViewController.swift */,
			);
			path = "美甲";
			sourceTree = "<group>";
		};
		A5FBC2172DD1942E0015329D /* vip */ = {
			isa = PBXGroup;
			children = (
				A5FBC45A2DD195460015329D /* VipViewController.swift */,
				A5FBC2152DD1942E0015329D /* APPMakeStoreIAPManager.swift */,
				A5FBC2162DD1942E0015329D /* APPStoreIAPObserver.swift */,
				A5E495C52DD6DED2007814CC /* DiscountPopupView.swift */,
			);
			path = vip;
			sourceTree = "<group>";
		};
		A5FBC45F2DD196E70015329D /* update */ = {
			isa = PBXGroup;
			children = (
				A5FBC45E2DD196E70015329D /* UpdateManager.swift */,
			);
			path = update;
			sourceTree = "<group>";
		};
		C4B153772C67D51F00E7420C /* iconImage */ = {
			isa = PBXGroup;
			children = (
				C4B1537C2C67D5F700E7420C /* icon1.png */,
				C4B1537B2C67D5F700E7420C /* icon2.png */,
				C4B1537A2C67D5F700E7420C /* icon3.png */,
				7974917E2C6DEE4300F5D89C /* icon4.png */,
				7974917F2C6DEE4300F5D89C /* icon5.png */,
			);
			path = iconImage;
			sourceTree = "<group>";
		};
		C4DA01552C5FBDE70058B6F7 /* InfoPlist */ = {
			isa = PBXGroup;
			children = (
				C4DA015A2C5FBE160058B6F7 /* InfoPlist.strings */,
				79EF1AAC2C6628F400B2D555 /* Localizable.strings */,
			);
			path = InfoPlist;
			sourceTree = "<group>";
		};
		D44D6A90A7BAAFF45F3BCB28 /* Pods */ = {
			isa = PBXGroup;
			children = (
				4D0E33C4E63B6D2D24F68AA6 /* Pods-HairCut.debug.xcconfig */,
				1FF9CED795EAA10E681585C5 /* Pods-HairCut.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7971E0B72C578042002381F1 /* HairCut */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7971E0CC2C578044002381F1 /* Build configuration list for PBXNativeTarget "HairCut" */;
			buildPhases = (
				428AF93F0279698EA117188C /* [CP] Check Pods Manifest.lock */,
				7971E0B42C578042002381F1 /* Sources */,
				7971E0B52C578042002381F1 /* Frameworks */,
				7971E0B62C578042002381F1 /* Resources */,
				24A47088A54E884A421D4F69 /* [CP] Embed Pods Frameworks */,
				26CBD1CD7562B5733151D29C /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = HairCut;
			productName = HairCut;
			productReference = 7971E0B82C578042002381F1 /* 发型测试.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7971E0B02C578042002381F1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					7971E0B72C578042002381F1 = {
						CreatedOnToolsVersion = 15.4;
						LastSwiftMigration = 1610;
					};
				};
			};
			buildConfigurationList = 7971E0B32C578042002381F1 /* Build configuration list for PBXProject "HairCut" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				"zh-Hant",
				vi,
				ja,
				"en-GB",
				"en-CA",
				"en-US",
				"en-AU",
				it,
				hu,
				ko,
				da,
				de,
				ru,
				fr,
				"fr-CA",
				ms,
				"pt-BR",
				"pt-PT",
				th,
				"es-MX",
				"es-ES",
			);
			mainGroup = 7971E0AF2C578042002381F1;
			productRefGroup = 7971E0B92C578042002381F1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7971E0B72C578042002381F1 /* HairCut */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7971E0B62C578042002381F1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				79F8FC3F2C6360C30054A742 /* FaceTestResult.json in Resources */,
				79EF1AAE2C6628F400B2D555 /* Localizable.strings in Resources */,
				C4B1537D2C67D5F700E7420C /* icon3.png in Resources */,
				79578DF72C61B2DF00387CD5 /* 纹理烫.jpg in Resources */,
				7971E0C52C578044002381F1 /* Assets.xcassets in Resources */,
				79578DF92C61B2DF00387CD5 /* 羊毛卷.jpg in Resources */,
				A5171F7E2E262F5E00C9233F /* FURenderKit快速集成文档.md in Resources */,
				C4B1537F2C67D5F700E7420C /* icon1.png in Resources */,
				C4DA01582C5FBE160058B6F7 /* InfoPlist.strings in Resources */,
				79578DF62C61B2DF00387CD5 /* 洛丽塔发型.jpg in Resources */,
				797491812C6DEE4300F5D89C /* icon5.png in Resources */,
				A59659C52D3A483C003DFD2E /* input.json in Resources */,
				797491802C6DEE4300F5D89C /* icon4.png in Resources */,
				A5171D972E25EF3E00C9233F /* beauty.json in Resources */,
				7971E0C82C578044002381F1 /* Base in Resources */,
				79578DFA2C61B2DF00387CD5 /* 爆炸头.jpg in Resources */,
				79578DF52C61B2DF00387CD5 /* 灰色渐变卷发.jpg in Resources */,
				A5F49A0A2E24A9720057B28C /* HXPhotoPicker.bundle in Resources */,
				C4B1537E2C67D5F700E7420C /* icon2.png in Resources */,
				79578DF82C61B2DF00387CD5 /* 挑染编织发.jpg in Resources */,
				A5DE73BF2E45CC1500E9FEE5 /* DrdpbEP7X5wvNgc-VOPHrp.xccrashpoint in Resources */,
				A5DE73C02E45CC1500E9FEE5 /* Dd-ygnFcfsvUANOm8EOQpA.xccrashpoint in Resources */,
				A5DE73C12E45CC1500E9FEE5 /* Cf8e0w1GBWNMkjzjyfdC8J.xccrashpoint in Resources */,
				A5DE73C22E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B.xccrashpoint in Resources */,
				A5DE73C32E45CC1500E9FEE5 /* DF3Jm2JaGsfRyv7x4bDP3B_副本.xccrashpoint in Resources */,
				A5DE73C42E45CC1500E9FEE5 /* BKpHzAc8Ri8t05shY3RgyH.xccrashpoint in Resources */,
				79F8FC3B2C634C9F0054A742 /* FaceTestResultImageMatch.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		24A47088A54E884A421D4F69 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HairCut/Pods-HairCut-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HairCut/Pods-HairCut-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-HairCut/Pods-HairCut-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		26CBD1CD7562B5733151D29C /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HairCut/Pods-HairCut-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HairCut/Pods-HairCut-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-HairCut/Pods-HairCut-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		428AF93F0279698EA117188C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-HairCut-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7971E0B42C578042002381F1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C4C734F52C5F383B00354F24 /* PickImageTool.swift in Sources */,
				A5171D992E25F03500C9233F /* BeautyParameter.swift in Sources */,
				79349D882CEAF10500ED9853 /* HairStyleEditColorCell.swift in Sources */,
				A5F49A0B2E24A9720057B28C /* HXVideoEditViewController.m in Sources */,
				A5F49A0C2E24A9720057B28C /* NSTimer+HXExtension.m in Sources */,
				A5F49A0D2E24A9720057B28C /* HXPhotoModel.m in Sources */,
				A5F49A0E2E24A9720057B28C /* UIView+HXExtension.m in Sources */,
				A5F49A0F2E24A9720057B28C /* HX_PhotoEditBottomView.m in Sources */,
				A5F49A102E24A9720057B28C /* UIImage+HXExtension.m in Sources */,
				A5F49A112E24A9720057B28C /* PHAsset+HXExtension.m in Sources */,
				A5F49A122E24A9720057B28C /* HXCollectionView.m in Sources */,
				A5F49A132E24A9720057B28C /* HXCameraBottomView.m in Sources */,
				A5F49A142E24A9720057B28C /* HXAlbumlistView.m in Sources */,
				A5F49A152E24A9720057B28C /* HXCircleProgressView.m in Sources */,
				A5F49A162E24A9720057B28C /* HXPhotoEditMosaicView.m in Sources */,
				A5DE73C62E45D06E00E9FEE5 /* ThreadSafeSnapKit.swift in Sources */,
				A5171DA32E26057900C9233F /* BeautyFilterManager.swift in Sources */,
				A5171D9F2E26030100C9233F /* BeautyEditVC.swift in Sources */,
				A5F49A172E24A9720057B28C /* HXPhotoPreviewImageViewCell.m in Sources */,
				A5F49A182E24A9720057B28C /* NSString+HXExtension.m in Sources */,
				A5F49A192E24A9720057B28C /* HXPhotoEditSplashView.m in Sources */,
				A5F49A1A2E24A9720057B28C /* HXAlbumListViewController.m in Sources */,
				A5F49A1B2E24A9720057B28C /* HXPhotoEditImageView.m in Sources */,
				A5F49A1C2E24A9720057B28C /* HXPhotoTools.m in Sources */,
				A5F49A1D2E24A9720057B28C /* HXPhotoEditChartletPreviewView.m in Sources */,
				A5F49A1E2E24A9720057B28C /* HXPhotoEditClippingToolBar.m in Sources */,
				A5F49A1F2E24A9720057B28C /* HXPhotoEditStickerItem.m in Sources */,
				A5F49A202E24A9720057B28C /* HXCustomCameraViewController.m in Sources */,
				A5F49A212E24A9720057B28C /* HXPhotoManager.m in Sources */,
				A5171D9B2E25F42B00C9233F /* PortraitBeautyVC.swift in Sources */,
				A5F49A222E24A9720057B28C /* HXPhotoEditStickerView.m in Sources */,
				A5F49A232E24A9720057B28C /* UIButton+HXExtension.m in Sources */,
				A5F49A242E24A9720057B28C /* HXPhotoPreviewBottomView.m in Sources */,
				A5F49A252E24A9720057B28C /* HXPhotoLimitView.m in Sources */,
				A5F49A262E24A9720057B28C /* HXPhotoCommon.m in Sources */,
				A5F49A272E24A9720057B28C /* HXCustomCameraController.m in Sources */,
				A5F49A282E24A9720057B28C /* HXPhotoEditDrawView.m in Sources */,
				A5F49A292E24A9720057B28C /* HXPhotoClippingView.m in Sources */,
				A5F49A2A2E24A9720057B28C /* HXPhotoEditStickerTrashView.m in Sources */,
				A5F49A2B2E24A9720057B28C /* NSBundle+HXPhotoPicker.m in Sources */,
				A5F49A2C2E24A9720057B28C /* HXPhotoViewPresentTransition.m in Sources */,
				A5F49A2D2E24A9720057B28C /* HXPreviewVideoView.m in Sources */,
				A5F49A2E2E24A9720057B28C /* HXPhotoEditResizeControl.m in Sources */,
				A5F49A2F2E24A9720057B28C /* HXAssetManager.m in Sources */,
				A5F49A302E24A9720057B28C /* HXPhotoEditChartletContentViewCell.m in Sources */,
				A5F49A312E24A9720057B28C /* HXCustomCollectionReusableView.m in Sources */,
				A5F49A322E24A9720057B28C /* HXPhotoPreviewViewController.m in Sources */,
				A5F49A332E24A9720057B28C /* HXPhotoViewTransition.m in Sources */,
				A5F49A342E24A9720057B28C /* UIFont+HXExtension.m in Sources */,
				A5F49A352E24A9720057B28C /* HXPhotoPersentInteractiveTransition.m in Sources */,
				A5F49A362E24A9720057B28C /* HXPhotoView.m in Sources */,
				A5F49A672E24A99D0057B28C /* PTMPhotoPickerHelper.m in Sources */,
				A5F49A372E24A9720057B28C /* HXPhotoEditingView.m in Sources */,
				A5F49A382E24A9720057B28C /* HXPhotoEditStickerItemContentView.m in Sources */,
				A5F49A392E24A9720057B28C /* HXPhotoConfiguration.m in Sources */,
				A5F49A3A2E24A9720057B28C /* HXPhotoEditSplashMaskLayer.m in Sources */,
				A5F49A3B2E24A9720057B28C /* HXCustomNavigationController.m in Sources */,
				A5F49A3C2E24A9720057B28C /* HXPhotoEditChartletModel.m in Sources */,
				A5F49A3D2E24A9720057B28C /* HXPhotoEditGraffitiColorModel.m in Sources */,
				A5F49A3E2E24A9720057B28C /* UIColor+HXExtension.m in Sources */,
				A5F49A3F2E24A9720057B28C /* HXAlbumModel.m in Sources */,
				A5F49A402E24A9720057B28C /* NSArray+HXExtension.m in Sources */,
				A5F49A412E24A9720057B28C /* HXPhotoEditGridLayer.m in Sources */,
				A5F49A422E24A9720057B28C /* HX_PhotoEditViewController.m in Sources */,
				A5F49A432E24A9720057B28C /* HXMECancelBlock.m in Sources */,
				A5F49A442E24A9720057B28C /* HXPhotoViewFlowLayout.m in Sources */,
				A5F49A452E24A9720057B28C /* HXPhotoEditGraffitiColorSizeView.m in Sources */,
				A5F49A462E24A9720057B28C /* HXCustomAssetModel.m in Sources */,
				A5F49A472E24A9720057B28C /* HXPreviewLivePhotoView.m in Sources */,
				A5F49A482E24A9720057B28C /* HXPhotoPreviewViewCell.m in Sources */,
				A5F49A492E24A9720057B28C /* HXPhotoEditGridView.m in Sources */,
				A5F49A4A2E24A9720057B28C /* HXFullScreenCameraPlayView.m in Sources */,
				A5F49A4B2E24A9720057B28C /* NSDate+HXExtension.m in Sources */,
				A5F49A4C2E24A9720057B28C /* HXPhotoEditConfiguration.m in Sources */,
				A5F49A4D2E24A9720057B28C /* HXPhotoViewController.m in Sources */,
				A5F49A4E2E24A9720057B28C /* HXPhotoEditViewController.m in Sources */,
				A5F49A4F2E24A9720057B28C /* HXPreviewImageView.m in Sources */,
				A5F49A502E24A9720057B28C /* HXPhotoPreviewLivePhotoCell.m in Sources */,
				A5F49A512E24A9720057B28C /* HXPhotoEditChartletListView.m in Sources */,
				A5F49A522E24A9720057B28C /* HXPhotoInteractiveTransition.m in Sources */,
				A5F49A532E24A9720057B28C /* HXPhotoBottomSelectView.m in Sources */,
				A5F49A542E24A9720057B28C /* UIViewController+HXExtension.m in Sources */,
				A5F49A552E24A9720057B28C /* HXPhotoEditGraffitiColorView.m in Sources */,
				A5F49A562E24A9720057B28C /* HXPhotoCustomNavigationBar.m in Sources */,
				A5EBA9ED2E2DE9AB0049EBD5 /* UsagePurchasePopupView.swift in Sources */,
				A5F49A572E24A9720057B28C /* HXPreviewContentView.m in Sources */,
				A5F49A582E24A9720057B28C /* HXPhotoSubViewCell.m in Sources */,
				A5F49A592E24A9720057B28C /* UIImageView+HXExtension.m in Sources */,
				A5F49A5A2E24A9720057B28C /* HXPhotoEditGridMaskLayer.m in Sources */,
				A5F49A5B2E24A9720057B28C /* HXPhotoEditGraffitiColorViewCell.m in Sources */,
				A5F49A5C2E24A9720057B28C /* HXPhotoEditStickerItemView.m in Sources */,
				A5F49A5D2E24A9720057B28C /* UILabel+HXExtension.m in Sources */,
				A5F49A5E2E24A9720057B28C /* HXPhotoEdit.m in Sources */,
				A5F49A5F2E24A9720057B28C /* HXPhotoEditTextView.m in Sources */,
				A5F49A602E24A9720057B28C /* HXPhotoEditTransition.m in Sources */,
				A5F49A612E24A9720057B28C /* HXPhotoPreviewVideoViewCell.m in Sources */,
				A5E61D3F2E29EF6A009EDBC4 /* HairEditVC.swift in Sources */,
				A5F49A622E24A9720057B28C /* HXCustomPreviewView.m in Sources */,
				A5F49A632E24A9720057B28C /* HXPhoto3DTouchViewController.m in Sources */,
				A5F49A642E24A9720057B28C /* HXPickerResult.m in Sources */,
				796D03592CE747B500046500 /* HaitStyleHairData.swift in Sources */,
				7971E0DF2C57A3D3002381F1 /* SettingVC.swift in Sources */,
				A5FBC4602DD196E70015329D /* UpdateManager.swift in Sources */,
				A5171DA12E26034B00C9233F /* BeautyParameterCell.swift in Sources */,
				79578DFE2C6207E700387CD5 /* FaceTestData.swift in Sources */,
				799686D42CED7BDB00971950 /* HDImageTool.m in Sources */,
				79C733BD2C5B6743002FBCE7 /* SettingIconViewCell.swift in Sources */,
				798F146C2C5CBD150029A34D /* HairCutChooseView.swift in Sources */,
				796D034F2CE6E55E00046500 /* HairStyleEditHairChooseView.swift in Sources */,
				A5EBA9F12E2E23CD0049EBD5 /* DeveloperAppCell.swift in Sources */,
				A5CE75062D8A9B75009C61F6 /* AIHairCutTipView.swift in Sources */,
				79349D812CEADEA900ED9853 /* HDColorCell.swift in Sources */,
				A5F49A6E2E24BCB10057B28C /* NailStyleViewController.swift in Sources */,
				A5F49A6F2E24BCB10057B28C /* NailProcessViewController.swift in Sources */,
				797215162CE5E337001B82BE /* HairStyleMainListView.swift in Sources */,
				C4C734F12C5F146200354F24 /* Array+Extension.swift in Sources */,
				A5B678582D8BA5AC009F5A53 /* MyAdManager.swift in Sources */,
				796A43202C6C57AC00E724C5 /* LanguageTool.swift in Sources */,
				C4C734EF2C5F138000354F24 /* Dictionary+Extension.swift in Sources */,
				793B25232C58814D005492DF /* HomeView.swift in Sources */,
				A5E61D432E29EFE7009EDBC4 /* HairFunctionCell.swift in Sources */,
				79C733C42C5B7029002FBCE7 /* Print+Extension.swift in Sources */,
				798F14622C5C6F5B0029A34D /* UIViewController+Extension.swift in Sources */,
				7971E0E62C57A91D002381F1 /* String+Extension.swift in Sources */,
				79C733C82C5B7202002FBCE7 /* FaceShapeTestVC.swift in Sources */,
				A5E61D412E29EFC9009EDBC4 /* HairEditModel.swift in Sources */,
				79C733CD2C5B9AEC002FBCE7 /* HairCutImageCell.swift in Sources */,
				C4992D332C5DC65500241D59 /* FaceShapeResultCell.swift in Sources */,
				7971E0E12C57A436002381F1 /* HomeVC.swift in Sources */,
				A5CE750C2D8AA68C009C61F6 /* UILabel+createLabels.m in Sources */,
				A5F49A732E24BD0C0057B28C /* VolcanoEngineAPI.swift in Sources */,
				79C733CF2C5B9B78002FBCE7 /* HairCutTextCell.swift in Sources */,
				793B25272C58BDA3005492DF /* SettingView.swift in Sources */,
				7971E0DD2C57A35E002381F1 /* TabbarVC.swift in Sources */,
				793B25252C589440005492DF /* UIColor+Extension.swift in Sources */,
				A5E495C62DD6DED3007814CC /* DiscountPopupView.swift in Sources */,
				796D03482CE6DC1200046500 /* HairStyleMainListHairCell.swift in Sources */,
				7971E0EA2C57A97D002381F1 /* HttpTool.swift in Sources */,
				79349D752CEAD24800ED9853 /* HDColorPickerLayer.swift in Sources */,
				A5F49A6A2E24ACA90057B28C /* NailCameraViewController.swift in Sources */,
				A5BC4A9A2DD47CC50048F92E /* TagBasedHairModelManager.swift in Sources */,
				79349D7B2CEAD5CB00ED9853 /* HDColorPickerBarView.swift in Sources */,
				79AD57032C5A4E7700A06A2F /* AboutUsView.swift in Sources */,
				A5CE741D2D8A715D009C61F6 /* UIButton+BackgroundState.swift in Sources */,
				798F146A2C5CBCEA0029A34D /* AIHairCutUploadButtonView.swift in Sources */,
				796D03572CE72B7400046500 /* HairStyleEditHairChooseCell.swift in Sources */,
				79AD56FF2C5A490A00A06A2F /* HotHairCutCollectionViewCell.swift in Sources */,
				79349D842CEADF5400ED9853 /* HDEditManager.swift in Sources */,
				796D03552CE6EEB000046500 /* HairStyleNavRightActionView.swift in Sources */,
				A5CE75102D8AA6DB009C61F6 /* ZYELabel.m in Sources */,
				C4992D372C5DCA3400241D59 /* FaceShapeSliderResultView.swift in Sources */,
				C4C734F32C5F15F700354F24 /* WebSocketTool.swift in Sources */,
				79EEBBE52C60A4F100905018 /* UIImage+Extension.swift in Sources */,
				7971E0D82C578646002381F1 /* WelcomeVC.swift in Sources */,
				7971E0D42C57853E002381F1 /* AppLoad.swift in Sources */,
				7971E0EC2C57A9F9002381F1 /* Const.swift in Sources */,
				793B25292C58C4F8005492DF /* WelcomeView.swift in Sources */,
				797215132CE5CAC5001B82BE /* HairStyleVC.swift in Sources */,
				79578DFC2C61B55800387CD5 /* PopularHairCutData.swift in Sources */,
				798F145E2C5C68F80029A34D /* AIHairResultVC.swift in Sources */,
				79C007452C5A33CF006521B7 /* LaunchScreenVC.swift in Sources */,
				A5FBC45B2DD195460015329D /* VipViewController.swift in Sources */,
				A5CE75032D8A7C89009C61F6 /* PopAnimationTool.m in Sources */,
				A5CE75042D8A7C89009C61F6 /* PopView.m in Sources */,
				79C733C62C5B71DB002FBCE7 /* AIHairCutVC.swift in Sources */,
				798F14602C5C69A00029A34D /* AIHairCutResultView.swift in Sources */,
				A5D2306A2D811EA8007D1911 /* AIHairModel.swift in Sources */,
				793B252B2C58CC20005492DF /* UIView+gradient.swift in Sources */,
				79349D7D2CEAD6C300ED9853 /* HDColorPickerView.swift in Sources */,
				A5CE74142D8A6C27009C61F6 /* ZYEExtension.swift in Sources */,
				A5CE74152D8A6C27009C61F6 /* ZYETapTool.swift in Sources */,
				A5CE74162D8A6C27009C61F6 /* UIDefault.swift in Sources */,
				A5F49A712E24BCE70057B28C /* NailModel.swift in Sources */,
				A5EBA9E62E2DE93E0049EBD5 /* VolcanoUsageManager.swift in Sources */,
				A5CE74172D8A6C27009C61F6 /* PTSWSwftObj.swift in Sources */,
				A5CE74182D8A6C27009C61F6 /* ZYEButtonPositionAndSpace.swift in Sources */,
				A5CE75092D8AA61D009C61F6 /* PTMImageSliderView.m in Sources */,
				A5CE74192D8A6C27009C61F6 /* ZyeButtonEx.swift in Sources */,
				A5CE741A2D8A6C27009C61F6 /* PTSWheader.swift in Sources */,
				A5CE741B2D8A6C27009C61F6 /* CreatUI.swift in Sources */,
				798E3CE82C65A1B000DE4093 /* FaceTestCellData.swift in Sources */,
				79EF1AA82C6620C500B2D555 /* ImageTool.swift in Sources */,
				A5171D922E25E92700C9233F /* PTMFilterHelper.m in Sources */,
				798F14642C5C7AA80029A34D /* FaceShapeTestView.swift in Sources */,
				799686D12CED728A00971950 /* HairCutMalloc.c in Sources */,
				A5FBC45D2DD196B20015329D /* STbaseVC.swift in Sources */,
				A5EBA9EF2E2E23A50049EBD5 /* DeveloperApp.swift in Sources */,
				A5EBA9EB2E2DE98F0049EBD5 /* HairEditUsageManager.swift in Sources */,
				A5FBC2182DD1942E0015329D /* APPStoreIAPObserver.swift in Sources */,
				A5FBC2192DD1942E0015329D /* APPMakeStoreIAPManager.swift in Sources */,
				799B6E262C6B346400FC2015 /* UserDefaultsTool.swift in Sources */,
				796D034A2CE6E35C00046500 /* HairStyleEditVC.swift in Sources */,
				A5556B682DCF393D00009F7E /* PTMPaintView.swift in Sources */,
				A5556B692DCF393D00009F7E /* BrushPreviewView.swift in Sources */,
				A5556B6A2DCF393D00009F7E /* AreaSelectViewController.swift in Sources */,
				A5556B6B2DCF393D00009F7E /* DrawView.swift in Sources */,
				79D8642C2CB4E020009DEB91 /* GoodRepulationTool.swift in Sources */,
				A59659C32D3A47F2003DFD2E /* AIHairJSON.swift in Sources */,
				798F146E2C5CBD490029A34D /* HairCutAreaView.swift in Sources */,
				79C733CA2C5B736A002FBCE7 /* AIHairCutView.swift in Sources */,
				79349D732CEACE3F00ED9853 /* HDColorPickerHelpers.swift in Sources */,
				79349D772CEAD42800ED9853 /* HDPointView.swift in Sources */,
				79349D792CEAD4A600ED9853 /* HDColorPickerBarLayer.swift in Sources */,
				79349D7F2CEADE2100ED9853 /* HDColorPalette.swift in Sources */,
				79C007472C5A33E5006521B7 /* LaunchScreenView.swift in Sources */,
				A5EBA9E92E2DE96E0049EBD5 /* AIStylistGuidePopupView.swift in Sources */,
				796D03512CE6E59B00046500 /* HairStyleEditRightAreaView.swift in Sources */,
				79EEBBE72C60ADA100905018 /* AIHairCutData.swift in Sources */,
				7971E0BC2C578042002381F1 /* AppDelegate.swift in Sources */,
				C4992D352C5DC9AB00241D59 /* FaceShapeTestResultView.swift in Sources */,
				79AD57012C5A4E6700A06A2F /* AboutUsVC.swift in Sources */,
				79C007412C59CD55006521B7 /* Enum.swift in Sources */,
				796D03532CE6E5C300046500 /* HairStyleEditColorChooseView.swift in Sources */,
				A596577E2D3A13B1003DFD2E /* RSA.m in Sources */,
				A596577F2D3A13B1003DFD2E /* UIImage+Base64.m in Sources */,
				A5CE732A2D8A6AF2009C61F6 /* AIHairCutSettingView.swift in Sources */,
				798F14662C5C90A30029A34D /* FaceShapeTestCell.swift in Sources */,
				A5BC4A9D2DD47CF80048F92E /* CategoryViewController.swift in Sources */,
				A5BC4A9E2DD47CF80048F92E /* CategoryListViewController.swift in Sources */,
				793B252D2C58E714005492DF /* SubscribeView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		7971E0C62C578044002381F1 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				7971E0C72C578044002381F1 /* Base */,
				79EF1AAA2C66279300B2D555 /* zh-Hans */,
				794B89022C6B3DF3000CE954 /* en */,
				797F80D82D23872000EEBDD8 /* zh-Hant */,
				797F80DB2D2387B200EEBDD8 /* vi */,
				797F80E12D23CFDB00EEBDD8 /* ja */,
				797F80E22D23D8A100EEBDD8 /* en-GB */,
				797F80E52D23D91500EEBDD8 /* en-CA */,
				797F80E82D23D9CE00EEBDD8 /* en-US */,
				797F80EB2D23DA9800EEBDD8 /* en-AU */,
				797F80EE2D23DB9F00EEBDD8 /* it */,
				797F80F12D23DC3F00EEBDD8 /* hu */,
				797F80F42D23DC9E00EEBDD8 /* ko */,
				797F80F72D23DD1F00EEBDD8 /* da */,
				797F80FA2D23DD7800EEBDD8 /* de */,
				797F80FD2D23DDC600EEBDD8 /* ru */,
				797F81002D23DDFF00EEBDD8 /* fr */,
				797F81032D23DE9400EEBDD8 /* fr-CA */,
				797F81062D23DEE700EEBDD8 /* ms */,
				797F81092D23DF3900EEBDD8 /* pt-BR */,
				797F810C2D23DF9B00EEBDD8 /* pt-PT */,
				797F810F2D23E00100EEBDD8 /* th */,
				797F81122D23E07F00EEBDD8 /* es-MX */,
				797F81152D23E0E400EEBDD8 /* es-ES */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		79EF1AAC2C6628F400B2D555 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				79EF1AAD2C6628F400B2D555 /* en */,
				79EF1AAF2C6628F800B2D555 /* zh-Hans */,
				797F80DA2D23872000EEBDD8 /* zh-Hant */,
				797F80DD2D2387B200EEBDD8 /* vi */,
				797F80E02D23CFDB00EEBDD8 /* ja */,
				797F80E42D23D8A100EEBDD8 /* en-GB */,
				797F80E72D23D91500EEBDD8 /* en-CA */,
				797F80EA2D23D9CE00EEBDD8 /* en-US */,
				797F80ED2D23DA9800EEBDD8 /* en-AU */,
				797F80F02D23DB9F00EEBDD8 /* it */,
				797F80F32D23DC3F00EEBDD8 /* hu */,
				797F80F62D23DC9E00EEBDD8 /* ko */,
				797F80F92D23DD1F00EEBDD8 /* da */,
				797F80FC2D23DD7800EEBDD8 /* de */,
				797F80FF2D23DDC600EEBDD8 /* ru */,
				797F81022D23DDFF00EEBDD8 /* fr */,
				797F81052D23DE9400EEBDD8 /* fr-CA */,
				797F81082D23DEE700EEBDD8 /* ms */,
				797F810B2D23DF3A00EEBDD8 /* pt-BR */,
				797F810E2D23DF9B00EEBDD8 /* pt-PT */,
				797F81112D23E00100EEBDD8 /* th */,
				797F81142D23E07F00EEBDD8 /* es-MX */,
				797F81172D23E0E400EEBDD8 /* es-ES */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		C4DA015A2C5FBE160058B6F7 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				C4DA01592C5FBE160058B6F7 /* en */,
				79EF1AAB2C66279300B2D555 /* zh-Hans */,
				797F80D92D23872000EEBDD8 /* zh-Hant */,
				797F80DC2D2387B200EEBDD8 /* vi */,
				797F80DF2D23CFDB00EEBDD8 /* ja */,
				797F80E32D23D8A100EEBDD8 /* en-GB */,
				797F80E62D23D91500EEBDD8 /* en-CA */,
				797F80E92D23D9CE00EEBDD8 /* en-US */,
				797F80EC2D23DA9800EEBDD8 /* en-AU */,
				797F80EF2D23DB9F00EEBDD8 /* it */,
				797F80F22D23DC3F00EEBDD8 /* hu */,
				797F80F52D23DC9E00EEBDD8 /* ko */,
				797F80F82D23DD1F00EEBDD8 /* da */,
				797F80FB2D23DD7800EEBDD8 /* de */,
				797F80FE2D23DDC600EEBDD8 /* ru */,
				797F81012D23DDFF00EEBDD8 /* fr */,
				797F81042D23DE9400EEBDD8 /* fr-CA */,
				797F81072D23DEE700EEBDD8 /* ms */,
				797F810A2D23DF3A00EEBDD8 /* pt-BR */,
				797F810D2D23DF9B00EEBDD8 /* pt-PT */,
				797F81102D23E00100EEBDD8 /* th */,
				797F81132D23E07F00EEBDD8 /* es-MX */,
				797F81162D23E0E400EEBDD8 /* es-ES */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		7971E0CA2C578044002381F1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7971E0CB2C578044002381F1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7971E0CD2C578044002381F1 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4D0E33C4E63B6D2D24F68AA6 /* Pods-HairCut.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = W83W7DVU38;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = HairCut/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = NSCameraUsageDescription;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = NSPhotoLibraryAddUsageDescription;
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = NSPhotoLibraryUsageDescription;
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "Need to get the ad identifier for your device in order to provide a better ad service";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.1;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-l\"swiftCoreGraphics\"",
					"-framework",
					"\"Alamofire\"",
					"-framework",
					"\"BSImagePicker\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"FBLPromises\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"Promises\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"UIKit\"",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.FoshanFullstack.hairstyle;
				PRODUCT_NAME = "发型测试";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/HairCut/HairCut-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7971E0CE2C578044002381F1 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1FF9CED795EAA10E681585C5 /* Pods-HairCut.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = W83W7DVU38;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = HairCut/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = NSCameraUsageDescription;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = NSPhotoLibraryAddUsageDescription;
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = NSPhotoLibraryUsageDescription;
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "Need to get the ad identifier for your device in order to provide a better ad service";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.1;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-l\"swiftCoreGraphics\"",
					"-framework",
					"\"Alamofire\"",
					"-framework",
					"\"BSImagePicker\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"FBLPromises\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"Promises\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"UIKit\"",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.FoshanFullstack.hairstyle;
				PRODUCT_NAME = "发型测试";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/HairCut/HairCut-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7971E0B32C578042002381F1 /* Build configuration list for PBXProject "HairCut" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7971E0CA2C578044002381F1 /* Debug */,
				7971E0CB2C578044002381F1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7971E0CC2C578044002381F1 /* Build configuration list for PBXNativeTarget "HairCut" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7971E0CD2C578044002381F1 /* Debug */,
				7971E0CE2C578044002381F1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7971E0B02C578042002381F1 /* Project object */;
}
