#!/bin/bash

# 创建共享库目录
SHARED_LIBS_DIR="$HOME/SharedLibs"
mkdir -p "$SHARED_LIBS_DIR"

echo "设置共享库目录: $SHARED_LIBS_DIR"

# 函数：设置共享库
setup_shared_lib() {
    local lib_name=$1
    local project_path=$2
    
    echo "处理库: $lib_name 在项目: $project_path"
    
    # 项目中的库路径
    local project_lib_path="$project_path/Pods/$lib_name"
    # 共享库路径
    local shared_lib_path="$SHARED_LIBS_DIR/$lib_name"
    
    if [ -d "$project_lib_path" ]; then
        if [ ! -d "$shared_lib_path" ]; then
            # 第一次：移动到共享目录
            echo "移动 $lib_name 到共享目录"
            mv "$project_lib_path" "$shared_lib_path"
        else
            # 已存在：删除项目中的副本
            echo "删除项目中的 $lib_name 副本"
            rm -rf "$project_lib_path"
        fi
        
        # 创建符号链接
        echo "创建符号链接: $project_lib_path -> $shared_lib_path"
        ln -sf "$shared_lib_path" "$project_lib_path"
    fi
}

# 使用方法
echo "使用方法："
echo "./setup_shared_libs.sh <项目路径1> <项目路径2> ..."
echo ""
echo "例如："
echo "./setup_shared_libs.sh /path/to/project1 /path/to/project2"
echo ""

# 如果提供了参数，处理项目
if [ $# -gt 0 ]; then
    # 要共享的大型库列表
    LARGE_LIBS=("Ads-CN" "OpenCV2" "FURenderKit")
    
    for project_path in "$@"; do
        echo "处理项目: $project_path"
        
        if [ ! -d "$project_path/Pods" ]; then
            echo "警告: $project_path/Pods 不存在，跳过"
            continue
        fi
        
        for lib in "${LARGE_LIBS[@]}"; do
            setup_shared_lib "$lib" "$project_path"
        done
        
        echo "项目 $project_path 处理完成"
        echo "---"
    done
    
    echo "所有项目处理完成！"
    echo "共享库位置: $SHARED_LIBS_DIR"
    echo "磁盘空间节省: 每个额外项目节省约 $(du -sh $SHARED_LIBS_DIR 2>/dev/null | cut -f1 || echo "未知") 空间"
fi
