# 共享库解决方案 - 真正节省磁盘空间

## 问题解决

✅ **真正解决了空间占用问题**：
- 所有大型库（Ads-CN: 809M, FURenderKit: 75M, BUTTSDKFramework: 37M）只存储一份
- 多个项目通过符号链接共享同一份文件
- 每个新项目节省约 **921M** 磁盘空间

## 目录结构

```
~/SharedLibs/                    # 共享库存储位置
├── Ads-CN/          (809M)     # 实际文件
├── FURenderKit/     (75M)      # 实际文件  
└── BUTTSDKFramework/ (37M)     # 实际文件

项目1/Pods/
├── Ads-CN -> ~/SharedLibs/Ads-CN              # 符号链接
├── FURenderKit -> ~/SharedLibs/FURenderKit    # 符号链接
└── 其他小库...

项目2/Pods/
├── Ads-CN -> ~/SharedLibs/Ads-CN              # 符号链接（共享同一份）
├── FURenderKit -> ~/SharedLibs/FURenderKit    # 符号链接（共享同一份）
└── 其他小库...
```

## 使用方法

### 新项目设置
```bash
cd /path/to/new/project
/path/to/pod_install_with_sharing.sh
```

### 现有项目转换
```bash
cd /path/to/existing/project
/path/to/setup_shared_libs.sh /path/to/project1 /path/to/project2
```

### 批量处理多个项目
```bash
./setup_shared_libs.sh \
  /path/to/project1 \
  /path/to/project2 \
  /path/to/project3
```

## 优势

1. **真正节省空间**：每个额外项目节省 921M 空间
2. **自动化**：脚本自动处理，无需手动操作
3. **透明性**：对项目代码完全透明，不影响编译和运行
4. **版本一致**：所有项目使用相同版本的库
5. **易于管理**：所有大型库集中在 `~/SharedLibs/` 目录

## 工作原理

1. **第一个项目**：正常下载库到 `~/SharedLibs/`
2. **后续项目**：
   - 检测到库已存在于共享目录
   - 删除项目中的重复副本
   - 创建符号链接指向共享库
   - 节省磁盘空间

## 注意事项

- ✅ 符号链接对 Xcode 和编译器完全透明
- ✅ 不影响项目的正常编译和运行
- ✅ 可以随时删除符号链接，恢复独立库
- ⚠️ 删除 `~/SharedLibs/` 会影响所有使用共享库的项目

## 空间节省示例

```
传统方式：
项目1: 921M (大型库) + 其他库
项目2: 921M (大型库) + 其他库  
项目3: 921M (大型库) + 其他库
总计: 2763M

共享方式：
共享库: 921M (一份)
项目1: 0M (符号链接) + 其他库
项目2: 0M (符号链接) + 其他库
项目3: 0M (符号链接) + 其他库
总计: 921M

节省: 1842M (66.7% 空间节省)
```
