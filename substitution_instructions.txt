# 需要进行本地化修改的文件和行

1. 已经修改：
   - AreaSelectViewController.swift 中第326行：
     替换：SVProgressHUD.show(withStatus: "正在生成mask...")
     为：SVProgressHUD.show(withStatus: local("正在生成mask..."))

2. 需要修改：
   - AIHairCutView.swift 中第279行：
     替换：SVProgressHUD.showInfo(withStatus: "请先选择修改区域")
     为：SVProgressHUD.showInfo(withStatus: local("请先选择修改区域"))

   - AIHairCutView.swift 中第304行：
     替换：SVProgressHUD.showError(withStatus: "获取数据失败")
     为：SVProgressHUD.showError(withStatus: local("获取数据失败"))

   - AIHairResultVC.swift 中第105行：
     替换：SVProgressHUD.showError(withStatus: "获取数据失败")
     为：SVProgressHUD.showError(withStatus: local("获取数据失败"))

3. 需要修改的其他未本地化文本文件：
   - DiscountPopupView.swift 中：
     替换：label.text = "成为会员可解锁全部功能"
     为：label.text = "成为会员可解锁全部功能".localized

     替换：let titleText = "恭喜获3折购月度会员"
     为：let titleText = "恭喜获3折购月度会员".localized

     替换：let originalPriceText = NSMutableAttributedString(string: "原价")
     为：let originalPriceText = NSMutableAttributedString(string: "原价".localized)

     替换：let currentPriceText = NSMutableAttributedString(string: "现价")
     为：let currentPriceText = NSMutableAttributedString(string: "现价".localized)

     替换：disclaimerLabel.text = "前3天免费，然后\(currencySymbol)\(formattedPriceDigits)/月，试用期间可随时取消"
     为：disclaimerLabel.text = String(format: "前3天免费，然后%@%@/月，试用期间可随时取消".localized, currencySymbol, formattedPriceDigits)

   - SubscribeView.swift 中：
     替换：benefitsLabel.text = "开通会员享受更多权益，所有功能任意使用，去广告更流畅。"
     为：benefitsLabel.text = "开通会员享受更多权益，所有功能任意使用，去广告更流畅。".localized

     替换：detailDescriptionLabel.text = "免费试用3天体验发型测试全部功能权限，您可以在试用期间无限制的使用所有功能，如果体验不满意可以随时取消，请在当前订阅周期到期前24小时以前"
     为：detailDescriptionLabel.text = "免费试用3天体验发型测试全部功能权限，您可以在试用期间无限制的使用所有功能，如果体验不满意可以随时取消，请在当前订阅周期到期前24小时以前".localized

     替换：subscribeButton.setTitle("立即订阅", for: .normal)
     为：subscribeButton.setTitle("立即订阅".localized, for: .normal)

     替换：priceLabel.text = "3天免费之后\(priceString)/月"
     为：priceLabel.text = String(format: "3天免费之后%@/月".localized, priceString)

   - VipViewController.swift 中：
     替换：explainButton.setTitle(NSLocalizedString("使用条款", comment: ""), for: .normal)
     为：explainButton.setTitle("使用条款".localized, for: .normal)

     替换：explainButton.setTitle(NSLocalizedString("隐私政策", comment: ""), for: .normal)
     为：explainButton.setTitle("隐私政策".localized, for: .normal)

     替换：explainButton.setTitle(NSLocalizedString("恢复订阅", comment: ""), for: .normal)
     为：explainButton.setTitle("恢复订阅".localized, for: .normal)

     替换：let alertController = UIAlertController(title: NSLocalizedString("提示", comment: ""), message: NSLocalizedString("价格加载失败,请重新加载", comment: ""), preferredStyle: .alert)
     为：let alertController = UIAlertController(title: "提示".localized, message: "价格加载失败,请重新加载".localized, preferredStyle: .alert)

     替换：let action = UIAlertAction(title: NSLocalizedString("好的", comment: ""), style: .default) { _ in
     为：let action = UIAlertAction(title: "好的".localized, style: .default) { _ in 