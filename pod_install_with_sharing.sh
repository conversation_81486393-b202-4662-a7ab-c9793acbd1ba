#!/bin/bash

# 智能 pod install 脚本，自动共享大型库

SHARED_LIBS_DIR="$HOME/SharedLibs"
LARGE_LIBS=("Ads-CN" "OpenCV2" "FURenderKit" "BUTTSDKFramework")

# 创建共享目录
mkdir -p "$SHARED_LIBS_DIR"

echo "开始 pod install..."
pod install

echo "Pod install 完成，开始设置库共享..."

# 处理每个大型库
for lib in "${LARGE_LIBS[@]}"; do
    project_lib_path="./Pods/$lib"
    shared_lib_path="$SHARED_LIBS_DIR/$lib"
    
    if [ -d "$project_lib_path" ]; then
        echo "处理库: $lib"
        
        if [ ! -d "$shared_lib_path" ]; then
            # 第一次：移动到共享目录
            echo "  移动到共享目录: $shared_lib_path"
            mv "$project_lib_path" "$shared_lib_path"
        else
            # 已存在：删除当前副本
            echo "  删除重复副本"
            rm -rf "$project_lib_path"
        fi
        
        # 创建符号链接
        echo "  创建符号链接"
        ln -sf "$shared_lib_path" "$project_lib_path"
        
        # 显示大小
        size=$(du -sh "$shared_lib_path" 2>/dev/null | cut -f1)
        echo "  库大小: $size"
    fi
done

echo ""
echo "✅ 库共享设置完成！"
echo "📁 共享库位置: $SHARED_LIBS_DIR"
echo "💾 总共享库大小: $(du -sh $SHARED_LIBS_DIR 2>/dev/null | cut -f1 || echo "未知")"
echo ""
echo "现在所有使用相同库的项目都会共享这些文件，节省磁盘空间！"
