#!/bin/bash

# 通用共享库 Pod 安装脚本
# 使用方法：在任何项目目录运行 ~/SharedLibs/scripts/pod_install_shared.sh

SHARED_LIBS_DIR="$HOME/SharedLibs"
LARGE_LIBS=("Ads-CN" "OpenCV2" "FURenderKit" "BUTTSDKFramework" "Masonry" "SVProgressHUD" "UMCommon" "UMDevice" "UMAPM")

# 检查是否在项目目录中
if [ ! -f "Podfile" ]; then
    echo "❌ 错误：当前目录没有 Podfile 文件"
    echo "请在项目根目录运行此脚本"
    exit 1
fi

# 创建共享目录
mkdir -p "$SHARED_LIBS_DIR"

echo "🚀 开始 pod install..."
echo "项目路径: $(pwd)"
echo ""

# 执行 pod install
if ! pod install; then
    echo "❌ pod install 失败"
    exit 1
fi

echo ""
echo "📦 Pod install 完成，开始设置库共享..."

# 处理每个大型库
total_saved=0
for lib in "${LARGE_LIBS[@]}"; do
    project_lib_path="./Pods/$lib"
    shared_lib_path="$SHARED_LIBS_DIR/$lib"
    
    if [ -d "$project_lib_path" ]; then
        echo "处理库: $lib"
        
        # 计算库大小
        if [ -d "$shared_lib_path" ]; then
            # 已存在共享库，计算节省的空间
            size_mb=$(du -sm "$project_lib_path" 2>/dev/null | cut -f1)
            total_saved=$((total_saved + size_mb))
            echo "  删除重复副本 (节省 ${size_mb}M)"
            rm -rf "$project_lib_path"
        else
            # 第一次，移动到共享目录
            size_mb=$(du -sm "$project_lib_path" 2>/dev/null | cut -f1)
            echo "  移动到共享目录: $shared_lib_path (${size_mb}M)"
            mv "$project_lib_path" "$shared_lib_path"
        fi
        
        # 创建符号链接
        echo "  创建符号链接"
        ln -sf "$shared_lib_path" "$project_lib_path"
    fi
done

echo ""
echo "✅ 库共享设置完成！"
echo "📁 共享库位置: $SHARED_LIBS_DIR"
echo "💾 本次节省空间: ${total_saved}M"
echo "📊 总共享库大小: $(du -sh $SHARED_LIBS_DIR 2>/dev/null | cut -f1 || echo "未知")"
echo ""
echo "🎉 现在所有使用相同库的项目都会共享这些文件！"

# 显示符号链接状态
echo ""
echo "📋 当前项目的共享库状态："
for lib in "${LARGE_LIBS[@]}"; do
    if [ -L "./Pods/$lib" ]; then
        target=$(readlink "./Pods/$lib")
        echo "  $lib -> $target"
    fi
done
