#!/bin/bash

# 配置共享库列表的脚本

SCRIPT_FILE="$HOME/SharedLibs/scripts/pod_install_shared.sh"

echo "🔧 共享库配置工具"
echo "=================="

# 显示当前配置
echo "📋 当前共享库列表："
current_libs=$(grep "LARGE_LIBS=" "$SCRIPT_FILE" | sed 's/.*(\(.*\)).*/\1/' | tr '"' ' ' | tr '(' ' ' | tr ')' ' ')
echo "$current_libs" | tr ' ' '\n' | grep -v '^$' | nl

echo ""
echo "🛠️ 操作选项："
echo "1. 添加新库到共享列表"
echo "2. 从共享列表删除库"
echo "3. 查看推荐的共享库"
echo "4. 重置为默认配置"
echo "5. 退出"

read -p "请选择操作 (1-5): " choice

case $choice in
    1)
        echo ""
        read -p "请输入要添加的库名 (例如: SDWebImage): " new_lib
        if [ ! -z "$new_lib" ]; then
            # 检查是否已存在
            if echo "$current_libs" | grep -q "$new_lib"; then
                echo "⚠️  库 '$new_lib' 已在共享列表中"
            else
                # 添加到列表
                sed -i '' "s/LARGE_LIBS=(\(.*\))/LARGE_LIBS=(\1 \"$new_lib\")/" "$SCRIPT_FILE"
                echo "✅ 已添加 '$new_lib' 到共享列表"
            fi
        fi
        ;;
    2)
        echo ""
        read -p "请输入要删除的库名: " remove_lib
        if [ ! -z "$remove_lib" ]; then
            # 从列表删除
            sed -i '' "s/\"$remove_lib\" //g" "$SCRIPT_FILE"
            sed -i '' "s/ \"$remove_lib\"//g" "$SCRIPT_FILE"
            echo "✅ 已从共享列表删除 '$remove_lib'"
        fi
        ;;
    3)
        echo ""
        echo "📚 推荐的共享库："
        echo "大型库 (>50M):"
        echo "  - Ads-CN (809M) - 广告SDK"
        echo "  - FURenderKit (75M) - FU渲染引擎"
        echo "  - OpenCV2 (>100M) - 计算机视觉"
        echo ""
        echo "中型库 (10-50M):"
        echo "  - BUTTSDKFramework (37M) - 语音SDK"
        echo "  - SDWebImage - 图片缓存"
        echo ""
        echo "小型但常用库:"
        echo "  - Masonry - 自动布局"
        echo "  - SVProgressHUD - 进度提示"
        echo "  - UMCommon - 友盟通用"
        echo "  - Alamofire - 网络请求"
        echo "  - SnapKit - Swift自动布局"
        ;;
    4)
        echo ""
        echo "🔄 重置为默认配置..."
        sed -i '' 's/LARGE_LIBS=.*/LARGE_LIBS=("Ads-CN" "OpenCV2" "FURenderKit" "BUTTSDKFramework" "Masonry" "SVProgressHUD" "UMCommon" "UMDevice" "UMAPM")/' "$SCRIPT_FILE"
        echo "✅ 已重置为默认配置"
        ;;
    5)
        echo "👋 退出配置工具"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        ;;
esac

echo ""
echo "📋 更新后的共享库列表："
updated_libs=$(grep "LARGE_LIBS=" "$SCRIPT_FILE" | sed 's/.*(\(.*\)).*/\1/' | tr '"' ' ' | tr '(' ' ' | tr ')' ' ')
echo "$updated_libs" | tr ' ' '\n' | grep -v '^$' | nl
