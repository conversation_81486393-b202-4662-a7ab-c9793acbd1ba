# 共享库通用脚本使用说明

## 🎯 一次设置，所有项目通用

脚本已放在 `~/SharedLibs/scripts/` 目录，所有项目都可以直接使用。

## 📝 使用方法

### 方法一：完整路径（推荐）
```bash
cd /path/to/your/project
~/SharedLibs/scripts/pod_install_shared.sh
```

### 方法二：简短别名
```bash
cd /path/to/your/project
~/SharedLibs/scripts/install
```

## 🔄 完整使用流程

### 1. 编辑项目的 Podfile
```ruby
# 直接写原始库名
pod 'Ads-CN'        # 需要广告SDK
pod 'OpenCV2'       # 需要OpenCV（可选）
pod 'FURenderKit'   # 需要FU渲染（可选）
```

### 2. 运行共享脚本
```bash
cd /path/to/your/project
~/SharedLibs/scripts/pod_install_shared.sh
```

### 3. 查看结果
脚本会自动：
- 执行 `pod install`
- 将大型库移动到 `~/SharedLibs/`
- 创建符号链接
- 显示节省的空间

## 📊 示例输出
```
🚀 开始 pod install...
项目路径: /Users/<USER>/Desktop/项目/测量相机/MeasurementCamera-iOS

📦 Pod install 完成，开始设置库共享...
处理库: Ads-CN
  删除重复副本 (节省 809M)
  创建符号链接

✅ 库共享设置完成！
📁 共享库位置: /Users/<USER>/SharedLibs
💾 本次节省空间: 809M
📊 总共享库大小: 922M

📋 当前项目的共享库状态：
  Ads-CN -> /Users/<USER>/SharedLibs/Ads-CN
```

## 🗂️ 目录结构
```
~/SharedLibs/
├── scripts/
│   ├── pod_install_shared.sh  # 主脚本
│   └── install                # 简短别名
├── Ads-CN/                    # 共享的广告SDK (809M)
├── FURenderKit/               # 共享的FU渲染 (75M)
└── BUTTSDKFramework/          # 共享的语音SDK (37M)
```

## ⚡ 快速测试

在您的测量相机项目中测试：
```bash
cd "/Users/<USER>/Desktop/项目/测量相机/MeasurementCamera-iOS"
~/SharedLibs/scripts/pod_install_shared.sh
```

## 🔧 自定义共享库列表

如果想要添加或修改共享的库，编辑脚本文件：

```bash
# 编辑脚本
nano ~/SharedLibs/scripts/pod_install_shared.sh

# 找到这一行并修改：
LARGE_LIBS=("Ads-CN" "OpenCV2" "FURenderKit" "BUTTSDKFramework" "Masonry" "SVProgressHUD" "UMCommon" "UMDevice" "UMAPM")
```

### 当前支持的共享库：
- **Ads-CN** (809M) - 广告SDK
- **FURenderKit** (75M) - FU渲染引擎
- **BUTTSDKFramework** (37M) - 语音SDK
- **Masonry** - 自动布局库
- **SVProgressHUD** - 进度提示库
- **UMCommon** - 友盟通用库
- **UMDevice** - 友盟设备库
- **UMAPM** - 友盟性能监控
- **OpenCV2** - 计算机视觉库（如果使用）

### 添加新库示例：
```bash
# 如果想添加 SDWebImage 到共享列表
LARGE_LIBS=("Ads-CN" "OpenCV2" "FURenderKit" "BUTTSDKFramework" "Masonry" "SVProgressHUD" "UMCommon" "UMDevice" "UMAPM" "SDWebImage")
```

## 🎉 优势

1. **一次设置**：脚本放在固定位置，所有项目通用
2. **自动检测**：自动检测项目中的大型库
3. **智能共享**：第一次移动，后续创建链接
4. **空间节省**：每个项目节省数百MB空间
5. **透明使用**：对开发和编译完全透明
6. **可定制**：可以自由添加或删除要共享的库
