#!/bin/bash

echo "恢复本地 Pod 结构..."

# 创建 AdsSDK 目录和文件
mkdir -p ~/LocalPods/AdsSDK/Classes

# 创建 AdsSDK.podspec
cat > ~/LocalPods/AdsSDK/AdsSDK.podspec << 'EOF'
Pod::Spec.new do |spec|
  spec.name         = "AdsSDK"
  spec.version      = "1.0.0"
  spec.summary      = "Ads-CN SDK wrapper"
  spec.description  = "A local pod that wraps Ads-CN SDK to avoid duplicating downloads across multiple projects"
  
  spec.homepage     = "https://github.com/yourname/AdsSDK"
  spec.license      = { :type => "MIT" }
  spec.author       = { "Your Name" => "<EMAIL>" }
  
  spec.platform     = :ios, "12.0"
  spec.source       = { :path => "." }
  
  # 如果有源文件
  spec.source_files = "Classes/**/*"
  
  # 只包含 Ads-CN
  spec.dependency 'Ads-CN'
  
end
EOF

# 创建 OpenCVSDK 目录和文件
mkdir -p ~/LocalPods/OpenCVSDK/Classes

# 创建 OpenCVSDK.podspec
cat > ~/LocalPods/OpenCVSDK/OpenCVSDK.podspec << 'EOF'
Pod::Spec.new do |spec|
  spec.name         = "OpenCVSDK"
  spec.version      = "1.0.0"
  spec.summary      = "OpenCV2 SDK wrapper"
  spec.description  = "A local pod that wraps OpenCV2 SDK to avoid duplicating downloads across multiple projects"
  
  spec.homepage     = "https://github.com/yourname/OpenCVSDK"
  spec.license      = { :type => "MIT" }
  spec.author       = { "Your Name" => "<EMAIL>" }
  
  spec.platform     = :ios, "12.0"
  spec.source       = { :path => "." }
  
  # 如果有源文件
  spec.source_files = "Classes/**/*"
  
  # 只包含 OpenCV2
  spec.dependency 'OpenCV2'
  
end
EOF

echo "✅ 本地 Pod 结构恢复完成！"
echo ""
echo "现在可以运行以下命令重新安装依赖："
echo "pod install"
echo ""
echo "或者如果需要完全重新下载："
echo "pod cache clean --all"
echo "rm -rf Pods Podfile.lock"
echo "pod install"
